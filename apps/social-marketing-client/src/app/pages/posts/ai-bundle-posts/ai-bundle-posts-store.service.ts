import { Inject, Injectable } from '@angular/core';
import { Store } from '../../../core/store';
import {
  AI_BUNDLE_POSTS_INITIAL_STATE,
  AI_BUNDLE_POSTS_STEPS,
  AIBundlePostsState,
  BundleSocialPosts,
  GenerateContentSetup,
  Media,
  MediaType,
  PostScheduleType,
  PostsInterface,
  SocialConnection,
  TechnicalPromptData,
} from './model';
import { PostableService, SocialServiceService } from '../../../shared/social-service/social-service.service';

import { ContentGenerationService, GeneratePostsResponse, MessageLength } from '@vendasta/social-posts';

import { catchError, finalize, map, switchMap, take, tap } from 'rxjs/operators';
import { BehaviorSubject, combineLatest, forkJoin, Observable, of, Subject } from 'rxjs';

import { CustomInstructionsService } from '../../../core/ai/custom-instructions.service';
import { ComposerSettingsService } from '../../../core/composer-settings/composer-settings.service';
import { NEW_POST_ID } from '../../../composer/constants';
import { Customization } from '../../../core/post/customization';
import { PostInterface } from '@vendasta/composer';
import { SocialService } from '../../../composer/post';
import { ComposerSettings } from '../../../composer/interfaces';
import { mediaentriesToUploadedMedia } from '../../../composer/shared-methods';
import { GenerateType } from '../../../composer/components/suggestions/generate-type';
import { STOCK_IMAGE_SERVICE } from './stock-images/stock-image.token';
import { StockImageService } from './stock-images/stock-image.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfigService, SMConfig } from '../../../core';
import { MetadataV2Interface } from '@vendasta/social-posts/lib/_internal/interfaces/social-post-v2.interface';

const TECHNICAL_PROMPT = 'The post must be about the provided prompt:';

interface AiBundlePostsViewModel extends AIBundlePostsState {
  allowedNetworks: SocialConnection[];
}

@Injectable()
export class AiBundlePostsStoreService extends Store<AIBundlePostsState> {
  private generateContentSubject = new BehaviorSubject<GenerateContentSetup | null>(null);
  private generationCompleteSubject = new Subject<void>();
  generateContent$ = this.generateContentSubject.asObservable();
  generationComplete$ = this.generationCompleteSubject.asObservable();
  allowedNetworks$ = this.loadAllowedNetworks();
  excludedServices = [SocialService.TIKTOK_ACCOUNT, SocialService.YOUTUBE_CHANNEL];
  receivedData: { postType: string; month: number };
  allowRestart = false;
  isTriggredFromCalendar = false;

  vm$: Observable<AiBundlePostsViewModel> = combineLatest([this.state$, this.allowedNetworks$]).pipe(
    map(([state, allowedNetworks]) => ({
      ...state,
      allowedNetworks,
    })),
  );
  private metaData: MetadataV2Interface[];

  constructor(
    private config: ConfigService,
    private router: Router,
    private route: ActivatedRoute,
    private socialService: SocialServiceService,
    private contentGenerationService: ContentGenerationService,
    private commonInstructionsService: CustomInstructionsService,
    private composerSettingsService: ComposerSettingsService,
    private configService: ConfigService,
    @Inject(STOCK_IMAGE_SERVICE) private stockImageService: StockImageService,
  ) {
    super(AI_BUNDLE_POSTS_INITIAL_STATE);
    this.configService.config$.pipe(take(1)).subscribe((config: SMConfig) => {
      this.metaData = [
        {
          name: 'ag_id',
          value: config.account_group_id,
        },
        {
          name: 'partner_id',
          value: config.partner_id,
        },
      ];
    });

    this.route.queryParams
      .pipe(
        map((params) => {
          if (params['date']) {
            this.isTriggredFromCalendar = true;
          }
        }),
      )
      .subscribe();
  }

  loadAllowedNetworks(): Observable<SocialConnection[]> {
    return this.socialService.loadApiServices().pipe(
      map(({ message }) =>
        message
          .filter((service) => !this.excludedServices.includes(service.serviceType))
          .filter((service) => !service.socialTokenBroken)
          .map(
            (service) =>
              ({
                ...service,
                iconPath: this.serviceIconPath(service.serviceType),
              }) as SocialConnection,
          ),
      ),
      tap((networks) => {
        this.setState({ allowedNetworks: networks });
      }),
    );
  }

  generateContent(contentPayload: GenerateContentSetup, showLoading = true, appendMode = false) {
    this.generateContentSubject.next(contentPayload);

    if (showLoading) {
      this.setState({ generating: true, generateContentSetup: contentPayload });
    } else {
      this.setState({ generateContentSetup: contentPayload });
    }

    const instructions$ = contentPayload.useAiInstructions
      ? this.commonInstructionsService.customInstructions$
      : of('');

    instructions$
      .pipe(
        switchMap((instructions) =>
          this._generateContent(contentPayload, null, instructions).pipe(
            map((result) =>
              result.map((content) => ({
                ...content,
                postType: PostScheduleType.SCHEDULE,
              })),
            ),
            tap((newContent) => {
              const currentState = this.state;
              const updatedContent = appendMode ? [...(currentState.content || []), ...newContent] : newContent;

              this.setState({
                content: updatedContent,
                technicalPromptData: {
                  technicalPrompt: `${TECHNICAL_PROMPT} ${contentPayload.topic}`,
                  commonInstructions: instructions,
                },
                selectedContent: [],
                currentStep: AI_BUNDLE_POSTS_STEPS.ListContent,
              });
            }),
            take(1),
            finalize(() => {
              if (showLoading) {
                this.setState({ generating: false });
              }
              this.generationCompleteSubject.next();
            }),
          ),
        ),
      )
      .subscribe();
  }

  regenerateContent(data: TechnicalPromptData) {
    this.setState({ generating: true });

    this._generateContent(this.state.generateContentSetup, data.technicalPrompt, data.commonInstructions)
      .pipe(
        map((result) => result.map((content) => ({ ...content, postType: PostScheduleType.SCHEDULE }))),
        tap((result) => {
          this.setState({
            content: result,
            selectedContent: [],
            technicalPromptData: data,
          });
        }),
        take(1),
        finalize(() => this.setState({ generating: false })),
      )
      .subscribe();
  }

  postsSelected(selectedPosts: BundleSocialPosts[]) {
    this.setState({ currentStep: AI_BUNDLE_POSTS_STEPS.CreatePosts, selectedContent: selectedPosts });
  }

  changeToStep(step: number) {
    this.setState({ currentStep: step });
  }

  getSelectedIndex() {
    return this.state.currentStep;
  }

  editBundlePost(bundlePost: BundleSocialPosts) {
    const services = new Map<PostableService, string>();
    const postId = NEW_POST_ID;
    const bundleServices = this.extractSelectSocialServices(bundlePost);

    bundleServices.forEach((service) => {
      services.set(service as PostableService, postId);
    });

    let composerSettings: ComposerSettings = {
      isBundleAIPost: true,
      composerCallback: this.handleContentEdited,
    };

    const createCustomization = (post: PostsInterface, services: Map<PostableService, string>) => {
      const { postText, medias } = post;
      const mediaEntries = medias || [];
      const uploadedMedia = mediaentriesToUploadedMedia(mediaEntries);
      return new Customization({
        postText,
        mediaEntries,
        uploadedMediaObjects: uploadedMedia,
        services,
      });
    };

    if (bundlePost.posts.length === 1) {
      const post = bundlePost.posts[0];
      const customization = createCustomization(post, services);
      composerSettings = {
        ...composerSettings,
        groupedCustomization: customization,
      };
    } else {
      const customizations = bundlePost.posts.map((post) => {
        return createCustomization(post, this.getPostableServiceBySsid(services, post.services[0].ssid));
      });
      composerSettings = {
        ...composerSettings,
        customizationSetup: {
          customizations,
          services,
        },
      };
    }

    this.setState({ currentEditPost: bundlePost });
    this.composerSettingsService.showComposer(composerSettings);
  }

  handleContentEdited = (posts: PostInterface[], mode: 'single' | 'customize') => {
    const { currentEditPost, allowedNetworks } = this.state;
    const postSocialServiceIds = new Set(posts.map((post) => post.socialServiceId));
    const postNetworks = allowedNetworks.filter((network) => postSocialServiceIds.has(network.ssid));

    if (mode === 'single') {
      const post = {
        postText: posts[0].postText,
        medias: posts[0].mediaEntries.map(
          (media) =>
            ({
              mediaType: media.mediaType,
              mediaUrl: media.mediaUrl,
            }) as Media,
        ),
        services: postNetworks,
      } as PostsInterface;
      currentEditPost.posts = [post];
    } else {
      currentEditPost.posts = posts.map((post) => ({
        postText: post.postText,
        medias: post.mediaEntries.map(
          (media) =>
            ({
              mediaType: media.mediaType,
              mediaUrl: media.mediaUrl,
            }) as Media,
        ),
        services: postNetworks.filter((network) => network.ssid === post.socialServiceId),
      }));
    }
  };

  extractSelectSocialServices(bundleSocialPosts: BundleSocialPosts): SocialConnection[] {
    const allServices: SocialConnection[][] = bundleSocialPosts.posts.map((post) => post.services ?? []);
    return allServices.flat();
  }

  private _generateContent(
    contentSetup: GenerateContentSetup,
    technicalPrompt?: string,
    commonInstructions?: string,
  ): Observable<BundleSocialPosts[]> {
    const { contentLength, networks, numberOfPosts, topic, tone, contentType } = contentSetup;
    const postLength = this.calculatePostLength(contentLength, networks);

    return forkJoin([
      this.contentGenerationService
        .generatePosts({
          numberOfPosts: numberOfPosts,
          topic: technicalPrompt || topic,
          tone: tone,
          postLength: postLength,
          commonInstructions: commonInstructions,
          metadata: this.metaData,
        })
        .pipe(map((result) => this.processGeneratedResponse(result, networks))),
      this.stockImageService.getImages(contentSetup?.imageTopic, numberOfPosts <= 3 ? 3 : numberOfPosts).pipe(
        catchError(() => {
          console.error('Error fetching images, returning empty array');
          return of([]);
        }),
      ),
    ]).pipe(
      map(([{ bundleSocialPosts }, images]) => {
        if (contentType === GenerateType.CONTENT) {
          return bundleSocialPosts;
        }
        // Attach images to posts if available
        return bundleSocialPosts.map((bundlePost: BundleSocialPosts, index: number) => {
          if (images[index]) {
            const media = { mediaType: MediaType.IMAGE, mediaUrl: images[index].localUrl } as Media;
            bundlePost.posts[0].medias = [media];
          }
          return bundlePost;
        });
      }),
    );
  }

  private processGeneratedResponse(
    generatedResponse: GeneratePostsResponse,
    networks: SocialConnection[],
  ): { bundleSocialPosts: BundleSocialPosts[]; keyword: string } {
    const posts = generatedResponse.posts.map(
      (post) =>
        ({
          posts: [
            {
              postText: post.text,
              services: networks,
            },
          ],
          selected: false,
        }) as BundleSocialPosts,
    );

    return { bundleSocialPosts: posts, keyword: generatedResponse.keyword };
  }

  private calculatePostLength(contentLength: number, networks: SocialConnection[]): number {
    const shortFormContentLength = 5;
    const longFormContentLength = networks.some((network) => network.serviceType === SocialService.TWITTER) ? 2 : 10;
    return contentLength === MessageLength.SHORT_FORM ? shortFormContentLength : longFormContentLength;
  }

  private serviceIconPath(service: string): string {
    const prefix = 'https://vstatic-prod.apigateway.co/social-marketing-client/assets/social-icons/';
    const networkFile = {
      [SocialService.FACEBOOK]: 'facebook.png',
      [SocialService.TWITTER]: 'X.png',
      [SocialService.GOOGLE_MY_BUSINESS]: 'google_my_business.png',
      [SocialService.GMB]: 'google_my_business.png',
      [SocialService.INSTAGRAM]: 'instagram.png',
      [SocialService.LINKEDIN]: 'linkedin.png',
      [SocialService.LINKEDIN_COMPANY]: 'linkedin.png',
      [SocialService.PINTEREST]: 'pinterest.png',
      REELS: 'instagram-reel.png',
      [SocialService.CURATED_CONTENT]: 'build_circle.svg',
      [SocialService.YOUTUBE_CHANNEL]: 'youtube_round.png',
      [SocialService.TIKTOK_ACCOUNT]: 'tiktok_round.png',
    };

    return `${prefix}${networkFile[service]}` || '';
  }

  getRedirectionVal(data: { postType: string; month: number }) {
    this.receivedData = data;
  }
  restart() {
    this.allowRestart = true;
    let url: string;
    if (
      (this.receivedData.postType && this.receivedData.postType == 'schedule') ||
      (['draft', 'visible', 'hidden'].includes(this.receivedData.postType) && this.isTriggredFromCalendar)
    ) {
      url = `/account/${this.config.accountGroupId}/calendar`;
      if (this.receivedData.month) {
        url += `?month=${this.receivedData.month}`;
      }
    } else {
      url = `/account/${this.config.accountGroupId}/posts?view=drafts`;
      if (this.receivedData.postType && this.receivedData.postType != 'draft') {
        url += `?status=${this.receivedData.postType}`;
      }
    }
    this.router.navigateByUrl(url);
  }

  private getPostableServiceBySsid(services: Map<PostableService, string>, ssid: string): Map<PostableService, string> {
    const newServices = new Map<PostableService, string>();
    services.forEach((value, key) => {
      if (key.ssid === ssid) {
        newServices.set(key, value);
      }
    });
    return newServices;
  }
}
