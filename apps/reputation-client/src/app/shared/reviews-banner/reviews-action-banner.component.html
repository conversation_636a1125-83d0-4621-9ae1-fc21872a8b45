<glxy-alert *ngIf="showBanner" [type]="'info'">
  <div class="alert-content">
    <div>
      <ng-container
        *ngIf="deletedReviewsBannerConfig.showReviewsBanner && editedReviewsBannerConfig.showReviewsBanner"
        class="message"
      >
        {{
          'COMMON.REVIEWS_BANNER.WARNING_REMOVED_AND_EDITED_REVIEWS'
            | translate: { changedReviewCount: changedReviewsCount }
        }}
      </ng-container>
      <ng-container
        *ngIf="deletedReviewsBannerConfig.showReviewsBanner && !editedReviewsBannerConfig.showReviewsBanner"
        class="message"
      >
        {{
          (this.deletedReviewsBannerConfig.reviewsCount > 1
            ? 'COMMON.REVIEWS_BANNER.WARNING_REMOVED_REVIEWS'
            : 'COMMON.REVIEWS_BANNER.WARNING_REMOVED_REVIEW'
          )
            | translate
              : {
                  deletedReviewCount: this.deletedReviewsBannerConfig.reviewsCount,
                }
        }}
      </ng-container>
      <ng-container
        *ngIf="!deletedReviewsBannerConfig.showReviewsBanner && editedReviewsBannerConfig.showReviewsBanner"
        class="message"
      >
        {{
          (editedReviewsBannerConfig.reviewsCount > 1
            ? 'COMMON.REVIEWS_BANNER.WARNING_EDITED_REVIEWS'
            : 'COMMON.REVIEWS_BANNER.WARNING_EDITED_REVIEW'
          )
            | translate
              : {
                  editedReviewCount: editedReviewsBannerConfig.reviewsCount,
                }
        }}
      </ng-container>
    </div>

    <div class="actions">
      <a
        *ngIf="deletedReviewsBannerConfig.showReviewsBanner && editedReviewsBannerConfig.showReviewsBanner"
        [queryParams]="{ removedReviews: true, editedReviews: true }"
        [routerLink]="[reviewsUrl$ | async]"
        class="action-button"
      >
        {{ 'COMMON.REVIEWS_BANNER.VIEW_REVIEWS' | translate }}
      </a>
      <a
        *ngIf="deletedReviewsBannerConfig.showReviewsBanner && !editedReviewsBannerConfig.showReviewsBanner"
        [queryParams]="{ removedReviews: true }"
        [routerLink]="[reviewsUrl$ | async]"
        class="action-button"
      >
        {{ 'COMMON.REVIEWS_BANNER.VIEW_REMOVED_REVIEWS' | translate }}
      </a>
      <a
        *ngIf="!deletedReviewsBannerConfig.showReviewsBanner && editedReviewsBannerConfig.showReviewsBanner"
        [queryParams]="{ editedReviews: true }"
        [routerLink]="[reviewsUrl$ | async]"
        class="action-button"
      >
        {{ 'COMMON.REVIEWS_BANNER.VIEW_EDITED_REVIEWS' | translate }}
      </a>
      <a (click)="dismiss()" class="action-button">
        {{ 'COMMON.REVIEWS_BANNER.DISMISS' | translate }}
      </a>
    </div>
  </div>
</glxy-alert>
