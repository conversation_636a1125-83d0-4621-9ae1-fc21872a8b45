import { Component, Input, OnInit } from '@angular/core';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { differenceInHours } from 'date-fns';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AppConfig, AppConfigService } from '../../core';
import {
  OverviewService,
  RecentEditedReviewsSummary,
  RecentRemovedReviewsSummary,
  ReviewBannerConfig,
} from '../../overview/overview.service';

@Component({
  selector: 'rm-reviews-action-banner',
  templateUrl: './reviews-action-banner.component.html',
  styleUrls: ['./reviews-action-banner.component.scss'],
  standalone: false,
})
export class ReviewsActionBannerComponent implements OnInit {
  @Input() removedReviewsSummary: RecentRemovedReviewsSummary;
  @Input() editedReviewsSummary: RecentEditedReviewsSummary;
  @Input() accountGroupId: string;
  showBanner = false;
  reviewsUrl$: Observable<string>;
  deletedReviewsBannerConfig: ReviewBannerConfig = <ReviewBannerConfig>{};
  editedReviewsBannerConfig: ReviewBannerConfig = <ReviewBannerConfig>{};
  changedReviewsCount: number;

  constructor(
    private configService: AppConfigService,
    private snowplowService: ProductAnalyticsService,
    private overviewService: OverviewService,
  ) {}

  ngOnInit(): void {
    this.deletedReviewsBannerConfig.reviewsCount = this.removedReviewsSummary.totalPotentiallyRemovedReviews;
    this.editedReviewsBannerConfig.reviewsCount = this.editedReviewsSummary.totalPotentiallyEditedReviews;
    this.changedReviewsCount =
      this.deletedReviewsBannerConfig.reviewsCount + this.editedReviewsBannerConfig.reviewsCount;
    this.reviewsUrl$ = this.configService.config$.pipe(
      map((config: AppConfig) => {
        if (config.accountGroupId) {
          return `/account/${config.accountGroupId}/app/reviews`;
        } else {
          return null;
        }
      }),
    );
    const dismissedDateTime = this.overviewService.getDismissedDateTime(this.accountGroupId);
    const latestDeleted = this.removedReviewsSummary.mostRecentRemovedTime;
    const latestEdited = this.editedReviewsSummary.mostRecentEditedTime;
    if (dismissedDateTime) {
      this.deletedReviewsBannerConfig.reviewsChangedSinceDismissed =
        this.changedReviewsCount > 0 && latestDeleted && latestDeleted > dismissedDateTime;
      this.editedReviewsBannerConfig.reviewsChangedSinceDismissed =
        this.changedReviewsCount > 0 && latestEdited && latestEdited > dismissedDateTime;

      this.showBanner =
        (this.deletedReviewsBannerConfig.reviewsChangedSinceDismissed ||
          this.editedReviewsBannerConfig.reviewsChangedSinceDismissed) &&
        differenceInHours(new Date(), dismissedDateTime) >= 24;
    } else {
      this.showBanner = this.changedReviewsCount > 0;
    }
    this.deletedReviewsBannerConfig.showReviewsBanner = dismissedDateTime
      ? this.deletedReviewsBannerConfig.reviewsChangedSinceDismissed
      : this.deletedReviewsBannerConfig.reviewsCount > 0;
    this.editedReviewsBannerConfig.showReviewsBanner = dismissedDateTime
      ? this.editedReviewsBannerConfig.reviewsChangedSinceDismissed
      : this.editedReviewsBannerConfig.reviewsCount > 0;
  }

  dismiss(): void {
    localStorage.setItem(`reviewsBannerDismissed${this.accountGroupId}`, new Date().toLocaleString());
    this.showBanner = false;
  }
}
