import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { combineLatest, forkJoin, Observable, of } from 'rxjs';

import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { VaProductNavActionButton, VaProductNavPageInformation } from '@vendasta/uikit';
import { first, map, mergeMap, shareReplay, switchMap, take, takeWhile } from 'rxjs/operators';
import {
  AppConfigService,
  ConnectAccountsService,
  ListingStats,
  socialServiceIconPath,
  SocialServiceType,
} from '../core';
import { HistoricalListingPointScores } from '../core/listings.api.service';
import { FacebookRecommendations } from '../core/reviews.api.service';
import { ColorOverrides, SingleSeriesChartConfig, SingleSeriesChartType } from '../shared';
import { BackToClassic, HideBackToClassic } from '../shared/back-to-classic';
import { GetProService } from '../shared/get-pro';
import { isListingScorePercentPartner } from '../shared/percentage-score';
import { UpgradeCTADialogService } from '../shared/upgrade-cta-dialog.service';
import {
  MentionStats,
  OverviewService,
  OverviewSettings,
  RecentEditedReviewsSummary,
  RecentDeletedAndEditedReviewsSummary,
  TopSource,
} from './overview.service';

@Component({
  selector: 'rm-overview',
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.scss'],
  standalone: false,
})
export class OverviewComponent implements OnInit {
  listingScore$: Observable<number>;
  listingIndustryAverage$: Observable<string>;
  startingListingScore$: Observable<number>;
  listingStats$: Observable<ListingStats>;
  reviewAverageRating$: Observable<string>;
  reviewIndustryAverage$: Observable<string>;
  reviewTotal$: Observable<string>;
  mentionTotal$: Observable<number>;
  initialAverageRating$: Observable<string>;
  mentionStatsChartConfig$: Observable<SingleSeriesChartConfig>;
  competitorsStatsChartConfig$: Observable<SingleSeriesChartConfig>;
  topReviewSources$: Observable<TopSource[]>;
  facebookRecommendations$: Observable<FacebookRecommendations>;
  facebookRecommendationHistory$: Observable<FacebookRecommendations>;
  facebookRecommendationsChartConfig$: Observable<SingleSeriesChartConfig>;
  facebookAccounts$: Observable<any>;
  hasFacebookOrGMBConnected$: Observable<boolean>;
  hasFacebookAndGMBConnected$: Observable<boolean>;
  showFewerCardsInRow$: Observable<boolean>;
  goToOldButton$: Observable<VaProductNavActionButton>;
  isExpressEnabled$: Observable<boolean>;
  showCompetiton$: Observable<boolean>;
  showMentions$: Observable<boolean>;
  showListings$: Observable<boolean>;
  showReviews$: Observable<boolean>;
  isPercentageScorePartner$: Observable<boolean>;
  percentageScore$: Observable<string>;
  facebookIcon: string;
  customerVoiceEnabled$: Observable<boolean>;
  customerVoiceUrlPath$: Observable<string>;
  accountGroupId$: Observable<string>;
  isNewAccount$: Observable<boolean>;
  showReviewWidget$: Observable<boolean>;
  editedReviewsSummary$: Observable<RecentEditedReviewsSummary>;
  deletedAndEditedReviewsSummary$: Observable<RecentDeletedAndEditedReviewsSummary>;

  settings$: Observable<OverviewSettings>;

  pageInformation$: Observable<VaProductNavPageInformation[]>;

  sentimentColorOverrides: ColorOverrides = [
    { name: 'COMMON.SHARED_TERMS.POSITIVE', value: '#3FB23F' },
    { name: 'COMMON.SHARED_TERMS.NEGATIVE', value: '#E0456A' },
    { name: 'COMMON.SHARED_TERMS.NEUTRAL', value: '#E5E5E5' },
    { name: 'COMMON.SHARED_TERMS.NO_RATING', value: '#2E8DED' },
  ];

  listingColorOverrides: ColorOverrides = [
    { name: 'Accurate', value: '#3FB23F' },
    { name: 'Possible', value: '#FFA000' },
    { name: 'Missing', value: '#E0456A' },
  ];

  constructor(
    private overviewService: OverviewService,
    private translateService: TranslateService,
    private router: Router,
    private route: ActivatedRoute,
    private config: AppConfigService,
    private connectAccountsService: ConnectAccountsService,
    private dialog: MatDialog,
    private getProService: GetProService,
    private snowplowService: ProductAnalyticsService,
    private upgradeCTADialogService: UpgradeCTADialogService,
  ) {}

  ngOnInit(): void {
    this.route.queryParams
      .pipe(
        take(1),
        switchMap((params) => {
          if (!!params.displayUpgradeCTA && params.displayUpgradeCTA === 'true' && !!params.upgradeUrl) {
            const targetRoute = decodeURI(params.upgradeUrl);
            const dialogRes = this.upgradeCTADialogService.openUpgradeModal(targetRoute).pipe(
              takeWhile((dialogRef) => !!dialogRef),
              switchMap((dialogRef) => dialogRef.afterClosed()),
            );
            return forkJoin([dialogRes, of(targetRoute)]);
          } else {
            return forkJoin([of(false), of('')]);
          }
        }),
        map(([dialogResult, targetRoute]) => {
          if (dialogResult) {
            const trackingLabel = `upgrade-cta-guard-${targetRoute}:${this.getProService.upgradeType}`;
            this.upgradeCTADialogService.businessCenterUpgrade(`${trackingLabel}:upgraded`);
          }
        }),
      )
      .subscribe();

    this.settings$ = this.overviewService.settings$;
    this.listingScore$ = this.overviewService.listingScore$;
    this.listingStats$ = this.overviewService.listingStats$;
    this.percentageScore$ = this.overviewService.percentageScoreText$;
    this.goToOldButton$ = this.route.queryParams.pipe(
      mergeMap((params) => {
        let isDebug = false;
        if (!!params.debug && params.debug === 'true') {
          isDebug = true;
        }
        return this.config.config$.pipe(
          map((conf) => {
            if (isDebug) {
              return BackToClassic(`account/${conf.accountGroupId}/overview?redirect=false`);
            }
            if (!HideBackToClassic.includes(conf.partnerId) && !conf.isExpressEnabled && conf.showOldDesignButton) {
              return BackToClassic(`account/${conf.accountGroupId}/overview?redirect=false`);
            } else {
              return null;
            }
          }),
        );
      }),
    );

    this.pageInformation$ = this.config.config$.pipe(
      first(),
      map((conf) => {
        const pageInformation = [
          {
            title: 'OVERVIEW.NAME',
            descriptionParagraphs: ['OVERVIEW.TOOLTIP.CONTENT', 'OVERVIEW.HELP_DRAWER.OVERVIEW_CONTENT'],
          },
        ];
        if (conf.installedApps) {
          if (conf.installedApps.find((a) => a.appid === 'RE')) {
            pageInformation.push({
              title: 'REVIEWS.NAME',
              descriptionParagraphs: ['OVERVIEW.HELP_DRAWER.REVIEWS_CONTENT'],
            });
          }

          if (conf.installedApps.find((a) => a.appid === 'VI')) {
            pageInformation.push({
              title: 'LISTINGS.NAME',
              descriptionParagraphs: ['OVERVIEW.HELP_DRAWER.LISTINGS_CONTENT'],
            });
          }

          if (conf.installedApps.find((a) => a.appid === 'CO')) {
            pageInformation.push({
              title: 'OVERVIEW.CARDS.COMPETITION.NAME',
              descriptionParagraphs: ['OVERVIEW.HELP_DRAWER.COMPETITION_CONTENT'],
            });
          }

          if (conf.installedApps.find((a) => a.appid === 'ME')) {
            pageInformation.push({
              title: 'MENTIONS.NAME',
              descriptionParagraphs: ['OVERVIEW.HELP_DRAWER.MENTIONS_CONTENT'],
            });
          }
        }

        return pageInformation;
      }),
    );
    this.isPercentageScorePartner$ = this.config.config$.pipe(
      map((conf) => isListingScorePercentPartner(conf.partnerId) || conf.showPercentageScore),
    );
    this.isExpressEnabled$ = this.config.config$.pipe(map((conf) => conf.isExpressEnabled));
    this.showCompetiton$ = this.config.config$.pipe(
      map((conf) => conf.installedApps && !!conf.installedApps.find((a) => a.appid === 'CO')),
    );
    this.showMentions$ = this.config.config$.pipe(
      map((conf) => conf.installedApps && !!conf.installedApps.find((a) => a.appid === 'ME')),
    );
    this.showListings$ = this.config.config$.pipe(
      map((conf) => conf.installedApps && !!conf.installedApps.find((a) => a.appid === 'VI')),
    );
    this.showReviews$ = this.config.config$.pipe(
      map((conf) => conf.installedApps && !!conf.installedApps.find((a) => a.appid === 'RE')),
    );
    this.showFewerCardsInRow$ = this.config.config$.pipe(
      map((conf) => {
        if (
          !conf ||
          !conf.installedApps ||
          conf.isExpressEnabled ||
          !!conf.installedApps.find((a) => a.appid === 'CO') ||
          !!conf.installedApps.find((a) => a.appid === 'ME') ||
          !!conf.installedApps.find((a) => a.appid === 'VI') ||
          !!conf.installedApps.find((a) => a.appid === 'RE')
        ) {
          return false;
        }

        return true;
      }),
    );
    this.isNewAccount$ = this.config.config$.pipe(
      map((conf) => {
        const finishScrappingTime = new Date(conf.account?.createdTime);
        const now = new Date();
        finishScrappingTime.setHours(finishScrappingTime.getHours() + 48);
        return now < finishScrappingTime;
      }),
    );

    this.reviewAverageRating$ = this.overviewService.reviewStats$.pipe(
      map((rs) => (rs && rs.averageRating ? (Math.round(rs.averageRating * 10) / 10).toFixed(1) : '0')),
      shareReplay(1),
    );
    this.reviewIndustryAverage$ = this.overviewService.reviewIndustryAverage$.pipe(
      map((rating) => (Math.round(rating.averageRating * 10) / 10).toFixed(1)),
    );
    this.topReviewSources$ = this.overviewService.topReviewSources$;
    this.reviewTotal$ = this.overviewService.reviewStats$.pipe(
      map((rs) => (rs && rs.total ? rs.total.toLocaleString() : '0')),
      shareReplay(1),
    );
    this.initialAverageRating$ = this.overviewService.initialAverageRating$.pipe(
      map((averageRating) => (Math.round(averageRating * 10) / 10).toFixed(1)),
      shareReplay(1),
    );

    this.listingIndustryAverage$ = this.overviewService.industryAverage$.pipe(
      map((ia) => (ia ? `${Math.round(ia)}` : 'TBD')),
      shareReplay(1),
    );

    this.startingListingScore$ = this.overviewService.historicalListingScores$.pipe(
      map((hls: HistoricalListingPointScores) => {
        if (hls.series.length > 0) {
          const filteredData = hls.series.filter((dataPoint) => {
            if (dataPoint[1] > 0) {
              return dataPoint;
            }
          });
          if (filteredData[0] && filteredData[0].length > 1) {
            return filteredData[0][1];
          }
        }
        return 0;
      }),
      shareReplay(1),
    );

    this.mentionStatsChartConfig$ = combineLatest([
      this.overviewService.mentionStats$,
      this.translateService.stream([
        'COMMON.SHARED_TERMS.POSITIVE',
        'COMMON.SHARED_TERMS.NEGATIVE',
        'COMMON.SHARED_TERMS.NEUTRAL',
      ]),
    ]).pipe(
      map(([ms, tr]) => {
        if (!ms.sentimentDataExist) {
          return {
            chartType: SingleSeriesChartType.HorizontalBar,
            seriesData: [],
          };
        }

        if (Object.keys(ms.sentiment.data).length === 0) {
          return {
            chartType: SingleSeriesChartType.HorizontalBar,
            showXAxis: false,
            showYAxis: true,
            showLegend: false,
            roundEdges: false,
            seriesData: [
              { name: tr['COMMON.SHARED_TERMS.POSITIVE'], value: 0 },
              { name: tr['COMMON.SHARED_TERMS.NEUTRAL'], value: 0 },
              { name: tr['COMMON.SHARED_TERMS.NEGATIVE'], value: 0 },
            ],
          };
        }
        return {
          chartType: SingleSeriesChartType.HorizontalBar,
          showXAxis: false,
          showYAxis: true,
          showLegend: false,
          roundEdges: false,
          colorOverrides: this.sentimentColorOverrides.map((o) => ({ name: tr[o.name], value: o.value })),
          seriesData: [
            {
              name: tr['COMMON.SHARED_TERMS.POSITIVE'],
              value: ms.sentiment.data.positive.reduce((a, b) => a + b, 0),
            },
            {
              name: tr['COMMON.SHARED_TERMS.NEUTRAL'],
              value: ms.sentiment.data.neutral.reduce((a, b) => a + b, 0),
            },
            {
              name: tr['COMMON.SHARED_TERMS.NEGATIVE'],
              value: ms.sentiment.data.negative.reduce((a, b) => a + b, 0),
            },
          ],
        };
      }),
      shareReplay(1),
    );

    this.mentionTotal$ = this.overviewService.mentionStats$.pipe(
      map((ms) => (ms && ms.sentimentDataExist ? this.getTotalMentions(ms) : 0)),
      shareReplay(1),
    );

    this.competitorsStatsChartConfig$ = this.overviewService.competitorsStats$.pipe(
      map((cs) => {
        if (!cs.shareOfVoiceDataExist) {
          return {
            chartType: SingleSeriesChartType.Pie,
            seriesData: [],
          };
        }
        // Somehow we have more than 3 competitors sometimes, truncate the list
        cs.shareOfVoice.labels = cs.shareOfVoice.labels.slice(0, 4);
        cs.shareOfVoice.data = cs.shareOfVoice.data.slice(0, 4);
        const sum: number = cs.shareOfVoice.data.reduce((accum, curr) => accum + curr, 0);
        return {
          chartType: SingleSeriesChartType.Pie,
          seriesData: cs.shareOfVoice.labels.map((label, i) => ({
            name: label,
            value: (cs.shareOfVoice.data[i] / sum) * 100,
          })),
          colorOverrides: [
            { name: cs.shareOfVoice.labels[0], value: '#42A5F5' },
            { name: cs.shareOfVoice.labels[1], value: '#80DEEA' },
            { name: cs.shareOfVoice.labels[2], value: '#81C784' },
            { name: cs.shareOfVoice.labels[3], value: '#FFEB3B' },
            { name: cs.shareOfVoice.labels[4], value: '#EC407A' },
            { name: cs.shareOfVoice.labels[5], value: '#AB47BC' },
          ],
          showLegend: false,
          doughnut: true,
          tooltipDisabled: false,
          view: [136, 136],
          arcWidth: 0.3,
          tooltipTextFunction: this.formatCompetitorStatsTooltip,
        };
      }),
      shareReplay(1),
    );

    this.facebookIcon = socialServiceIconPath(SocialServiceType.FACEBOOK);
    this.facebookRecommendations$ = this.overviewService.facebookRecommendations$;
    this.facebookRecommendationHistory$ = this.overviewService.facebookRecommendationHistory$;
    this.facebookRecommendationsChartConfig$ = combineLatest([
      this.overviewService.facebookRecommendations$,
      this.translateService.stream(['COMMON.SHARED_TERMS.RECOMMEND', 'COMMON.SHARED_TERMS.DO_NOT_RECOMMEND']),
    ]).pipe(
      map(([fbr, tr]) => {
        if (fbr.total <= 0) {
          return {
            chartType: SingleSeriesChartType.Pie,
            seriesData: [],
          };
        }
        return {
          chartType: SingleSeriesChartType.Pie,
          seriesData: [
            { name: tr['COMMON.SHARED_TERMS.RECOMMEND'], value: fbr.positive },
            { name: tr['COMMON.SHARED_TERMS.DO_NOT_RECOMMEND'], value: fbr.negative },
          ],
          colorOverrides: [
            { name: tr['COMMON.SHARED_TERMS.RECOMMEND'], value: '#81C784' },
            { name: tr['COMMON.SHARED_TERMS.DO_NOT_RECOMMEND'], value: '#EF5350' },
          ],
          showLegend: false,
          doughnut: true,
          view: [136, 136],
          arcWidth: 0.3,
        };
      }),
      shareReplay(1),
    );
    this.facebookAccounts$ = this.connectAccountsService.connectAccountSettings$.pipe(
      map((accountSettingsMap) => accountSettingsMap.get(SocialServiceType.FACEBOOK).services),
    );
    this.hasFacebookOrGMBConnected$ = this.connectAccountsService.connectAccountSettings$.pipe(
      map((accountSettingsMap) => {
        const facebookAccount = accountSettingsMap.get(SocialServiceType.FACEBOOK);
        if (!!facebookAccount && !!facebookAccount.services && facebookAccount.services.length > 0) {
          return true;
        }

        const gmbAccount = accountSettingsMap.get(SocialServiceType.GMB);
        if (!!gmbAccount && !!gmbAccount.services && gmbAccount.services.length > 0) {
          return true;
        }

        return false;
      }),
    );
    this.hasFacebookAndGMBConnected$ = this.connectAccountsService.connectAccountSettings$.pipe(
      map((accountSettingsMap) => {
        const facebookAndGMBInfo = this.overviewService.getFacebookAndGMBAccount(accountSettingsMap);
        return !!facebookAndGMBInfo.facebookAccountName && !!facebookAndGMBInfo.gmbAccountName;
      }),
    );
    this.customerVoiceEnabled$ = this.overviewService.customerVoiceEnabled$;
    this.customerVoiceUrlPath$ = this.overviewService.customerVoiceUrlPath$;

    this.accountGroupId$ = this.config.config$.pipe(map((conf) => conf.accountGroupId));

    this.deletedAndEditedReviewsSummary$ = combineLatest([
      this.overviewService.recentRemovedReviewsSummary$,
      this.overviewService.recentEditedReviewsSummary$,
    ]).pipe(
      map(([recentRemovedReviewsSummary, recentEditedReviewsSummary]) => {
        return {
          removedReviewsSummary: recentRemovedReviewsSummary,
          editedReviewsSummary: recentEditedReviewsSummary,
        };
      }),
    );
    this.overviewService.triggerReloadingRemovedReviewsSummary();
    this.overviewService.triggerReloadingEditedReviewsSummary();
    this.showReviewWidget$ = this.config.config$.pipe(map((config) => config.partnerId !== 'HMSI'));
  }

  formatCompetitorStatsTooltip({ data }): string {
    const label: string = data.name;
    const val: number = data.value;

    return `
      <span class="tooltip-label">${label}</span>
      <span class="tooltip-val">${val.toPrecision(3)}%</span>
    `;
  }

  getTotalMentions(ms: MentionStats): number {
    if (Object.keys(ms.sentiment.data).length !== 0) {
      const totalPos = ms.sentiment.data.positive.reduce((a, b) => a + b, 0);
      const totalNeu = ms.sentiment.data.neutral.reduce((a, b) => a + b, 0);
      const totalNeg = ms.sentiment.data.negative.reduce((a, b) => a + b, 0);
      return totalNeg + totalNeu + totalPos;
    } else {
      return 0;
    }
  }

  starCount(stars: number): number[] {
    stars = Math.round(stars);
    return stars >= 0 && stars <= 5
      ? Array(stars)
          .fill(1)
          .concat(Array(5 - stars).fill(0))
      : [];
  }

  navigateOnChartClick(path: string): void {
    this.router.navigate([path], { relativeTo: this.route });
  }
}
