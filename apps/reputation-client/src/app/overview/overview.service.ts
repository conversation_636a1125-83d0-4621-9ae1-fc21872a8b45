import { Injectable } from '@angular/core';

import { BehaviorSubject, Observable, EMPTY as ObservableEmpty, merge as ObservableMerge } from 'rxjs';
import { catchError, distinctUntilChanged, map, skipWhile, switchMap, tap } from 'rxjs/operators';

import { subtractDays } from '@vendasta/galaxy/utility/date-utils';
import { Review } from '@vendasta/reviews';
import { AppNotificationService } from '../app-notification.service';
import {
  AppConfigService,
  CompetitionApiService,
  ConnectAccountSetting,
  HistoricalListingPointScores,
  ListingService,
  ListingStats,
  MentionsApiService,
  ReviewIndustryState,
  ReviewsApiService,
  SocialServiceType,
} from '../core';
import { FacebookRecommendations, ReviewSourceStatistics } from '../core/reviews.api.service';

export interface ReviewStats {
  sentiment: {
    positive: number;
    negative: number;
    neutral: number;
    noRating: number;
  };
  total: number;
  averageRating: number;
}

export interface RecentRemovedReviewsSummary {
  totalPotentiallyRemovedReviews: number;
  mostRecentRemovedTime: Date;
}

export interface RecentEditedReviewsSummary {
  totalPotentiallyEditedReviews: number;
  mostRecentEditedTime: Date;
}

export interface RecentDeletedAndEditedReviewsSummary {
  removedReviewsSummary: RecentRemovedReviewsSummary;
  editedReviewsSummary: RecentEditedReviewsSummary;
}

export interface ReviewBannerConfig {
  reviewsCount: number;
  reviewsChangedSinceDismissed: boolean;
  showReviewsBanner: boolean;
}

export interface MentionStats {
  sentiment: {
    averageSentiment: number;
    data: {
      negative: number[];
      neutral: number[];
      positive: number[];
    };
    labels: string[];
    newMentionCount: number;
  };
  sentimentDataExist: boolean;
}

export interface CompetitorsStats {
  shareOfVoice: {
    searchName: string;
    companyPercent: number;
    labels: string[];
    data: number[];
    title: string;
  };
  shareOfVoiceDataExist: boolean;
}

export interface TopSource {
  sourceName: string;
  sourceId: number;
  reviewCount: number;
}

export interface FacebookAndGMBInfo {
  gmbAccountName: string;
  facebookAccountName: string;
  gmbProfileUrl?: string;
  facebookUrl: string;
}

export interface OverviewSettings {
  welcomeMessageDismissed: boolean;
}

export interface RecentReviews {
  accountGroupId: string;
  reviews: Review[];
  total: number;
  lowRatingTotal: number;
}

@Injectable()
export class OverviewService {
  private settings$$: BehaviorSubject<OverviewSettings> = new BehaviorSubject({ welcomeMessageDismissed: true });
  private reviewStats$$: BehaviorSubject<ReviewStats> = new BehaviorSubject(null);
  private recentRemovedReviewsSummary$$: BehaviorSubject<RecentRemovedReviewsSummary> =
    new BehaviorSubject<RecentRemovedReviewsSummary>(null);
  private recentEditedReviewsSummary$$: BehaviorSubject<RecentEditedReviewsSummary> =
    new BehaviorSubject<RecentEditedReviewsSummary>(null);
  private initialReviewNumber$$: BehaviorSubject<number> = new BehaviorSubject(null);
  private initialAverageRating$$: BehaviorSubject<number> = new BehaviorSubject(null);
  private competitorsStats$$: BehaviorSubject<CompetitorsStats> = new BehaviorSubject(null);
  private reviewIndustryAverage$$: BehaviorSubject<ReviewIndustryState> = new BehaviorSubject(null);
  private topReviewSources$$: BehaviorSubject<TopSource[]> = new BehaviorSubject(null);
  private facebookRecommendations$$: BehaviorSubject<FacebookRecommendations> = new BehaviorSubject(null);
  private facebookRecommendationHistory$$: BehaviorSubject<FacebookRecommendations> = new BehaviorSubject(null);
  private customerVoiceEnabled$$: BehaviorSubject<boolean> = new BehaviorSubject(null);
  private customerVoiceUrlPath$$: BehaviorSubject<string> = new BehaviorSubject(null);
  private mentionStats$$: BehaviorSubject<MentionStats> = new BehaviorSubject(null);
  private tickReloadingRemovedReviews$$ = new BehaviorSubject(true);
  private tickReloadingEditedReviews$$ = new BehaviorSubject(true);
  recentReviews$: Observable<RecentReviews>;

  public readonly recentRemovedReviewsSummary$ = this.recentRemovedReviewsSummary$$
    .asObservable()
    .pipe(skipWhile((value) => value === null));
  public readonly recentEditedReviewsSummary$ = this.recentEditedReviewsSummary$$
    .asObservable()
    .pipe(skipWhile((value) => value === null));

  constructor(
    private listingService: ListingService,
    private reviewsApi: ReviewsApiService,
    private mentionsApi: MentionsApiService,
    private competitionApi: CompetitionApiService,
    private config: AppConfigService,
    private appNotification: AppNotificationService,
  ) {
    // TODO: probably should use accoutn srvice
    config.config$
      .pipe(
        tap((c) => {
          this.settings$$.next({ welcomeMessageDismissed: c.account.welcomeMessageDismissed });
        }),
        distinctUntilChanged((a, b) => a.accountGroupId === b.accountGroupId),
        switchMap((c) => {
          const dismissedDateTime = this.getDismissedDateTime(c.accountGroupId);
          return ObservableMerge(
            this.reviewsApi.getReviewStats(c.partnerId, c.accountGroupId).pipe(
              catchError((err) => {
                console.error(err);
                this.appNotification.publishErrorMessage('Failed to load review stats');
                return ObservableEmpty;
              }),
              tap((rs) => this.reviewStats$$.next(rs)),
            ),
            this.loadRemovedReviewsSummary(c.partnerId, c.accountGroupId).pipe(
              catchError((err) => {
                console.error(err);
                return ObservableEmpty;
              }),
              tap((rs) => this.recentRemovedReviewsSummary$$.next(rs)),
            ),
            this.loadEditedReviewsSummary(c.accountGroupId, dismissedDateTime).pipe(
              catchError((err) => {
                console.error(err);
                return ObservableEmpty;
              }),
              tap((rs) => this.recentEditedReviewsSummary$$.next(rs)),
            ),
            this.reviewsApi.getReviewHistoryAverageRatings(c.accountGroupId).pipe(
              catchError((err) => {
                console.error(err);
                return ObservableEmpty;
              }),
              tap((res) => {
                const series = res ? res.series : null;
                let initialResult = null;
                if (series && series.quarterly && series.quarterly.intervals && series.quarterly.intervals.length > 0) {
                  initialResult = series.quarterly.intervals[0];
                } else if (
                  series &&
                  series.monthly &&
                  series.monthly.intervals &&
                  series.monthly.intervals.length > 0
                ) {
                  initialResult = series.monthly.intervals[0];
                }

                if (initialResult && initialResult.length > 1) {
                  this.initialAverageRating$$.next(initialResult[1]);
                  this.initialReviewNumber$$.next(initialResult[0]);
                } else {
                  this.initialAverageRating$$.next(1.0);
                  this.initialReviewNumber$$.next(1);
                }
              }),
            ),
            c.isExpressEnabled
              ? ObservableEmpty
              : this.competitionApi.getCompetitorsStats(c.partnerId, c.accountGroupId).pipe(
                  catchError((err) => {
                    console.error(err);
                    this.appNotification.publishErrorMessage("Failed to load competitors' stats");
                    return ObservableEmpty;
                  }),
                  tap((cs) => this.competitorsStats$$.next(this.sortCompetitorsStats(cs))),
                ),
            this.reviewsApi.getReviewsIndustryState(c.accountGroupId).pipe(
              catchError((err) => {
                console.error(err);
                this.appNotification.publishErrorMessage('Failed to load review industry average stats');
                return ObservableEmpty;
              }),
              tap((rs) => this.reviewIndustryAverage$$.next(rs)),
            ),
            this.reviewsApi.getTopReviewSources(c.accountGroupId).pipe(
              catchError((err) => {
                console.error(err);
                this.appNotification.publishErrorMessage('Failed to load top review sources');
                return ObservableEmpty;
              }),
              tap((rs) => this.topReviewSources$$.next(this.formatTopSources(rs))),
            ),
            this.reviewsApi.getFacebookRecommendations(c.accountGroupId).pipe(
              catchError((err) => {
                console.error(err);
                return ObservableEmpty;
              }),
              tap((rs) => this.facebookRecommendations$$.next(rs)),
            ),
            this.reviewsApi.getFacebookRecommendationHistoryStats(c.accountGroupId).pipe(
              catchError((err) => {
                console.error(err);
                return ObservableEmpty;
              }),
              tap((rs) => this.facebookRecommendationHistory$$.next(rs)),
            ),
            this.reviewsApi.getReviewSettings(c.accountGroupId).pipe(
              catchError((err) => {
                console.error(err);
                return ObservableEmpty;
              }),
              tap((rs) => {
                const customerVoiceEnabled = rs.showCvButton;
                const customerVoiceUrlPath = `/account/${c.accountGroupId}/reviews/customer-voice/`;
                this.customerVoiceUrlPath$$.next(customerVoiceUrlPath);
                this.customerVoiceEnabled$$.next(customerVoiceEnabled);
              }),
            ),
            c.isExpressEnabled
              ? ObservableEmpty
              : this.mentionsApi.getMentionStats(c.partnerId, c.accountGroupId).pipe(
                  catchError((err) => {
                    console.error(err);
                    this.appNotification.publishErrorMessage('Failed to load mention stats');
                    return ObservableEmpty;
                  }),
                  tap((ms) => this.mentionStats$$.next(ms)),
                ),
          );
        }),
      )
      .subscribe();

    this.recentReviews$ = config.config$.pipe(
      distinctUntilChanged((a, b) => a.accountGroupId === b.accountGroupId),
      switchMap((c) => {
        return this.reviewsApi
          .listReviews(
            {
              partnerId: c.partnerId,
              accountGroupId: c.accountGroupId,
              filters: {
                dateRange: {
                  start: subtractDays(new Date(), 30),
                  end: new Date(),
                },
              },
            },
            false,
          )
          .pipe(
            catchError((err) => {
              console.error(err);
              return ObservableEmpty;
            }),
            map((rs) => {
              const lowRatings = ['1', '2', '3'];
              let lowRatingTotal = 0;
              for (const rating of lowRatings) {
                lowRatingTotal += rs.ratingStats.ratingsCount[rating];
              }
              return {
                accountGroupId: c.accountGroupId,
                reviews: rs.reviews.slice(0, 3),
                total: rs.ratingStats.total,
                lowRatingTotal: lowRatingTotal,
              };
            }),
          );
      }),
    );
  }

  private loadRemovedReviewsSummary(
    partnerId: string,
    accountGroupId: string,
  ): Observable<RecentRemovedReviewsSummary> {
    return this.tickReloadingRemovedReviews$$.pipe(
      switchMap(() => this.reviewsApi.getRemovedReviewsSummary(partnerId, accountGroupId)),
    );
  }

  private loadEditedReviewsSummary(
    accountGroupId: string,
    dismissedDateTime: Date,
  ): Observable<RecentEditedReviewsSummary> {
    return this.tickReloadingEditedReviews$$.pipe(
      switchMap(() => this.reviewsApi.getEditedReviewsSummary(accountGroupId, dismissedDateTime)),
    );
  }

  triggerReloadingRemovedReviewsSummary(): void {
    const currentRemovedReviews = this.recentRemovedReviewsSummary$$.getValue();
    if (!!currentRemovedReviews && currentRemovedReviews.totalPotentiallyRemovedReviews > 0) {
      this.tickReloadingRemovedReviews$$.next(true);
    }
  }

  triggerReloadingEditedReviewsSummary(): void {
    const currentEditedReviews = this.recentEditedReviewsSummary$$.getValue();
    if (!!currentEditedReviews && currentEditedReviews.totalPotentiallyEditedReviews > 0) {
      this.tickReloadingEditedReviews$$.next(true);
    }
  }

  get listingStats$(): Observable<ListingStats> {
    return this.listingService.listingStats$;
  }

  get listingScore$(): Observable<number> {
    return this.listingService.listingScore$.pipe(map((listingScore) => listingScore.pointScore));
  }

  get percentageScoreText$(): Observable<string> {
    return this.listingService.percentageScoreText$;
  }

  get industryAverage$(): Observable<any> {
    return this.listingService.listingScore$.pipe(
      map((listingScore) => {
        if (listingScore.Country !== null && listingScore.Country !== undefined) {
          return listingScore.Country.industryAverage;
        } else if (listingScore.regions.length >= 1) {
          return listingScore[listingScore.regions[0]].industryAverage;
        }
      }),
    );
  }

  get historicalListingScores$(): Observable<HistoricalListingPointScores> {
    return this.listingService.historicalListingScores$;
  }

  get reviewStats$(): Observable<ReviewStats> {
    return this.reviewStats$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get reviewIndustryAverage$(): Observable<ReviewIndustryState> {
    return this.reviewIndustryAverage$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get initialReviewNumber$(): Observable<number> {
    return this.initialReviewNumber$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get initialAverageRating$(): Observable<number> {
    return this.initialAverageRating$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get mentionStats$(): Observable<MentionStats> {
    return this.mentionStats$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get competitorsStats$(): Observable<CompetitorsStats> {
    return this.competitorsStats$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get settings$(): Observable<OverviewSettings> {
    return this.settings$$.asObservable();
  }

  get topReviewSources$(): Observable<TopSource[]> {
    return this.topReviewSources$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get facebookRecommendations$(): Observable<FacebookRecommendations> {
    return this.facebookRecommendations$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get facebookRecommendationHistory$(): Observable<FacebookRecommendations> {
    return this.facebookRecommendationHistory$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get customerVoiceEnabled$(): Observable<boolean> {
    return this.customerVoiceEnabled$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get customerVoiceUrlPath$(): Observable<string> {
    return this.customerVoiceUrlPath$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  private sortCompetitorsStats(cs: CompetitorsStats): CompetitorsStats {
    // We get the data back with our business first then sorted in ascending order,
    // want our business first and sorted in descending instead
    if (!cs.shareOfVoiceDataExist) {
      return cs;
    }
    const firstVal = cs.shareOfVoice.data.shift();
    const firstLbl = cs.shareOfVoice.labels.shift();
    cs.shareOfVoice.data.reverse();
    cs.shareOfVoice.labels.reverse();
    cs.shareOfVoice.data.unshift(firstVal);
    cs.shareOfVoice.labels.unshift(firstLbl);
    return cs;
  }

  private formatTopSources(rs: ReviewSourceStatistics[]): TopSource[] {
    const topSources: TopSource[] = rs.map((src) => ({
      sourceName: src.source.name,
      sourceId: src.source.id,
      reviewCount: src.count,
    }));
    return topSources.sort((a, b) => b.reviewCount - a.reviewCount).slice(0, 3);
  }

  public getDismissedDateTime(accountGroupId: string): Date {
    const dismissedTimeString = localStorage.getItem(`reviewsBannerDismissed${accountGroupId}`);
    if (dismissedTimeString) {
      return new Date(dismissedTimeString);
    }
    return null;
  }

  public getFacebookAndGMBAccount(settings: Map<SocialServiceType, ConnectAccountSetting>): FacebookAndGMBInfo {
    const facebookUser = settings.get(SocialServiceType.FACEBOOK).services.find((service) => !!service.pages.length);
    const facebookAccount = facebookUser ? facebookUser.pages[0].name : '';
    const facebookUrl = facebookUser ? facebookUser.pages[0].profileUrl : '';
    const gmbAccount =
      settings.get(SocialServiceType.GMB).services.length > 0
        ? settings.get(SocialServiceType.GMB).services[0].name
        : '';
    const gmbProfileUrl =
      settings.get(SocialServiceType.GMB).services.length > 0
        ? settings.get(SocialServiceType.GMB).services[0].profileUrl
        : null;
    return {
      gmbAccountName: gmbAccount,
      facebookAccountName: facebookAccount,
      facebookUrl: facebookUrl,
      gmbProfileUrl: gmbProfileUrl,
    };
  }
}
