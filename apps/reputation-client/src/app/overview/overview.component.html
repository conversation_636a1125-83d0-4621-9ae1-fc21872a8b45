<rm-page
  [pageInfo]="pageInformation$ | async"
  [pageTitle]="'OVERVIEW.NAME' | translate"
  [primaryActionButton]="goToOldButton$ | async"
  [showReconnectAlert]="true"
  [showReconnectAction]="true"
>
  <ng-container *ngIf="showReviewWidget$ | async">
    <rm-update-review-widget-banner></rm-update-review-widget-banner>
  </ng-container>
  <ng-container *ngIf="showReviews$ | async">
    <ng-container *ngIf="deletedAndEditedReviewsSummary$ | async as reviewsSummary">
      <rm-reviews-action-banner
        [accountGroupId]="accountGroupId$ | async"
        [editedReviewsSummary]="reviewsSummary.editedReviewsSummary"
        [removedReviewsSummary]="reviewsSummary.removedReviewsSummary"
      ></rm-reviews-action-banner>
    </ng-container>
  </ng-container>
  <rm-welcome-banner
    [bothConnected]="hasFacebookAndGMBConnected$ | async"
    [hasReviews]="(reviewTotal$ | async) !== '0'"
    [isNewAccount]="isNewAccount$ | async"
  ></rm-welcome-banner>
  <div class="container">
    <div class="overview-cards" [class.overview-cards-wide]="showFewerCardsInRow$ | async">
      <!-- Review Summary Card -->
      <mat-card *ngIf="showReviews$ | async" class="high-card">
        <mat-card-header>
          <mat-card-title class="reviews-card-title">
            {{ 'REVIEWS.REVIEW_SUMMARY' | translate }}
          </mat-card-title>
        </mat-card-header>
        <ng-container *ngIf="reviewAverageRating$ | async as reviewAverageRating; else loadingContent">
          <ng-container *ngIf="facebookRecommendations$ | async as facebookRecommendations; else loadingContent">
            <ng-container *ngIf="facebookRecommendationHistory$ | async as frh; else loadingContent">
              <mat-card-content
                *ngIf="
                  (reviewTotal$ | async) !== '0' || facebookRecommendations.total !== 0;
                  else noReviewsOrRecommendations
                "
                class="ratings"
              >
                <!-- Review Star Rating -->
                <div *ngIf="(reviewTotal$ | async) !== '0'; else noReviews" class="rating">
                  <div class="average-rating rating-info">
                    <div class="rating-text">
                      <span class="rating-number">{{ reviewAverageRating }}</span>
                      <mat-icon class="star-lit">star</mat-icon>
                    </div>
                    <div class="subtext">
                      <div class="stat">
                        <span class="stat-label">
                          {{ 'REVIEWS.MANAGE.TOTAL_REVIEWS' | translate }}
                        </span>
                        <span class="stat-value">{{ reviewTotal$ | async }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="start-at-stats">
                    <div *ngIf="initialAverageRating$ | async as initial" class="stat">
                      <span class="stat-label">
                        {{ 'COMMON.SHARED_TERMS.START_AT' | translate }}
                      </span>
                      <span class="stat-value">{{ initial }}</span>
                    </div>
                  </div>
                </div>

                <!-- Facebook Recommendations -->
                <div *ngIf="facebookRecommendations.total !== 0; else noRecommendations" class="rating">
                  <div class="average-rating rating-info">
                    <div class="rating-text">
                      <span class="facebook-recommendations-text">{{ facebookRecommendations.recommendation }}%</span>
                      <img class="facebook-recommendations-icon" src="{{ facebookIcon }}" />
                    </div>
                    <div class="subtext">
                      {{ 'COMMON.SHARED_TERMS.RECOMMENDED' | translate }}
                    </div>
                  </div>
                  <div class="start-at-stats">
                    <div class="stat">
                      <span class="stat-label">
                        {{ 'COMMON.SHARED_TERMS.START_AT' | translate }}
                      </span>
                      <span class="stat-value">{{ frh.recommendation }}%</span>
                    </div>
                  </div>
                </div>
              </mat-card-content>
            </ng-container>
          </ng-container>
        </ng-container>
        <a *ngIf="(reviewTotal$ | async) !== '0'" [routerLink]="['../reviews']" class="card-link" mat-ripple></a>
      </mat-card>

      <!-- Insights Card -->
      <rm-insights-card-wrapper
        *ngIf="showReviews$ | async"
        [displayNumber]="5"
        class="insights-card"
      ></rm-insights-card-wrapper>

      <!-- Listing Summary Card -->
      <mat-card *ngIf="showListings$ | async" class="high-card">
        <mat-card-header>
          <mat-card-title>
            {{ 'COMMON.SHARED_TERMS.LISTING_SUMMARY' | translate }}
          </mat-card-title>
        </mat-card-header>
        <mat-card-content *ngIf="listingStats$ | async as ls; else loadingContent" class="listing-score ratings">
          <ng-container *ngIf="(isPercentageScorePartner$ | async) === false; else showPercentageScore">
            <div class="rating">
              <div class="rating-info">
                <div *ngIf="listingScore$ | async as listingScore" class="rating-text">
                  {{ listingScore }}
                </div>
                <div class="subtext">
                  {{ 'COMMON.SHARED_TERMS.LISTING_SCORE' | translate }}
                </div>
              </div>
              <div class="start-at-stats">
                <div *ngIf="startingListingScore$ | async as sls" class="stat">
                  <span class="stat-label">{{ 'COMMON.SHARED_TERMS.START_AT' | translate }}</span>
                  <span class="stat-value">{{ sls }}</span>
                </div>
                <div *ngIf="listingIndustryAverage$ | async as lia" class="stat">
                  <span class="stat-label">
                    {{ 'COMMON.SHARED_TERMS.INDUSTRY_AVG' | translate }}
                  </span>
                  <span class="stat-value">{{ lia }}</span>
                </div>
              </div>
            </div>
          </ng-container>
          <ng-template #showPercentageScore>
            <div class="rating">
              <div class="rating-info">
                <div *ngIf="percentageScore$ | async as percentScore" class="rating-text">{{ percentScore }}%</div>
                <div class="subtext">
                  {{ 'COMMON.SHARED_TERMS.LISTING_SCORE' | translate }}
                </div>
              </div>
            </div>
          </ng-template>

          <div class="listing-accuracy-stats">
            <div class="listing-stat">
              <div class="number">
                <mat-icon [style.color]="listingColorOverrides[0].value">check_circle</mat-icon>
                {{ ls.displaySourcesWithGreenListings }}
              </div>
              <div class="title">
                {{ 'LISTINGS.STATUSES.ACCURATE' | translate }}
              </div>
            </div>
            <div class="listing-stat">
              <div class="number">
                <mat-icon [style.color]="listingColorOverrides[1].value">error</mat-icon>
                {{ ls.displayListingsWithErrors }}
              </div>
              <div class="title">
                {{ 'LISTINGS.STATUSES.FOUND_WITH_POSSIBLE_ERRORS' | translate }}
              </div>
            </div>
            <div class="listing-stat">
              <div class="number">
                <mat-icon [style.color]="listingColorOverrides[2].value">cancel</mat-icon>
                {{ ls.totalSourcesMissingListings }}
              </div>
              <div class="title">
                {{ 'LISTINGS.STATUSES.NOT_FOUND' | translate }}
              </div>
            </div>
          </div>
        </mat-card-content>
        <a [routerLink]="['../visibility']" [style.z-index]="2" class="card-link" mat-ripple></a>
      </mat-card>

      <!-- Top Review Sources Card -->
      <mat-card *ngIf="(isExpressEnabled$ | async) === false && (showReviews$ | async)">
        <mat-card-header>
          <mat-card-title>
            {{ 'OVERVIEW.CARDS.TOP_REVIEW_SITES.NAME' | translate }}
          </mat-card-title>
        </mat-card-header>
        <ng-container *ngIf="topReviewSources$ | async as trs; else loadingContent">
          <mat-card-content *ngIf="trs?.length > 0; else noTopSites" class="review-sources">
            <div class="review-sources-content">
              <ng-container *ngFor="let topSource of trs">
                <div class="review-source">
                  <va-icon
                    class="source-icon"
                    [diameter]="40"
                    [iconUrl]="'//www.cdnstyles.com/static/images/icon50/sourceId-' + topSource.sourceId + '.png'"
                  ></va-icon>
                  <div class="source-info">
                    <span class="source-name">{{ topSource.sourceName }}</span>
                    <span class="review-count">
                      {{ topSource.reviewCount }}
                      {{ 'REVIEWS.NAME' | translate }}
                    </span>
                  </div>
                </div>
              </ng-container>
            </div>
          </mat-card-content>
        </ng-container>
        <a
          *ngIf="(topReviewSources$ | async)?.length > 0"
          [routerLink]="['../reviews/statistics']"
          class="card-link"
          mat-ripple
        ></a>
      </mat-card>

      <!-- Competition Card -->
      <mat-card *ngIf="(isExpressEnabled$ | async) === false && (showCompetiton$ | async)">
        <mat-card-header>
          <mat-card-title>
            {{ 'OVERVIEW.CARDS.COMPETITION.NAME' | translate }}
          </mat-card-title>
          <mat-card-subtitle *ngIf="(competitorsStatsChartConfig$ | async)?.seriesData?.length > 0" class="description">
            {{ 'OVERVIEW.CARDS.COMPETITION.TOOLTIP' | translate }}
          </mat-card-subtitle>
        </mat-card-header>
        <ng-container *ngIf="competitorsStatsChartConfig$ | async as cscc; else loadingContent">
          <mat-card-content *ngIf="cscc?.seriesData?.length > 0; else noCompetitions" class="competition">
            <div class="chart-container">
              <rm-chart (chartSelect)="navigateOnChartClick('../competition')" [config]="cscc"></rm-chart>
            </div>
            <ul class="competition-legend">
              <ng-container *ngFor="let sd of cscc.seriesData; let i = index">
                <li class="legend-line">
                  <mat-icon [ngStyle]="{ color: cscc.colorOverrides[i].value }" class="legend-color">lens</mat-icon>
                  <span class="legend-label">{{ sd.name }}</span>
                </li>
              </ng-container>
            </ul>
          </mat-card-content>
        </ng-container>
        <a
          *ngIf="(competitorsStatsChartConfig$ | async)?.seriesData?.length > 0"
          [routerLink]="['../competition']"
          class="card-link"
          mat-ripple
        ></a>
      </mat-card>

      <!-- Mentions Card -->
      <mat-card *ngIf="(isExpressEnabled$ | async) === false && (showMentions$ | async)">
        <mat-card-header>
          <mat-card-title>
            {{ 'MENTIONS.RECENT_MENTIONS' | translate }}
          </mat-card-title>
        </mat-card-header>
        <ng-container *ngIf="mentionStatsChartConfig$ | async as mscc; else loadingContent">
          <mat-card-content *ngIf="mscc?.seriesData?.length > 0; else noMentions" class="mentions">
            <div class="mentions-info">
              <div class="rating-text">
                {{ mentionTotal$ | async }}
              </div>
              <div class="subtext">
                {{ 'COMMON.TIME_SERIES.THIS_WEEK' | translate }}
              </div>
            </div>
            <rm-chart (chartSelect)="navigateOnChartClick('../mentions')" [config]="mscc"></rm-chart>
          </mat-card-content>
        </ng-container>
        <a
          *ngIf="(mentionStatsChartConfig$ | async)?.seriesData?.length > 0"
          [routerLink]="['../mentions']"
          class="card-link"
          mat-ripple
        ></a>
      </mat-card>
    </div>
  </div>
</rm-page>

<ng-template #loadingContent>
  <mat-card-content class="loading-content">
    <div class="stencil-shimmer"></div>
  </mat-card-content>
</ng-template>

<ng-template #noReviews>
  <div class="rating">
    <div class="average-rating rating-info">
      <div class="rating-text">
        0
        <mat-icon class="star-lit fade">star</mat-icon>
      </div>
      <div class="subtext">
        <div class="stat">
          <span class="stat-label">{{ 'REVIEWS.MANAGE.TOTAL_REVIEWS' | translate }}</span>
          <span class="stat-value">0</span>
        </div>
      </div>
    </div>
    <div class="start-at-stats">
      <div class="stat">
        <span class="stat-label">{{ 'COMMON.SHARED_TERMS.START_AT' | translate }}</span>
        <span class="stat-value">0</span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #noRecommendations>
  <div class="rating">
    <div class="average-rating rating-info">
      <div class="rating-text">
        <div class="facebook-recommendations-text">0%</div>
        <img class="facebook-recommendations-icon fade" src="{{ facebookIcon }}" />
      </div>
      <div class="subtext">
        {{ 'COMMON.SHARED_TERMS.RECOMMENDED' | translate }}
      </div>
    </div>
    <div class="start-at-stats">
      <div class="stat">
        <span class="stat-label">{{ 'COMMON.SHARED_TERMS.START_AT' | translate }}</span>
        <span class="stat-value">0%</span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #noReviewsOrRecommendations>
  <ng-container *ngIf="hasFacebookOrGMBConnected$ | async; else noAccountConnected">
    <div *ngIf="customerVoiceEnabled$ | async" class="overview--empty-state">
      <rm-empty-state
        [buttonLink]="customerVoiceUrlPath$ | async"
        [buttonText]="'OVERVIEW.NO_REVIEW_STATS.BUTTON_REQUEST_REVIEWS'"
        [description]="'OVERVIEW.NO_REVIEW_STATS.DESCRIPTION_REQUEST_REVIEWS'"
        [icon]="'star'"
        [openNewLocationForLink]="true"
      ></rm-empty-state>
    </div>
    <div *ngIf="(customerVoiceEnabled$ | async) === false" class="overview--empty-state">
      <rm-empty-state [description]="'OVERVIEW.NO_REVIEW_STATS.DESCRIPTION'" [icon]="'star'"></rm-empty-state>
    </div>
  </ng-container>
</ng-template>

<ng-template #noAccountConnected>
  <div class="overview--empty-state">
    <rm-empty-state
      [description]="'OVERVIEW.NO_REVIEW_STATS.DESCRIPTION_CONNECT_ACCOUNT'"
      [icon]="'star'"
    ></rm-empty-state>
  </div>
</ng-template>

<ng-template #noTopSites>
  <div class="overview--empty-state">
    <rm-empty-state [description]="'OVERVIEW.NO_TOP_SITES_STATS.DESCRIPTION'" [icon]="'star'"></rm-empty-state>
  </div>
</ng-template>

<ng-template #noCompetitions>
  <div class="overview--empty-state">
    <rm-empty-state
      [buttonLink]="'settings/competitors'"
      [buttonText]="'OVERVIEW.NO_COMPETITION_STATS.BUTTON_TEXT'"
      [description]="'OVERVIEW.NO_COMPETITION_STATS.DESCRIPTION'"
      [icon]="'poll'"
    ></rm-empty-state>
  </div>
</ng-template>

<ng-template #noMentions>
  <div class="overview--empty-state">
    <rm-empty-state
      [buttonLink]="'settings/mentions'"
      [buttonText]="'OVERVIEW.NO_MENTION_STATS.BUTTON_TEXT'"
      [description]="'OVERVIEW.NO_MENTION_STATS.DESCRIPTION'"
      [icon]="'textsms'"
    ></rm-empty-state>
  </div>
</ng-template>
