import { Injectable } from '@angular/core';

import { TranslateService } from '@ngx-translate/core';
import {
  CheckboxFilterField,
  DatePickerFilterField,
  FilterField,
  Filters,
  FilterSection,
  SearchSelectFilterField,
} from '@vendasta/uikit';
import {
  BehaviorSubject,
  combineLatest,
  Observable,
  EMPTY as ObservableEmpty,
  merge as ObservableMerge,
  of,
} from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  first,
  map,
  scan,
  shareReplay,
  skip,
  skipWhile,
  startWith,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';

import { ActivatedRoute, Params } from '@angular/router';
import { FeatureFlagService } from '@galaxy/partner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ConciergeReviewResponseData } from '@vendasta/reviews';
import { isEqual } from 'lodash-es';
import {
  AppConfigService,
  i18nKey,
  ListReviewsRequest,
  ListReviewsResponse,
  Review,
  ReviewComment,
  ReviewFilters,
  ReviewHistoryAverageRatings,
  ReviewHistoryStats,
  ReviewsApiService,
  ReviewSettings,
  ReviewStats,
  ReviewStatus,
  WordCloudItem,
} from '../core';
import { ReviewIndustryState, ReviewSourceStatistics } from '../core/reviews.api.service';

export interface ReviewFeedData {
  reviews: Review[];
  wordCloud: any;
  ratingStats: any;
  sourcesCount?: object;
}

@Injectable()
export class OldReviewsService {
  featureFlags$: Observable<{ hideGoogleTranslatedReviewContent: boolean }>;
  reviewsFeed$: Observable<Review[]>;
  public readonly filters$: Observable<Filters>;
  public readonly reviewSettings$: Observable<ReviewSettings>;
  private search$$: BehaviorSubject<string> = new BehaviorSubject('');
  private reviewFeedData$$: BehaviorSubject<ReviewFeedData> = new BehaviorSubject(null);
  private loadMoreReviews$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private reviewsFeedCursor$$: BehaviorSubject<string> = new BehaviorSubject(null);
  private loadingReviewsFeed$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private reviewHistoryStats$$: BehaviorSubject<ReviewHistoryStats> = new BehaviorSubject(null);
  private reviewHistoryAverageRatings$$: BehaviorSubject<ReviewHistoryAverageRatings> = new BehaviorSubject(null);
  private topReviewSources$$: BehaviorSubject<ReviewSourceStatistics[]> = new BehaviorSubject(null);
  private reviewIndustryState$$: BehaviorSubject<ReviewIndustryState> = new BehaviorSubject(null);
  private startLoadingReviewStatistics$$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  private filterValueChanged$: Observable<FilterField<any>>;

  constructor(
    private reviewsApi: ReviewsApiService,
    private config: AppConfigService,
    private snackbarService: SnackbarService,
    private translateService: TranslateService,
    private featureFlagService: FeatureFlagService,
    private route: ActivatedRoute,
  ) {
    const partnerIdAndAccountGroupId$ = this.config.config$.pipe(
      map((c) => ({ partnerId: c.partnerId, accountGroupId: c.accountGroupId })),
      distinctUntilChanged((a, b) => a.partnerId === b.partnerId && a.accountGroupId === b.accountGroupId),
      shareReplay(1),
    );

    const accountGroupId$ = partnerIdAndAccountGroupId$.pipe(
      map((c) => c.accountGroupId),
      distinctUntilChanged(),
      shareReplay(1),
    );

    const filterParams$ = this.route.queryParams.pipe(
      distinctUntilChanged(isEqual),
      map((params) => this.buildFilterParams(params)),
    );

    this.filters$ = accountGroupId$.pipe(
      switchMap((accountGroupId) => combineLatest([this.getReviewFilters(accountGroupId), filterParams$])),
      map(([filters, filterParams]) => this.loadFiltersWithParams(filters, filterParams)),
      shareReplay(1),
    );

    this.filterValueChanged$ = this.filters$.pipe(
      switchMap((filters) =>
        ObservableMerge(
          ...filters.fields.map((field) => {
            const initialField = {
              ...field,
              value: typeof field.value === 'object' ? { ...field.value } : field.value,
            };
            return field.valueObservable.pipe(
              // Required for distinctUntilChanged, as a and b will be same object otherwise
              // and when the value of a is updated be will also be updated, resulting in wrong comparison
              map((f) => ({ ...f })),
              startWith(initialField), // Added to populate the previous value in the distinct
              distinctUntilChanged((a, b) => JSON.stringify(a.value) === JSON.stringify(b.value)),
              skip(1), // Added to skip the value added for the distincts initial value
            );
          }),
        ),
      ),
      startWith(null as FilterField<any>),
    ) as Observable<FilterField<any>>;

    // load reviews feed
    partnerIdAndAccountGroupId$
      .pipe(switchMap((c) => this.loadReviewsFeed(c.partnerId, c.accountGroupId)))
      .subscribe((reviewFeedData) => {
        this.startLoadingReviewStatistics$$.next(true);
        this.reviewFeedData$$.next(reviewFeedData);
      });

    this.reviewSettings$ = accountGroupId$.pipe(
      switchMap((accountGroupId) => this.loadReviewSettings(accountGroupId)),
      shareReplay(1),
    );

    // The reviews do not load fast enough without this to display on the page properly
    this.reviewSettings$.subscribe();

    // load review statistics
    accountGroupId$.pipe(switchMap((accountGroupId) => this.loadReviewStatisticsData(accountGroupId))).subscribe();

    this.reviewsFeed$ = ObservableMerge(
      this.reviewFeedData$$.asObservable().pipe(
        skipWhile((value) => value === null),
        map((r) => r.reviews),
      ),
      combineLatest([this.filterValueChanged$, this.search$]).pipe(map(() => null)),
    ).pipe(
      scan(
        (acc, reviews) =>
          reviews ? [...acc.filter((old) => !reviews.find((r) => r.reviewID === old.reviewID)), ...reviews] : [],
        [],
      ),
      shareReplay(1),
    );

    // The reviews do not load fast enough without this to display on the page properly
    this.reviewsFeed$.subscribe();

    const sourcesCount$ = this.reviewFeedData$$.pipe(
      skipWhile((value) => value === null),
      map((r) => r.sourcesCount),
      distinctUntilChanged(),
    );

    // set source filter counts (the numbers in brackets beside the source name filters)
    combineLatest([this.filters$, sourcesCount$]).subscribe(([filters, sourcesCount]) => {
      const srcSection = filters?.sections?.find((section) => section.title === 'REVIEWS.MANAGE.FILTER.SOURCES');
      const srcFields = srcSection?.fields;
      if (!!srcFields && !!sourcesCount) {
        srcFields.forEach((field) => {
          const sourceID = field.id;
          const reviewCountStr = field?.name?.split(/[()]+/)[1];
          const oldReviewsCount = reviewCountStr ? parseInt(reviewCountStr, 10) : 0;
          const reviewsCount = sourcesCount[sourceID] || 0;
          if (oldReviewsCount !== reviewsCount) {
            field.name = field.name.replace(`(${oldReviewsCount})`, `(${reviewsCount})`);
          }
        });
      }
    });
  }

  get reviewHistoryStats$(): Observable<ReviewHistoryStats> {
    return this.reviewHistoryStats$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get hasMoreReviews$(): Observable<boolean> {
    return this.reviewsFeedCursor$$.asObservable().pipe(map(Boolean));
  }

  get loadingReviews$(): Observable<boolean> {
    return this.loadingReviewsFeed$$.asObservable();
  }

  get wordCloud$(): Observable<WordCloudItem[]> {
    return this.reviewFeedData$$.asObservable().pipe(
      skipWhile((value) => value === null),
      map((r) => r.wordCloud),
    );
  }

  get stats$(): Observable<ReviewStats> {
    return this.reviewFeedData$$.asObservable().pipe(
      skipWhile((value) => value === null),
      map((r) => r.ratingStats),
    );
  }

  get reviewHistoryAverageRatings$(): Observable<ReviewHistoryAverageRatings> {
    return this.reviewHistoryAverageRatings$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get topReviewSources$(): Observable<ReviewSourceStatistics[]> {
    return this.topReviewSources$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  get search$(): Observable<string> {
    return this.search$$.asObservable().pipe(debounceTime(200), distinctUntilChanged());
  }

  get curSearchTerm(): string {
    return this.search$$.getValue();
  }

  get industryState$(): Observable<ReviewIndustryState> {
    return this.reviewIndustryState$$.asObservable().pipe(skipWhile((value) => value === null));
  }

  buildFilterParams(params: Observable<Params>): Map<string, string> {
    const filterParams = new Map<string, string>();
    if (params['startDate']) {
      filterParams.set('startDate', params['startDate']);
    }
    if (params['endDate']) {
      filterParams.set('endDate', params['endDate']);
    }
    if (params['sourceId']) {
      filterParams.set('sourceId', params['sourceId']);
    }
    if (params['rating']) {
      filterParams.set('rating', params['rating']);
    }
    if (params['responseStatus']) {
      filterParams.set('responseStatus', params['responseStatus']);
    }
    if (params['removedReviews']) {
      filterParams.set('removedReviews', 'true');
    }
    if (params['editedReviews']) {
      filterParams.set('editedReviews', 'true');
    }
    return filterParams;
  }

  kickoffLoadingReviewStatisticsData(): void {
    this.startLoadingReviewStatistics$$.next(true);
  }

  publishReview(reviewID: string): void {
    this.config.config$
      .pipe(
        first(),
        switchMap(({ accountGroupId, partnerId }) =>
          this.reviewsApi.updateReview({
            accountGroupId: accountGroupId,
            partnerId: partnerId,
            reviewID,
            isPublished: true,
          }),
        ),
        catchError((err) => {
          console.error(err);
          this.snackbarService.errorSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_PUBLISHING_REVIEW'),
          );
          return ObservableEmpty;
        }),
        withLatestFrom(this.reviewFeedData$$),
        tap(([, current]) => {
          // Update review status
          const reviews = current.reviews;
          const i = reviews.findIndex((r) => r.reviewID === reviewID);
          if (i >= 0) {
            reviews[i] = { ...reviews[i], isPublished: true };
            this.reviewFeedData$$.next({ ...current });
          }
          this.snackbarService.successSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.REVIEW_PUBLISHED'),
          );
        }),
      )
      .subscribe();
  }

  unpublishReview(reviewID: string): void {
    this.config.config$
      .pipe(
        first(),
        switchMap(({ accountGroupId, partnerId }) =>
          this.reviewsApi.updateReview({
            accountGroupId: accountGroupId,
            partnerId: partnerId,
            reviewID,
            isPublished: false,
          }),
        ),
        catchError((err) => {
          console.error(err);
          this.snackbarService.errorSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_UNPUBLISHING_REVIEW'),
          );
          return ObservableEmpty;
        }),
        withLatestFrom(this.reviewFeedData$$),
        tap(([, current]) => {
          // Update review status
          const reviews = current.reviews;
          const i = reviews.findIndex((r) => r.reviewID === reviewID);
          if (i >= 0) {
            reviews[i] = { ...reviews[i], isPublished: false };
            this.reviewFeedData$$.next({ ...current, reviews });
          }
          this.snackbarService.successSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.REVIEW_UNPUBLISHED'),
          );
        }),
      )
      .subscribe();
  }

  publishToReviewWidget(reviewID: string): void {
    this.config.config$
      .pipe(
        first(),
        switchMap(({ accountGroupId, partnerId }) =>
          this.reviewsApi.updateReview({
            accountGroupId: accountGroupId,
            partnerId: partnerId,
            reviewID,
            isPublishedThirdPartyReview: true,
          }),
        ),
        catchError((err) => {
          console.error(err);
          this.snackbarService.errorSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_PUBLISHING_REVIEW_TO_WIDGET'),
          );
          return ObservableEmpty;
        }),
        withLatestFrom(this.reviewFeedData$$),
        tap(([, current]) => {
          // Update review status
          const reviews = current.reviews;
          const i = reviews.findIndex((r) => r.reviewID === reviewID);
          if (i >= 0) {
            reviews[i] = { ...reviews[i], publishThirdPartyReview: true };
            this.reviewFeedData$$.next({ ...current });
          }
          this.snackbarService.successSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.REVIEW_PUBLISHED_TO_WIDGET'),
          );
        }),
      )
      .subscribe();
  }

  unpublishFromReviewWidget(reviewID: string): void {
    this.config.config$
      .pipe(
        first(),
        switchMap(({ accountGroupId, partnerId }) =>
          this.reviewsApi.updateReview({
            accountGroupId: accountGroupId,
            partnerId: partnerId,
            reviewID,
            isPublishedThirdPartyReview: false,
          }),
        ),
        catchError((err) => {
          console.error(err);
          this.snackbarService.errorSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_UNPUBLISHING_REVIEW_FROM_WIDGET'),
          );
          return ObservableEmpty;
        }),
        withLatestFrom(this.reviewFeedData$$),
        tap(([, current]) => {
          // Update review status
          const reviews = current.reviews;
          const i = reviews.findIndex((r) => r.reviewID === reviewID);
          if (i >= 0) {
            reviews[i] = { ...reviews[i], publishThirdPartyReview: false };
            this.reviewFeedData$$.next({ ...current, reviews });
          }
          this.snackbarService.successSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.REVIEW_UNPUBLISHED_FROM_WIDGET'),
          );
        }),
      )
      .subscribe();
  }

  updateReviewStatus(reviewID: string, status: ReviewStatus): void {
    this.config.config$
      .pipe(
        first(),
        switchMap(({ accountGroupId, partnerId }) =>
          this.reviewsApi.updateReview({
            accountGroupId: accountGroupId,
            partnerId: partnerId,
            reviewID,
            status,
          }),
        ),
        catchError((err) => {
          console.error(err);
          this.snackbarService.errorSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_UPDATING_REVIEW_STATUS'),
          );
          return ObservableEmpty;
        }),
        withLatestFrom(this.reviewFeedData$$),
        tap(([, current]) => {
          // Update review status
          const reviews = current.reviews;
          const i = reviews.findIndex((r) => r.reviewID === reviewID);
          if (i >= 0) {
            reviews[i] = { ...reviews[i], status };
            this.reviewFeedData$$.next({ ...current, reviews });
          }
          this.snackbarService.successSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.REVIEW_STATUS_UPDATED'),
          );
        }),
      )
      .subscribe();
  }

  createReviewComment(reviewID: string, commentText: string): void {
    this.config.config$
      .pipe(
        first(),
        switchMap(({ accountGroupId }) => {
          return this.reviewsApi.canPublishReviewCommentToSource(reviewID, accountGroupId).pipe(
            skipWhile((value) => value === null),
            take(1),
            switchMap((res) => {
              if (res && !res.canPublish) {
                this.snackbarService.errorSnack(
                  res.message && res.message.length > 0
                    ? res.message
                    : this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_ADDING_COMMENT'),
                );
                return ObservableEmpty;
              }

              return this.reviewsApi.createReviewComment({
                accountGroupId: accountGroupId,
                reviewID,
                commentText,
              });
            }),
          );
        }),
        catchError((err) => {
          console.error(err);
          this.snackbarService.errorSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_ADDING_COMMENT'),
          );
          return ObservableEmpty;
        }),
        tap((newComment: ReviewComment) => {
          // Update review status
          const current = this.reviewFeedData$$.getValue();
          const reviews = current.reviews;
          const i = reviews.findIndex((r) => r.reviewID === reviewID);
          if (i >= 0) {
            reviews[i] = { ...reviews[i], comments: [...reviews[i].comments, newComment] };
            reviews[i].status = newComment.postedByOwner
              ? ReviewStatus.OWNER_RESPONDED
              : ReviewStatus.DIGITAL_AGENT_RESPONDED;
            this.reviewFeedData$$.next({ ...current, reviews });
          }
          this.snackbarService.successSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.COMMENT_SAVED'),
          );
        }),
      )
      .subscribe();
  }

  editReviewComment(comment: ReviewComment, updatedCommentText: string): Observable<any> {
    const erc = this.config.config$.pipe(
      first(),
      switchMap(({ accountGroupId }) =>
        this.reviewsApi.updateReviewComment({
          accountGroupId: accountGroupId,
          reviewCommentId: comment.commentId,
          updatedCommentText: updatedCommentText,
        }),
      ),
      catchError((err) => {
        console.error(err);
        this.snackbarService.errorSnack(
          this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_EDITING_COMMENT'),
        );
        return ObservableEmpty;
      }),
      tap((updatedComment: ReviewComment) => {
        Object.assign(comment, updatedComment);
        this.snackbarService.successSnack(
          this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.COMMENT_EDITED'),
        );
      }),
      shareReplay(),
    );
    return erc;
  }

  createConciergeFeedback(reviewId: string, feedbackText: string, callback?: () => void): void {
    this.config.config$
      .pipe(
        first(),
        switchMap(({ accountGroupId, partnerId }) => {
          return this.reviewsApi.provideConciergeFeedback({
            accountGroupId: accountGroupId,
            reviewId: reviewId,
            feedback: feedbackText,
            partnerId: partnerId,
          });
        }),
        tap(() => {
          this.snackbarService.successSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.FEEDBACK_SAVED'),
          );
          if (callback) {
            callback();
          }
        }),
        catchError((err) => {
          console.error(err);
          this.snackbarService.errorSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_ADDING_FEEDBACK'),
          );
          return ObservableEmpty;
        }),
      )
      .subscribe();
  }

  approveConciergeResponse(reviewId: string, callback?: () => void): void {
    this.config.config$
      .pipe(
        first(),
        switchMap(({ accountGroupId, partnerId }) => {
          return this.reviewsApi.approveConciergeResponse({
            accountGroupId: accountGroupId,
            reviewId: reviewId,
            partnerId: partnerId,
          });
        }),
        tap(() => {
          this.snackbarService.successSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.RESPONSE_APPROVED'),
          );
          if (callback) {
            callback();
          }
        }),
        catchError((err) => {
          console.error(err);
          this.snackbarService.errorSnack(
            this.translateService.instant('REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_APPROVING_FEEDBACK'),
          );
          return ObservableEmpty;
        }),
      )
      .subscribe();
  }

  getConciergeReviewResponse(reviewId: string): Observable<ConciergeReviewResponseData> {
    const response = this.config.config$.pipe(
      first(),
      switchMap(({ accountGroupId, partnerId }) => {
        return this.reviewsApi.getConciergeReviewResponse({
          accountGroupId: accountGroupId,
          reviewId: reviewId,
          partnerId: partnerId,
        });
      }),
    );
    return response;
  }

  loadMore(): void {
    this.loadMoreReviews$$.next(true);
  }

  updateSearch(term: string): void {
    this.search$$.next(term);
  }

  buildFilters(filters): Filters {
    const formData = filters.formdata;
    const vfilterSections = filters.sections;
    const sections = [
      new FilterSection({
        title: 'COMMON.FILTER_LABELS.DATE_RANGE',
        type: 'or',
        fields: [
          new DatePickerFilterField({
            id: 'startDate',
            name: 'COMMON.FILTER_LABELS.START',
            value: null,
          }),
          new DatePickerFilterField({
            id: 'endDate',
            name: 'COMMON.FILTER_LABELS.END',
            value: null,
          }),
        ],
      }),
    ];

    const srcFields = vfilterSections.find((d) => d.name === 'sources').fields;
    if (srcFields && srcFields.length > 0) {
      sections.push(
        new FilterSection({
          title: 'REVIEWS.MANAGE.FILTER.SOURCES',
          type: 'or',
          fields: vfilterSections
            .find((d) => d.name === 'sources')
            .fields.map((s) => {
              return new CheckboxFilterField({
                name: s.label,
                id: s.name.split('_')[1],
                value: false,
              });
            }),
        }),
      );
    }

    sections.push(
      new FilterSection({
        title: 'COMMON.SHARED_TERMS.RATINGS',
        type: 'or',
        fields: vfilterSections
          .find((d) => d.name === 'ratings')
          .fields.map((s) => {
            return new CheckboxFilterField({
              name: i18nKey(s.label),
              id: s.name.indexOf('no_rating') > -1 ? '0' : s.name.split('_')[1],
              value: false,
            });
          }),
      }),
    );

    const changesSection = vfilterSections.find((d) => d.name === 'changes');
    if (changesSection) {
      const changesFields = changesSection.fields;
      if (changesFields && changesFields.length > 0) {
        sections.push(
          new FilterSection({
            title: 'REVIEWS.MANAGE.FILTER.CHANGES',
            type: 'or',
            fields: vfilterSections
              .find((d) => d.name === 'changes')
              .fields.map((s) => {
                return new CheckboxFilterField({
                  name: i18nKey(s.label),
                  id: s.name.split('Flag')[0],
                  value: false,
                });
              }),
          }),
        );
      }
    }
    sections.push(
      new FilterSection({
        title: 'COMMON.ACTION_LABELS.STATUS',
        type: 'or',
        fields: vfilterSections
          .find((d) => d.name === 'status')
          .fields.filter((field) => {
            if (field.visible) {
              return true;
            }

            if (formData) {
              const fieldFormData = formData.find((d) => d.name === field.name);
              return fieldFormData && fieldFormData.visible;
            }

            return false;
          })
          .map((s) => {
            if (s.control === 'dropdown') {
              const options = [
                { label: s.placeholder, value: '' },
                ...filters.formdata.find((d) => d.name === s.name).options,
              ].map((opt) => ({
                ...opt,
                label: i18nKey(opt.label),
              }));
              return new SearchSelectFilterField({
                name: '',
                id: s.name,
                value: options[0],
                optionDisplayProperty: 'label',
                dropdownWidth: '200px',
                width: 210,
                options,
              });
            } else {
              return new CheckboxFilterField({
                name: i18nKey(s.label),
                id: s.name.split('Flag')[0],
                value: false,
              });
            }
          }),
      }),
    );
    return new Filters('COMMON.FILTER_LABELS.FILTERS', sections);
  }

  private loadFiltersWithParams(filters: Filters, filterParams: Map<string, string>): Filters {
    filters.innerSections.map((filter) => {
      if (filterParams.has('startDate')) {
        if (filter.title === 'COMMON.FILTER_LABELS.DATE_RANGE') {
          filter.fields.forEach((field) => {
            if (field.id === 'startDate') {
              field.changeValue(new Date(filterParams.get('startDate')));
            }
          });
        }
      }
      if (filterParams.has('endDate')) {
        if (filter.title === 'COMMON.FILTER_LABELS.DATE_RANGE') {
          filter.fields.forEach((field) => {
            if (field.id === 'endDate') {
              field.changeValue(new Date(filterParams.get('endDate')));
            }
          });
        }
      }
      if (filterParams.has('sourceId')) {
        if (filter.title === 'REVIEWS.MANAGE.FILTER.SOURCES') {
          filter.fields.map((field) => {
            field.changeValue(field.id === filterParams.get('sourceId'));
          });
        }
      }
      if (filterParams.has('rating')) {
        if (filter.title === 'COMMON.SHARED_TERMS.RATINGS') {
          filter.fields.map((field) => {
            field.changeValue(filterParams.get('rating').includes(field.id));
          });
        }
      }
      if (filterParams.has('responseStatus')) {
        if (filter.title === 'COMMON.ACTION_LABELS.STATUS') {
          filter.fields.map((field) => {
            if (field.id === 'responseStatus') {
              field.options.forEach((option) => {
                if (option.value === filterParams.get('responseStatus')) {
                  field.changeValue(option);
                }
              });
            }
          });
        }
      }
      if (filterParams.has('removedReviews') && filterParams.get('removedReviews') === 'true') {
        if (filter.title === 'REVIEWS.MANAGE.FILTER.CHANGES') {
          filter.fields.forEach((field) => {
            if (field.type === 'checkbox' && field.id === 'removedReviews') {
              field.changeValue(true);
            }
          });
        }
      }
      if (filterParams.has('editedReviews') && filterParams.get('editedReviews') === 'true') {
        if (filter.title === 'REVIEWS.MANAGE.FILTER.CHANGES') {
          filter.fields.forEach((field) => {
            if (field.type === 'checkbox' && field.id === 'editedReviews') {
              field.changeValue(true);
            }
          });
        }
      }
      return filter;
    });
    return filters;
  }

  private loadReviewSettings(accountGroupId: string): Observable<ReviewSettings> {
    return this.reviewsApi.getReviewSettings(accountGroupId);
  }

  private loadReviewStatisticsData(accountGroupId: string): Observable<any> {
    return this.startLoadingReviewStatistics$$.pipe(
      distinctUntilChanged(),
      switchMap((startLoadingReviewStatistics) => {
        if (startLoadingReviewStatistics) {
          return ObservableMerge(
            this.reviewsApi
              .getReviewHistoryStats(accountGroupId)
              .pipe(tap((resp) => this.reviewHistoryStats$$.next(resp))),
            this.reviewsApi
              .getReviewHistoryAverageRatings(accountGroupId)
              .pipe(tap((resp) => this.reviewHistoryAverageRatings$$.next(resp.series))),
            this.reviewsApi
              .getReviewsIndustryState(accountGroupId)
              .pipe(tap((resp) => this.reviewIndustryState$$.next(resp))),
            this.reviewsApi.getTopReviewSources(accountGroupId).pipe(tap((resp) => this.topReviewSources$$.next(resp))),
          );
        } else {
          return ObservableEmpty;
        }
      }),
    );
  }

  private loadReviewsFeed(partnerId: string, accountGroupId: string): Observable<ReviewFeedData> {
    this.featureFlags$ = this.config.config$.pipe(
      switchMap((config) => {
        return this.featureFlagService
          .batchGetStatus(config.partnerId, config.account?.accountGroup?.marketId, [
            'hide_google_translated_review_content',
          ])
          .pipe(catchError(() => of(null)));
      }),
      catchError(() =>
        of({
          hideGoogleTranslatedReviewContent: false,
        }),
      ),
      map((status) => {
        return {
          hideGoogleTranslatedReviewContent: status?.['hide_google_translated_review_content'],
        };
      }),
      shareReplay(1),
    );
    let hideGoogleTranslations = false;
    this.featureFlags$.subscribe((flags) => (hideGoogleTranslations = flags.hideGoogleTranslatedReviewContent));

    return combineLatest([
      this.filters$,
      this.search$,
      combineLatest([this.filterValueChanged$, this.search$]).pipe(tap(() => this.reviewsFeedCursor$$.next(null))),
      this.loadMoreReviews$$.asObservable(),
    ]).pipe(
      withLatestFrom(this.reviewsFeedCursor$$.asObservable()),
      map(([[filters, search], cursor]) => {
        const req: ListReviewsRequest = {
          cursor,
          partnerId,
          accountGroupId,
          filters: { ...this.reviewFilters(filters), search },
        };
        return req;
      }),
      distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
      tap(() => this.loadingReviewsFeed$$.next(true)),
      switchMap((req) => this.reviewsApi.listReviews(req, hideGoogleTranslations)),
      tap((res: ListReviewsResponse) => {
        this.loadingReviewsFeed$$.next(false);
        this.reviewsFeedCursor$$.next(res.cursor);
      }),
    );
  }

  private getReviewFilters(accountGroupId: string): Observable<Filters> {
    return this.reviewsApi.getReviewFilters(accountGroupId).pipe(map((filters) => this.buildFilters(filters)));
  }

  private reviewFilters(f: Filters): ReviewFilters {
    const starRatings = f.sections
      .find((s) => s.title === 'COMMON.SHARED_TERMS.RATINGS')
      .fields.reduce((acc, field) => {
        if (field.value) {
          acc.push(field.id);
        }
        return acc;
      }, []);
    const srcFilterSection = f.sections.find((s) => s.title === 'REVIEWS.MANAGE.FILTER.SOURCES');
    const sourceIDs =
      srcFilterSection && srcFilterSection.fields
        ? srcFilterSection.fields.reduce((acc, field) => {
            if (field.value) {
              acc.push(field.id);
            }
            return acc;
          }, [])
        : [];

    const changesSection = f.sections.find((s) => s.title === 'REVIEWS.MANAGE.FILTER.CHANGES');
    let changes;
    if (changesSection) {
      changes = changesSection.fields
        .filter((field) => field.type === 'checkbox')
        .reduce((acc, field) => {
          if (field.value) {
            acc.push(field.id);
          }
          return acc;
        }, []);
    }

    const commentsAndReviewsStatuses = f.sections
      .find((s) => s.title === 'COMMON.ACTION_LABELS.STATUS')
      .fields.filter((field) => field.type === 'checkbox')
      .reduce((acc, field) => {
        if (field.value) {
          acc.push(field.id);
        }
        return acc;
      }, []);
    const start = f.fields.find((field) => field.id === 'startDate').value;
    const end = f.fields.find((field) => field.id === 'endDate').value;
    const dateRange = start || end ? { start: start || new Date(0), end: end || new Date() } : null;
    return {
      sourceIDs,
      starRatings,
      changes: changes,
      commentsAndReviewsStatuses: commentsAndReviewsStatuses,
      dateRange,
      sharedStatus: f.fields.find((field) => field.id === 'sharedStatus').value.value,
      publishingStatus: f.fields.find((field) => field.id === 'publishingStatus').value.value,
      responseStatus: f.fields.find((field) => field.id === 'responseStatus').value.value,
    };
  }
}
