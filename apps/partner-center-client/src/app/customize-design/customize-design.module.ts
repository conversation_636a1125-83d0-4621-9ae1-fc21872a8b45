import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { VaFormsModule } from '@vendasta/forms';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyButtonGroupModule } from '@vendasta/galaxy/button-group';
import { GalaxyEmailViewerModule } from '@vendasta/galaxy/email-viewer';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { StencilsModule, VaOrderFormModule } from '@vendasta/store';
import { VaMaterialTableModule } from '@vendasta/uikit';
import { VFormModule } from '@vendasta/vform';
import { ColorPickerModule } from 'ngx-color-picker';
import { VaImageUploaderComponent } from '../common/file-upload/va-image-upload.component';
import { PageComponent } from '../common/page.component';
import { PartnerModule } from '../partner/partner.module';
import { CustomizeExecReportComponent } from './customize-exec-report/customize-exec-report.component';
import { CustomizeExecReportService } from './customize-exec-report/customize-exec-report.service';
import { PreviewEmailDialogComponent } from './order-config//preview-email-dialog/preview-email-dialog.component';
import { AutomationNameDisplayComponent } from './order-config/automation-name-display/automation-name-display.component';
import { OrderConfigComponent } from './order-config/order-config.component';
import { RestoreWarningDialogComponent } from './order-config/restore-warning-dialog/restore-warning-dialog.component';
import { SalesOrdersDeclineReasonsModule } from './sales-orders-decline-reasons/sales-orders-decline-reasons.module';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { OrderConfigMarketSelectionComponent } from './order-config/order-config-market-selection/order-config-market-selection.component';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';

@NgModule({
  imports: [
    CommonModule,
    StencilsModule,
    MatSidenavModule,
    MatCardModule,
    MatListModule,
    MatIconModule,
    MatProgressBarModule,
    PartnerModule,
    MatInputModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatExpansionModule,
    VaOrderFormModule,
    DragDropModule,
    VaFormsModule,
    MatCheckboxModule,
    MatDialogModule,
    MatSelectModule,
    TranslateModule,
    VFormModule,
    ColorPickerModule,
    MatRadioModule,
    MatTableModule,
    RouterModule,
    VaMaterialTableModule,
    GalaxyPipesModule,
    GalaxyPageModule,
    GalaxyInputModule,
    GalaxyAlertModule,
    GalaxyUploaderModule,
    GalaxyButtonGroupModule,
    GalaxyEmailViewerModule,
    MatButtonToggleModule,
    SalesOrdersDeclineReasonsModule,
    VaImageUploaderComponent,
    PageComponent,
    GalaxyFormFieldModule,
    GalaxyStickyFooterModule,
  ],
  declarations: [
    CustomizeExecReportComponent,
    OrderConfigComponent,
    OrderConfigMarketSelectionComponent,
    RestoreWarningDialogComponent,
    PreviewEmailDialogComponent,
    AutomationNameDisplayComponent,
  ],
  providers: [CustomizeExecReportService],
})
export class CustomizeDesignModule {}
