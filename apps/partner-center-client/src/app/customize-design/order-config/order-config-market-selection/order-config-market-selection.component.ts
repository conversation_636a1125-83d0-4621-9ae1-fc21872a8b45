import { Component } from '@angular/core';
import { Observable } from 'rxjs';
import { OrderConfigService } from '../order-config.service';
import { Market } from '@vendasta/partner';

@Component({
  selector: 'app-order-config-market-selection',
  template: `
    <glxy-form-field>
      <glxy-label>
        {{ 'CUSTOMIZE_SALES_ORDERS_MARKET_SELECTOR.LABEL' | translate }}
      </glxy-label>
      <mat-select (selectionChange)="updateSelectedMarket($event.value)" [value]="selectedMarketId$ | async">
        <mat-option [value]="''">
          {{ 'CUSTOMIZE_SALES_ORDERS_MARKET_SELECTOR.PARTNER_DEFAULT' | translate }}
        </mat-option>
        <mat-option *ngFor="let market of markets$ | async" [value]="market?.marketId">
          {{ market?.name }}
        </mat-option>
      </mat-select>
    </glxy-form-field>
  `,
  standalone: false,
})
export class OrderConfigMarketSelectionComponent {
  selectedMarketId$: Observable<string> = this.orderConfigService.orderConfigMarketId$;
  markets$: Observable<Market[]> = this.orderConfigService.getMarkets();

  constructor(private orderConfigService: OrderConfigService) {}

  updateSelectedMarket(marketId: string) {
    this.orderConfigService.updateMarket(marketId);
  }
}
