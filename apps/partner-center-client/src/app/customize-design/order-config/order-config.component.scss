@use 'design-tokens' as *;

:host ::ng-deep .settings-container input {
  border: none !important ;
}

.order-config-title {
  margin-top: 16px;
}

.settings-container {
  margin: 0 auto;
  max-width: 1200px;
}

.form-container > .mat-mdc-card {
  margin-bottom: 10px;
}

.default-notes {
  width: 500px;
  @media screen and (max-width: $media--tablet-minimum - 1) {
    width: 100%;
  }
}

.save {
  .spinner {
    width: 24px;
  }
}

.hint {
  color: $blue;
  cursor: pointer;
}

.settings-container mat-panel-title {
  .handle-icon {
    vertical-align: middle;
    color: rgba(0, 0, 0, 0.54) !important;
  }

  .title-text {
    display: inline-block;
    margin-left: 8px;
    padding-top: 2px;
  }
}

.custom-field-label {
  margin: 8px 0;
  font-size: 16px;
}

.add-custom-field {
  font-size: 14px;
  margin-top: 8px;
  display: block;
}

.no-fields {
  margin-left: 16px;
}

.steps {
  display: flex;
  flex-direction: column;
}

.collect-payment-config {
  margin-left: 32px;
}

.tax-field {
  .mat-mdc-form-field {
    margin-right: 16px;
  }

  .mat-mdc-form-field.ng-invalid {
    height: 100px;
  }
}

.terms-of-service {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
  padding-top: 10px;

  .delete-button {
    position: absolute;
    right: 0;
    top: 0;
  }
}

.tos-card {
  margin-bottom: 10px;
}

.tos-list,
.extra-fields {
  margin-top: 24px;
}

.no-tos {
  margin-top: 10px;
}

.button {
  margin-right: 5px;
}

.loading-config {
  ::ng-deep .mat-mdc-card {
    margin: 0 0 10px;
  }
}

.email-content-form {
  display: flex;
  flex-direction: column;
}

.remove-custom-action {
  margin-left: 20px;
}

.custom-description {
  @include text-preset-5;
  margin-top: 16px;
  margin-bottom: 8px;
}
