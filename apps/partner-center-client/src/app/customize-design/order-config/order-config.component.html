<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>
      {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.CONFIGURATION_TITLE' | translate }}
    </glxy-page-title>
    <glxy-page-nav>
      <glxy-page-nav-button
        [useHistory]="true"
        [historyBackButtonTitle]="'COMMON.ACTION_LABELS.BACK' | translate"
      ></glxy-page-nav-button>
    </glxy-page-nav>
  </glxy-page-toolbar>
  <glxy-page-wrapper [widthPreset]="'wide'">
    @if (markets$ | async; as markets) {
      @if (markets.length > 1) {
        <app-order-config-market-selection></app-order-config-market-selection>
      }
    }
    <h1 class="order-config-title">
      {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.TITLE' | translate }}
    </h1>
    <div class="settings-container" *ngIf="!loading && !saving; else loadingConfig">
      <div class="form-container" [formGroup]="configForm">
        <mat-card appearance="outlined">
          <mat-card-header>
            <mat-card-title>
              {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.TITLE' | translate }}
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div formGroupName="workflowStepConfig" class="steps">
              <mat-checkbox formControlName="maintainTermsAndConditionsAgreement">
                {{
                  'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.MAINTAIN_TERMS_AND_CONDITIONS_AGREEMENT'
                    | translate
                }}
              </mat-checkbox>
              <mat-checkbox formControlName="allowOrderFormEditing">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.ALLOW_ORDER_FORM_EDITING' | translate }}
              </mat-checkbox>
            </div>
            <div class="steps" formGroupName="salespersonOptions">
              <mat-checkbox formControlName="validateRequiredFields">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.SALESPEOPLE_SETTINGS.VALIDATE_REQUIRED_FIELDS' | translate }}
              </mat-checkbox>
              <mat-checkbox formControlName="disableSellingStandaloneProducts">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.SALESPEOPLE_SETTINGS.DISABLE_SELLING_PRODUCTS' | translate }}
              </mat-checkbox>
              <mat-checkbox formControlName="disableTagging">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.SALESPEOPLE_SETTINGS.DISABLE_TAGGING' | translate }}
              </mat-checkbox>
            </div>
            <div class="steps" formGroupName="customerOptions">
              <mat-checkbox formControlName="allowCustomerInitiatedOrders">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_SETTINGS.ALLOW_TO_SEND_AN_ORDER_FROM_STORE' | translate }}
              </mat-checkbox>
            </div>
          </mat-card-content>
        </mat-card>
        <mat-card appearance="outlined">
          <mat-card-header>
            <mat-card-title>
              {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.SALESPERSON_SUBMIT_ACTIONS' | translate }}
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div formGroupName="workflowStepConfig">
              <div class="steps">
                <mat-checkbox formControlName="allowSendDirectToAdmins">
                  {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.ALLOW_TO_SEND_ORDER_TO_ADMIN' | translate }}
                </mat-checkbox>
                <mat-checkbox formControlName="canChargeSmbOnOrderSubmission">
                  {{
                    'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.CAN_CHARGE_SMB_ON_ORDER_SUBMISSION'
                      | translate
                  }}
                </mat-checkbox>
                <div>
                  <mat-checkbox
                    formControlName="allowSendToCustomers"
                    (change)="toggleCollectPaymentFromCustomers($event.checked)"
                  >
                    {{
                      'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.ALLOW_TO_SEND_ORDER_TO_CUSTOMER_APPROVAL'
                        | translate
                    }}
                  </mat-checkbox>

                  <div *ngIf="salespersonAcceptsPayment$ | async" class="collect-payment-config">
                    <mat-checkbox
                      [disabled]="configForm.get('workflowStepConfig.allowSendToCustomers').value === false"
                      formControlName="allowCollectPaymentFromCustomers"
                    >
                      {{
                        'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.ALLOW_TO_COLLECT_PAYMENT_FROM_CUSTOMER'
                          | translate
                      }}
                    </mat-checkbox>
                  </div>
                </div>
              </div>
              <mat-error *ngIf="configForm.get('workflowStepConfig').errors">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.MIN_STEP_REQUIREMENT_ERROR' | translate }}
              </mat-error>
              <div *ngIf="customSalespersonActions$ | async">
                <div class="custom-description">
                  {{
                    'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.CUSTOM_SALESPERSON_ACTION_DESCRIPTION'
                      | translate
                  }}
                </div>
                <ng-container *ngFor="let customAction of getCustomAutomationActionsArray().controls; let i = index">
                  <div [formGroup]="customAction">
                    <mat-form-field>
                      <input
                        formControlName="label"
                        required
                        matInput
                        [placeholder]="'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.BUTTON_LABEL' | translate"
                        type="text"
                      />
                    </mat-form-field>
                    <ng-container *ngIf="!!customAction.get('automationId').value">
                      <app-automation-name-display
                        [automationId]="customAction.get('automationId').value"
                      ></app-automation-name-display>
                    </ng-container>
                    <ng-container *ngIf="!customAction.get('automationId').value">
                      <a (click)="chooseAutomation(customAction.get('automationId'))">
                        {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.CHOOSE_AUTOMATION' | translate }}
                      </a>
                    </ng-container>
                    <button
                      class="remove-custom-action"
                      (click)="removeCustomAutomationAction(i)"
                      mat-icon-button
                      matTooltipPosition="right"
                      matTooltip="Remove Custom Action"
                    >
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>
                </ng-container>
                <ng-container *ngIf="getCustomAutomationActionsArray().controls?.length < 5">
                  <a (click)="addCustomAutomationAction()" class="add-custom-field">
                    {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.ADD_AUTOMATION_ACTION' | translate }}
                  </a>
                </ng-container>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
        <mat-card appearance="outlined">
          <mat-card-header>
            <mat-card-title>
              {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.ADMIN_SUBMIT_ACTIONS' | translate }}
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div formGroupName="workflowStepConfig">
              <div class="steps">
                <mat-checkbox formControlName="allowAdminSkipApprovalWorkflow">
                  {{
                    'CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.ALLOW_ADMIN_SKIP_APPROVAL_WORKFLOW'
                      | translate
                  }}
                </mat-checkbox>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
        <mat-card appearance="outlined">
          <mat-card-header>
            <mat-card-title>
              {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.TITLE' | translate }}
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <mat-form-field class="default-notes">
              <mat-label>
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.CONTENT_LABEL' | translate }}
              </mat-label>
              <textarea
                matInput
                cdkTextareaAutosize
                #autosize="cdkTextareaAutosize"
                cdkAutosizeMinRows="1"
                cdkAutosizeMaxRows="15"
                formControlName="defaultNotesContent"
              ></textarea>
              <mat-hint>
                <span
                  class="hint"
                  matTooltipPosition="right"
                  matTooltip="{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.CONTENT_TOOLTIP_TEXT' | translate }}"
                >
                  {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.CONTENT_TOOLTIP_LABEL' | translate }}
                </span>
              </mat-hint>
            </mat-form-field>

            <div class="extra-fields" formArrayName="extraFields">
              <div class="custom-field-label">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.CUSTOM_FIELD_SECTION' | translate }}
              </div>
              <ng-container *ngIf="getExtraFieldsArray().controls.length > 0; else firstField">
                <mat-accordion [multi]="true" cdkDropList (cdkDropListDropped)="onDrop($event)">
                  <mat-expansion-panel
                    cdkDrag
                    *ngFor="let extraFieldGroup of getExtraFieldsArray().controls; let i = index"
                  >
                    <mat-expansion-panel-header class="grab-mask">
                      <mat-panel-title>
                        <mat-icon class="icon-container handle-icon">drag_handle</mat-icon>
                        <div *ngIf="extraFieldGroup.value.typeControl; else noFieldTypeYet">
                          <div class="title-text">
                            <span>
                              {{ extraFieldGroup.value.typeControl.name | translate }}
                            </span>
                            <span *ngIf="extraFieldGroup.value.labelControl">
                              - {{ extraFieldGroup.value.labelControl }}
                            </span>
                          </div>
                        </div>
                        <ng-template #noFieldTypeYet>
                          <div class="title-text">
                            <span>
                              {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.NEW_FIELD_TITLE' | translate }}
                            </span>
                          </div>
                        </ng-template>
                      </mat-panel-title>
                      <mat-panel-description></mat-panel-description>
                    </mat-expansion-panel-header>
                    <store-field-builder
                      class="color-primary order-form-field-form-container"
                      [orderFormFieldGroup]="extraFieldGroup"
                      [supportedFieldTypes]="supportedFiledTypes"
                      (removeField)="removeExtraFieldsFormField(i)"
                    ></store-field-builder>
                  </mat-expansion-panel>
                </mat-accordion>
              </ng-container>
              <a (click)="addExtraFieldsFormField()" class="add-custom-field">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.ADD_CUSTOM_FIELD' | translate }}
              </a>
            </div>
          </mat-card-content>
        </mat-card>
        <mat-card appearance="outlined">
          <mat-card-content>
            <mat-card-header>
              <mat-card-title>
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.TITLE' | translate }}
              </mat-card-title>
            </mat-card-header>
            <forms-file-group-uploader
              label="{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.CUSTOM_TOS' | translate }}"
              [uploadUrl]="uploadUrl"
              [formGroup]="configForm"
              [formControlName]="'termsOfServiceFormControl'"
              numFiles="1"
              (fileUploadError)="handleFileUploadError($event)"
            ></forms-file-group-uploader>
            <div formGroupName="customerTermsOfServiceOptions">
              <mat-checkbox formControlName="hideDefaultTermsOfService">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.HIDE_TOS' | translate }}
              </mat-checkbox>
              <div class="tos-list" formArrayName="termsOfService">
                <ng-container *ngIf="getTermsOfServiceArray().controls.length > 0; else noToS">
                  <div *ngFor="let tos of getTermsOfServiceArray().controls; let i = index" class="tos-card">
                    <mat-card appearance="outlined">
                      <mat-card-content>
                        <div [formGroupName]="i" class="terms-of-service">
                          <button
                            class="delete-button"
                            (click)="removeTermsOfServiceFormField(i)"
                            mat-icon-button
                            title="{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.DELETE_TOS' | translate }}"
                            matTooltipPosition="right"
                            matTooltip="{{
                              'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.DELETE_TOS' | translate
                            }}"
                          >
                            <mat-icon>close</mat-icon>
                          </button>
                          <mat-form-field>
                            <input formControlName="text" required matInput placeholder="Text" type="text" />
                          </mat-form-field>
                          <forms-file-group-uploader
                            label="{{
                              'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.TOS_FIELD_LABEL' | translate
                            }}"
                            [uploadUrl]="uploadUrl"
                            [formGroup]="tos"
                            [formControlName]="'termsOfServiceUrl'"
                            numFiles="1"
                            (fileUploadError)="handleFileUploadError($event)"
                          ></forms-file-group-uploader>
                          <mat-form-field>
                            <input
                              formControlName="linkTitle"
                              matInput
                              placeholder="{{
                                'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.LINK_TITLE' | translate
                              }}"
                              type="text"
                            />
                          </mat-form-field>
                          <mat-checkbox formControlName="required">
                            {{
                              'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.REQUIRE_CUSTOMER_TO_AGREE'
                                | translate
                            }}
                          </mat-checkbox>
                        </div>
                      </mat-card-content>
                    </mat-card>
                  </div>
                </ng-container>
              </div>
              <a (click)="addTermsOfServiceFormField()" class="add-custom-field">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.ADD_TERMS_OF_SERVICE' | translate }}
              </a>
            </div>
            <ng-template #noToS>
              <div class="no-tos">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.CUSTOMER_TOS_SETTINGS.NO_TOS_MESSAGE' | translate }}
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined">
          <mat-card-header>
            <mat-card-title>
              {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.TITLE' | translate }}
            </mat-card-title>
            <mat-card-subtitle>
              {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.SUBTITLE' | translate }}
            </mat-card-subtitle>
          </mat-card-header>
          <mat-card-content formGroupName="emailOptions">
            <mat-accordion>
              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.CONTRACT_AWAITING_APPROVAL' | translate }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{
                      'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.CONTRACT_AWAITING_APPROVAL_DESCRIPTION'
                        | translate
                    }}
                  </mat-panel-description>
                </mat-expansion-panel-header>
                <ng-template
                  [ngTemplateOutlet]="emailContentForm"
                  [ngTemplateOutletContext]="{
                    formGroup: configForm.get('emailOptions').get('contractAwaitingApproval'),
                  }"
                ></ng-template>
                <button
                  class="button"
                  mat-raised-button
                  color="primary"
                  (click)="previewEmail('contract-approve', 'contractAwaitingApproval')"
                >
                  {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.PREVIEW_EMAIL' | translate }}
                </button>
              </mat-expansion-panel>
              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.ORDER_PROCESSED' | translate }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.ORDER_PROCESSED_DESCRIPTION' | translate }}
                  </mat-panel-description>
                </mat-expansion-panel-header>
                <ng-template
                  [ngTemplateOutlet]="emailContentForm"
                  [ngTemplateOutletContext]="{
                    formGroup: configForm.get('emailOptions').get('orderProcessed'),
                  }"
                ></ng-template>
                <button
                  class="button"
                  mat-raised-button
                  color="primary"
                  (click)="previewEmail('order-submitted', 'orderProcessed')"
                >
                  {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.PREVIEW_EMAIL' | translate }}
                </button>
              </mat-expansion-panel>
              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.ORDER_DECLINED' | translate }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.ORDER_DECLINED_DESCRIPTION' | translate }}
                  </mat-panel-description>
                </mat-expansion-panel-header>
                <ng-template
                  [ngTemplateOutlet]="emailContentForm"
                  [ngTemplateOutletContext]="{
                    formGroup: configForm.get('emailOptions').get('orderDeclined'),
                  }"
                ></ng-template>
                <button
                  class="button"
                  mat-raised-button
                  color="primary"
                  (click)="previewEmail('order-declined', 'orderDeclined')"
                >
                  {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.PREVIEW_EMAIL' | translate }}
                </button>
              </mat-expansion-panel>
            </mat-accordion>
          </mat-card-content>
        </mat-card>
        <ng-template #emailContentForm let-formGroup="formGroup">
          <div class="email-content-form" [formGroup]="formGroup">
            <glxy-form-field>
              <glxy-label>{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.SUBJECT' | translate }}</glxy-label>
              <input
                formControlName="subject"
                matInput
                placeholder="{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.SUBJECT' | translate }}"
                type="text"
              />
            </glxy-form-field>
            <glxy-form-field>
              <glxy-label>{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.HEADING' | translate }}</glxy-label>
              <input
                formControlName="heading"
                matInput
                placeholder="{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.HEADING' | translate }}"
                type="text"
              />
            </glxy-form-field>
            <glxy-form-field>
              <glxy-label>
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.BODY' | translate }}
              </glxy-label>
              <textarea
                matInput
                cdkTextareaAutosize
                #autosize="cdkTextareaAutosize"
                cdkAutosizeMinRows="5"
                cdkAutosizeMaxRows="15"
                formControlName="body"
              ></textarea>
            </glxy-form-field>
            <glxy-form-field>
              <glxy-label>{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.BUTTON' | translate }}</glxy-label>
              <input
                formControlName="button"
                matInput
                placeholder="{{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.EMAIL_OPTIONS_SETTINGS.BUTTON' | translate }}"
                type="text"
              />
            </glxy-form-field>
          </div>
        </ng-template>
        <glxy-sticky-footer>
          <div class="save">
            <ng-container *ngIf="!saving">
              <ng-container *ngIf="config$ | async as config">
                <button class="button" mat-raised-button *ngIf="config.marketId" (click)="restore()">
                  {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.BUTTONS.RESTORE_TO_DEFAULTS' | translate }}
                </button>
              </ng-container>
              <button class="button" mat-raised-button color="primary" (click)="submit()">
                {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.BUTTONS.SAVE' | translate }}
              </button>
            </ng-container>
          </div>
        </glxy-sticky-footer>
      </div>
    </div>

    <ng-template #loadingConfig>
      <div class="loading-config">
        <store-list-stencil [numRows]="7" [rowHeight]="'32px'"></store-list-stencil>
        <store-list-stencil [numRows]="2" [rowHeight]="'32px'"></store-list-stencil>
        <store-list-stencil [numRows]="1" [rowHeight]="'32px'"></store-list-stencil>
        <store-list-stencil [numRows]="1" [rowHeight]="'32px'"></store-list-stencil>
        <store-list-stencil [numRows]="2" [rowHeight]="'32px'"></store-list-stencil>
        <store-list-stencil [numRows]="3" [rowHeight]="'32px'"></store-list-stencil>
      </div>
    </ng-template>

    <ng-template #firstField>
      <div class="no-fields">
        {{ 'CUSTOMIZE_SALES_ORDERS_CONFIG.DEFAULT_CONTENT.NO_CUSTOM_FIELD_MESSAGE' | translate }}
      </div>
    </ng-template>
  </glxy-page-wrapper>
</glxy-page>
