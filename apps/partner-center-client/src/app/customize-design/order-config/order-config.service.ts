import { Inject, Injectable, Optional } from '@angular/core';
import { Market } from '@vendasta/partner';
import { BehaviorSubject, Observable, of, shareReplay } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class OrderConfigService {
  orderConfigMarketId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  orderConfigMarketId$: Observable<string> = this.orderConfigMarketId$$.asObservable().pipe(shareReplay(1));

  constructor(@Optional() @Inject('markets$') private markets$: Observable<Market[]> = of([])) {}

  getMarkets(): Observable<Market[]> {
    return this.markets$;
  }

  updateMarket(updatedMarketId: string): void {
    this.orderConfigMarketId$$.next(updatedMarketId);
  }
}
