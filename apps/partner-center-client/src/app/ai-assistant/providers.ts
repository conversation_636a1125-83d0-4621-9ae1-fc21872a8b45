import { inject, InjectionToken } from '@angular/core';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { PartnerModelApiService, PartnerService, WhitelabelService } from '@galaxy/partner';
import { combineLatest, Observable, of } from 'rxjs';
import { distinctUntilChanged, filter, map, shareReplay, switchMap } from 'rxjs/operators';
import { AiAssistantConfig, AiConnection, ConnectionType, NamespaceConfig } from '@galaxy/ai-assistant';
import {
  AI_DEFAULT_PARTNERS_ASSISTANT_CONNECTIONS,
  AI_DEFAULT_PARTNERS_ASSISTANT_WORKFORCE,
  NEW_WIDGET_CONFIGURATION_URL,
} from './ai-assistant-constants';
import { FeatureFlagService } from '@galaxy/partner';
import { Connection, Namespace } from '@vendasta/ai-assistants';
import { AppConfigService } from '../app-config.service';

export const AI_ASSISTANTS_CONFIG = new InjectionToken<AiAssistantConfig>(
  '[Partner Center Client]: Token for AI assistants config',
  {
    providedIn: 'root',
    factory: function (): AiAssistantConfig {
      const partnerService = inject(PartnerService);
      const partnerModelService = inject(PartnerModelApiService);
      const whitelabelService = inject(WhitelabelService);
      const appConfigService = inject(AppConfigService);
      const featureFlagService = inject(FeatureFlagService);

      const accountGroupId$ = of('');
      const marketId$ = of('');
      const partnerId$ = partnerService.getPartnerId().pipe(
        map((partnerId) => partnerId),
        filter<string>(Boolean),
        distinctUntilChanged(),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const partnerModel$ = partnerId$.pipe(
        distinctUntilChanged(),
        switchMap((partnerId) => partnerModelService.getPartnerModel({ partnerId: partnerId })),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const wlConfig$ = partnerId$.pipe(
        switchMap((partnerId) => whitelabelService.getConfiguration(partnerId)),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const phoneSupportingRegion$ = partnerModel$.pipe(
        distinctUntilChanged(),
        map((partner) => ['US', 'CA'].includes(partner.partner.countryCode)),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const voiceAIFeatureFlagEnabled$ = combineLatest([partnerId$, marketId$]).pipe(
        distinctUntilChanged(),
        switchMap(([partnerId, marketId]) =>
          featureFlagService.batchGetStatus(partnerId, marketId, ['partner_voice_receptionist']),
        ),
        map((status) => status['partner_voice_receptionist']),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const voiceAIAvailable$ = combineLatest([voiceAIFeatureFlagEnabled$, phoneSupportingRegion$]).pipe(
        distinctUntilChanged(),
        map(([ffEnabled, isPhoneSupported]) => ffEnabled && isPhoneSupported),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const defaultAIWorkforce$ = of(AI_DEFAULT_PARTNERS_ASSISTANT_WORKFORCE);

      const namespaceConfig$: Observable<NamespaceConfig> = partnerId$.pipe(
        filter(Boolean),
        map((partnerId) => {
          return {
            namespace: new Namespace({
              partnerNamespace: {
                partnerId,
              },
            }),
            root: `/ai`,
          };
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const defaultConnections$ = combineLatest([partnerId$, wlConfig$]).pipe(
        map(([partnerId, config]) => {
          const webchatEnabled = config.enabledFeatures.includes('inbox-ai-webchat');
          return buildAIDefaultConnections(partnerId, webchatEnabled);
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const currentUserId$ = appConfigService.config$.pipe(
        map((config) => config.unifiedUserId),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      return {
        accountGroupId$,
        marketId$,
        partnerId$,
        defaultAIWorkforce$,
        namespaceConfig$: namespaceConfig$,
        defaultConnections$,
        currentUserId$,
        voiceAIAvailable$,
      };
    },
  },
);

function buildAIDefaultConnections(partnerId: string, webchatEnabled: boolean): AiConnection[] {
  const defaultConnections = AI_DEFAULT_PARTNERS_ASSISTANT_CONNECTIONS.map((aiConnection) => ({
    ...aiConnection,
    connection: new Connection({
      ...aiConnection.connection,
      namespace: new Namespace({
        partnerNamespace: {
          partnerId: partnerId,
        },
      }),
    }),
  }));

  const aiDefaultConnections: AiConnection[] = [];
  defaultConnections.forEach((defaultConnection) => {
    if (defaultConnection.connection.connectionType === ConnectionType.WebchatWidget && webchatEnabled) {
      const newAiConnection = structuredClone(defaultConnection);
      newAiConnection.cta.action.url = NEW_WIDGET_CONFIGURATION_URL;
      aiDefaultConnections.push(newAiConnection);
    } else {
      aiDefaultConnections.push(defaultConnection);
    }
  });

  return aiDefaultConnections;
}
