import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ProductStatsWithId } from '@vendasta/store';
import { DisplayValueDescriptor } from '@vendasta/uikit';
import { combineLatest, merge, Observable, Subscription } from 'rxjs';
import { debounceTime, map, publishReplay, refCount, switchMap, tap } from 'rxjs/operators';
import { ActivationStatsService } from '../../activation-stats/activation-stats.service';
import { AppEditionService } from '../../activation/core/app-edition.service';
import { ProductEditionMap } from '../../activation/core/interface';
import { PartnerService } from '../../core/partner.service';
import { BILLABLE_ACCOUNT_CONFIG, BILLABLE_ITEMS_CHART_NAME, TOP_PRODUCT_CARD_CONFIG } from '../constants';
import { SaasMetricsFilterService } from '../saas-metrics-filter/saas-metrics-filter.service';
import { ColorPool, createColorPool, getLastEntryInSeries } from '../saas-metrics.helper';
import { BillableCount, ChartColor, ChartEntry, ChartSeries, ProductUIItem } from '../saas-metrics.interface';
import { SaasMetricsService } from '../saas-metrics.service';
import { SaasMetricsStubService } from '../saas-metrics.stub-service';
import { resizeWindow, WINDOW_RESIZE_DEBOUNCE_TIME_MS } from '../utils';

export interface TopProductChartEntry extends ChartEntry {
  id: string;
}

function mapBillCountToChartEntry(item: BillableCount): ChartEntry {
  return {
    name: new Date(item.date),
    value: item.billableAccounts,
  } as ChartEntry;
}

@Component({
  templateUrl: './billed-products.component.html',
  styleUrls: ['../saas-metrics.component.scss', './billed-products.component.scss'],
  providers: [SaasMetricsStubService, PartnerService, ActivationStatsService],
  standalone: false,
})
export class BilledProductsComponent implements OnInit, OnDestroy {
  constructor(
    private saasMetricsService: SaasMetricsService,
    private partnerService: PartnerService,
    private activationStatsService: ActivationStatsService,
    private readonly saasMetricsFilterService: SaasMetricsFilterService,
    private appEditionService: AppEditionService,
  ) {}

  colorPool: ColorPool = createColorPool();

  topProductColors: ChartColor[] = [];

  topProductCard = {
    ...TOP_PRODUCT_CARD_CONFIG,
    chartColors: this.topProductColors,
  };

  topProductData$: Observable<TopProductChartEntry[]>;
  // Number one top selling product
  topProduct$: Observable<ProductUIItem>;
  billableAccountsData$: Observable<ChartSeries[]>;
  billableAccountCurrentValue$: Observable<DisplayValueDescriptor>;
  readonly billableAccountCardConfig = BILLABLE_ACCOUNT_CONFIG;
  formatXAxisScale$: Observable<(val: Date) => string>;

  subscription: Subscription = new Subscription();

  /**
   * Dispatches a window resize event
   */
  private triggerWindowResize(): void {
    window.dispatchEvent(new Event('resize'));
  }

  ngOnInit(): void {
    const filters$ = this.saasMetricsFilterService.getFilters();
    this.formatXAxisScale$ = this.saasMetricsFilterService.getFormatFunction();

    const partnerProducts$ = filters$.pipe(
      switchMap((filters) => {
        return this.activationStatsService.getPurchaseStatsForPartner(
          this.partnerService.partnerId,
          filters.dateFilter.start,
          filters.dateFilter.end,
        );
      }),
      publishReplay(1),
      refCount(),
    );

    const productUIItems$ = partnerProducts$.pipe(
      switchMap((productStats) => {
        const productIds = Array.from(productStats.values()).map((ps) => ps.appId);
        return this.saasMetricsService.getProductUIItems(productIds);
      }),
      publishReplay(1),
      refCount(),
    );

    const productEditions$ = partnerProducts$.pipe(
      switchMap((productStats) => {
        const productIds = Array.from(productStats.values()).map((ps) => ps.appId);
        return this.appEditionService.getEditionsForApps(productIds);
      }),
    );

    this.topProductData$ = combineLatest([partnerProducts$, productUIItems$, productEditions$]).pipe(
      map(([products, productUIItems, productEditions]) =>
        this.buildTopProducts(products, productUIItems, productEditions),
      ),
      map((productEntries) => productEntries.sort(compareProductEntries)),
      map((sortedStats) => sortedStats.slice(0, 10)),
      tap((stats) => {
        stats.map((stat) => this.colorPool.addColorEntry(stat.name as string, this.topProductColors));
      }),
    );

    this.topProduct$ = combineLatest([this.topProductData$, productUIItems$]).pipe(
      map(([topProducts, productUIItems]) => {
        if (!topProducts.length) {
          return;
        }

        const { name, value, id } = topProducts[0];
        const ui = productUIItems.get(id);
        return {
          value,
          icon: ui.icon,
          name: name as string,
        };
      }),
    );

    /****** [START] BILLABLE ACCOUNTS  ******/
    this.billableAccountsData$ = filters$.pipe(
      switchMap((filters) => {
        return this.saasMetricsService.getActiveBillableItemsForPartner(filters);
      }),
      map((billableCounts) => {
        const chartData = billableCounts.map(mapBillCountToChartEntry);
        return [
          {
            name: BILLABLE_ITEMS_CHART_NAME,
            series: chartData,
          } as ChartSeries,
        ];
      }),
      publishReplay(1),
      refCount(),
    );

    this.billableAccountCurrentValue$ = this.billableAccountsData$.pipe(
      map(getLastEntryInSeries),
      map((entry) => {
        return {
          value: entry.value,
          displayType: 'number',
        } as DisplayValueDescriptor;
      }),
      tap(this.triggerWindowResize),
    );

    /****** [END] BILLABLE ACCOUNTS  ******/

    const resizeSub = merge(this.billableAccountCurrentValue$, this.topProduct$)
      .pipe(debounceTime(WINDOW_RESIZE_DEBOUNCE_TIME_MS))
      .subscribe(resizeWindow);

    this.subscription.add(resizeSub);
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  public buildTopProducts(
    products: Map<string, ProductStatsWithId>,
    productUIItems: Map<string, ProductUIItem>,
    productEditions: ProductEditionMap,
  ): TopProductChartEntry[] {
    const topProducts: TopProductChartEntry[] = [];

    products.forEach((activeStats) => {
      const uiItem = productUIItems.get(activeStats.appId);
      if (!uiItem) {
        return;
      }

      let productName = uiItem.name;
      if (productEditions.has(activeStats.appId)) {
        const editions = productEditions.get(activeStats.appId);
        const edition = editions.filter(
          (ed) => ed.editionId === (activeStats.editionId === 'default' ? '' : activeStats.editionId),
        );
        if (edition && edition.length) {
          productName += ' | ' + edition[0].editionName;
        }
      }

      topProducts.push({
        name: productName,
        value: activeStats.activations,
        id: activeStats.appId,
      } as TopProductChartEntry);
    });
    return topProducts;
  }
}

/**
 * Predicate for sorting chart entries by value.
 * In the case of a tie, sort by alphabetical ordering.
 * @param a - First item to compare
 * @param b - Second item to compare
 * @returns - Number used by array sort.
 */
export function compareProductEntries(a: ChartEntry, b: ChartEntry): number {
  let diff = b.value - a.value;

  if (!diff) {
    diff = (a.name as string).localeCompare(b.name as string);
  }

  return diff;
}
