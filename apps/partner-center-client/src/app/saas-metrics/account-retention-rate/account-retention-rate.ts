import { Component, OnDestroy, OnInit } from '@angular/core';
import { DisplayValueDescriptor } from '@vendasta/uikit';
import { combineLatest, merge, Observable, Subscription } from 'rxjs';
import { debounceTime, map, share, switchMap, take, tap } from 'rxjs/operators';
import { ApiService } from '../../api-service/api.service';
import { TaxonomyService } from '../../core/taxonomy.service';
import { AccountOverview } from '../../dashboard/account-overview';
import {
  ACCOUNT_RETENTION_BY_TAXONOMY_BOTTOM6_CHART_CONFIG,
  ACCOUNT_RETENTION_BY_TAXONOMY_TOP6_CHART_CONFIG,
  BILLABLE_ACCOUNTS_CARD_CONFIG,
} from '../constants';
import { SaasMetricsFilterService } from '../saas-metrics-filter/saas-metrics-filter.service';
import {
  ColorPool,
  createColorPool,
  formatDateAsXAxisShortDateLabel,
  formatNumberAsPercentage,
} from '../saas-metrics.helper';
import { ChartColor, ChartEntry, ChartSeries, RetentionData } from '../saas-metrics.interface';
import { reduceChartEntryToNEntries, SaasMetricsService } from '../saas-metrics.service';
import { resizeWindow, WINDOW_RESIZE_DEBOUNCE_TIME_MS } from '../utils';
import { CHURNED_ACCOUNTS, CONFIG, EXISTING_ACCOUNTS, NEW_ACCOUNTS } from './constants';

@Component({
  templateUrl: './account-retention-rate.html',
  styleUrls: ['./account-retention-rate.scss', '../saas-metrics.component.scss'],
  standalone: false,
})
export class AccountRetentionRateComponent implements OnInit, OnDestroy {
  formatDateAsXAxisShortDateLabel = formatDateAsXAxisShortDateLabel;
  formatNumberAsPercentage = formatNumberAsPercentage;
  math = Math;

  billableAccounts = {
    ...BILLABLE_ACCOUNTS_CARD_CONFIG,
  };

  accountRetentionByTaxonomyCardTop6 = {
    ...ACCOUNT_RETENTION_BY_TAXONOMY_TOP6_CHART_CONFIG,
  };

  accountRetentionByTaxonomyCardBottom6 = {
    ...ACCOUNT_RETENTION_BY_TAXONOMY_BOTTOM6_CHART_CONFIG,
  };

  config = {
    ...CONFIG,
  };
  retentionBreakdownData$: Observable<ChartSeries[]>;
  currentTotalRetention$: Observable<DisplayValueDescriptor>;

  accountRetentionByTaxonomyBottom6$: Observable<ChartEntry[]>;
  accountRetentionByTaxonomyTop6$: Observable<ChartEntry[]>;

  subscription: Subscription = new Subscription();

  billableAccounts$: Observable<number>;
  numAccounts$: Observable<string>;

  constructor(
    private saasMetricsService: SaasMetricsService,
    private readonly saasMetricsFilterService: SaasMetricsFilterService,
    private taxonomyService: TaxonomyService,
    private apiService: ApiService,
  ) {}

  colorPool: ColorPool = createColorPool();
  retentionTaxonomyColorsTop: ChartColor[] = [];
  retentionTaxonomyColorsBottom: ChartColor[] = [];

  ngOnInit(): void {
    const filters$ = this.saasMetricsFilterService.getFilters();

    const accountRetentionByCategoryData$ = filters$.pipe(
      switchMap((filters) => {
        return combineLatest([
          //Get the start dates retention
          this.getAccountRetentionDataForDate(filters.dateFilter.start, filters.dateFilter.start),
          //Get the end dates retention
          this.getAccountRetentionDataForDate(filters.dateFilter.end, filters.dateFilter.start),
        ]);
      }),
    );

    const accountRetentionByTaxonomyData$: Observable<ChartEntry[]> = accountRetentionByCategoryData$.pipe(
      map(([start, end]) => {
        end.forEach(function (value: number, key: string): void {
          value = value * 100;
          end.set(key, value);
        });
        // Add all of the values to the end map
        start.forEach(function (value: number, key: string): void {
          const curr = end.get(key);
          if (!curr) {
            end.set(key, 0);
            return;
          }

          value = parseFloat((curr / value).toFixed(2));

          end.set(key, value);
        });

        const arr: ChartEntry[] = [];
        end.forEach((val, key) => {
          arr.push({
            name: key,
            value: val,
          } as ChartEntry);
        });

        return arr;
      }),
    );

    this.accountRetentionByTaxonomyTop6$ = accountRetentionByTaxonomyData$.pipe(
      map((entries) => {
        return reduceChartEntryToNEntries(entries, true, 6);
      }),
      tap((topSix) => {
        topSix.map((entry) => {
          this.colorPool.addColorEntry(entry.name as string, this.retentionTaxonomyColorsTop);
        });
      }),
    );

    this.accountRetentionByTaxonomyBottom6$ = accountRetentionByTaxonomyData$.pipe(
      map((entries) => {
        return reduceChartEntryToNEntries(entries, false, 6);
      }),
      tap((bottomSix) => {
        bottomSix.map((entry) => {
          this.colorPool.addColorEntry(entry.name as string, this.retentionTaxonomyColorsBottom);
        });
      }),
    );

    const retentionRateData$ = filters$.pipe(
      switchMap((filters) => this.saasMetricsService.getSMBRetentionForPartner(filters)),
    );

    this.retentionBreakdownData$ = retentionRateData$.pipe(
      map((retentionData) => mapBreakdownComponentsToChartEntries(retentionData)),
    );

    this.currentTotalRetention$ = retentionRateData$.pipe(
      map((retentionData) => {
        let retentionRate = 0;
        if (retentionData.length) {
          retentionRate = retentionData[retentionData.length - 1].rate;
          retentionRate = Math.trunc(retentionRate * 100) / 100;
        }

        return {
          value: retentionRate,
          displayType: 'percentage',
        } as DisplayValueDescriptor;
      }),
    );

    this.billableAccounts$ = retentionRateData$.pipe(
      map((retentionData) => {
        if (!retentionData.length) {
          return 0;
        }
        return retentionData[retentionData.length - 1].activations_including_one_time;
      }),
    );

    this.numAccounts$ = this.getAccountOverview().pipe(
      map((accountOverview: AccountOverview) => accountOverview.numAccounts.toString() || ''),
    );

    //deferred resize.
    this.subscription.add(
      merge(this.retentionBreakdownData$, this.accountRetentionByTaxonomyTop6$, this.accountRetentionByTaxonomyBottom6$)
        .pipe(debounceTime(WINDOW_RESIZE_DEBOUNCE_TIME_MS))
        .subscribe(resizeWindow),
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  getAccountRetentionDataForDate(date: Date, created: Date): Observable<Map<string, number>> {
    const accountRetentionCountForDate$ = this.saasMetricsService.getAccountRetentionCountForDate(date, created);
    const taxonomyNameMap$ = accountRetentionCountForDate$.pipe(
      map((accountRetentionCount) => accountRetentionCount.map((resp) => resp.taxonomy)),
      switchMap((taxonomyIds) => this.taxonomyService.getTaxonomyMapForIds(taxonomyIds)),
    );

    return combineLatest([accountRetentionCountForDate$, taxonomyNameMap$]).pipe(
      take(1),
      map(([accountRetentionCount, taxonomyNameMap]) => {
        const accountRetentionData = new Map<string, number>();
        accountRetentionCount.map((resp) => {
          const taxonomyName = taxonomyNameMap.get(resp.taxonomy);
          accountRetentionData.set(taxonomyName, resp.count + (accountRetentionData.get(taxonomyName) || 0));
        });
        return accountRetentionData;
      }),
    );
  }

  getAccountOverview(): Observable<AccountOverview> {
    const url = `/_ajax/v1/account/stats/overview`;
    return this.apiService.get(url).pipe(
      map((result) => new AccountOverview(result)),
      share(),
    );
  }
}

/**
 * Converts a RetentionData into an array of ChartSeries for plotting.
 * @param {RetentionData} retentionData - The retention data to convert.
 * @returns {ChartSeries[]}
 */
function mapBreakdownComponentsToChartEntries(retentionData: RetentionData[]): ChartSeries[] {
  const breakdownChartSeries: ChartSeries[] = [];
  retentionData.map((item, idx, arr) => {
    const dateTime = new Date(item.date);
    dateTime.setFullYear(dateTime.getUTCFullYear(), dateTime.getUTCMonth(), dateTime.getUTCDate()); //need to remain in UTC.

    breakdownChartSeries.push({
      name: dateTime.toLocaleDateString(),
      series: [
        {
          name: CHURNED_ACCOUNTS,
          value: idx > 0 ? (arr[idx - 1].activations - item.retained_activations) * -1 : 0,
        },
        {
          name: NEW_ACCOUNTS,
          value: item.activations - item.retained_activations,
        },
        {
          name: EXISTING_ACCOUNTS,
          value: item.retained_activations,
        },
      ],
    });
  });
  return breakdownChartSeries;
}
