import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { resizeWindow, WINDOW_RESIZE_DEBOUNCE_TIME_MS } from '../utils';

@Component({
  selector: 'app-estimated-revenue',
  templateUrl: './estimated-revenue.component.html',
  styleUrls: ['./estimated-revenue.component.scss', '../saas-metrics.component.scss'],
  standalone: false,
})
export class EstimatedRevenueComponent implements OnInit, OnDestroy {
  debouncedResize$$ = new Subject();

  ngOnInit(): void {
    this.debouncedResize$$.pipe(debounceTime(WINDOW_RESIZE_DEBOUNCE_TIME_MS)).subscribe(resizeWindow);
  }

  ngOnDestroy(): void {
    this.debouncedResize$$.complete();
  }

  debouncedResize(): void {
    this.debouncedResize$$.next(null);
  }
}
