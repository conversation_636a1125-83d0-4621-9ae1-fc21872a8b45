import { Component, Inject, OnInit } from '@angular/core';
import { DisplayValueDescriptor } from '@vendasta/uikit';
import { Observable, combineLatest, of } from 'rxjs';
import { map, publishReplay, refCount, switchMap, tap, withLatestFrom } from 'rxjs/operators';
import { AppEditionService } from '../../activation/core/app-edition.service';
import { ProductEditionMap } from '../../activation/core/interface';
import { MONTHLY_RECURRING_REVENUE_CONFIG, TOP_ARPPA_CONTRIBUTORS_CONFIG, TOTAL_ACCOUNTS_CONFIG } from '../constants';
import { Currency, CurrencyFormatter } from '../currency.service';
import { SaasMetricsFilterService } from '../saas-metrics-filter/saas-metrics-filter.service';
import { COLORS } from '../saas-metrics.helper';
import {
  AverageRevenueAndCountByProduct,
  BubbleChartEntry,
  ChartColor,
  ChartSeries,
  EstimatedRevenueAndArppaForPartner,
  MonthlyRecurringRevenueAndTotalAccounts,
  ProductUIItem,
} from '../saas-metrics.interface';
import { SaasMetricsService } from '../saas-metrics.service';
import { resizeWindow } from '../utils';

const TOP_ARPPA_CONTRIBUTORS_COLORS = [
  COLORS.green,
  COLORS.yellow,
  COLORS.orange,
  COLORS.pink,
  COLORS.lilac,
  COLORS.purple,
];
const NUM_TOP_ARPPA_CONTRIBUTORS = 6;

@Component({
  selector: 'app-arppa',
  templateUrl: './arppa.component.html',
  styleUrls: ['../saas-metrics.component.scss', './arppa.component.scss'],
  standalone: false,
})
export class ArppaComponent implements OnInit {
  constructor(
    private saasMetricsService: SaasMetricsService,
    private saasMetricsFilterService: SaasMetricsFilterService,
    private appEditionService: AppEditionService,
    @Inject('CURRENCY') public currency$: Observable<Currency>,
    @Inject('kFormat') public axisTicks: Observable<CurrencyFormatter>,
  ) {}

  monthlyRecurringRevenueAndTotalAccountsData$: Observable<MonthlyRecurringRevenueAndTotalAccounts>;
  revenueDisplay$: Observable<DisplayValueDescriptor>;
  totalAccountsDisplay$: Observable<DisplayValueDescriptor>;

  topContributorsData$: Observable<ChartSeries[]>;
  topContributor$: Observable<ProductUIItem> = of(null);

  monthlyRecurringRevenueCard = MONTHLY_RECURRING_REVENUE_CONFIG;
  totalAccountsCard = TOTAL_ACCOUNTS_CONFIG;
  topContributorsCard = TOP_ARPPA_CONTRIBUTORS_CONFIG;
  topContributorsColors: ChartColor[] = [];

  ngOnInit(): void {
    const filters$ = this.saasMetricsFilterService.getFilters();

    this.monthlyRecurringRevenueAndTotalAccountsData$ = filters$.pipe(
      switchMap((filters) => {
        return this.saasMetricsService.getEstimatedRevenueAndArppaForPartner(filters).pipe(
          map((revenueAndArppa) => {
            return mapEstimatedRevenueAndArppaForPartner(revenueAndArppa, filters.dateFilter.end);
          }),
        );
      }),
      publishReplay(1),
      refCount(),
    );

    //TODO: handle currencies other than USD
    this.revenueDisplay$ = this.monthlyRecurringRevenueAndTotalAccountsData$.pipe(
      withLatestFrom(this.currency$),
      map(([entry, currency]) => {
        return {
          value: entry.revenue,
          displayType: 'currency',
          currencyCode: currency.sign,
        } as DisplayValueDescriptor;
      }),
    );

    this.totalAccountsDisplay$ = this.monthlyRecurringRevenueAndTotalAccountsData$.pipe(
      map((revenueAndArppa) => {
        return {
          value: revenueAndArppa.total,
          displayType: 'number',
        } as DisplayValueDescriptor;
      }),
    );

    const dataPoints$ = filters$.pipe(
      switchMap((filters) => this.saasMetricsService.getTopARPPAContributors(filters)),
      map((data) => data.slice(0, NUM_TOP_ARPPA_CONTRIBUTORS)),
    );

    const productInfo$ = dataPoints$.pipe(
      switchMap((data) => {
        const ids = data.map((el) => el.productId);
        return this.saasMetricsService.getProductUIItems(ids);
      }),
    );

    const productEditions$ = dataPoints$.pipe(
      switchMap((data) => {
        const productIds = data.map((dt) => dt.productId);
        return this.appEditionService.getEditionsForApps(productIds);
      }),
    );

    this.topContributor$ = combineLatest([dataPoints$, productInfo$, productEditions$]).pipe(
      map(([data, products, productEditions]) => {
        return buildProductUIItemFromDataPoint(data[0], products, productEditions);
      }),
      tap(resizeWindow),
    );

    this.topContributorsData$ = combineLatest([dataPoints$, productInfo$, productEditions$, this.currency$]).pipe(
      map(([data, products, productEditions, currency]) => {
        data.forEach((ar) => {
          let productsName = ar.productId;
          if (products.has(ar.productId)) {
            productsName = products.get(ar.productId).name;
          }

          if (productEditions.has(ar.productId)) {
            const editions = productEditions.get(ar.productId);
            const edition = editions.filter((ed) => ed.editionId === (ar.editionId === 'default' ? '' : ar.editionId));
            if (edition && edition.length) {
              productsName += ' | ' + edition[0].editionName;
            }
          }
          ar.productId = productsName;
        });
        return buildTopARPPAContributorsChartSeries(currency, data);
      }),
      tap((entries) => this.setColorsForTopProducts(entries)),
      tap(resizeWindow),
    );
  }

  /**
   * Maps the current list of top products to matching colors, so the chart
   * bars remain in the correct color scheme.
   * @param entries - Top product entries
   */
  private setColorsForTopProducts(entries: ChartSeries[]): void {
    this.topContributorsColors.length = 0;
    this.topContributorsColors.push(
      ...entries.map((entry, index) => {
        return {
          name: entry.name,
          value: TOP_ARPPA_CONTRIBUTORS_COLORS[index],
        } as ChartColor;
      }),
    );
  }
}

/**
 * mapEstimatedRevenueAndArppaForPartner extracts the previous month's total accounts and revenue from the data returned by MLA
 *
 * @param {EstimatedRevenueAndArppaForPartner[]} items: array of date and estimated revenue and total accounts
 * @param {Date} currentDate: the current date
 * @returns {MonthlyRecurringRevenueAndTotalAccounts}
 */
function mapEstimatedRevenueAndArppaForPartner(
  items: EstimatedRevenueAndArppaForPartner[],
  _currentDate: Date,
): MonthlyRecurringRevenueAndTotalAccounts {
  if (!items.length) {
    return { revenue: 0, total: 0 };
  }

  const lastMonthItem = items.slice(-1)[0];

  const revenue = lastMonthItem && lastMonthItem.estimatedRevenue ? lastMonthItem.estimatedRevenue.estimatedRevenue : 0;
  const total = lastMonthItem && lastMonthItem.totalAccounts ? lastMonthItem.totalAccounts.total : 0;
  return {
    revenue,
    total,
  };
}

/**
 * Combines the top 6 products by revenue with the number of activations to build the top ARPPA contributors chart
 * @param topProducts - List of the top products by revenue
 * @param activations - The number of activations of the top 6 products
 */
export function buildTopARPPAContributorsChartSeries(
  currency: Currency,
  topProducts: AverageRevenueAndCountByProduct[],
): ChartSeries[] {
  return topProducts.map((el) => {
    return {
      name: el.productId,
      series: [
        {
          name: 'ARPPA',
          x: el.count,
          y: el.averageRevenue,
          r: 80,
        } as BubbleChartEntry,
      ],
    } as ChartSeries;
  });
}

/**
 * Sorts the top ARPPA contributors data points by average revenue in descending order
 * @param data - the data points
 */
export function sortTopContributorsDataPoints(data: ChartSeries[]): ChartSeries[] {
  if (!data || !data.length) {
    return null;
  }
  return data.sort((a, b) => {
    const aSeries = a.series[0] as BubbleChartEntry;
    const bSeries = b.series[0] as BubbleChartEntry;
    return bSeries.y - aSeries.y;
  });
}

/**
 * Builds the Product UI Item from a single data point in the chart
 * @param data - the data point
 * @param products - a map of product ID to product info
 * @param productEditions - a map of product ID to product edition
 */
export function buildProductUIItemFromDataPoint(
  data: AverageRevenueAndCountByProduct,
  products: Map<string, ProductUIItem>,
  productEditions: ProductEditionMap,
): ProductUIItem {
  let productName = data.productId;
  let productIcon = '';
  if (products.has(data.productId)) {
    const product = products.get(data.productId);
    productName = product.name;
    productIcon = product.icon;
  }

  if (productEditions.has(data.productId)) {
    const editions = productEditions.get(data.productId);
    const edition = editions.filter((ed) => ed.editionId === (data.editionId === 'default' ? '' : data.editionId));
    if (edition && edition.length) {
      productName += ' | ' + edition[0].editionName;
    }
  }

  return {
    name: productName,
    icon: productIcon,
    value: data.averageRevenue,
  } as ProductUIItem;
}
