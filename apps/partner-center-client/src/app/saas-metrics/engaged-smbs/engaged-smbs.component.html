<glxy-page>
  <glxy-page-toolbar [showExtendedToolbar]="true">
    <glxy-page-nav>
      <glxy-page-nav-button [previousPageUrl]="'/saas-metrics'"></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>
      {{ 'SAAS_METRICS.ENGAGED_ACCOUNTS_PAGE_TITLE' | translate }}
    </glxy-page-title>
    <glxy-page-extended-toolbar>
      <app-saas-metrics-filter></app-saas-metrics-filter>
    </glxy-page-extended-toolbar>
  </glxy-page-toolbar>

  <glxy-page-wrapper [maxWidth]="1400">
    <div class="row row-gutters">
      <!--SMB Engagement Breakdown-->
      <div class="col col-xs-12 col-sm-6">
        @if (smbEngagementBreakdownData$ | async; as smbData) {
          <app-saas-metrics-card
            [header]="engagedAccountsBreakdownChartConfig.header"
            [chartSubTitle]="engagedAccountsBreakdownChartConfig.subTitle"
          >
            <ngx-charts-line-chart
              [autoScale]="true"
              [results]="smbData"
              [showGridLines]="true"
              [xAxis]="true"
              [yAxis]="true"
              [animations]="false"
              [customColors]="engagedAccountsBreakdownChartConfig.chartColors"
              [xAxisTickFormatting]="formatXAxisScale$ | async"
              [yAxisTickFormatting]="formatYaxisScale"
              [curve]="curveStepAfter"
            ></ngx-charts-line-chart>
          </app-saas-metrics-card>
        } @else {
          <app-loading-state-card [header]="engagedAccountsBreakdownChartConfig.header"></app-loading-state-card>
        }
      </div>
      <!--SMB Product Engagement-->
      <div class="col col-xs-12 col-sm-6">
        @if (smbProductEngagementData$ | async; as data) {
          <app-saas-metrics-card
            [header]="productEngagementChartConfig.header"
            [chartSubTitle]="productEngagementChartConfig.subTitle"
            [currentPeriod$]="totalSMBProductEngagement$"
          >
            <ngx-charts-bar-horizontal
              class="force-hover-state"
              [results]="data"
              [legend]="false"
              [xAxis]="true"
              [xAxisTickFormatting]="formatDataAsPercent"
              [xScaleMax]="100"
              [roundEdges]="false"
              [yAxis]="true"
              [maxYAxisTickLength]="24"
              [trimYAxisTicks]="true"
              [xAxisTicks]="[0, 25, 50, 75, 100]"
              [animations]="false"
              [customColors]="productEngagementChartConfig.chartColors"
            >
              <ng-template #tooltipTemplate let-model="model">
                <div class="chart-layout-top chart-layout-bottom">
                  {{ model.name }}
                </div>
                <div class="chart-layout-bottom">{{ model.value + '%' }}</div>
              </ng-template>
            </ngx-charts-bar-horizontal>
          </app-saas-metrics-card>
        } @else {
          <app-loading-state-card [header]="productEngagementChartConfig.header"></app-loading-state-card>
        }
      </div>
    </div>
  </glxy-page-wrapper>
</glxy-page>
