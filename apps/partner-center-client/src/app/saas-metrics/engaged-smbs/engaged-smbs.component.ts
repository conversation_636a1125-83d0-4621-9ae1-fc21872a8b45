import { Component, OnDestroy, OnInit } from '@angular/core';
import { DisplayValueDescriptor } from '@vendasta/uikit';
import { curveStepAfter } from 'd3-shape';
import { combineLatest, merge, Observable, Subscription } from 'rxjs';
import { debounceTime, map, switchMap } from 'rxjs/operators';
import { ProductService } from '../../core/product.service';
import { ENGAGED_ACCOUNTS_BREAKDOWN_CHART_CONFIG, PRODUCT_ENGAGEMENT_CHART_CONFIG } from '../constants';
import { SaasMetricsFilterService } from '../saas-metrics-filter/saas-metrics-filter.service';
import {
  ColorPool,
  COLORS,
  createColorPool,
  formatYAxisWholeNumber,
  getLastEntryInSeries,
} from '../saas-metrics.helper';
import {
  ChartColor,
  ChartEntry,
  ChartSeries,
  SMBEngagementBreakdown,
  SMBProductCount,
} from '../saas-metrics.interface';
import { reduceChartEntryToNEntries, SaasMetricsService } from '../saas-metrics.service';
import { resizeWindow, WINDOW_RESIZE_DEBOUNCE_TIME_MS } from '../utils';

const SMB_PRODUCT_ENGAGEMENT_SERIES_NAME = 'Products';
const SMB_ENGAGEMENT_BREAKDOWN_SERIES_NAME = 'Business App';
const ENGAGED_BREAKDOWN_COLORS = [
  {
    name: SMB_PRODUCT_ENGAGEMENT_SERIES_NAME,
    value: COLORS.green,
  },
  {
    name: SMB_ENGAGEMENT_BREAKDOWN_SERIES_NAME,
    value: COLORS.yellow,
  },
];

@Component({
  templateUrl: './engaged-smbs.component.html',
  styleUrls: ['./engaged-smbs.component.scss', '../saas-metrics.component.scss'],
  standalone: false,
})
export class EngagedSmbsComponent implements OnInit, OnDestroy {
  private readonly colorPool: ColorPool = createColorPool();
  private smbProductEngagementColors: ChartColor[] = [];
  protected readonly curveStepAfter = curveStepAfter;

  protected smbEngagementBreakdownData$: Observable<ChartSeries[]>;
  protected readonly engagedAccountsBreakdownChartConfig = {
    ...ENGAGED_ACCOUNTS_BREAKDOWN_CHART_CONFIG,
    chartColors: ENGAGED_BREAKDOWN_COLORS,
  };

  protected totalSMBProductEngagement$: Observable<DisplayValueDescriptor>;
  protected smbProductEngagementData$: Observable<ChartEntry[]>;
  protected formatXAxisScale$: Observable<(val: Date) => string>;
  protected readonly productEngagementChartConfig = {
    ...PRODUCT_ENGAGEMENT_CHART_CONFIG,
    chartColors: this.smbProductEngagementColors,
  };

  private readonly subscription: Subscription = new Subscription();

  constructor(
    private readonly saasMetricsService: SaasMetricsService,
    private readonly saasMetricsFilterService: SaasMetricsFilterService,
    private readonly productService: ProductService,
  ) {}

  ngOnInit(): void {
    const filters$ = this.saasMetricsFilterService.getFilters();
    this.formatXAxisScale$ = this.saasMetricsFilterService.getFormatFunction();
    const smbEngagementByServiceProvider$ = filters$.pipe(
      switchMap((filters) => this.saasMetricsService.getSMBProductEngagementForPartner(filters)),
    );

    const productNames$ = smbEngagementByServiceProvider$.pipe(
      switchMap((smbEngagement) => {
        return this.productService.getProducts(smbEngagement.map((e) => e.product));
      }),
      map((products) => {
        return products.reduce((prev, curr) => {
          prev.set(curr.product_id, curr.name);
          return prev;
        }, new Map<string, string>());
      }),
    );

    const smbProductEngagementForPartner$ = combineLatest([smbEngagementByServiceProvider$, productNames$]).pipe(
      map(([smbEngagement, productNames]) => {
        const productEngagement = smbEngagement.map((e) => {
          return {
            engagedSmbs: e.engagedSmbs,
            product: productNames.has(e.product) ? productNames.get(e.product) : '',
          } as SMBProductCount;
        });
        return productEngagement.filter((pe) => pe.product);
      }),
    );

    const smbEngagementBreakdownForPartnerRolling30$ = filters$.pipe(
      switchMap((filters) => this.saasMetricsService.getSMBEngagementBreakdownForPartnerRolling30(filters)),
    );

    this.smbEngagementBreakdownData$ = smbEngagementBreakdownForPartnerRolling30$.pipe(
      map((smbEngagementBreakdown: SMBEngagementBreakdown[]) => {
        return smbEngagementBreakdown.map((smbCount) => {
          const chartData = smbCount.smbCount.map((b) => {
            return {
              name: b.date,
              value: b.engagedSmbs,
            };
          });
          return {
            name: smbCount.engagementType,
            series: chartData,
          };
        });
      }),
    );

    this.totalSMBProductEngagement$ = this.smbEngagementBreakdownData$.pipe(
      map((engagementBreakdown) => {
        const lastEntry = getLastEntryInSeries(engagementBreakdown);
        return {
          value: lastEntry.value,
          displayType: 'number',
        } as DisplayValueDescriptor;
      }),
    );

    this.smbProductEngagementData$ = combineLatest([
      smbProductEngagementForPartner$,
      this.totalSMBProductEngagement$,
    ]).pipe(
      map(([productEngagement, totalProductEngagement]) => {
        return productEngagement.map((b) => {
          this.colorPool.addColorEntry(b.product, this.smbProductEngagementColors);
          const engagementVal: number = totalProductEngagement.value as number;
          let engagePercent = 0;
          if (engagementVal) {
            engagePercent = Math.min(1, b.engagedSmbs / engagementVal) * 100;
          }

          return {
            name: b.product,
            value: parseFloat(engagePercent.toFixed(2)),
          };
        });
      }),
      map((chartEntries) => {
        // Sort and filter the data to the top 6
        return reduceChartEntryToNEntries(chartEntries, true, 6);
      }),
    );

    const resizeSub = merge(this.smbProductEngagementData$, this.smbEngagementBreakdownData$)
      .pipe(debounceTime(WINDOW_RESIZE_DEBOUNCE_TIME_MS))
      .subscribe(resizeWindow);

    this.subscription.add(resizeSub);
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  protected formatDataAsPercent(val: number): string {
    if (val <= 100) {
      return val + '%';
    }
    return '0%';
  }

  /**
   * If a value contains a decimal, return empty string
   */
  protected formatYaxisScale(value: number): string {
    return formatYAxisWholeNumber(value);
  }
}
