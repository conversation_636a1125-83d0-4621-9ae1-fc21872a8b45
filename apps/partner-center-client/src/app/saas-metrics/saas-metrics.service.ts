import { Inject, Injectable } from '@angular/core';
import { Attribute, Bucket, EmailEventBucket, EmailEventService, EventType, NotificationType } from '@vendasta/email';
import {
  Alignment,
  AlignmentPeriod,
  AlignmentPeriodCalendar,
  CompositeFilter,
  CompositeFilterOperator,
  CustomAlignerConfig,
  DateRange as SDKDateRange,
  FieldFilter,
  FieldFilterOperator,
  Filter,
  GroupBy,
  GroupByDimension,
  GroupByOperator,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  MetricResult,
  MultiLocationAnalyticsService,
  Order,
  OrderBy,
  OrderByOrderColumn,
  PropertyType,
  QueryMetricsRequest,
  ResourceId,
  TypedValue,
  UnaryFilter,
  UnaryFilterOperator,
} from '@vendasta/multi-location-analytics';
import { LocalRefreshingCacheService } from '@vendasta/uikit';
import moment from 'moment';
import { combineLatest, Observable, of } from 'rxjs';
import { map, publishReplay, refCount, withLatestFrom } from 'rxjs/operators';
import { extractMetricsFromResponse } from '../core/multi-location-analytics';
import { PartnerService } from '../core/partner.service';
import { ResellerItemService } from '../core/reseller/reseller-item.service';
import { Currency } from './currency.service';
import { SaasMetricsFilterData } from './saas-metrics-filter/saas-metrics-filter.service';
import {
  AccountRetentionCount,
  AverageRevenueAndCountByProduct,
  BasketSize,
  BillableCount,
  BusinessCount,
  ChartEntry,
  ChartSeries,
  DateRange,
  EstimatedRevenueAndArppaForPartner,
  EstimatedRevenueBreakdownForPartner,
  MarketRevenue,
  ProductRevenue,
  ProductUIItem,
  RetentionData,
  SalespersonEngagement,
  SMBCount,
  SMBEngagementBreakdown,
  SMBProductCount,
} from './saas-metrics.interface';
import {
  calculateChurnAndTotal,
  constructEstimatedRevenueAndArppa,
  constructEstimatedRevenueBreakdown,
  getUIInfoForProducts,
  offsetMonths,
  sortEntriesByDateKey,
  splitBreakdownsToArrays,
  toUTC,
} from './utils';

interface CustomAlignerParams {
  startDimension: string;
  endDimension: string;
  groupBy: string;
}

const MS_PER_DAY: number = 1000 * 60 * 60 * 24;
// Used for rounding a float to 4 decimal places by using Math.round(num * ROUNDING_FACTOR) / ROUNDING_FACTOR
const ROUNDING_FACTOR = 10000;

type metricPaddingStepSize = 'day' | 'month';

@Injectable({
  providedIn: 'root',
})
export class SaasMetricsService {
  constructor(
    private multiLocationAnalyticsService: MultiLocationAnalyticsService,
    private partnerService: PartnerService,
    private resellerItemService: ResellerItemService,
    private emailEventService: EmailEventService,
    private localRefreshingCacheService: LocalRefreshingCacheService,
    @Inject('CURRENCY') public currency$: Observable<Currency>,
  ) {}

  private attachFiltersToReq(
    req: QueryMetricsRequest,
    filters: SaasMetricsFilterData,
    customAlignerParams?: CustomAlignerParams,
  ): void {
    const { dateFilter } = filters;

    if (dateFilter) {
      req.dateRange = new SDKDateRange({
        start: dateFilter.start,
        end: dateFilter.end,
      });

      if (customAlignerParams) {
        req.alignment = Alignment.ALIGN_CUSTOM;

        req.alignmentPeriod = new AlignmentPeriod({
          calendar: AlignmentPeriodCalendar.CALENDAR_DAY,
        });

        req.groupBy = new GroupBy({
          dimension: [
            new GroupByDimension({
              dimension: customAlignerParams.groupBy,
            }),
          ],
          groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
        });

        req.customAligner = new CustomAlignerConfig({
          skipFilteringEntitiesToDateRange: true,
          alignmentBucketFilter: new Filter({
            compositeFilter: new CompositeFilter({
              op: CompositeFilterOperator.AND,
              filters: [
                new Filter({
                  fieldFilter: new FieldFilter({
                    dimension: customAlignerParams.startDimension,
                    operator: FieldFilterOperator.LESS_THAN_OR_EQUAL,
                    comparisonField: 'aligner__start_time',
                  }),
                }),
                new Filter({
                  compositeFilter: new CompositeFilter({
                    op: CompositeFilterOperator.OR,
                    filters: [
                      new Filter({
                        fieldFilter: new FieldFilter({
                          dimension: customAlignerParams.endDimension,
                          operator: FieldFilterOperator.GREATER_THAN,
                          comparisonField: 'aligner__start_time',
                        }),
                      }),
                      new Filter({
                        unaryFilter: new UnaryFilter({
                          dimension: customAlignerParams.endDimension,
                          op: UnaryFilterOperator.IS_NULL,
                        }),
                      }),
                    ],
                  }),
                }),
              ],
            }),
          }),
        });
      }
    }
  }

  private getBaseQuery(metricName: string, marketIds?: string[]): QueryMetricsRequest {
    const req = new QueryMetricsRequest({
      partnerId: this.partnerService.partnerId,
      metricName: metricName,
      resourceIds: [
        {
          marketId: {
            marketIds: marketIds,
          },
        },
      ],
    });
    return req;
  }

  getProductUIItems(ids: string[]): Observable<Map<string, ProductUIItem>> {
    return combineLatest([this.resellerItemService.getAddonData(), this.resellerItemService.getProductData()]).pipe(
      map(([addons, apps]) => getUIInfoForProducts(ids, addons, apps)),
    );
  }

  getSalespersonEngagementForPartner(filters: SaasMetricsFilterData): Observable<SalespersonEngagement[]> {
    const { dateFilter } = filters;
    if (!dateFilter) {
      return of([]);
    }

    const rollingDateFilter = createRollingAverageDateRange(dateFilter);
    if (!rollingDateFilter) {
      return of([]);
    }

    const req: QueryMetricsRequest = this.getBaseQuery('salesperson_engagement_v2', ['__ALL__']); //filters.marketIds
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'distinct_engaged',
          aggOp: MeasureAggregateOperator.SUM,
          alias: 'total',
        }),
      }),
    ];

    req.groupBy = new GroupBy({
      dimension: [
        new GroupByDimension({
          dimension: 'timestamp',
        }),
      ],
    });

    req.alignmentPeriod = new AlignmentPeriod({
      calendar: AlignmentPeriodCalendar.CALENDAR_DAY,
    });
    req.alignment = Alignment.ALIGN_DELTA;

    this.attachFiltersToReq(req, { ...filters, dateFilter: rollingDateFilter });

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map((resp) => {
        const metrics = resp.metricResults[0].metrics.metrics;
        if (!metrics) {
          return [];
        }

        let spEngagement: SalespersonEngagement;
        let spEngagementMetric = metrics.map((m) => {
          spEngagement = {
            date: new Date(m.dimension),
            count: parseInt(m.measures[0], 10),
          };
          spEngagement.date = new Date(
            spEngagement.date.getUTCFullYear(),
            spEngagement.date.getUTCMonth(),
            spEngagement.date.getUTCDate(),
          );
          return spEngagement;
        });

        if (spEngagementMetric.length) {
          const dateRangeStart = new Date(rollingDateFilter.start);
          dateRangeStart.setHours(0, 0, 0, 0);
          spEngagementMetric = this.padMetricDataWithZeroValues(
            spEngagementMetric,
            dateRangeStart,
            rollingDateFilter.end,
            'day',
            'useLastValue',
          );
        }

        return spEngagementMetric;
      }),
      map(sortEntriesByDateKey),
      publishReplay(1),
      refCount(),
    );
  }

  getActiveBillableItemsForPartner(filters: SaasMetricsFilterData): Observable<BillableCount[]> {
    const req = this.getBaseQuery('activation_history_unaligned', filters.marketIds);
    req.filter = new Filter({
      compositeFilter: new CompositeFilter({
        op: CompositeFilterOperator.AND,
        filters: [
          {
            fieldFilter: {
              dimension: 'is_trial',
              operator: FieldFilterOperator.EQUAL,
              value: new TypedValue({
                value: false,
                valueType: PropertyType.PROPERTY_TYPE_BOOL,
              }),
            },
          },
        ],
      }),
    });
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'activation',
          aggOp: MeasureAggregateOperator.COUNT,
          alias: 'active',
        }),
      }),
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'cancellation',
          aggOp: MeasureAggregateOperator.COUNT,
          alias: 'cancelled',
        }),
      }),
    ];

    const alignerParams: CustomAlignerParams = {
      startDimension: 'activation',
      endDimension: 'deactivation',
      groupBy: 'aligner__start_time',
    };

    this.attachFiltersToReq(req, filters, alignerParams);

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map((resp) => {
        const metrics = resp.metricResults[0].metrics.metrics;
        if (!metrics) {
          return [];
        }
        return metrics.map((m) => {
          return {
            date: new Date(m.dimension),
            billableAccounts: parseInt(m.measures[0], 10) - parseInt(m.measures[1], 10),
          } as BillableCount;
        });
      }),
      map(sortEntriesByDateKey),
      publishReplay(1),
      refCount(),
    );
  }

  getNumberOfBusinessesOverTimeForPartner(filters: SaasMetricsFilterData): Observable<BusinessCount[]> {
    const req = this.getBaseQuery('activation_history_unaligned', filters.marketIds);
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'business_id',
          aggOp: MeasureAggregateOperator.COUNT_DISTINCT,
          alias: 'num_accounts',
        }),
      }),
    ];

    const alignerParams: CustomAlignerParams = {
      startDimension: 'activation',
      endDimension: 'deactivation',
      groupBy: 'aligner__start_time',
    };

    // Filter out accounts that only have cancelled products
    req.filter = new Filter({
      unaryFilter: new UnaryFilter({
        dimension: 'cancellation',
        op: UnaryFilterOperator.IS_NULL,
      }),
    });

    this.attachFiltersToReq(req, filters, alignerParams);
    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map((resp) => {
        const metrics = resp.metricResults[0].metrics.metrics;
        if (!metrics) {
          return [];
        }
        return metrics.map((m) => {
          return {
            date: new Date(m.dimension),
            accounts: parseInt(m.measures[0], 10),
          } as BusinessCount;
        });
      }),
      map(sortEntriesByDateKey),
      publishReplay(1),
      refCount(),
    );
  }

  getBasketSizeForPartner(filters: SaasMetricsFilterData): Observable<BasketSize[]> {
    const billableItems$ = this.getActiveBillableItemsForPartner(filters);
    const numBusinesses$ = this.getNumberOfBusinessesOverTimeForPartner(filters);
    return combineLatest([billableItems$, numBusinesses$]).pipe(
      map(([billableItems, numBusinesses]) => mergeBilledProductsAndBusinessCountData(billableItems, numBusinesses)),
      map(sortEntriesByDateKey),
      map((basketSizeData) => {
        if (filters.dateFilter && basketSizeData && basketSizeData.length) {
          basketSizeData = this.padMetricDataWithZeroValues(
            basketSizeData,
            filters.dateFilter.start,
            filters.dateFilter.end,
            'day',
          );
        }

        return basketSizeData;
      }),
      publishReplay(1),
      refCount(),
    );
  }

  getTotalSMBEngagementForPartner(filters: SaasMetricsFilterData): Observable<SMBCount[]> {
    const req = this.getBaseQuery('smb_engagement_v2_summary', ['__ALL__']);
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'distinct_engaged',
          aggOp: MeasureAggregateOperator.SUM,
          alias: 'engaged_accounts',
        }),
      }),
    ];

    req.groupBy = new GroupBy({
      dimension: [
        new GroupByDimension({
          dimension: 'date',
        }),
      ],
      groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
    });

    req.orderBy = new OrderBy({
      orderBy: [
        new OrderByOrderColumn({
          column: 'date',
          order: Order.ORDER_ASC,
        }),
      ],
    });
    this.attachFiltersToReq(req, filters);

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      map((metricResult) => {
        let smbCount: SMBCount;
        let smbEngagementMetric = metricResult.map((m) => {
          smbCount = {
            date: new Date(m.dimension),
            engagedSmbs: parseInt(m.measures[0], 10),
          };
          smbCount.date = new Date(
            smbCount.date.getUTCFullYear(),
            smbCount.date.getUTCMonth(),
            smbCount.date.getUTCDate(),
          );

          return smbCount;
        });

        if (filters.dateFilter && smbEngagementMetric.length) {
          const dateRangeStart = new Date(filters.dateFilter.start);
          dateRangeStart.setHours(0, 0, 0, 0);
          smbEngagementMetric = this.padMetricDataWithZeroValues(
            smbEngagementMetric,
            dateRangeStart,
            filters.dateFilter.end,
            'day',
            'useLastValue',
          );
        }

        return smbEngagementMetric;
      }),
      publishReplay(1),
      refCount(),
    );
  }

  getSMBEngagementBreakdownForPartnerRolling30(filters: SaasMetricsFilterData): Observable<SMBEngagementBreakdown[]> {
    const req = this.getBaseQuery('smb_engagement_v2_breakdown', ['__ALL__']);
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'distinct_engaged',
          aggOp: MeasureAggregateOperator.SUM,
          alias: 'product_engagement',
          filter: new Filter({
            fieldFilter: new FieldFilter({
              dimension: 'engagement_type',
              operator: FieldFilterOperator.EQUAL,
              value: new TypedValue({
                value: 'product_engagement',
                valueType: PropertyType.PROPERTY_TYPE_STRING,
              }),
            }),
          }),
        }),
      }),
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'distinct_engaged',
          aggOp: MeasureAggregateOperator.SUM,
          alias: 'business_center_engagement',
          filter: new Filter({
            fieldFilter: new FieldFilter({
              dimension: 'engagement_type',
              operator: FieldFilterOperator.EQUAL,
              value: new TypedValue({
                value: 'business_center_engagement',
                valueType: PropertyType.PROPERTY_TYPE_STRING,
              }),
            }),
          }),
        }),
      }),
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'distinct_engaged',
          aggOp: MeasureAggregateOperator.SUM,
          alias: 'email_engagement',
          filter: new Filter({
            fieldFilter: new FieldFilter({
              dimension: 'engagement_type',
              operator: FieldFilterOperator.EQUAL,
              value: new TypedValue({
                value: 'email_engagement',
                valueType: PropertyType.PROPERTY_TYPE_STRING,
              }),
            }),
          }),
        }),
      }),
    ];

    req.groupBy = new GroupBy({
      dimension: [
        new GroupByDimension({
          dimension: 'date',
        }),
      ],
      groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
    });

    req.orderBy = new OrderBy({
      orderBy: [
        new OrderByOrderColumn({
          column: 'date',
          order: Order.ORDER_ASC,
        }),
      ],
    });
    this.attachFiltersToReq(req, filters);

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      map((metrics) => [
        {
          engagementType: 'Products',
          smbCount: metrics.map((m) => ({
            date: new Date(m.dimension),
            engagedSmbs: Number(m.measures[0]),
          })),
        },
        {
          engagementType: 'Business App',
          smbCount: metrics.map((m) => ({
            date: new Date(m.dimension),
            engagedSmbs: Number(m.measures[1]),
          })),
        },
        {
          engagementType: 'Email',
          smbCount: metrics.map((m) => ({
            date: new Date(m.dimension),
            engagedSmbs: Number(m.measures[2]),
          })),
        },
      ]),
      publishReplay(1),
      refCount(),
    );
  }

  getSMBProductEngagementForPartner(filters: SaasMetricsFilterData): Observable<SMBProductCount[]> {
    const req = this.getBaseQuery('smb_engagement_rolling_30', filters.marketIds);
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'business_id',
          aggOp: MeasureAggregateOperator.COUNT_DISTINCT,
          alias: 'smbs',
        }),
      }),
    ];

    const startDate = new Date(filters.dateFilter.end);
    startDate.setDate(filters.dateFilter.end.getDate() - 1);
    req.dateRange = new SDKDateRange({
      start: startDate,
      end: filters.dateFilter.end,
    });

    req.groupBy = new GroupBy({
      dimension: [
        new GroupByDimension({
          dimension: 'service_provider_id',
        }),
      ],
      groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
    });

    req.orderBy = new OrderBy({
      orderBy: [
        new OrderByOrderColumn({
          column: 'smbs',
          order: Order.ORDER_DESC,
        }),
      ],
    });

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      map((metrics) =>
        metrics.map((m) => ({
          engagedSmbs: Number(m.measures[0]),
          product: m.dimension,
        })),
      ),
      publishReplay(1),
      refCount(),
    );
  }

  getAccountRetentionCountForDate(date: Date, created: Date): Observable<AccountRetentionCount[]> {
    const query = this.getQueryForAccountRetention(date, created);
    return this.multiLocationAnalyticsService.queryMetrics(query).pipe(
      map((resp) => {
        const metrics = resp.metricResults[0].metrics.metrics;
        if (!metrics) {
          return [];
        }
        return metrics.map((m) => {
          return {
            taxonomy: m.dimension,
            count: parseInt(m.measures[0], 10),
          } as AccountRetentionCount;
        });
      }),
    );
  }

  getQueryForAccountRetention(date: Date, start: Date): QueryMetricsRequest {
    const q = new QueryMetricsRequest({
      partnerId: this.partnerService.partnerId,
      metricName: 'paid_activation_counts',
      resourceIds: [
        new ResourceId({
          marketId: {
            marketIds: [],
          },
        }),
      ],
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'business_id',
            aggOp: MeasureAggregateOperator.COUNT_DISTINCT,
            alias: 'business',
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [{ dimension: `UNNEST(account_group__top_level_tax_ids)` }],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
    });

    // filter out all activations that are deactivated,
    // any new activations, and any new accounts
    q.filter = new Filter({
      compositeFilter: {
        op: CompositeFilterOperator.AND,
        filters: [
          {
            compositeFilter: {
              op: CompositeFilterOperator.OR,
              filters: [
                {
                  unaryFilter: new UnaryFilter({
                    op: UnaryFilterOperator.IS_NULL,
                    dimension: 'deactivation',
                  }),
                },
                {
                  fieldFilter: new FieldFilter({
                    operator: FieldFilterOperator.GREATER_THAN,
                    dimension: 'deactivation',
                    value: new TypedValue({
                      value: date,
                      valueType: PropertyType.PROPERTY_TYPE_TIMESTAMP,
                    }),
                  }),
                },
              ],
            },
          },
          {
            //Filter out all activations after the date
            fieldFilter: new FieldFilter({
              operator: FieldFilterOperator.LESS_THAN_OR_EQUAL,
              dimension: 'activation',
              value: new TypedValue({
                value: start,
                valueType: PropertyType.PROPERTY_TYPE_TIMESTAMP,
              }),
            }),
          },
          {
            //Filter out all accounts that where created after the start date
            fieldFilter: new FieldFilter({
              operator: FieldFilterOperator.LESS_THAN_OR_EQUAL,
              dimension: 'created',
              value: new TypedValue({
                value: start,
                valueType: PropertyType.PROPERTY_TYPE_TIMESTAMP,
              }),
            }),
          },
        ],
      },
    });
    return q;
  }

  getSMBRetentionForPartner(filters: SaasMetricsFilterData): Observable<RetentionData[]> {
    const req = this.getBaseQuery('account_retention_v2', filters.marketIds);
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'business_id',
          aggOp: MeasureAggregateOperator.COUNT,
          alias: 'active_accounts',
          filter: new Filter({
            fieldFilter: new FieldFilter({
              dimension: 'repeated',
              operator: FieldFilterOperator.EQUAL,
              value: new TypedValue({
                value: true,
                valueType: PropertyType.PROPERTY_TYPE_BOOL,
              }),
            }),
          }),
        }),
      }),
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'retained_account',
          aggOp: MeasureAggregateOperator.SUM,
          alias: 'retained_accounts',
          filter: new Filter({
            fieldFilter: new FieldFilter({
              dimension: 'repeated',
              operator: FieldFilterOperator.EQUAL,
              value: new TypedValue({
                value: true,
                valueType: PropertyType.PROPERTY_TYPE_BOOL,
              }),
            }),
          }),
        }),
      }),
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'business_id',
          aggOp: MeasureAggregateOperator.COUNT,
          alias: 'active_accounts_with_one_time',
        }),
      }),
    ];

    req.groupBy = new GroupBy({
      dimension: [
        new GroupByDimension({
          dimension: 'date',
        }),
      ],
      groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
    });

    req.orderBy = new OrderBy({
      orderBy: [
        new OrderByOrderColumn({
          column: 'date',
          order: Order.ORDER_ASC,
        }),
      ],
    });

    const startDate = offsetMonths(filters.dateFilter.start, -1);
    startDate.setDate(1);
    const dateFilter = {
      start: new Date(startDate.getFullYear(), startDate.getMonth()),
      end: filters.dateFilter.end,
    };
    this.attachFiltersToReq(req, { ...filters, dateFilter });

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      map(this.parseAccountRetentionMetric),
      map((retentionLevels) => {
        if (retentionLevels.length) {
          const dateRangeEnd = new Date(dateFilter.end.getFullYear(), dateFilter.end.getMonth());
          retentionLevels = this.padMetricDataWithZeroValues(retentionLevels, dateFilter.start, dateRangeEnd, 'month');
        }

        //remove the extra month added at the beginning of the range.
        retentionLevels.splice(0, 1);

        return retentionLevels;
      }),
    );
  }

  getEstimatedRevenueAndArppaForPartner(
    filters: SaasMetricsFilterData,
  ): Observable<EstimatedRevenueAndArppaForPartner[]> {
    const req = this.getBaseQuery('account_revenue', filters.marketIds);

    const revenueMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'revenue',
        aggOp: MeasureAggregateOperator.SUM,
        alias: 'revenue',
      }),
    };

    const accountsMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'business_id',
        aggOp: MeasureAggregateOperator.COUNT_DISTINCT,
        alias: 'accounts',
      }),
    };

    req.measures = [new Measure(revenueMeasure), new Measure(accountsMeasure)];

    req.groupBy = new GroupBy({
      dimension: [
        new GroupByDimension({
          dimension: 'month',
        }),
      ],
      groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
    });

    const startDate = offsetMonths(filters.dateFilter.start, 0);
    startDate.setUTCDate(1);
    const endDate = offsetMonths(filters.dateFilter.end, 1);
    endDate.setUTCDate(0);
    const dateFilter = {
      start: startDate,
      end: endDate,
    };
    this.attachFiltersToReq(req, { ...filters, dateFilter });

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      withLatestFrom(this.currency$),
      map(([metrics, currency]) => metrics.map((metric) => constructEstimatedRevenueAndArppa(currency, metric))),
      map((revenueAndArppa) => revenueAndArppa.map(convertContainedTimestampsToUTC)),
      map((revenueAndArppa) =>
        this.padMetricDataWithZeroValues(revenueAndArppa, startDate, endDate, 'month', 'useLastValue'),
      ),
      publishReplay(1),
      refCount(),
    );
  }

  /**
   * Gets the products that are contributing the most to ARPPA, and the average revenue per product
   * @param marketId - the market ID to get stats for
   */
  getTopARPPAContributors(filters: SaasMetricsFilterData): Observable<AverageRevenueAndCountByProduct[]> {
    const req = this.getBaseQuery('revenue_line_items', filters.marketIds);

    const revenueMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'revenue',
        aggOp: MeasureAggregateOperator.AVG,
        alias: 'average_revenue',
      }),
    };

    const countMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'activation_id',
        aggOp: MeasureAggregateOperator.COUNT_DISTINCT,
        alias: 'count',
      }),
    };

    req.measures = [new Measure(revenueMeasure), new Measure(countMeasure)];

    req.groupBy = new GroupBy({
      dimension: [
        new GroupByDimension({
          dimension: 'app_id',
        }),
        new GroupByDimension({
          dimension: 'edition_id',
        }),
      ],
      groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
    });

    this.attachFiltersToReq(req, filters);

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      withLatestFrom(this.currency$),
      map(([metrics, currency]) => {
        return metrics
          .map((metric) => constructTopARPPAContributorData(currency, metric))
          .sort((a, b) => b.averageRevenue - a.averageRevenue);
      }),
      publishReplay(1),
      refCount(),
    );
  }

  getEstimatedRevenueBreakdownForPartner(
    filters: SaasMetricsFilterData,
  ): Observable<EstimatedRevenueBreakdownForPartner> {
    const req = this.getBaseQuery('account_revenue_breakdown', filters.marketIds);

    const newMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'new',
        aggOp: MeasureAggregateOperator.SUM,
        alias: 'new',
      }),
    };

    const existingMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'existing',
        aggOp: MeasureAggregateOperator.SUM,
        alias: 'existing',
      }),
    };

    const expansionMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'expansion',
        aggOp: MeasureAggregateOperator.SUM,
        alias: 'expansion',
      }),
    };

    const contractionMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'contraction',
        aggOp: MeasureAggregateOperator.SUM,
        alias: 'contraction',
      }),
    };

    req.measures = [
      new Measure(newMeasure),
      new Measure(existingMeasure),
      new Measure(expansionMeasure),
      new Measure(contractionMeasure),
    ];

    req.groupBy = new GroupBy({
      dimension: [
        new GroupByDimension({
          dimension: 'month',
        }),
      ],
      groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
    });

    const startDate = offsetMonths(filters.dateFilter.start, -1);
    startDate.setUTCDate(1);
    const endDate = offsetMonths(filters.dateFilter.end, 1);
    endDate.setUTCDate(0);
    const dateFilter = {
      start: startDate,
      end: endDate,
    };

    this.attachFiltersToReq(req, { ...filters, dateFilter });

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      withLatestFrom(this.currency$),
      map(([metrics, currency]) => metrics.map((metric) => constructEstimatedRevenueBreakdown(currency, metric))),
      map((breakdown) => breakdown.map(convertContainedTimestampsToUTC)),
      map((breakdown) => this.padMetricDataWithZeroValues(breakdown, startDate, endDate, 'month', 'useLastValue')),
      map((breakdownResp) => calculateChurnAndTotal(breakdownResp)),
      map((revenueBreakdown) => splitBreakdownsToArrays(revenueBreakdown)),
      publishReplay(1),
      refCount(),
    ) as Observable<EstimatedRevenueBreakdownForPartner>;
  }

  getRevenueByProduct(filters: SaasMetricsFilterData): Observable<ProductRevenue[]> {
    const req = this.revenueByQuery(['app_id', 'edition_id']);
    const startDate = offsetMonths(filters.dateFilter.start, 0);
    startDate.setUTCDate(1);
    const endDate = offsetMonths(filters.dateFilter.end, 1);
    endDate.setUTCDate(0);
    const dateFilter = {
      start: startDate,
      end: endDate,
    };
    this.attachFiltersToReq(req, { ...filters, dateFilter });

    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      withLatestFrom(this.currency$),
      map(([metrics, currency]) =>
        metrics.map((metric) => {
          return {
            productId: metric.dimension,
            editionId: metric.results.metrics[0].dimension,
            //           revenue: convertBaseCurrencyUnitToDisplayUnit(metric.results.metrics[0].measures[0], 'USD')
            revenue: currency.displayValue(metric.results.metrics[0].measures[0]),
          } as ProductRevenue;
        }),
      ),
      publishReplay(1),
      refCount(),
    );
  }

  getRevenueByMarket(filters: SaasMetricsFilterData): Observable<MarketRevenue[]> {
    const req = this.revenueByQuery(['account_revenue.market_id']);
    const startDate = offsetMonths(filters.dateFilter.start, 0);
    startDate.setUTCDate(1);
    const endDate = offsetMonths(filters.dateFilter.end, 1);
    endDate.setUTCDate(0);
    const dateFilter = {
      start: startDate,
      end: endDate,
    };
    this.attachFiltersToReq(req, { ...filters, dateFilter });
    return this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      withLatestFrom(this.currency$),
      map(([resp, currency]) => {
        const metrics = resp.metricResults[0].metrics.metrics;
        if (!metrics) {
          return [];
        }
        return metrics.map(
          (metric) =>
            ({
              marketId: metric.dimension,
              //         revenue: convertBaseCurrencyUnitToDisplayUnit(metric.measures[0], 'USD');
              revenue: currency.displayValue(metric.measures[0]),
            }) as MarketRevenue,
        );
      }),
      publishReplay(1),
      refCount(),
    );
  }

  getEmailBreakdownForNotificationType(
    dateRange?: DateRange,
    marketId?: string,
    notificationTypes?: NotificationType[],
  ): Observable<ChartSeries[]> {
    const buckets = this.createEmailEventBuckets();
    return this.getEmailBreakdown(buckets, dateRange, this.partnerService.partnerId, marketId, notificationTypes).pipe(
      map((eventBuckets) => {
        return this.fromEmailEventBucketsToChartSeries(eventBuckets);
      }),
    );
  }

  getEmailBreakdown(
    buckets: Bucket[],
    dateRange?: DateRange,
    partnerId?: string,
    marketId?: string,
    notificationTypes?: NotificationType[],
    eventTypes?: EventType[],
  ): Observable<EmailEventBucket[]> {
    return this.emailEventService.getEventRollups(
      buckets,
      dateRange,
      partnerId,
      marketId,
      notificationTypes,
      eventTypes,
    );
  }

  createEmailEventBuckets(): Bucket[] {
    const buckets: Bucket[] = [];
    buckets.push(createBucket('Open Emails', 'event', 'open'));
    buckets.push(createBucket('Click Emails', 'event', 'click'));
    return buckets;
  }

  fromEmailEventBucketsToChartSeries(emailEvents: EmailEventBucket[]): ChartSeries[] {
    const chartSeries: ChartSeries[] = [];
    emailEvents.forEach((emailEvent) => {
      if (!emailEvent.eventRollup || emailEvent.eventRollup.length <= 0) {
        return [];
      }
      const chartEntry: ChartEntry[] = [];
      emailEvent.eventRollup.forEach((rollUp) => {
        chartEntry.push({ name: new Date(rollUp.date), value: rollUp.quantity || 0 });
      });
      chartSeries.push({ name: emailEvent.bucketName, series: chartEntry });
    });
    return chartSeries;
  }

  queryMetricDataIsEmpty$(metricName: string, filters: SaasMetricsFilterData): Observable<boolean> {
    const req = this.getBaseQuery(metricName, filters.marketIds);
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: '*',
          aggOp: MeasureAggregateOperator.COUNT,
          alias: 'record_count',
        }),
      }),
    ];

    req.dateRange = new SDKDateRange({
      start: (filters.dateFilter || { start: new Date(0) }).start,
      end: (filters.dateFilter || { end: new Date() }).end,
    });

    req.limit = 1;
    const queryMetricData = this.multiLocationAnalyticsService.queryMetrics(req).pipe(
      map(extractMetricsFromResponse),
      map((resp) => {
        return !resp.length || !resp[0].measures || !resp[0].measures.length || !parseInt(resp[0].measures[0], 10);
      }),
    );

    return this.localRefreshingCacheService
      .getCacheValueAndRefresh('saasMetric.cachedData.' + metricName, queryMetricData)
      .pipe(publishReplay(1), refCount());
  }

  private revenueByQuery(dimensions: string[]): QueryMetricsRequest {
    const req = this.getBaseQuery('account_revenue');

    const revenueMeasure = {
      aggregate: new MeasureAggregate({
        measure: 'revenue',
        aggOp: MeasureAggregateOperator.SUM,
        alias: 'revenue',
      }),
    };

    req.measures = [new Measure(revenueMeasure)];

    const metricDimensions = dimensions.map((dim) => {
      return new GroupByDimension({
        dimension: dim,
      });
    });

    req.groupBy = new GroupBy({
      dimension: metricDimensions,
      groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
    });

    return req;
  }

  private setDefaultValues<T extends { date: Date }>(item: T, defaultValues: any, useZero = true): void {
    Object.keys(item).forEach((keyn) => {
      if (typeof item[keyn] === 'number') {
        if (useZero) {
          item[keyn] = 0;
        }
      } else if (typeof item[keyn] === 'object') {
        this.setDefaultValues(item[keyn], defaultValues, useZero);
      }
    });
    Object.assign(item, defaultValues);
  }

  /**
   * Pad array of metric data with zero on both ends.
   * @param metric - the metric array.
   * @param lowerBound - the lower bound of the date range.
   * @param upperBound - the upper bound of the date range.
   * @param stepSize - the increment size between items in the array.
   */
  padMetricDataWithZeroValues<T extends { date: Date }>(
    metric: T[],
    lowerBound: Date,
    upperBound: Date,
    stepSize: metricPaddingStepSize,
    useValue: 'useFirstValue' | 'useLastValue' | 'useValues' | 'useZero' = 'useZero',
  ): T[] {
    if (!metric || !metric.length) {
      return [];
    }

    metric = JSON.parse(JSON.stringify(metric), (key, value) => (key === 'date' ? new Date(value) : value));

    if (lowerBound > upperBound) {
      [lowerBound, upperBound] = [upperBound, lowerBound];
    }

    const extractDateCompareValue = (date: Date): string => {
      return stepSize === 'month' ? moment.utc(date).format('YYYYMM') : moment.utc(date).format('YYYYMMDD');
    };

    let zeroVal: T;
    let newDate: moment.Moment;
    while (extractDateCompareValue(metric[0].date) > extractDateCompareValue(lowerBound)) {
      newDate = moment.utc(metric[0].date);
      newDate.add(-1, stepSize);

      zeroVal = JSON.parse(JSON.stringify(metric[0]));
      this.setDefaultValues(
        zeroVal,
        { date: newDate.toDate() },
        useValue !== 'useFirstValue' && useValue !== 'useValues',
      );

      metric.unshift(zeroVal);
    }

    while (extractDateCompareValue(metric[metric.length - 1].date) < extractDateCompareValue(upperBound)) {
      newDate = moment.utc(metric[metric.length - 1].date);
      newDate.add(1, stepSize);

      zeroVal = JSON.parse(JSON.stringify(metric[metric.length - 1]));
      this.setDefaultValues(
        zeroVal,
        { date: newDate.toDate() },
        useValue !== 'useLastValue' && useValue !== 'useValues',
      );

      metric.push(zeroVal);
    }

    return metric;
  }

  parseAccountRetentionMetric(metricData: MetricResult[]): RetentionData[] {
    metricData = JSON.parse(JSON.stringify(metricData || []));
    metricData.sort((a, b) => (a.dimension < b.dimension ? -1 : 1));

    return metricData.map((m, idx) => {
      const retentionData = {
        date: new Date(m.dimension),
        activations: parseInt(m.measures[0], 10),
        retained_activations: parseInt(m.measures[1], 10),
        activations_including_one_time: parseInt(m.measures[2], 10),
        rate: 0.0,
      } as RetentionData;

      if (idx > 0) {
        const prevActivations = parseInt(metricData[idx - 1].measures[0], 10);
        retentionData.rate = prevActivations === 0 ? 0.0 : retentionData.retained_activations / prevActivations;
        retentionData.rate = Math.trunc(retentionData.rate * 10000) / 10000;
      }
      retentionData.date = new Date(
        retentionData.date.getUTCFullYear(),
        retentionData.date.getUTCMonth(),
        retentionData.date.getUTCDate(),
      );

      return retentionData;
    });
  }
}

export function convertDateRangetoRangeOfDates(dateRange: DateRange): Date[] {
  if (dateRange.start && dateRange.end) {
    const start: number = dateRange.start.getTime();
    const end: number = dateRange.end.getTime();
    const daysBetweenDates: number = Math.ceil((end - start) / MS_PER_DAY);

    // The days array will contain a Date object for each day between dates (inclusive)
    const days: Date[] = Array.from(new Array(daysBetweenDates + 1), (v, i) => new Date(start + i * MS_PER_DAY));
    return days;
  }
  return undefined;
}

/**
 * Combines two arrays of data, count of Billable Items over time and count of Businesses over time, and calculates the
 * average basket size over time
 * @param billableItems List of BillableCount objects that represent the number of billed products over time
 * @param numBusinesses List of BusinessCount objects that represent the count of businesses for a partner over time
 */
export function mergeBilledProductsAndBusinessCountData(
  billableItems: BillableCount[],
  numBusinesses: BusinessCount[],
): BasketSize[] {
  if (!(billableItems && numBusinesses)) {
    return [];
  }
  let keys = billableItems.map((b) => b.date);
  // Determine the proper order to display the data points in
  keys = keys.sort((a, b) => (a < b ? -1 : 1));

  // Local interface for building a mapping of date to billable items and business count
  interface BillableItemsAndNumBusinessesForDate {
    billableItems: number;
    numBusinesses: number;
  }

  const mapping = new Map<number, BillableItemsAndNumBusinessesForDate>();
  billableItems.map((b) => {
    const dateKey = b.date.valueOf();
    const m = mapping.get(dateKey) || ({} as BillableItemsAndNumBusinessesForDate);
    m.billableItems = b.billableAccounts;
    mapping.set(dateKey, m);
  });
  numBusinesses.map((n) => {
    const dateKey = n.date.valueOf();
    const m = mapping.get(dateKey);
    if (!m) {
      return;
    }
    m.numBusinesses = n.accounts;
    mapping.set(dateKey, m);
  });

  // Construct list of data points in proper order
  const basketSizeData: BasketSize[] = [];
  keys.forEach((key) => {
    const item = mapping.get(key.valueOf());
    if (!item) {
      return;
    }
    const basketSize = item.billableItems / item.numBusinesses || 0;
    // For rounding to 4 decimal places
    if (!basketSize) {
      return;
    }
    basketSizeData.push({
      date: key,
      basketSize: Math.round(basketSize * ROUNDING_FACTOR) / ROUNDING_FACTOR,
    } as BasketSize);
  });
  return basketSizeData;
}

/**
 * Sort and filter the ChartEntry items down to the top n entries
 * @param chartEntries: array of line chart entries to be sorted and filtered
 * @param ascendingOrder: when set to true, sorts the return value in ascending order, otherwise sorts in descending order.
 * @param n: the max number of entries to return
 */
export function reduceChartEntryToNEntries(
  chartEntries: ChartEntry[],
  ascendingOrder: boolean,
  n: number,
): ChartEntry[] {
  if (!chartEntries || chartEntries.length === 0) {
    return [];
  }

  if (ascendingOrder) {
    return chartEntries.sort((a, b) => b.value - a.value).slice(0, n);
  } else {
    return chartEntries.sort((a, b) => a.value - b.value).slice(0, n);
  }
}

/**
 * Create a Bucket to get metric data for.
 * @param bucketName - The name of the bucket
 * @param key - The key of the attribute to get the data for
 * @param value - The Value of the attribute to get the data for
 */
export function createBucket(bucketName: string, key: string, value: string): Bucket {
  return new Bucket({
    bucketName: bucketName,
    attribute: {
      key,
      value,
    } as Attribute,
  });
}

export function constructTopARPPAContributorData(
  currency: Currency,
  metric: MetricResult,
): AverageRevenueAndCountByProduct {
  const productId = metric.dimension;
  const editionId = metric.results.metrics[0].dimension;
  const averageRevenue = currency.penniesToDollars(parseInt(metric.results.metrics[0].measures[0], 10));
  const count = parseInt(metric.results.metrics[0].measures[1], 10);
  return {
    productId: productId,
    averageRevenue: averageRevenue,
    count: count,
    editionId,
  };
}

export function createRollingAverageDateRange(dateRange: DateRange): DateRange | null {
  const { start, end } = dateRange;
  if (!start || !end) {
    return null;
  }

  return {
    start,
    end,
  } as DateRange;
}

/**
 * Convert timestamps for all entries in a breakdown item, to UTC.
 *
 * @param breakdown - BraekdownItem to convert timestamps in
 */
export function convertContainedTimestampsToUTC<T extends { date: Date }>(breakdownItem: T): T {
  if (!breakdownItem) {
    return;
  }

  // Convert high level date to UTC
  if (breakdownItem.date) {
    breakdownItem.date = toUTC(breakdownItem.date);
  }

  // Convert sub-metric dates to UTC
  Object.keys(breakdownItem).forEach((key: string) => {
    const entry = breakdownItem[key];
    if (entry && entry.date) {
      entry.date = toUTC(entry.date);
    }
  });

  return breakdownItem;
}
