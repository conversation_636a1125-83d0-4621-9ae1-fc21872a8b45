import { COLORS } from './saas-metrics.helper';

/*****************************************************************
 *                          I18N                                 *
 *****************************************************************/

// Root path to Saas metrics I18N in en_devel.json
export const SAAS_METRICS_I18N_PATH = 'SAAS_METRICS';

// Billable accounts chart
const BILLABLE_ACCOUNT_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.BILLABLE_ITEMS`;
const BILLABLE_ACCOUNT_CARD_TEXT_I18N = {
  title: { key: `${BILLABLE_ACCOUNT_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${BILLABLE_ACCOUNT_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${B<PERSON><PERSON>BLE_ACCOUNT_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
};

// Billable accounts > Basket Size chart
const BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH = `${BILLABLE_ACCOUNT_I18N_PATH}.BASKET_SIZE`;
const BILLABLE_ACCOUNT_BASKET_SIZE_CARD_TEXT_I18N = {
  title: { key: `${BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH}.CARD.SUBTITLE` },
  info: { key: `${BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH}.CARD.INFO` },
  footerText: { key: `${BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  emptyFooterText: { key: `${BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH}.CARD.EMPTY_FOOTER_LINK_TEXT` },
  emptySubtitle: { key: `${BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH}.CARD.EMPTY_SUBTITLE` },
  valueDescriptor: { key: `${BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH}.CARD.VALUE_DESCRIPTOR` },
  actionText: { key: `${BILLABLE_ACCOUNT_BASKET_SIZE_I18N_PATH}.CARD.DIALOG_ACTION_TEXT` },
};
// Billable accounts > Top billed products chart
const TOP_BILLED_PRODUCT_CARD_TEXT_I18N_PATH = `${BILLABLE_ACCOUNT_I18N_PATH}.TOP_PRODUCTS`;
const TOP_BILLED_PRODUCT_CARD_TEXT_I18N = {
  title: { key: `${TOP_BILLED_PRODUCT_CARD_TEXT_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${TOP_BILLED_PRODUCT_CARD_TEXT_I18N_PATH}.CARD.SUBTITLE` },
  bodySubtitle: { key: `${TOP_BILLED_PRODUCT_CARD_TEXT_I18N_PATH}.CARD.BODY_SUBTITLE_TEXT` },
  footerText: { key: `${TOP_BILLED_PRODUCT_CARD_TEXT_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
};

// SMB Engagement chart
const ENGAGED_SMB_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.SMB_ENGAGEMENT`;
const ENGAGED_SMB_CARD_TEXT_I18N = {
  title: { key: `${ENGAGED_SMB_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${ENGAGED_SMB_I18N_PATH}.CARD.SUBTITLE` },
  info: { key: `${ENGAGED_SMB_I18N_PATH}.CARD.INFO` },
  footerText: { key: `${ENGAGED_SMB_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  emptyFooterText: { key: `${ENGAGED_SMB_I18N_PATH}.CARD.EMPTY_FOOTER_LINK_TEXT` },
  emptySubtitle: { key: `${ENGAGED_SMB_I18N_PATH}.CARD.EMPTY_SUBTITLE` },
  valueDescriptor: { key: `${ENGAGED_SMB_I18N_PATH}.CARD.VALUE_DESCRIPTOR` },
  actionText: { key: `${ENGAGED_SMB_I18N_PATH}.CARD.DIALOG_ACTION_TEXT` },
};

// Salesperson Engagement Chart
const ENGAGED_SALESPERSON_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.SALESPERSON_ENGAGEMENT`;
export const ENGAGED_SALESPERSON_CARD_TEXT_I18N = {
  title: { key: `${ENGAGED_SALESPERSON_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${ENGAGED_SALESPERSON_I18N_PATH}.CARD.SUBTITLE` },
  info: { key: `${ENGAGED_SALESPERSON_I18N_PATH}.CARD.INFO` },
  emptyFooterText: { key: `${ENGAGED_SALESPERSON_I18N_PATH}.CARD.EMPTY_FOOTER_LINK_TEXT` },
  emptySubtitle: { key: `${ENGAGED_SALESPERSON_I18N_PATH}.CARD.EMPTY_SUBTITLE` },
  valueDescriptor: { key: `${ENGAGED_SALESPERSON_I18N_PATH}.CARD.VALUE_DESCRIPTOR` },
  actionText: { key: `${ENGAGED_SALESPERSON_I18N_PATH}.CARD.DIALOG_ACTION_TEXT` },
};

// SMB Product Engagement chart
const SMB_PRODUCT_ENGAGEMENT_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.SMB_PRODUCT_ENGAGEMENT`;
export const SMB_PRODUCT_ENGAGEMENT_CARD_TEXT_I18N = {
  title: { key: `${SMB_PRODUCT_ENGAGEMENT_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${SMB_PRODUCT_ENGAGEMENT_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${SMB_PRODUCT_ENGAGEMENT_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  subTitle: { key: `${SMB_PRODUCT_ENGAGEMENT_I18N_PATH}.CARD.SUBTITLE_TEXT` },
};

// SMB Engagement Breakdown chart
const SMB_ENGAGEMENT_BREAKDOWN_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.SMB_ENGAGEMENT_BREAKDOWN`;
export const SMB_ENGAGEMENT_BREAKDOWN_CARD_TEXT_I18N = {
  title: { key: `${SMB_ENGAGEMENT_BREAKDOWN_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${SMB_ENGAGEMENT_BREAKDOWN_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${SMB_ENGAGEMENT_BREAKDOWN_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  subTitle: { key: `${SMB_ENGAGEMENT_BREAKDOWN_I18N_PATH}.CARD.SUBTITLE_TEXT` },
};

// ARPPA chart
const ARPPA_CARD_TEXT_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.ARPPA`;
const ARPPA_CARD_TEXT_I18N = {
  title: { key: `${ARPPA_CARD_TEXT_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${ARPPA_CARD_TEXT_I18N_PATH}.CARD.SUBTITLE` },
  info: { key: `${ARPPA_CARD_TEXT_I18N_PATH}.CARD.INFO` },
  footerText: { key: `${ARPPA_CARD_TEXT_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  emptyFooterText: { key: `${ARPPA_CARD_TEXT_I18N_PATH}.CARD.EMPTY_FOOTER_LINK_TEXT` },
  emptySubtitle: { key: `${ARPPA_CARD_TEXT_I18N_PATH}.CARD.EMPTY_SUBTITLE` },
  valueDescriptor: { key: `${ARPPA_CARD_TEXT_I18N_PATH}.CARD.VALUE_DESCRIPTOR` },
  actionText: { key: `${ARPPA_CARD_TEXT_I18N_PATH}.CARD.DIALOG_ACTION_TEXT` },
};
// ARPPA > Monthly Reccurring Revenue card
const MONTHLY_RECURRING_REVENUE_I18N_PATH = `${ARPPA_CARD_TEXT_I18N_PATH}.MONTHLY_RECURRING_REVENUE`;
const MONTHLY_RECURRING_REVENUE_CARD_TEXT_I18N = {
  title: { key: `${MONTHLY_RECURRING_REVENUE_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${MONTHLY_RECURRING_REVENUE_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${MONTHLY_RECURRING_REVENUE_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
};
// ARPPA > Total Accounts card
const TOTAL_ACCOUNTS_I18N_PATH = `${ARPPA_CARD_TEXT_I18N_PATH}.TOTAL_ACCOUNTS`;
const TOTAL_ACCOUNTS_CARD_TEXT_I18N = {
  title: { key: `${TOTAL_ACCOUNTS_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${TOTAL_ACCOUNTS_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${TOTAL_ACCOUNTS_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
};
// ARPPA > Top contributors card
const TOP_ARPPA_CONTRIBUTORS_I18N_PATH = `${ARPPA_CARD_TEXT_I18N_PATH}.TOP_ARPPA_CONTRIBUTORS`;
const TOP_ARPPA_CONTRIBUTORS_CARD_TEXT_I18N = {
  title: { key: `${TOP_ARPPA_CONTRIBUTORS_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${TOP_ARPPA_CONTRIBUTORS_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${TOP_ARPPA_CONTRIBUTORS_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  xAxisLabel: { key: `${TOP_ARPPA_CONTRIBUTORS_I18N_PATH}.CHART.X_AXIS_LABEL` },
  yAxisLabel: { key: `${TOP_ARPPA_CONTRIBUTORS_I18N_PATH}.CHART.Y_AXIS_LABEL` },
  chartSubtitle: { key: `${TOP_ARPPA_CONTRIBUTORS_I18N_PATH}.CHART.SUBTITLE` },
  info: { key: `${TOP_ARPPA_CONTRIBUTORS_I18N_PATH}.CARD.INFO` },
};

// Gross Revenue chart
const GROSS_REV_CARD_TEXT_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.GROSS_REVENUE`;
const GROSS_REV_CARD_TEXT_I18N = {
  title: { key: `${GROSS_REV_CARD_TEXT_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${GROSS_REV_CARD_TEXT_I18N_PATH}.CARD.SUBTITLE` },
  info: { key: `${GROSS_REV_CARD_TEXT_I18N_PATH}.CARD.INFO` },
  footerText: { key: `${GROSS_REV_CARD_TEXT_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  emptyFooterText: { key: `${GROSS_REV_CARD_TEXT_I18N_PATH}.CARD.EMPTY_FOOTER_LINK_TEXT` },
  emptySubtitle: { key: `${GROSS_REV_CARD_TEXT_I18N_PATH}.CARD.EMPTY_SUBTITLE` },
  valueDescriptor: { key: `${GROSS_REV_CARD_TEXT_I18N_PATH}.CARD.VALUE_DESCRIPTOR` },
  actionText: { key: `${GROSS_REV_CARD_TEXT_I18N_PATH}.CARD.DIALOG_ACTION_TEXT` },
};

// SMB Retention Rate chart
const SMB_RETENTION_RATE_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.SMB_RETENTION_RATE`;
export const SMB_RETENTION_RATE_CARD_TEXT_I18N = {
  title: { key: `${SMB_RETENTION_RATE_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${SMB_RETENTION_RATE_I18N_PATH}.CARD.SUBTITLE` },
  info: { key: `${SMB_RETENTION_RATE_I18N_PATH}.CARD.INFO` },
  footerText: { key: `${SMB_RETENTION_RATE_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  emptyFooterText: { key: `${SMB_RETENTION_RATE_I18N_PATH}.CARD.EMPTY_FOOTER_LINK_TEXT` },
  emptySubtitle: { key: `${SMB_RETENTION_RATE_I18N_PATH}.CARD.EMPTY_SUBTITLE` },
  valueDescriptor: { key: `${SMB_RETENTION_RATE_I18N_PATH}.CARD.VALUE_DESCRIPTOR` },
  actionText: { key: `${SMB_RETENTION_RATE_I18N_PATH}.CARD.DIALOG_ACTION_TEXT` },
};

// Billable Accounts card
const BILLABLE_ACCOUNTS_CARD_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.BILLABLE_ACCOUNTS_CARD`;
const BILLABLE_ACCOUNTS_CARD_TEXT_I18N = {
  title: { key: `${BILLABLE_ACCOUNTS_CARD_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${BILLABLE_ACCOUNTS_CARD_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${BILLABLE_ACCOUNTS_CARD_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
  totalAccounts: { key: `${BILLABLE_ACCOUNTS_CARD_I18N_PATH}.CARD.TOTAL_ACCOUNTS_TEXT` },
};

// Account Retention Top 6 chart
const ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CARD_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.ACCOUNT_RETENTION_BY_TAXONOMY_TOP6`;
const ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CARD_TEXT_I18N = {
  title: { key: `${ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CARD_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CARD_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CARD_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
};

// Account Retention Bottom 6 chart
const ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CARD_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.ACCOUNT_RETENTION_BY_TAXONOMY_BOTTOM6`;
const ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CARD_TEXT_I18N = {
  title: { key: `${ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CARD_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CARD_I18N_PATH}.CARD.SUBTITLE` },
  footerText: { key: `${ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CARD_I18N_PATH}.CARD.FOOTER_LINK_TEXT` },
};

// Top Market chart
const TOP_MARKET_CHART_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.TOP_MARKET_CHART`;
export const TOP_MARKET_CHART_CARD_TEXT_I18N = {
  title: { key: `${TOP_MARKET_CHART_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${TOP_MARKET_CHART_I18N_PATH}.CARD.SUBTITLE` },
};

// Top App chart
const TOP_APP_CHART_I18N_PATH = `${SAAS_METRICS_I18N_PATH}.TOP_APP_CHART`;
export const TOP_APP_CHART_CARD_TEXT_I18N = {
  title: { key: `${TOP_APP_CHART_I18N_PATH}.CARD.TITLE` },
  subtitle: { key: `${TOP_APP_CHART_I18N_PATH}.CARD.SUBTITLE` },
};

/*****************************************************************
 *                          CTAS                              *
 *****************************************************************/
export const SAAS_METRICS_CTAS = 'saas-metrics-ctas';
export const SAAS_METRICS_CLICK_ACTION = 'clicked';
/*****************************************************************
 *                          COLOURS                              *
 *****************************************************************/
const PRIMARY_CHART_COLOUR = '#42a5f5';
const TRANSPARENT_CHART_COLOUR = 'transparent';

/*****************************************************************
 *                        STRINGS                                *
 *****************************************************************/
export const BILLABLE_ITEMS_CHART_NAME = 'Billable Items';
export const BILLABLE_ITEMS_BASKET_SIZE_CHART_NAME = `Basket Size`;
export const SMB_ENGAGEMENT_CHART_NAME = 'SMB Engagement';
export const SALESPERSON_ENGAGEMENT_CHART_NAME = 'Salesperson Engagement';
export const ARPPA_CHART_NAME = 'ARPPA';
export const TOP_ARPPA_CONTRIBUTORS_CHART_NAME = 'top_arppa_contributors';
export const GROSS_REV_CHART_NAME = 'Gross Revenue';
export const SMB_RETENTION_CHART_NAME = 'Account Retention';
export const SMB_PRODUCT_ENGAGEMENT_CHART_NAME = 'smb_product_engagement';
export const SMB_ENGAGEMENT_BREAKDOWN_CHART_NAME = 'smb_engagement_breakdown';
export const ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CHART_NAME = 'account_retention_rate_by_taxonomy_top6';
export const ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CHART_NAME = 'account_retention_rate_by_taxonomy_bottom6';
export const LINE_CHART_PROP = 'Prop';

/*****************************************************************
 *                        CHART CONFIG                           *
 *****************************************************************/
export const SMB_ENGAGEMENT_CONFIG = {
  header: {
    title: ENGAGED_SMB_CARD_TEXT_I18N.title,
    subtitle: ENGAGED_SMB_CARD_TEXT_I18N.subtitle,
    info: ENGAGED_SMB_CARD_TEXT_I18N.info,
    valueDescriptor: ENGAGED_SMB_CARD_TEXT_I18N.valueDescriptor,
    action: {
      text: GROSS_REV_CARD_TEXT_I18N.actionText,
      url: 'https://support.vendasta.com/hc/en-us/articles/*************',
    },
  },
  footer: {
    text: ENGAGED_SMB_CARD_TEXT_I18N.footerText,
  },
  chartColors: [
    {
      name: SMB_ENGAGEMENT_CHART_NAME,
      value: PRIMARY_CHART_COLOUR,
    },
  ],
  emptyChart: {
    subtitle: ENGAGED_SMB_CARD_TEXT_I18N.emptySubtitle,
    footer: ENGAGED_SMB_CARD_TEXT_I18N.emptyFooterText,
    icon: 'email',
  },
};

export const BILLABLE_ACCOUNT_CONFIG = {
  header: {
    title: BILLABLE_ACCOUNT_CARD_TEXT_I18N.title,
    subtitle: BILLABLE_ACCOUNT_CARD_TEXT_I18N.subtitle,
  },
  footer: {
    text: BILLABLE_ACCOUNT_CARD_TEXT_I18N.footerText,
  },
  chartColors: [
    {
      name: BILLABLE_ITEMS_CHART_NAME,
      value: COLORS.green,
    },
  ],
};

export const BILLABLE_ACCOUNT_BASKET_SIZE_CONFIG = {
  header: {
    title: BILLABLE_ACCOUNT_BASKET_SIZE_CARD_TEXT_I18N.title,
    subtitle: BILLABLE_ACCOUNT_BASKET_SIZE_CARD_TEXT_I18N.subtitle,
    info: BILLABLE_ACCOUNT_BASKET_SIZE_CARD_TEXT_I18N.info,
    valueDescriptor: BILLABLE_ACCOUNT_BASKET_SIZE_CARD_TEXT_I18N.valueDescriptor,
    action: {
      text: GROSS_REV_CARD_TEXT_I18N.actionText,
      url: 'https://support.vendasta.com/hc/en-us/articles/*************',
    },
  },
  footer: {
    text: BILLABLE_ACCOUNT_BASKET_SIZE_CARD_TEXT_I18N.footerText,
  },
  chartColors: [
    {
      name: BILLABLE_ITEMS_BASKET_SIZE_CHART_NAME,
      value: PRIMARY_CHART_COLOUR,
    },
  ],
  emptyChart: {
    subtitle: BILLABLE_ACCOUNT_BASKET_SIZE_CARD_TEXT_I18N.emptySubtitle,
    footer: BILLABLE_ACCOUNT_BASKET_SIZE_CARD_TEXT_I18N.emptyFooterText,
    icon: 'store',
  },
};

export const TOP_PRODUCT_CARD_CONFIG = {
  header: {
    title: TOP_BILLED_PRODUCT_CARD_TEXT_I18N.title,
    subtitle: TOP_BILLED_PRODUCT_CARD_TEXT_I18N.subtitle,
  },
  footer: {
    text: TOP_BILLED_PRODUCT_CARD_TEXT_I18N.footerText,
  },
  bodySubtitle: {
    text: TOP_BILLED_PRODUCT_CARD_TEXT_I18N.bodySubtitle,
  },
};

export const ARPPA_CHART_CONFIG = {
  header: {
    title: ARPPA_CARD_TEXT_I18N.title,
    subtitle: ARPPA_CARD_TEXT_I18N.subtitle,
    info: ARPPA_CARD_TEXT_I18N.info,
    valueDescriptor: ARPPA_CARD_TEXT_I18N.valueDescriptor,
    action: {
      text: GROSS_REV_CARD_TEXT_I18N.actionText,
      url: 'https://support.vendasta.com/hc/en-us/articles/*************',
    },
  },
  footer: {
    text: ARPPA_CARD_TEXT_I18N.footerText,
  },
  chartColors: [
    {
      name: ARPPA_CHART_NAME,
      value: PRIMARY_CHART_COLOUR,
    },
  ],
  emptyChart: {
    subtitle: ARPPA_CARD_TEXT_I18N.emptySubtitle,
    footer: ARPPA_CARD_TEXT_I18N.emptyFooterText,
    icon: 'edit',
  },
};

export const MONTHLY_RECURRING_REVENUE_CONFIG = {
  header: {
    title: MONTHLY_RECURRING_REVENUE_CARD_TEXT_I18N.title,
    subtitle: MONTHLY_RECURRING_REVENUE_CARD_TEXT_I18N.subtitle,
  },
  footer: {
    text: MONTHLY_RECURRING_REVENUE_CARD_TEXT_I18N.footerText,
  },
};

export const TOTAL_ACCOUNTS_CONFIG = {
  header: {
    title: TOTAL_ACCOUNTS_CARD_TEXT_I18N.title,
    subtitle: TOTAL_ACCOUNTS_CARD_TEXT_I18N.subtitle,
  },
  footer: {
    text: TOTAL_ACCOUNTS_CARD_TEXT_I18N.footerText,
  },
};

export const TOP_ARPPA_CONTRIBUTORS_CONFIG = {
  header: {
    title: TOP_ARPPA_CONTRIBUTORS_CARD_TEXT_I18N.title,
    subtitle: TOP_ARPPA_CONTRIBUTORS_CARD_TEXT_I18N.subtitle,
    info: TOP_ARPPA_CONTRIBUTORS_CARD_TEXT_I18N.info,
  },
  footer: {
    text: TOP_ARPPA_CONTRIBUTORS_CARD_TEXT_I18N.footerText,
  },
  chartColors: [
    {
      name: TOP_ARPPA_CONTRIBUTORS_CHART_NAME,
      value: PRIMARY_CHART_COLOUR,
    },
  ],
  axisLabels: {
    x: TOP_ARPPA_CONTRIBUTORS_CARD_TEXT_I18N.xAxisLabel,
    y: TOP_ARPPA_CONTRIBUTORS_CARD_TEXT_I18N.yAxisLabel,
  },
  chartSubtitle: TOP_ARPPA_CONTRIBUTORS_CARD_TEXT_I18N.chartSubtitle,
};

export const SALESPERSON_ENGAGEMENT_CONFIG = {
  header: {
    title: ENGAGED_SALESPERSON_CARD_TEXT_I18N.title,
    subtitle: ENGAGED_SALESPERSON_CARD_TEXT_I18N.subtitle,
    info: ENGAGED_SALESPERSON_CARD_TEXT_I18N.info,
    valueDescriptor: ENGAGED_SALESPERSON_CARD_TEXT_I18N.valueDescriptor,
    action: {
      text: GROSS_REV_CARD_TEXT_I18N.actionText,
      url: 'https://support.vendasta.com/hc/en-us/articles/*************',
    },
  },
  chartColors: [
    {
      name: SALESPERSON_ENGAGEMENT_CHART_NAME,
      value: PRIMARY_CHART_COLOUR,
    },
  ],
  emptyChart: {
    subtitle: ENGAGED_SALESPERSON_CARD_TEXT_I18N.emptySubtitle,
    footer: ENGAGED_SALESPERSON_CARD_TEXT_I18N.emptyFooterText,
    icon: 'person_add',
  },
};

export const GROSS_REV_CHART_CONFIG = {
  header: {
    title: GROSS_REV_CARD_TEXT_I18N.title,
    subtitle: GROSS_REV_CARD_TEXT_I18N.subtitle,
    info: GROSS_REV_CARD_TEXT_I18N.info,
    valueDescriptor: GROSS_REV_CARD_TEXT_I18N.valueDescriptor,
    action: {
      text: GROSS_REV_CARD_TEXT_I18N.actionText,
      url: 'https://support.vendasta.com/hc/en-us/articles/*************',
    },
  },
  footer: {
    text: GROSS_REV_CARD_TEXT_I18N.footerText,
  },
  chartColors: [
    {
      name: GROSS_REV_CHART_NAME,
      value: PRIMARY_CHART_COLOUR,
    },
  ],
  emptyChart: {
    subtitle: GROSS_REV_CARD_TEXT_I18N.emptySubtitle,
    footer: GROSS_REV_CARD_TEXT_I18N.emptyFooterText,
    icon: 'group_add',
  },
};

export const RETENTION_RATE_CHART_CONFIG = {
  header: {
    title: SMB_RETENTION_RATE_CARD_TEXT_I18N.title,
    subtitle: SMB_RETENTION_RATE_CARD_TEXT_I18N.subtitle,
    info: SMB_RETENTION_RATE_CARD_TEXT_I18N.info,
    valueDescriptor: SMB_RETENTION_RATE_CARD_TEXT_I18N.valueDescriptor,
    action: {
      text: GROSS_REV_CARD_TEXT_I18N.actionText,
      url: 'https://support.vendasta.com/hc/en-us/articles/*************',
    },
  },
  footer: {
    text: SMB_RETENTION_RATE_CARD_TEXT_I18N.footerText,
  },
  chartColors: [
    {
      name: SMB_RETENTION_CHART_NAME,
      value: PRIMARY_CHART_COLOUR,
    },
    {
      name: LINE_CHART_PROP,
      value: TRANSPARENT_CHART_COLOUR,
    },
  ],
  emptyChart: {
    subtitle: SMB_RETENTION_RATE_CARD_TEXT_I18N.emptySubtitle,
    footer: SMB_RETENTION_RATE_CARD_TEXT_I18N.emptyFooterText,
    icon: 'add_shopping_cart',
  },
};

export const BILLABLE_ACCOUNTS_CARD_CONFIG = {
  header: {
    title: BILLABLE_ACCOUNTS_CARD_TEXT_I18N.title,
  },
  footer: {
    text: BILLABLE_ACCOUNTS_CARD_TEXT_I18N.footerText,
  },
  subTitle: {
    text: BILLABLE_ACCOUNTS_CARD_TEXT_I18N.subtitle,
  },
  totalAccounts: {
    text: BILLABLE_ACCOUNTS_CARD_TEXT_I18N.totalAccounts,
  },
};

export const ACCOUNT_RETENTION_BY_TAXONOMY_TOP6_CHART_CONFIG = {
  header: {
    title: ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CARD_TEXT_I18N.title,
  },
  footer: {
    text: ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CARD_TEXT_I18N.footerText,
  },
  subTitle: {
    text: ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CARD_TEXT_I18N.subtitle,
  },
  chartColors: [
    {
      name: ACCOUNT_RETENTION_RATE_BY_TAXONOMY_TOP6_CHART_NAME,
    },
  ],
};

export const ACCOUNT_RETENTION_BY_TAXONOMY_BOTTOM6_CHART_CONFIG = {
  header: {
    title: ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CARD_TEXT_I18N.title,
  },
  footer: {
    text: ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CARD_TEXT_I18N.footerText,
  },
  subTitle: {
    text: ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CARD_TEXT_I18N.subtitle,
  },
  chartColors: [
    {
      name: ACCOUNT_RETENTION_RATE_BY_TAXONOMY_BOTTOM6_CHART_NAME,
    },
  ],
};

export const PRODUCT_ENGAGEMENT_CHART_CONFIG = {
  header: {
    title: SMB_PRODUCT_ENGAGEMENT_CARD_TEXT_I18N.title,
    subtitle: SMB_PRODUCT_ENGAGEMENT_CARD_TEXT_I18N.subtitle,
  },
  footer: {
    text: SMB_PRODUCT_ENGAGEMENT_CARD_TEXT_I18N.footerText,
  },
  subTitle: {
    text: SMB_PRODUCT_ENGAGEMENT_CARD_TEXT_I18N.subTitle,
  },
  chartColors: [
    {
      name: SMB_PRODUCT_ENGAGEMENT_CHART_NAME,
    },
  ],
};

export const ENGAGED_ACCOUNTS_BREAKDOWN_CHART_CONFIG = {
  header: {
    title: SMB_ENGAGEMENT_BREAKDOWN_CARD_TEXT_I18N.title,
    subtitle: SMB_ENGAGEMENT_BREAKDOWN_CARD_TEXT_I18N.subtitle,
  },
  footer: {
    text: SMB_ENGAGEMENT_BREAKDOWN_CARD_TEXT_I18N.footerText,
  },
  subTitle: {
    text: SMB_ENGAGEMENT_BREAKDOWN_CARD_TEXT_I18N.subTitle,
  },
  chartColors: [
    {
      name: SMB_ENGAGEMENT_BREAKDOWN_CHART_NAME,
    },
  ],
};

export const TOP_APP_CHART_CONFIG = {
  header: {
    title: TOP_APP_CHART_CARD_TEXT_I18N.title,
    subtitle: TOP_APP_CHART_CARD_TEXT_I18N.subtitle,
  },
};

export const TOP_MARKET_CHART_CONFIG = {
  header: {
    title: TOP_MARKET_CHART_CARD_TEXT_I18N.title,
    subtitle: TOP_MARKET_CHART_CARD_TEXT_I18N.subtitle,
  },
};
