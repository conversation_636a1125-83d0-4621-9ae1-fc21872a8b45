import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { DOCUMENT, Location } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  HostListener,
  inject,
  Inject,
  NgZone,
  OnDestroy,
  OnInit,
  QueryList,
  TemplateRef,
  ViewChildren,
} from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIconRegistry } from '@angular/material/icon';
import { MatDrawerMode } from '@angular/material/sidenav';
import { DomSanitizer, Title } from '@angular/platform-browser';
import { ActivatedRoute, RouteConfigLoadEnd, RouteConfigLoadStart, Router } from '@angular/router';
import { CustomUserDropdownItem } from '@galaxy/atlas';
import { Environment, EnvironmentService } from '@galaxy/core';
import { SubscriptionTierInterface, SubscriptionTiersService, UITheme, WhitelabelService } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { getLocale } from '@vendasta/galaxy/i18n';
import { IAMService, User, UserIdentifier } from '@vendasta/iamv2';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import moment from 'moment';
import {
  BehaviorSubject,
  combineLatest,
  debounce,
  distinctUntilChanged,
  filter,
  interval,
  map,
  Observable,
  ReplaySubject,
  shareReplay,
  startWith,
  Subscription,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { devServer } from '../globals';
import { initializeWootric } from '../lib/wootric';
import { AnalyticsService } from './analytics';
import { AppConfig, AppConfigService, Product } from './app-config.service';
import type { MenuItem } from './app-nav-item.component';
import { FailedPaymentsService } from './billing/failed-payments/failed-payments.service';
import { AccessService, Feature, StubAccessService } from './core/access';
import { Views } from './core/access/interface';
import { BillingFeatureService } from './core/billing/billing-feature.service';
import { FeatureFlags } from './core/features';
import { NavigationService } from './core/navigation-service.service';
import { PartnerService } from './core/partner.service';
import { PartnerServiceV2 } from './core/partner.service.v2';
import { PartnerAjaxResponse } from './core/partner/partner-ajax.response';
import { UnifiedTOSService } from './core/tos-unified.service';
import { UrlService } from './core/url.service';
import { uiThemeFromEnum, uiThemeToEnum, UserBrandingService } from './core/user-branding.service';
import { HelpSideDrawerTemplateRefService } from './help-center/help-side-drawer/help-side-drawer-template-ref.service';
import {
  AccountGroupSearcherComponent,
  OmniSearchComponent,
  OmniSearchService,
  PartnerOverviewComponent,
  PartnerSwitcherComponent,
  UserSwitcherComponent,
} from './omni-search';
import { Intent } from './omni-search/intent';
import { PartnerTrialStoreService, TrialStatus } from './core/partner';
import { StyleService } from '@vendasta/galaxy/style-service';
import { GalaxyTheme } from '@vendasta/galaxy/style-service/src/style.service';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';

declare let window: any;
declare let initializeHotJar: any;
declare let zE: any; // Zendesk wrapping function
declare let Canny: any; // canny wrapping function

const DEFAULT_NAV_THEME = 'dark';
const DEFAULT_FAVICON_URL = 'favicon.ico';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: false,
})
export class AppComponent implements OnInit, AfterViewInit, OnDestroy {
  private styleService = inject(StyleService);
  private aiIconService = inject(GalaxyAiIconService);

  constructor(
    private failedPaymentsService: FailedPaymentsService, // Not used here, but needs to be instantiated at app root
    private zone: NgZone,
    private router: Router,
    private unifiedTosService: UnifiedTOSService,
    private analytics: AnalyticsService, // Not used here, but needs to be instantiated at app root
    private readonly configService: AppConfigService,
    private billingFeatureService: BillingFeatureService, // Not used here, but needs to be instantiated at app root
    private environmentService: EnvironmentService,
    private productAnalytics: ProductAnalyticsService,
    public dialog: MatDialog,
    private omniSearchService: OmniSearchService,
    @Inject(StubAccessService) private access: AccessService,
    private matIconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
    private translate: TranslateService,
    private readonly whitelabelService: WhitelabelService,
    private readonly partnerService: PartnerServiceV2,
    private route: ActivatedRoute,
    private readonly location: Location,
    private readonly partnerServiceV1: PartnerService,
    private readonly breakpointObserver: BreakpointObserver,
    private navService: NavigationService, // Not used here, but needs to be instantiated at app root
    private helpSideDrawerTemplateRefService: HelpSideDrawerTemplateRefService,
    private userBrandingService: UserBrandingService,
    private urlService: UrlService,
    @Inject(FeatureFlags.INBOX_AI_ATLAS_LINK)
    public readonly inboxAIFeatureFlag$: Observable<boolean>,
    @Inject(FeatureFlags.MEETINGS_AUTOMATIC_ANALYSIS)
    public readonly recallFeatureFlag$: Observable<boolean>,
    @Inject(FeatureFlags.FULLSCREEN_INBOX_VIEW)
    public readonly fullscreenInboxViewFeatureFlag$: Observable<boolean>,
    private readonly iamService: IAMService,
    private cdr: ChangeDetectorRef,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly title: Title,
    private partnerTrialStoreService: PartnerTrialStoreService,
  ) {
    // expose this component's navtheme and logo methods to the global scope
    window.ngAppComponent = {
      zone: this.zone,
      setWhiteLabelColor: (value: string) => this.setWhiteLabelColor(value),
      setNavTheme: (value: string) => this.setNavTheme(value),
      setWhiteLabelLogo: (value: string) => this.setWhiteLabelLogo(value),
      iframeClickHandler: () => this.iframeClickHandler(),
      revealOmnisearch: (event: any) => this.revealOmnisearch(event),
      ngRouter: this.router,
      component: this,
    };

    moment.locale(getLocale());

    // This is typically populated by here, but not when the app-config load failed and redirected within angular
    // to local login.
    if (this.configService.config) {
      this.partnerId = this.configService.config.partnerId;
    }

    this.initializeTitleAndFavIcon();
  }

  private subscriptions: Subscription[] = [];

  @ViewChildren('helpCenter') helpCenter: QueryList<TemplateRef<any>>;

  readonly helpCenterTemplateId = 'helpCenter';

  omnisearchDialogRef: MatDialogRef<OmniSearchComponent>;

  content: string;
  userOptions: Array<MenuItem> = [];

  hasAccessToMarkets$ = this.access.hasAccessToFeature(Feature.markets);

  partner$ = this.partnerServiceV1.partnerConfig$;

  configLoading$ = this.configService.loading$;
  private checkingPaymentRequired$$ = new BehaviorSubject<boolean>(true);
  pageLoading$: Observable<boolean>;
  loadingLazyModule = false;

  accountExecutive: string;
  userCanCustomizeWhitelabel: boolean;
  userCanAccessOmnisearch: boolean;
  subscriptionTier: string;

  isSuperAdminRoute$ = this.urlService.isSuperAdminRoute$;
  currentUrl$ = this.urlService.currentUrl$;

  hasWhitelabel$: Observable<boolean>;
  useDarkNavHeader$: Observable<boolean>;
  vaNavTheme$: Observable<string>;

  userEmail: string;
  partnerId: string;
  showAtlasPidSelector$: Observable<boolean>;
  private viewMode$$ = new ReplaySubject<MatDrawerMode>();
  readonly viewMode$ = this.viewMode$$.asObservable();

  menuItems: CustomUserDropdownItem[] = [];
  nonSuperadminTabs$: Observable<MenuItem[]>;
  navItems$: Observable<MenuItem[]>;
  activeMenuId$: Observable<string>;

  partnerName$ = this.configService.config$.pipe(map((config) => config.partnerName));
  partnerId$ = this.configService.config$.pipe(map((config) => config.partnerId));
  atlasTitle$ = combineLatest([this.isSuperAdminRoute$, this.partnerName$, this.partnerId$]).pipe(
    map(([superadmin, partnerName, partnerId]) => (superadmin ? 'Superadmin' : `${partnerName} (${partnerId})`)),
  );

  isMobile$: Observable<boolean>;

  helpDrawerSelected$ = this.helpSideDrawerTemplateRefService.open$.pipe(
    map((open) => (open ? 'atlas-navbar__item-selected' : '')),
  );

  // Don't show the nav if current page is user-onboarding
  isOnboardingUrl$ = this.currentUrl$.pipe(map((url) => url.startsWith('/user-onboarding')));

  hasViewAccess$ = this.access
    .hasAccessToViews([
      Views.assignedSalespersonCard,
      Views.helpCenter,
      Views.newReleases,
      Views.inboxAi,
      Views.inboxFeature,
    ])
    .pipe(shareReplay(1));
  hasViewAccessForSalesContactCard$ = this.hasViewAccess$.pipe(
    map((access) => access.get(Views.assignedSalespersonCard)),
  );
  hasViewAccessForHelpCenter$ = this.hasViewAccess$.pipe(map((access) => access.get(Views.helpCenter)));
  hasViewAccessForNewReleases$ = this.hasViewAccess$.pipe(map((access) => access.get(Views.newReleases)));
  hasViewAccessForInboxAI$ = this.hasViewAccess$.pipe(
    map((access) => access.get(Views.inboxAi) && access.get(Views.inboxFeature)),
  );
  readonly hasViewAccessForInboxFeature$ = this.hasViewAccess$.pipe(
    map((access) => access.get(Views.inboxFeature)),
    map((hasAccess) => hasAccess),
  );

  protected disableInboxFeature$ = this.hasViewAccessForInboxFeature$.pipe(map((hasAccess) => !hasAccess));

  /** Gets more information about the current user. */
  private readonly user$: Observable<User> = this.configService.config$.pipe(
    map((config) => config.unifiedUserId || config.userId),
    switchMap((id) => this.iamService.getUser(new UserIdentifier({ userId: id }))),
  );

  static getCurrentPathForNavSelection(currentPath: string): string {
    // get current path when embedded in vstatic url
    if (currentPath.startsWith('/vstatic')) {
      const regex = new RegExp('[\\?&]embed=([^&#]*)');
      const results = regex.exec(currentPath);
      const embedRoute = results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
      currentPath = embedRoute ? embedRoute : currentPath;
    } else {
      const currentPathParts = currentPath.split('?');
      currentPath = currentPathParts[0];
      // Parse restricted page path to get path to the restricted page
      if (currentPath.startsWith('/restricted/') && currentPathParts.length > 1) {
        const queryParts = currentPathParts[1].split('=');
        if (queryParts.length > 1) {
          currentPath = decodeURIComponent(queryParts[1]);
        }
      }
    }
    if (!currentPath.startsWith('/') && currentPath !== '') {
      currentPath = '/' + currentPath;
    }
    return currentPath;
  }

  // Grab all of the jinja variables that base handler dumped on global.html and pull the block
  // content into the angular app
  ngOnInit(): void {
    this.styleService.update('partner-center-client', GalaxyTheme.Light);
    this.viewMode$$.next(this.isLargeScreen());

    this.nonSuperadminTabs$ = this.configService.config$.pipe(map((config) => getNavItems(config.products)));
    this.navItems$ = combineLatest([this.nonSuperadminTabs$, this.isSuperAdminRoute$]).pipe(
      map(([nonSuperadminTabs, superadmin]) => {
        return superadmin ? superadminTabs : nonSuperadminTabs;
      }),
      distinctUntilChanged(),
      shareReplay(1),
    );

    this.activeMenuId$ = combineLatest([this.navItems$, this.currentUrl$]).pipe(
      map(([navItems, url]) => this.setSelectedNavItemForPath(navItems, url)),
    );

    this.subscriptions.push(
      // If it is taking longer than 0.5s to load the lazy module, show the loading indicator
      this.router.events
        .pipe(
          filter((event) => event instanceof RouteConfigLoadStart || event instanceof RouteConfigLoadEnd),
          debounce((e) => {
            let delay = 500; // 500ms
            if (e instanceof RouteConfigLoadEnd) {
              delay = 0;
            }
            return interval(delay);
          }),
        )
        .subscribe((e) => {
          this.loadingLazyModule = e instanceof RouteConfigLoadStart;
        }),
    );

    this.pageLoading$ = this.configLoading$.pipe(
      map((configLoading) => {
        return configLoading;
      }),
    );

    if (this.environmentService.getEnvironment() === Environment.PROD) {
      initializeHotJar();
      this.partnerServiceV1.subscriptionTier$.subscribe((s) => {
        if (SubscriptionTiersService.isFree(s) && Object.prototype.hasOwnProperty.call(window, 'hj')) {
          // tslint:disable-next-line
          window['hj']('trigger', 'is-free-user');
        }
      });
      this.configService.config$.subscribe((config) => setHotJarIdentity(config));
    }

    this.subscriptions.push(
      this.configService.config$
        .pipe(
          tap((config) => {
            if (config.wootricEnabled) {
              initializeWootric({
                email: config.userEmail,
                createdAt: config.userCreated,
                accountToken: 'NPS-652e0546',
                persona: 'Partner',
                success: config.accountExecutive,
                partnerId: config.partnerId,
                partnerName: config.partnerName,
                userMarket: config.userMarket,
              });
            }

            if (!devServer) {
              this.productAnalytics.initialize({
                environment: this.environmentService.getEnvironment(),
                projectUUID: 'a4f6ec25-67a7-4edf-972a-29e80320f67f',
                postHogID: 'F-GqYmiH8D1O6ojHLctu7SWCr7YovJtv9nbTv_-S-uo',
                projectName: 'partner-center-client',
                partner: {
                  pid: config.partnerId,
                  name: config.partnerName,
                  pidChanges$: this.configService.config$.pipe(map((c) => c.partnerId)),
                },
              });
            }

            this.userEmail = config.userEmail;
            this.accountExecutive = config.accountExecutive;
            this.userCanCustomizeWhitelabel = config.userCanCustomizeWhitelabel;
            this.userCanAccessOmnisearch = config.userCanAccessAccounts || config.isSuperAdmin;
            this.registerSuperadminOmnisearchIntent(config, {
              title: 'Switch Partners',
              description: "Switch Partner Center's context to a given PID.",
              shortcut: 'p',
              itemOfIntent: null,
              callback: () => {
                this.showPartnerSwitcherDialog();
              },
            });
            this.registerSuperadminOmnisearchIntent(config, {
              title: 'Partner Overview',
              description: 'View the Partner Details',
              shortcut: 'o',
              itemOfIntent: null,
              callback: () => {
                this.showPartnerOverviewDialog();
              },
            });
            this.registerPartnerOmnisearchIntent({
              title: 'Find a Business',
              description: 'Go to the account details page for a business.',
              shortcut: 'b',
              itemOfIntent: null,
              callback: () => {
                this.showSwitchLocationDialog();
              },
            });
            this.registerPartnerOmnisearchIntent({
              title: 'Find a User',
              description: 'Search for a business user or salesperson and impersonate them.',
              shortcut: 'u',
              itemOfIntent: null,
              callback: () => this.showImpersonateUserDialog(),
            });
            this.registerSuperadminOmnisearchIntent(config, {
              title: 'Return to Superadmin',
              description: 'Go back to superadmin.',
              shortcut: 's',
              routerUrl: '/superadmin/partners',
            });
            this.buildUserOptions();
          }),
          switchMap((config) => {
            return combineLatest([
              this.partnerServiceV1.partnerConfig$,
              this.partnerServiceV1.subscriptionTier$,
              this.user$,
              this.partnerTrialStoreService.partnerTrial$,
            ]).pipe(
              tap(([partnerConfig, subscription, user, trial]) => {
                this.subscriptionTier = subscription.id;
                this.productAnalytics.trackProperties({
                  isOnTrial: trial.status === TrialStatus.ACTIVE,
                  subscriptionTier: subscription.id,
                });
                this.zendeskInit(config, partnerConfig, subscription);
                this.unifiedTosService.getTermsOfServiceData(subscription);
                if (this.environmentService.getEnvironment() === Environment.PROD) {
                  this.initializeUserOnboarding(config, subscription, user);
                }
                this.initializeCanny();
              }),
            );
          }),
        )
        .subscribe(),
    );

    this.hasWhitelabel$ = this.access.hasAccessToFeature(Feature.whitelabel).pipe(startWith(true));

    this.useDarkNavHeader$ = this.userBrandingService.branding$.pipe(
      map(
        (branding) => branding.uiTheme === UITheme.UI_THEME_DARK && ![undefined, ''].includes(branding.darkModeLogoUrl),
      ),
    );

    this.vaNavTheme$ = combineLatest([
      this.hasWhitelabel$,
      this.userBrandingService.branding$,
      this.isSuperAdminRoute$,
    ]).pipe(
      map(([hasWhitelabel, branding, superadminRoute]) =>
        hasWhitelabel && !superadminRoute
          ? 'glxy-nav--platform-' + uiThemeFromEnum(branding.uiTheme) + '-theme'
          : 'glxy-nav--platform-' + DEFAULT_NAV_THEME + '-theme',
      ),
    );

    this.showAtlasPidSelector$ = this.configService.config$.pipe(
      map((config: AppConfig) => config.isSuperAdmin || config.isImpersonated),
      shareReplay(1),
    );

    this.matIconRegistry.addSvgIcon(
      'concierge',
      this.sanitizer.bypassSecurityTrustResourceUrl('/static/images/products/task-manager.svg'),
    );

    this.matIconRegistry.addSvgIcon(
      'automation',
      this.sanitizer.bypassSecurityTrustResourceUrl('/static/images/products/automation.svg'),
    );

    this.matIconRegistry.addSvgIcon(
      'facebook-f-logo-circle-1024',
      this.sanitizer.bypassSecurityTrustResourceUrl('/static/images/facebook/facebook-f-logo-circle-1024.svg'),
    );

    this.matIconRegistry.addSvgIcon(
      'hotness',
      this.sanitizer.bypassSecurityTrustResourceUrl('/static/images/marketing-campaign/hotness.svg'),
    );

    this.isMobile$ = this.breakpointObserver.observe([Breakpoints.XSmall]).pipe(map((result) => result.matches));
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());

    // close the change log if it happens to be open
    Canny('closeChangelog');
  }

  ngAfterViewInit(): void {
    this.helpCenter.changes.pipe(startWith(this.helpCenter), take(1)).subscribe((ref) => {
      this.helpSideDrawerTemplateRefService.registerTemplate(this.helpCenterTemplateId, ref.first);
    });
  }

  private registerSuperadminOmnisearchIntent(config: AppConfig, intent: Intent): void {
    if (config.isSuperAdmin || config.isImpersonated) {
      this.omniSearchService.registerIntent(intent);
    }
  }

  private registerPartnerOmnisearchIntent(intent: Intent): void {
    if (this.configService.config.userCanAccessAccounts) {
      this.omniSearchService.registerIntent(intent);
    }
  }

  private initializeTitleAndFavIcon() {
    this.userBrandingService.branding$.pipe(take(1)).subscribe((branding) => {
      this.title.setTitle(branding?.name || this.translate.instant('PAGE.DEFAULT_TITLE'));

      const favIcon = this.document.getElementById('app-favicon');
      const faviconUrl = branding?.faviconUrl || DEFAULT_FAVICON_URL;
      favIcon.setAttribute('href', `${faviconUrl}?v=${Date.now()}`);
    });
  }

  // This is the Zendesk initialization so the user can be known by support.
  private zendeskInit(
    config: AppConfig,
    partnerConfig: PartnerAjaxResponse,
    subscription: SubscriptionTierInterface,
  ): void {
    const chatTags = [config.partnerId, subscription.name];
    if (partnerConfig.priority_chat_support === true) {
      chatTags.push('PriorityChat');
    }
    zE(() => {
      let prefill = config.userFirstName;
      if (config.userLastName) {
        prefill = prefill + ' ' + config.userLastName;
      }
      zE('webWidget', 'chat:addTags', chatTags);
      zE('webWidget', 'prefill', { name: { value: prefill }, email: { value: config.userEmail } });
    });
  }

  initializeCanny(): void {
    this.cdr.detectChanges(); // Force change detection
    Canny('initChangelog', {
      appID: '636d173a834b780c31120d3b',
      position: 'bottom',
      align: 'left',
      theme: 'light',
    });
  }

  private initializeUserOnboarding(config: AppConfig, subscription: SubscriptionTierInterface, user: User): void {
    // Determine the roles that the user can perform.
    // This will help Chameleon appropriately segment users and apply role-based filters.
    const permissions: { [role: string]: boolean } = {};
    Object.keys(user.roles).forEach((role) => (permissions[role] = true));

    // Pass important user attributes to Chameleon so segmentation can be done more accurately.
    window.chmln.identify(user.userId, {
      created: config.userCreated,
      isAdmin: config.userIsAdmin,
      project: 'partner-center-client',
      role_permissions: permissions,

      company: {
        uid: config.partnerId,
        created: config.partnerCreated,
        name: config.partnerName,
        plan: subscription.id,
        version: subscription.tierVersion,
      },
    });
  }

  public setSelectedNavItemForPath(navItems: MenuItem[], currentPath: string): string {
    currentPath = AppComponent.getCurrentPathForNavSelection(currentPath);
    if (currentPath.startsWith('/businesses/accounts')) {
      currentPath = currentPath.replace('/businesses/accounts', '/manage-accounts');
    }
    if (currentPath.startsWith('/billing')) {
      if (currentPath.startsWith('/billing/AG-')) {
        currentPath = '/manage-accounts';
      } else if (currentPath.startsWith('/billing/payments')) {
        // For the Billing payments tab we want to set the activeMenuId to `payments`
        currentPath = '/billing/payments';
      } else {
        // For all other billing paths we want to set activeMenuId to 'administration'
        currentPath = '/settings';
      }
    }
    if (currentPath.startsWith('/action-lists')) {
      currentPath = '/action-lists/manage';
    }
    if (currentPath.startsWith('/task-manager/groups')) {
      currentPath = '/task-manager/users/manage';
    }

    if (currentPath.startsWith('/automations/config/smb/templates')) {
      currentPath = '/settings';
    }

    let allNavItems = navItems;
    const nestedNavItems = navItems.filter((item) => item.nestedItems).map((item) => item.nestedItems);
    if (nestedNavItems.length > 0) {
      allNavItems = allNavItems.concat(nestedNavItems.reduce((a, b) => a.concat(b)));
    }

    let activeMenuId = '';
    allNavItems
      .filter((item) => Boolean(item.url) && (currentPath + '/').startsWith(item.url))
      .sort((a, b) => a.url.length - b.url.length)
      .map((item) => (activeMenuId = item.menuId));
    return activeMenuId;
  }

  /**
   * @deprecated
   * Does nothing. Superseeded by the nav theme. Still called by non angular pages.
   */
  public setWhiteLabelColor(_value: string): void {
    /** pass */
  }

  /** setNavTheme */
  public setNavTheme(value: string): void {
    this.userBrandingService.setUiTheme(uiThemeToEnum(value));
  }

  /** setWhiteLabelLogo */
  public setWhiteLabelLogo(value: string): void {
    this.userBrandingService.setLogoUrl(value);
  }

  /** Builds the user options in the user tab in the navbar */
  private buildUserOptions(): void {
    this.menuItems = [];
    let partner_id = '';
    if (this.configService.config.partnerId) {
      partner_id = ' (' + this.configService.config.partnerId + ')';
    }
    this.userOptions = [
      {
        rightIcon: 'fec-icon-dropdown',
        label: this.configService.config.userFirstName + ' ' + this.configService.config.userLastName + partner_id,
        menuId: '',
        icon: 'fec-icon-user',
        nestedItems: [],
        expanded: false,
      },
    ];

    for (const option of this.configService.config.userOptions) {
      this.userOptions[0].nestedItems.push({
        label: option.label,
        url: option.url,
        menuId: option.menuId,
        expanded: false,
      });
      if (option.menuId === 'edit-profile') {
        this.menuItems.push({
          label: option.label,
          route: option.url,
        });
      } else {
        this.menuItems.push({
          label: option.label,
          url: option.url,
        });
      }
    }
  }

  public iframeClickHandler(): void {
    if (this.userOptions.length > 0 && this.userOptions[0].expanded) {
      this.userOptions[0].expanded = false;
    }
  }

  public revealOmnisearch(e: any): void {
    this.showOmniSearch(e);
  }

  @HostListener('window:keydown', ['$event'])
  showOmniSearch(event: any): void {
    if (!this.userCanAccessOmnisearch) {
      return;
    }

    const tag = event.target.tagName.toLowerCase();
    if (event.key === '/' && tag !== 'input' && tag !== 'textarea') {
      if (this.omnisearchDialogRef === null || this.omnisearchDialogRef === undefined) {
        this.omnisearchDialogRef = this.dialog.open(OmniSearchComponent, { width: '600px' });
        this.omnisearchDialogRef.afterClosed().subscribe(() => {
          this.omnisearchDialogRef = null;
        });
      } else {
        this.omnisearchDialogRef.close();
      }
    }
  }

  showSwitchLocationDialog(): void {
    this.dialog.open(AccountGroupSearcherComponent, { width: '600px' });
  }

  showImpersonateUserDialog(): void {
    this.dialog.open(UserSwitcherComponent, { width: '600px' });
  }

  showPartnerSwitcherDialog(): void {
    this.dialog.open(PartnerSwitcherComponent, { width: '600px' });
  }

  showAtlasPidSelector(): void {
    this.showAtlasPidSelector$.pipe(take(1)).subscribe((show: boolean) => {
      if (show) {
        this.showPartnerSwitcherDialog();
      }
    });
  }

  showPartnerOverviewDialog(): void {
    this.dialog.open(PartnerOverviewComponent, { width: '600px' });
  }

  showHelpCenter(templateId?: string): void {
    this.helpSideDrawerTemplateRefService.toggle(templateId);
  }

  @HostListener('window:resize', ['$event'])
  onResize(): void {
    this.viewMode$$.next(this.isLargeScreen());
  }

  private isLargeScreen(): MatDrawerMode {
    return window.matchMedia('(min-width: 1440px)').matches ? 'side' : 'over';
  }
}

function setHotJarIdentity(config: AppConfig): void {
  try {
    if (config?.unifiedUserId && typeof window !== 'undefined' && window.hj) {
      // userCreated is in seconds
      const createdAdjustedToMilliseconds = config.userCreated * 1000;
      const userCreatedDate = new Date(createdAdjustedToMilliseconds);
      const identityInfo: { created: string; userNotNew?: boolean } = {
        created: userCreatedDate.toISOString(),
      };
      // For HotJar identity we only want to add the userNotNew Property if they were created longer than 1 week ago.
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      if (userCreatedDate.getTime() <= oneWeekAgo.getTime()) {
        identityInfo.userNotNew = true;
      }
      window.hj('identify', config.unifiedUserId, identityInfo);
    }
  } catch (e) {
    console.error(`failed to set HotJar identity: ${e}`);
  }
}

// why is this needed? Cant we just use the nav items from the config service?
function getNavItems(navData: Product[]): Array<MenuItem> {
  const navItems = Array<MenuItem>();
  for (const nav of navData) {
    const navItem: MenuItem = {
      url: nav.link,
      label: nav.title,
      icon: nav.icon,
      menuId: nav.tab_id,
      i18n_key: nav.i18n_key,
      expanded: false,
      ngRoute: !nav.pop_out && nav.ng_route !== false,
      svgIcon: !!nav.svg_icon,
      locked: nav.locked,
      badge: nav.badge,
    };
    if (nav.pop_out) {
      navItem.popOut = nav.pop_out;
    }
    if (nav.right_icon) {
      navItem.rightIcon = nav.right_icon;
    }
    if (nav.nested_tabs) {
      navItem.nestedItems = getNavItems(nav.nested_tabs);
    }
    if (nav.subsection_header) {
      navItem.subsectionHeader = nav.subsection_header;
    }
    navItems.push(navItem);
  }
  return navItems;
}

const superadminTabs = getNavItems([
  {
    locked: false,
    nested_tabs: null,
    link: '/superadmin/partners/',
    title: 'Partners',
    tab_id: 'sa',
    i18n_key: 'NAV_ITEMS.SUPERADMIN.PARTNERS',
    ng_route: true,
    svg_icon: false,
    icon: 'people',
  },
  {
    locked: false,
    nested_tabs: null,
    link: '/superadmin/products/',
    title: 'Manage Products',
    tab_id: 'products_superadmin',
    i18n_key: 'NAV_ITEMS.SUPERADMIN.MANAGE_PRODUCTS',
    ng_route: true,
    svg_icon: false,
    icon: 'apps',
  },
]);
