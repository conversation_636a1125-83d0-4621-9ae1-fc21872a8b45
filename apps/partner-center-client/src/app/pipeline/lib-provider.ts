import { inject, InjectionToken } from '@angular/core';
import { FeatureFlagService, WhitelabelService } from '@galaxy/partner';
import { Salesperson } from '@galaxy/types';
import { ListProductsRequest, MarketplacePackagesApiService } from '@vendasta/marketplace-packages';
import { SalespersonService } from '@vendasta/sales';
import {
  ClientOrigin,
  Opportunity as ApiOpportunity,
  SalesOpportunitiesApiService,
} from '@vendasta/sales-opportunities';
import {
  AccountUrlFunction,
  MARKET_SELECTOR_SERVICE_TOKEN,
  Opportunity,
  OrdersUrl,
  OrdersUrlFunction,
  PartnerOverrideConfig,
  PipelineBoardConfig,
  PipelineTableConfig,
  PipelineTableSavedFiltersConfig,
  UserPipelines,
  UserPipelinesServiceConfig,
  DetailsConfig,
  CompanyUrlFunction,
} from '@vendasta/sales-ui';
import { combineLatest, iif, mergeMap, of } from 'rxjs';
import { catchError, distinctUntilChanged, filter, map, shareReplay, switchMap } from 'rxjs/operators';
import { AppConfigService } from '../app-config.service';
import { FeatureFlags } from '../core/features';
import { SalespeopleService } from '../manage_accounts/salespeople.service';
import { SalesTeamsService } from '../sales/sales-teams.service';
import { StubAccessService } from '../core/access';
import { Views } from '../core/access/interface';

const defaultFallbackCurrency = 'USD';
const pipelineSettingsURL = '/pipeline/settings/';

export const USER_PIPELINES_CONFIG_INJECT_TOKEN = new InjectionToken<UserPipelinesServiceConfig>('desc', {
  providedIn: 'root',
  factory: function (): UserPipelinesServiceConfig {
    const partnerAAConfigService = inject(AppConfigService);
    const partnerId$ = partnerAAConfigService.config$.pipe(
      map((config) => config.partnerId),
      filter((partnerId) => !!partnerId),
      distinctUntilChanged(),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    return {
      partnerId$: partnerId$,
    };
  },
});

export const PIPELINE_BOARD_CONFIG_INJECT_TOKEN = new InjectionToken<PipelineBoardConfig>(
  '[Partner Center Client]: Token for Pipeline Board config',
  {
    providedIn: 'any',
    factory: function (): PipelineBoardConfig {
      const salespeopleService = inject(SalespeopleService);
      const partnerAAConfigService = inject(AppConfigService);
      const partnerConfigService = inject(WhitelabelService);
      const userPipelines = inject(UserPipelines);
      const marketplacePackagesService = inject(MarketplacePackagesApiService);
      const marketSelectorService = inject(MARKET_SELECTOR_SERVICE_TOKEN);
      const featureFlagService = inject(FeatureFlagService);
      const salespersonService = inject(SalespersonService);
      const viewAccessService = inject(StubAccessService);

      const marketId$ = marketSelectorService.selectedMarketId$;

      const partnerId$ = partnerAAConfigService.config$.pipe(
        map((config) => config.partnerId),
        filter((partnerId) => !!partnerId),
        distinctUntilChanged(),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const partnerMarket$ = combineLatest([partnerId$, marketId$]).pipe(
        map(([partnerId, marketId]) => ({
          partnerId,
          marketId,
        })),
        distinctUntilChanged(isPartnerMarketSame),
      );

      const salesCenterCurrency$ = partnerMarket$.pipe(
        switchMap((pm) => {
          return partnerConfigService.getConfiguration(pm.partnerId, pm.marketId);
        }),
        map((config) => {
          return config?.salesConfiguration?.stDefaultDisplayCurrency || defaultFallbackCurrency;
        }),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const products$ = partnerMarket$.pipe(
        switchMap((pm) => {
          const listProductsRequest = <ListProductsRequest>{
            partnerId: pm.partnerId,
            marketId: pm.marketId,
            pageSize: 600,
          };
          return marketplacePackagesService.listProducts(listProductsRequest).pipe(map((res) => res.products));
        }),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const canCreateOrders$ = partnerId$.pipe(
        switchMap((partnerId) => {
          return featureFlagService.batchGetStatus(partnerId, '', [FeatureFlags.NO_ADMIN_ORDER_CREATION]);
        }),
        map((featureFlagStatus) => featureFlagStatus[FeatureFlags.NO_ADMIN_ORDER_CREATION]),
        shareReplay(1),
      );

      const userId$ = partnerAAConfigService.config$.pipe(
        map((config) => config.unifiedUserId),
        filter((userId) => !!userId),
        distinctUntilChanged(),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const salespeople$ = combineLatest([partnerId$, marketId$]).pipe(
        switchMap(([partnerId, marketId]) => salespeopleService.getSalespeople(partnerId, marketId)),
        map((salespeople) => Salesperson.convertSalespeopleFromSalesResponse(salespeople)),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const salesperson$ = combineLatest([partnerId$, userId$]).pipe(
        switchMap(([partnerId, userId]) => salespersonService.getSalespersonByUserId(partnerId, userId)),
        catchError(() => of(null)),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const salespersonId$ = salesperson$.pipe(map((salesperson) => salesperson?.salespersonId || ''));

      const salespersonName$ = salesperson$.pipe(
        map((salesperson) => (salesperson ? `${salesperson.firstName} ${salesperson.lastName}` : '')),
      );

      const pipelineSettingsAccess$ = combineLatest([salesperson$, partnerAAConfigService.config$]).pipe(
        map(([salesperson, config]) => {
          return (
            (config.userIsAdmin && config.userCanAccessSales) ||
            config.isSuperAdmin ||
            salesperson?.isSalesManager === true
          );
        }),
      );

      const hasAccessToAllAccountsInMarket$ = viewAccessService.hasAccessToViews([Views.allOpportunitiesView]).pipe(
        map((views) => views.get(Views.allOpportunitiesView)),
        catchError(() => of(true)),
        shareReplay(1),
      );

      const allSalespeople$ = hasAccessToAllAccountsInMarket$.pipe(
        mergeMap((hasAccess) => iif(() => hasAccess, salespeople$, of([]))),
      );

      return {
        pipelineSettingsURL: pipelineSettingsURL,
        pipelineSettingsAccess$: pipelineSettingsAccess$,
        partnerId$: partnerId$,
        marketId$: marketId$,
        allSalespeople$: allSalespeople$,
        salesTeamsService: inject(SalesTeamsService),
        tagService: inject(SalesOpportunitiesApiService),
        hasAccessToAllAccountsInMarket$: hasAccessToAllAccountsInMarket$,
        initWithLoggedInSalesperson: false,
        products$: products$,
        pipelines$: userPipelines.partnerPipelines$,
        currentPipeline$: userPipelines.getCurrentPipeline(),
        defaultCurrency$: salesCenterCurrency$,
        salespersonId$: salespersonId$,
        salespersonName$: salespersonName$,
        opportunityInfoUrlBuilder: null,
        canCreateOrders$: canCreateOrders$,
        getOrdersUrl: getOrdersUrlFn,
        detailsConfig: detailsConfig,
      };
    },
  },
);

export const PIPELINE_CONFIG_TOKEN = new InjectionToken<PipelineTableConfig>(
  '[Partner Center Client]: Token for Pipeline table config',
  {
    providedIn: 'any',
    factory: function (): PipelineTableConfig {
      const salespeopleService = inject(SalespeopleService);
      const partnerAAConfigService = inject(AppConfigService);
      const partnerConfigService = inject(WhitelabelService);
      const marketplacePackagesService = inject(MarketplacePackagesApiService);
      const marketSelectorService = inject(MARKET_SELECTOR_SERVICE_TOKEN);
      const salespersonService = inject(SalespersonService);
      const viewAccessService = inject(StubAccessService);

      const partnerId$ = partnerAAConfigService.config$.pipe(
        map((config) => config.partnerId),
        filter((partnerId) => !!partnerId),
        distinctUntilChanged(),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const marketId$ = marketSelectorService.selectedMarketId$;

      const partnerMarket$ = combineLatest([partnerId$, marketId$]).pipe(
        map(([partnerId, marketId]) => ({
          partnerId,
          marketId,
        })),
        distinctUntilChanged(isPartnerMarketSame),
      );

      const salespeople$ = combineLatest([partnerId$, marketId$]).pipe(
        distinctUntilChanged(),
        switchMap(([partnerId, marketId]) => salespeopleService.getSalespeople(partnerId, marketId)),
        map((salespeople) => Salesperson.convertSalespeopleFromSalesResponse(salespeople)),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const products$ = partnerMarket$.pipe(
        switchMap((pm) => {
          const listProductsRequest = <ListProductsRequest>{
            partnerId: pm.partnerId,
            marketId: pm.marketId,
            pageSize: 600,
          };
          return marketplacePackagesService.listProducts(listProductsRequest).pipe(map((res) => res.products));
        }),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const salesCenterCurrency$ = partnerMarket$.pipe(
        switchMap((pm) => {
          return partnerConfigService.getConfiguration(pm.partnerId, pm.marketId);
        }),
        map((config) => {
          return config?.salesConfiguration?.stDefaultDisplayCurrency || defaultFallbackCurrency;
        }),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const pipelineSettingsAccess$ = partnerAAConfigService.config$.pipe(
        switchMap((config) => {
          if ((config.userIsAdmin && config.userCanAccessSales) || config.isSuperAdmin) {
            return of(true);
          } else {
            return salespersonService
              .getSalespersonByUserId(config.partnerId, config.unifiedUserId)
              .pipe(map((salesperson) => salesperson?.isSalesManager === true));
          }
        }),
      );

      const hasAccessToAllAccountsInMarket$ = viewAccessService.hasAccessToViews([Views.allOpportunitiesView]).pipe(
        map((views) => views.get(Views.allOpportunitiesView)),
        catchError(() => of(true)),
        shareReplay(1),
      );

      const userId$ = partnerAAConfigService.config$.pipe(
        map((config) => config.unifiedUserId),
        filter((userId) => !!userId),
        distinctUntilChanged(),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const salesperson$ = combineLatest([partnerId$, userId$]).pipe(
        switchMap(([partnerId, userId]) => salespersonService.getSalespersonByUserId(partnerId, userId)),
        catchError(() => of(null)),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const salespersonId$ = salesperson$.pipe(map((salesperson) => salesperson?.salespersonId || ''));

      const salespersonName$ = salesperson$.pipe(
        map((salesperson) => (salesperson ? `${salesperson.firstName} ${salesperson.lastName}` : '')),
      );

      const allSalespeople$ = hasAccessToAllAccountsInMarket$.pipe(
        mergeMap((hasAccess) => iif(() => hasAccess, salespeople$, of([]))),
      );

      return {
        pipelineSettingsURL: pipelineSettingsURL,
        pipelineSettingsAccess$: pipelineSettingsAccess$,
        defaultCurrency$: salesCenterCurrency$,
        salespersonId$: salespersonId$,
        salespersonName$: salespersonName$,
        allSalespeople$: allSalespeople$,
        getAccountDetailsUrl: getAccountDetailsUrlFn,
        getCompanyDetailsUrl: getCompanyDetailsUrlFn,
        initWithLoggedInSalesperson: true,
        partnerId$: partnerId$,
        marketId$: marketId$,
        products$: products$,
        salesTeamsService: inject(SalesTeamsService),
        tagService: inject(SalesOpportunitiesApiService),
        hasAccessToAllAccountsInMarket$: hasAccessToAllAccountsInMarket$,
        clientOrigin: ClientOrigin.CLIENT_ORIGIN_PARTNER_CENTER,
      } as PipelineTableConfig;
    },
  },
);

export const PIPELINE_TABLE_SAVED_FILTERS_INJECT_TOKEN = new InjectionToken<PipelineTableSavedFiltersConfig>(
  '[Partner Center Client]: Token for Pipeline config',
  {
    providedIn: 'root',
    factory: function (): PipelineTableSavedFiltersConfig {
      return {
        canAccessSaveFilters$: of(true),
      };
    },
  },
);

export const PARTNER_OVERRIDE_CONFIG_INJECT_TOKEN = new InjectionToken<PartnerOverrideConfig>(
  '[Partner Center Client]: Token for overriding Pipeline Board config',
  {
    providedIn: 'root',
    factory: function (): PartnerOverrideConfig {
      const partnerAAConfigService = inject(AppConfigService);
      const partnerId$ = partnerAAConfigService.config$.pipe(
        map((config) => config.partnerId),
        filter((partnerId) => !!partnerId),
        distinctUntilChanged(),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const bypassQuantityRestrictionsPartnerIds = ['VMF'];
      const bypassQuantityRestrictions$ = partnerId$.pipe(
        map((partnerId) => bypassQuantityRestrictionsPartnerIds.includes(partnerId)),
      );

      return {
        bypassQuantityRestrictions$: bypassQuantityRestrictions$,
      };
    },
  },
);

const getAccountDetailsUrlFn: AccountUrlFunction = (opportunity: Opportunity) => {
  return `/businesses/accounts/${opportunity.accountGroupId}/details`;
};

const getCompanyDetailsUrlFn: CompanyUrlFunction = (companyId: string) => {
  return `/crm/company/profile/${companyId}`;
};

const getOrdersUrlFn: OrdersUrlFunction = (opportunity: ApiOpportunity): OrdersUrl => {
  return <OrdersUrl>{
    route: `/businesses/accounts/${opportunity.accountGroupId}/activation`,
  };
};

const detailsConfig: DetailsConfig = {
  showCRMDetails: true,
  getAccountDetailsUrl: getAccountDetailsUrlFn,
  getCompanyDetailsUrl: getCompanyDetailsUrlFn,
};

interface PartnerMarket {
  partnerId: string;
  marketId: string;
}
const isPartnerMarketSame = (prev: PartnerMarket, curr: PartnerMarket): boolean => {
  return prev?.partnerId === curr?.partnerId && prev?.marketId === curr?.marketId;
};
