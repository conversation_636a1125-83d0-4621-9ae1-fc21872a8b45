import { Component } from '@angular/core';
import { ListCustomFieldManagementPageComponent } from '@galaxy/crm/dynamic';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { AuxiliaryDataPageComponent } from '@vendasta/auxiliary-data-components';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { AuxiliaryDataPageModule } from '@vendasta/auxiliary-data-components';
import { ObjectType } from '@galaxy/crm/static';

@Component({
  templateUrl: './crm-custom-field-management.component.html',
  imports: [AuxiliaryDataPageModule, ListCustomFieldManagementPageComponent],
})
export class CustomFieldManagementComponent {
  protected readonly legacyCustomFieldTabs = [
    { label: 'Orders', component: AuxiliaryDataPageComponent, inputs: { objectType: 'order' } },
    { label: 'Products', component: AuxiliaryDataPageComponent, inputs: { objectType: 'product' } },
    { label: 'Projects & Tasks', component: AuxiliaryDataPageComponent, inputs: { objectType: 'task' } },
    { label: 'Accounts', component: AuxiliaryDataPageComponent, inputs: { objectType: 'business' } },
    { label: 'Users', component: AuxiliaryDataPageComponent, inputs: { objectType: 'user' } },
  ];

  protected readonly supportedObjects: ObjectType[] = ['Contact', 'Company'];
}
