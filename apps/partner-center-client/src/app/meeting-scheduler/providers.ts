import { inject, InjectionToken } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import {
  getDefaultMeetingSchedulerIcons,
  getDefaultMeetingSchedulerLinks,
  MeetingSchedulerConfig,
} from '@galaxy/meeting-scheduler';
import { MeetingIntegrationsConfig } from '@vendasta/integrations';
import {
  ApplicationContextPropertiesSet,
  Calendar,
  HostService,
  MeetingSourceOrigin,
  newPartnerCenterApplicationContextProperties,
} from '@vendasta/meetings';
import { RetryConfig, retryer } from '@vendasta/rx-utils';
import { combineLatest, of, throwError } from 'rxjs';
import { catchError, distinctUntilChanged, filter, map, shareReplay, startWith, switchMap } from 'rxjs/operators';
import { AppConfigService } from '../app-config.service';
import { ImageService } from '../core/utils/image.service';
import { FeatureFlagService } from '@galaxy/partner';
import { SalesTeam } from '@galaxy/types';
import { Salesperson, SalespersonService, SalesTeamService } from '@vendasta/sales';

function registerIcon(iconName: string, url: string, registry: MatIconRegistry, sanitizer: DomSanitizer): void {
  registry.addSvgIcon(iconName, sanitizer.bypassSecurityTrustResourceUrl(url));
}

export function initializeIconRegistration(matIconRegistry: MatIconRegistry, sanitizer: DomSanitizer): void {
  const imgService = inject(ImageService);
  registerIcon(
    'meet-icon',
    imgService.getImageSrc('meeting-scheduler/google-hangouts-meet.svg'),
    matIconRegistry,
    sanitizer,
  );
  registerIcon('zoom-icon', imgService.getImageSrc('meeting-scheduler/zoom-icon.svg'), matIconRegistry, sanitizer);
  registerIcon('teams-icon', imgService.getImageSrc('meeting-scheduler/teams-icon.svg'), matIconRegistry, sanitizer);
}

export interface MatIconInitializer {
  initialize(): void;
}

export const MatIconInitializerToken = new InjectionToken<MatIconInitializer>(
  '[Meeting Scheduler]: Token for registering mat icons',
);

export function matIconRegistrationFactory(
  matIconRegistry: MatIconRegistry,
  sanitizer: DomSanitizer,
): MatIconInitializer {
  return {
    initialize: function (): void {
      initializeIconRegistration(matIconRegistry, sanitizer);
    },
  };
}

const icons = getDefaultMeetingSchedulerIcons();
icons.meetingListGroupsSection = 'store';

export enum SERVICE_KEYS {
  integrationsPage = 'integrations-page',
  eventsAndMeetingsPage = 'event-and-meetings',

  eventsAndMeetingsPageCalendarConnected = 'events-and-meetings-calendar-connected',
  eventsAndMeetingsPageConferencingConnected = 'events-and-meetings-conferencing-connected',
}

export const MEETING_SCHEDULER_CONFIG_TOKEN = new InjectionToken<MeetingSchedulerConfig>(
  '[Partner Center]: token for MeetingScheduler config',
  {
    providedIn: 'root',
    factory: function (): MeetingSchedulerConfig {
      const salesTeamService = inject(SalesTeamService);
      const EVENTS_AND_MEETINGS_ROOT = 'events-and-meetings';
      const appConfig = inject(AppConfigService);
      const featureFlagService = inject(FeatureFlagService);
      const salesPersonService = inject(SalespersonService);

      const partnerId$ = appConfig.config$.pipe(map((config) => config.partnerId));

      const featureFlags$ = partnerId$.pipe(
        switchMap((partnerId) => featureFlagService.batchGetStatus(partnerId, '', ['meeting_type_expansion_enabled'])),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasFeatureMeetingExpansionEnabled$ = featureFlags$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_type_expansion_enabled'] ?? false),
        startWith(false),
      );

      const featureFlagsEmailTemplate$ = partnerId$.pipe(
        switchMap((partnerId) =>
          featureFlagService.batchGetStatus(partnerId, '', ['meeting_scheduler_email_template']),
        ),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasFeatureMeetingEmailTemplateEnabled$ = featureFlagsEmailTemplate$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_scheduler_email_template'] ?? false),
        startWith(false),
      );
      const applicationContext$ = partnerId$.pipe(
        map((partnerId) =>
          newPartnerCenterApplicationContextProperties({
            partner_id: partnerId,
          }),
        ),
      );

      const featureFlagsGroupsandServices$ = partnerId$.pipe(
        switchMap((partnerId) => featureFlagService.batchGetStatus(partnerId, '', ['meeting_scheduler_service_menu'])),
        shareReplay({ bufferSize: 1, refCount: true }),
      );
      const hasFeatureGroupsandServicesEnabled$ = featureFlagsGroupsandServices$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_scheduler_service_menu'] ?? false),
        startWith(false),
      );

      const featureFlagsEmbedCode$ = partnerId$.pipe(
        switchMap((partnerId) => featureFlagService.batchGetStatus(partnerId, '', ['meeting_scheduler_embed_code'])),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasFeatureEmbedCodeEnabled$ = featureFlagsEmbedCode$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_scheduler_embed_code'] ?? false),
        startWith(false),
      );

      const featureFlagsRoundRobin$ = partnerId$.pipe(
        switchMap((partnerId) => featureFlagService.batchGetStatus(partnerId, '', ['meeting_scheduler_round_robin'])),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasFeatureRoundRobinEnabled$ = featureFlagsRoundRobin$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_scheduler_round_robin'] ?? false),
        startWith(false),
      );

      const featureFlagsServiceAndTeam$ = partnerId$.pipe(
        switchMap((partnerId) =>
          featureFlagService.batchGetStatus(partnerId, '', ['meeting_scheduler_team_event_for_partner_center']),
        ),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasServiceAndTeamLinks$ = featureFlagsServiceAndTeam$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_scheduler_team_event_for_partner_center'] ?? false),
        startWith(false),
      );

      const groupCalendSlugEditing$ = partnerId$.pipe(
        switchMap((partnerId) => featureFlagService.batchGetStatus(partnerId, '', ['group_calendar_slug_editing'])),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasGroupCalendarSlugEditingEnabled$ = groupCalendSlugEditing$.pipe(
        map((featureFlagStatus) => featureFlagStatus['group_calendar_slug_editing'] ?? false),
        startWith(false),
      );

      const featureFlagsPhysicalLocation$ = partnerId$.pipe(
        switchMap((partnerId) =>
          featureFlagService.batchGetStatus(partnerId, '', ['meeting_scheduler_physical_location']),
        ),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasFeaturePhysicalLocation$ = featureFlagsPhysicalLocation$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_scheduler_physical_location'] ?? false),
        startWith(false),
      );

      const featureFlagsBeforeBuffer$ = partnerId$.pipe(
        switchMap((partnerId) => featureFlagService.batchGetStatus(partnerId, '', ['meeting_before_buffer'])),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasFeatureBeforeBuffer$ = featureFlagsBeforeBuffer$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_before_buffer'] ?? false),
        startWith(false),
      );

      const featureMicrosoftTeamsEnabled$ = partnerId$.pipe(
        switchMap((partnerId) =>
          featureFlagService.batchGetStatus(partnerId, '', ['meeting_scheduler_microsoft_teams_integration']),
        ),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

      const hasFeatureMicrosoftTeams$ = featureMicrosoftTeamsEnabled$.pipe(
        map((featureFlagStatus) => featureFlagStatus['meeting_scheduler_microsoft_teams_integration'] ?? false),
        startWith(false),
      );

      const hostIdRetryConfig: RetryConfig = {
        maxAttempts: 5,
        retryDelay: 200,
        timeoutMilliseconds: 10000,
      };
      const hostService = inject(HostService);
      const uid$ = appConfig.config$.pipe(map((config) => config.unifiedUserId));
      const userFirstName$ = appConfig.config$.pipe(map((config) => config?.userFirstName));
      const userLastName$ = appConfig.config$.pipe(map((config) => config?.userLastName));
      const hostId$ = combineLatest([
        applicationContext$.pipe(filter<ApplicationContextPropertiesSet>(Boolean)),
        uid$.pipe(filter<string>(Boolean)),
      ]).pipe(
        switchMap(([applicationContextProperties, userId]) => {
          return hostService.buildHostId({
            userId,
            applicationContextProperties: applicationContextProperties,
          });
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
        retryer(hostIdRetryConfig),
      );

      const salespersonId$ = combineLatest([partnerId$, uid$]).pipe(
        switchMap(([partnerId, userId]) => salesPersonService.getSalespersonByUserId(partnerId, userId)),
        map((salesperson: Salesperson) => salesperson.salespersonId),
        shareReplay({ bufferSize: 1, refCount: true }),
        catchError(() => of('')),
      );

      const salesTeamsForSalesPerson$ = salespersonId$.pipe(
        switchMap((salespersonId) => {
          if (!salespersonId) {
            return of([]);
          }
          return salesTeamService.getSalesTeamsBySalesperson(salespersonId).pipe(
            distinctUntilChanged((a, b) => {
              if (!a || !b) {
                return false;
              }
              const salesTeamGroupId_A = a.reduce((key, salesTeam) => `${key}-${salesTeam.groupId}`, '');
              const salesTeamGroupId_B = b.reduce((key, salesTeam) => `${key}-${salesTeam.groupId}`, '');
              return salesTeamGroupId_A === salesTeamGroupId_B;
            }),
            catchError((err) => {
              if (err.status === 404) {
                return of([]);
              }
              return throwError(() => err);
            }),
          );
        }),
        shareReplay(1),
      );

      return {
        hostId$: hostId$,
        applicationContext$: applicationContext$,
        userId$: uid$,

        meetingIntegrationsServiceKeys: {
          integrationsPage: SERVICE_KEYS.integrationsPage,
          eventsAndMeetingsPage: SERVICE_KEYS.eventsAndMeetingsPage,
          eventsAndMeetingsPageCalendarConnected: SERVICE_KEYS.eventsAndMeetingsPageCalendarConnected,
          eventsAndMeetingsPageConferencingConnected: SERVICE_KEYS.eventsAndMeetingsPageConferencingConnected,
        },
        featureServicesAndTeamLinksEnabled$: hasServiceAndTeamLinks$,
        featureGroupCalendarSlugEditingEnabled$: hasGroupCalendarSlugEditingEnabled$,
        featureGroupsandServicesEnabled$: hasFeatureGroupsandServicesEnabled$,
        featureMeetingExpansionEnabled$: hasFeatureMeetingExpansionEnabled$,
        featureEmailTemplateEnabled$: hasFeatureMeetingEmailTemplateEnabled$,
        featureEmbedCodeEnabled$: hasFeatureEmbedCodeEnabled$,
        featureRoundRobinEnabled$: hasFeatureRoundRobinEnabled$,
        featurePhysicalLocationEnabled$: hasFeaturePhysicalLocation$,
        featureMeetingBeforeBufferEnabled$: hasFeatureBeforeBuffer$,
        featureMicrosoftTeamsEnabled$: hasFeatureMicrosoftTeams$,
        calendarService: {
          getCalendars: () =>
            salesTeamsForSalesPerson$.pipe(
              map((salesTeams) => {
                return (salesTeams || []).map(
                  (t: SalesTeam) =>
                    ({
                      externalId: t.groupId,
                      displayName: t.teamName,
                      id: '',
                    }) as Calendar,
                );
              }),
            ),
        },
        icons: icons,
        recommendedPersonalCalendarSlugs$: combineLatest([userFirstName$, userLastName$]).pipe(
          distinctUntilChanged(),

          map(([firstName, lastName]) => {
            if (!firstName || !lastName) {
              return [];
            }
            firstName = firstName.toLowerCase();
            lastName = lastName.toLowerCase();
            const fullNameLowerCase = `${firstName}${lastName}`;
            const cleanedFullName = fullNameLowerCase.replace(/\W/g, '');
            const firstInitialLastName = `${firstName.slice(0, 1)}${lastName}`;
            return [
              firstInitialLastName,
              cleanedFullName,

              `${firstInitialLastName}1`,
              `${cleanedFullName}1`,

              `${firstInitialLastName}2`,
              `${cleanedFullName}2`,

              `${firstInitialLastName}3`,
              `${cleanedFullName}3`,

              `${firstInitialLastName}${Math.floor(Math.random() * 90 + 10)}`,
              `${cleanedFullName}${Math.floor(Math.random() * 90 + 10)}`,
            ];
          }),
        ),

        recommendedGroupCalendarSlugs$: salesTeamsForSalesPerson$.pipe(
          distinctUntilChanged(),
          map((teams) => {
            const result = {};
            teams.forEach((team) => {
              if (team.groupId && team.teamName) {
                const externalCalendarId = team.groupId;
                const lowered = team.teamName.toLowerCase();
                const cleaned = lowered.replace(/\s+/g, ' ').trim();
                const parts = cleaned.split(/\s/g);
                const dashSeparated = parts.join('-');
                const nonSeparated = parts.join();
                result[externalCalendarId] = [
                  dashSeparated,
                  nonSeparated,
                  `${dashSeparated}${Math.floor(Math.random() * 90 + 10)}`,
                  `${dashSeparated}${Math.floor(Math.random() * 90 + 10)}`,
                  `${nonSeparated}${Math.floor(Math.random() * 90 + 10)}`,
                  `${nonSeparated}${Math.floor(Math.random() * 90 + 10)}`,
                ];
              }
            });
            return result;
          }),
        ),

        links: {
          ...getDefaultMeetingSchedulerLinks({
            meetingListPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
            settingsPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
            calendarPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
            eventTypePage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
            groupPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
            servicePage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
            calendarViewPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
            meetingTypeListViewPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
          }),
          integrationsPage: '', // empty string is used to remove "integration settings" button
        },
      };
    },
  },
);

const PROFILE_SETTINGS = 'meeting-type';
const EVENTS_AND_MEETINGS_ROOT = 'events-and-meetings';

const settingsUrl = `/${PROFILE_SETTINGS}`;
const integrationsUrl = `${settingsUrl}/integrations`;
const eventsAndMeetingsSettingsUrls = `/${EVENTS_AND_MEETINGS_ROOT}/meeting-type?defaults=true`;

export const PARTNER_CENTER_MEETING_INTEGRATIONS_CONFIG = new InjectionToken<MeetingIntegrationsConfig>(
  '[Partner Center]: token for meeting integrations config',
  {
    providedIn: 'root',
    factory: function (): MeetingIntegrationsConfig {
      const HOSTNAME =
        window.location.hostname === 'localhost' ? `localhost:${location.port || 4321}` : window.location.hostname;
      const FULL_URL = `${window.location.protocol}//${HOSTNAME}${integrationsUrl}`;
      const EVENTS_MEETINGS_URL = `${window.location.protocol}//${HOSTNAME}${eventsAndMeetingsSettingsUrls}`;
      const appConfig = inject(AppConfigService);
      const uid$ = appConfig.config$.pipe(map((config) => config.unifiedUserId));
      return {
        userId$: uid$,
        namespace$: appConfig.config$.pipe(map((c) => c.partnerId)),
        origin: MeetingSourceOrigin.PartnerCenter,
        redirectInfo$: {
          [SERVICE_KEYS.integrationsPage]: {
            connectionSuccessfulReturnURL: FULL_URL,
            disconnectSuccessReturnURL: FULL_URL,
          },
          [SERVICE_KEYS.eventsAndMeetingsPage]: {
            connectionSuccessfulReturnURL: EVENTS_MEETINGS_URL,
            disconnectSuccessReturnURL: EVENTS_MEETINGS_URL,
          },
          [SERVICE_KEYS.eventsAndMeetingsPageCalendarConnected]: {
            connectionSuccessfulReturnURL: `${EVENTS_MEETINGS_URL}&calendarConnected=true`,
            disconnectSuccessReturnURL: `${EVENTS_MEETINGS_URL}&calendarConnected=false`,
          },
          [SERVICE_KEYS.eventsAndMeetingsPageConferencingConnected]: {
            connectionSuccessfulReturnURL: `${EVENTS_MEETINGS_URL}&conferencingConnected=true`,
            disconnectSuccessReturnURL: `${EVENTS_MEETINGS_URL}&conferencingConnected=false`,
          },
        },
      };
    },
  },
);
