import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { FeatureFlagService } from '@galaxy/partner';
import { AffiliateInterface, StatusType } from '@vendasta/prospect';
import { combineLatest, Observable, Subscription } from 'rxjs';
import { map, shareReplay, switchMap, take } from 'rxjs/operators';
import { AccessRestrictedDialogComponent } from '../access';
import { AffiliateService } from '../affiliates/affiliate.service';
import { AppConfig, AppConfigService } from '../app-config.service';
import { AccessService, Feature, StubAccessService } from '../core/access';
import { FeatureFlags } from '../core/features';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { ViewModeService } from '@galaxy/conversation/core';
import { SettingsAccessService } from './settings-access.service';

@Component({
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
  standalone: false,
})
export class SettingsComponent implements OnInit, OnDestroy {
  appConfig$: Observable<AppConfig>;
  affiliateConfig$: Observable<AffiliateInterface>;
  vigAdapter$: Observable<boolean>;

  facebookAdLeadsEnabled$: Observable<boolean>;
  translatePortalEnabled$: Observable<boolean>;
  hasAccessToCustomize$: Observable<boolean>;
  hasAccessToServiceAccounts$: Observable<boolean>;
  aiAssistantEnabled$: Observable<boolean>;

  displayColumns = 3;
  breakpointObserverSubscription: Subscription;
  STATUS_TYPE_APPROVED = StatusType.STATUS_TYPE_APPROVED;

  protected readonly hasAccessToSingleSignOn = this.settingsAccessService.featureAccessSingleSignOn;
  protected readonly hasAccessToTranslate = this.settingsAccessService.featureAccessTranslate;
  protected readonly hasAccessToManageOrders = this.settingsAccessService.viewAccessManageOrders;
  constructor(
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    @Inject('MARKET_ID') private readonly marketId$: Observable<string>,
    @Inject(StubAccessService) private accessService: AccessService,
    private appConfigService: AppConfigService,
    private feature: FeatureFlagService,
    private affiliateService: AffiliateService,
    private readonly breakpointObserver: BreakpointObserver,
    private dialog: MatDialog,
    private router: Router,
    private viewModeService: ViewModeService,
    private settingsAccessService: SettingsAccessService,
  ) {
    const featureFlags$ = combineLatest([this.partnerId$, this.marketId$]).pipe(
      switchMap(([partnerId, marketId]) =>
        feature.batchGetStatus(partnerId, marketId, [
          FeatureFlags.VIG_ADAPTER,
          FeatureFlags.FACEBOOK_AD_LEADS_FOR_CLOUD_BROKERS,
          FeatureFlags.WEBLATE_TRANSLATION_PORTAL,
          FeatureFlags.CUSTOMIZE_BUSINESS_APP,
        ]),
      ),
      shareReplay(1),
    );
    this.vigAdapter$ = featureFlags$.pipe(map((v) => v[FeatureFlags.VIG_ADAPTER]));
    this.facebookAdLeadsEnabled$ = featureFlags$.pipe(map((v) => v[FeatureFlags.FACEBOOK_AD_LEADS_FOR_CLOUD_BROKERS]));
    this.translatePortalEnabled$ = featureFlags$.pipe(map((v) => v[FeatureFlags.WEBLATE_TRANSLATION_PORTAL]));
  }
  private subscriptions: Subscription[] = [];

  ngOnInit(): void {
    this.appConfig$ = this.appConfigService.config$;
    this.affiliateService.loadAffiliate();
    this.affiliateConfig$ = this.affiliateService.getAffiliate();

    this.subscriptions.push(
      this.breakpointObserver.observe([Breakpoints.XSmall, Breakpoints.Small]).subscribe((observe) => {
        if (observe.breakpoints[Breakpoints.Small]) {
          this.displayColumns = 2;
        } else if (observe.breakpoints[Breakpoints.XSmall]) {
          this.displayColumns = 1;
        } else {
          this.displayColumns = 3;
        }
      }),
    );

    this.hasAccessToCustomize$ = this.accessService.hasAccessToFeature(Feature.customizeBusinessApp);
    this.hasAccessToServiceAccounts$ = this.accessService.hasAccessToFeature(Feature.serviceAccounts);
  }

  private openAccessRestrictedDialog(featureId: string): void {
    this.dialog.closeAll();
    this.dialog.open(AccessRestrictedDialogComponent, {
      width: AccessRestrictedDialogComponent.DEFAULT_WIDTH,
      maxWidth: AccessRestrictedDialogComponent.DEFAULT_MAX_WIDTH,
      data: { featureId: featureId },
    });
  }

  onCustomizeBusinessAppPressed(): void {
    this.hasAccessToCustomize$
      .pipe(
        map((hasAccess) => {
          if (hasAccess) {
            this.router.navigateByUrl('/customize-business-app');
            return;
          }
          this.openAccessRestrictedDialog(Feature.customizeBusinessApp);
        }),
        take(1),
      )
      .subscribe();
  }

  onServiceAccountsAppPressed(): void {
    this.hasAccessToServiceAccounts$
      .pipe(
        map((hasAccess) => {
          if (hasAccess) {
            this.router.navigateByUrl('/integrations/service-accounts');
            return;
          }
          this.openAccessRestrictedDialog(Feature.serviceAccounts);
        }),
        take(1),
      )
      .subscribe();
  }

  onInboxSettingsPressed(): void {
    this.viewModeService.openSettings();
  }

  handleRouteToSSOPage(): void {
    if (this.hasAccessToSingleSignOn()) {
      this.router.navigateByUrl('/integrations/sso');
    } else {
      this.openAccessRestrictedDialog(Feature.singleSignOn);
    }
  }

  handleRouteToTranslatePage(): void {
    if (this.hasAccessToTranslate()) {
      this.router.navigateByUrl('/translate');
    } else {
      this.openAccessRestrictedDialog(Feature.translate);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
