import { Inject, Injectable } from '@angular/core';
import { AccessService, Feature, StubAccessService } from '../core/access';
import { toSignal } from '@angular/core/rxjs-interop';
import { Views } from '../core/access/interface';
import { catchError, map, shareReplay, startWith } from 'rxjs/operators';
import { FeatureFlagService } from '@galaxy/partner';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class SettingsAccessService {
  private readonly viewAccess$ = this.accessService
    .hasAccessToViews([Views.ordersCustomFields, Views.partnerAll, Views.editCrmLeadScore, Views.viewManageSalesOrders])
    .pipe(
      catchError((_) => {
        const map = new Map<Views, boolean>();
        map.set(Views.ordersCustomFields, false);
        map.set(Views.partnerAll, false);
        map.set(Views.editCrmLeadScore, false);
        map.set(Views.viewManageSalesOrders, false);
        return of(map);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

  readonly viewAccessEditCustomFields = toSignal(
    this.viewAccess$.pipe(map((access) => access.get(Views.ordersCustomFields))),
  );

  readonly viewAccessManageOrders = toSignal(
    this.viewAccess$.pipe(map((access) => access.get(Views.viewManageSalesOrders))),
  );

  readonly featureAccessCustomFields = toSignal(
    this.accessService.hasAccessToFeature(Feature.customFields).pipe(startWith(false)),
  );

  readonly viewAccessEditPipeline = toSignal(this.viewAccess$.pipe(map((access) => access.get(Views.partnerAll))));

  readonly featureAccessPipeline = toSignal(
    this.accessService.hasAccessToFeature(Feature.managePipeline).pipe(startWith(false)),
  );

  readonly viewAccessEditCRMScore = toSignal(
    this.viewAccess$.pipe(map((viewAccess) => viewAccess.get(Views.editCrmLeadScore))),
  );

  readonly featureAccessCRMScore = toSignal(
    this.accessService.hasAccessToFeature(Feature.crmLeadScore).pipe(startWith(false)),
  );

  readonly featureAccessSingleSignOn = toSignal(
    this.accessService.hasAccessToFeature(Feature.singleSignOn).pipe(startWith(false)),
  );

  readonly featureAccessTranslate = toSignal(
    this.accessService.hasAccessToFeature(Feature.translate).pipe(startWith(false)),
  );
  constructor(
    @Inject(StubAccessService) private readonly accessService: AccessService,
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    private readonly featureFlagService: FeatureFlagService,
  ) {}
}
