<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>
      {{ 'ADMINISTRATION.PAGE_TITLE' | translate }}
    </glxy-page-title>
  </glxy-page-toolbar>
  <glxy-page-wrapper [widthPreset]="'wide'">
    <ng-container
      *ngIf="{
        appConfig: appConfig$ | async,
        vigAdapter: vigAdapter$ | async,
        affiliateConfig: affiliateConfig$ | async,
        facebookAdLeadsEnabled: facebookAdLeadsEnabled$ | async,
        aiAssistantEnabled: aiAssistantEnabled$ | async,
      } as pageData"
    >
      <ng-container *ngIf="!!pageData.affiliateConfig; else loading">
        <div class="card-container">
          <mat-card appearance="outlined">
            <mat-card-header>
              <mat-card-title>{{ 'ADMINISTRATION.MY_ACCOUNT' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <mat-grid-list [cols]="displayColumns" rowHeight="84px" gutterSize="24px">
                <mat-grid-tile *ngIf="pageData.appConfig.userCanAccessBilling">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>card_membership</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="my-plan" routerLink="/subscription-tier/my-plan"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.MY_PLAN.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.MY_PLAN.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="pageData.appConfig.userIsAdmin">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>emoji_people</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="my-team" routerLink="/my-team"></a>
                      <div class="title">
                        {{ 'SETTINGS_PAGE.SECTIONS.ADMIN_MY_TEAM.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'SETTINGS_PAGE.SECTIONS.ADMIN_MY_TEAM.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="pageData.appConfig.userCanAccessBilling">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>receipt</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="billing" routerLink="/billing"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.MY_BILLING.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.MY_BILLING.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="pageData.appConfig.userCanAccessBilling">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>insert_chart</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="billing-reports" routerLink="/billing/reports"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.REPORTS.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.REPORTS.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="pageData.appConfig.userCanAccessBilling">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>text_snippet</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="billing-documents" routerLink="/billing/documents"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.FINANCIAL_DOCUMENTS.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.FINANCIAL_DOCUMENTS.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="pageData.appConfig.userCanAccessBilling">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>attach_money</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="billing-pricing" routerLink="/billing/pricing"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.PRICING.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.PRICING.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="pageData.appConfig.userCanAccessCompanyProfile">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>person</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="company-profile" routerLink="/company-profile"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.COMPANY_PROFILE.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.COMPANY_PROFILE.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile>
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>person_search</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="affiliates" routerLink="/affiliates"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.AFFILIATE_PROGRAMS.TITLE' | translate }}
                      </div>
                      <p *ngIf="pageData.affiliateConfig?.status !== STATUS_TYPE_APPROVED; else affiliateApproved">
                        {{ 'ADMINISTRATION.AFFILIATE_PROGRAMS.DESCRIPTION_ONE' | translate }}
                      </p>
                      <ng-template #affiliateApproved>
                        <p>
                          {{ 'ADMINISTRATION.AFFILIATE_PROGRAMS.DESCRIPTION_TWO' | translate }}
                        </p>
                      </ng-template>
                    </div>
                  </div>
                </mat-grid-tile>
              </mat-grid-list>
            </mat-card-content>
          </mat-card>
        </div>
        <app-data-management-settings></app-data-management-settings>
        <div class="card-container">
          <mat-card appearance="outlined">
            <mat-card-header>
              <mat-card-title>{{ 'ADMINISTRATION.PLATFORM_SETTINGS' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <mat-grid-list [cols]="displayColumns" rowHeight="84px" gutterSize="24px">
                <ng-container *ngIf="pageData.appConfig.userCanCustomizeWhitelabel">
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>important_devices</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="customize-design" routerLink="/customize-design"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.CUSTOMIZE_PLATFORM.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.CUSTOMIZE_PLATFORM.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>store</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="customize-business-app" (click)="onCustomizeBusinessAppPressed()"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.CUSTOMIZE_BUSINESS_APP.TITLE' | translate }}
                          <div class="upgrade-icon">
                            <mat-icon
                              >{{ (hasAccessToCustomize$ | async) === false ? 'arrow_circle_up' : '' }}
                            </mat-icon>
                          </div>
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.CUSTOMIZE_BUSINESS_APP.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>notifications</mat-icon>
                      </div>
                      <div class="settings">
                        <a
                          data-action="customize-notifications"
                          routerLink="/customize-design/default-notifications"
                        ></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.CLIENT_NOTIFICATIONS.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.CLIENT_NOTIFICATIONS.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>format_paint</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="customize-design" routerLink="/customize-branding"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.CUSTOMIZE_BRANDING_V2.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.CUSTOMIZE_BRANDING_V2.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>question_answer</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="inbox-settings" (click)="onInboxSettingsPressed()"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.INBOX_SETTINGS.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.INBOX_SETTINGS.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>menu_book</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="ai-knowledge" routerLink="/ai-knowledge"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.AI_KNOWLEDGE.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.AI_KNOWLEDGE.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>smart_toy</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="ai-workforce" routerLink="/ai/assistants"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.AI_WORKFORCE_SETTINGS.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.AI_WORKFORCE_SETTINGS.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                </ng-container>
              </mat-grid-list>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="card-container">
          <mat-card appearance="outlined">
            <mat-card-header>
              <mat-card-title>{{ 'ADMINISTRATION.COMMERCE_SETTINGS' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <mat-grid-list [cols]="displayColumns" rowHeight="84px" gutterSize="24px">
                @if (pageData.appConfig.userCanAccessRetailBilling) {
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>credit_card</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="vendasta-payments" routerLink="/billing/settings"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.VENDASTA_PAYMENTS.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.VENDASTA_PAYMENTS.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                }
                @if (pageData.appConfig.userCanAccessRetailBilling) {
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>payments</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="billing-defaults" routerLink="/billing/business-configuration/defaults"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.ACCOUNT_BILLING.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.ACCOUNT_BILLING.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                }
                @if (hasAccessToManageOrders()) {
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>text_snippet</mat-icon>
                      </div>
                      <div class="settings">
                        <a
                          data-action="customize-sales-orders"
                          routerLink="/customize-design/customize-sales-orders"
                        ></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.ORDER_SETTINGS' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.ORDER_SETTINGS_DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                }
                @if (pageData.appConfig.userCanAccessRetailBilling) {
                  <mat-grid-tile>
                    <div class="settings-container">
                      <div class="icon">
                        <mat-icon>info</mat-icon>
                      </div>
                      <div class="settings">
                        <a data-action="billing-taxes" routerLink="/billing/taxes"></a>
                        <div class="title">
                          {{ 'ADMINISTRATION.TAX_RATES.TITLE' | translate }}
                        </div>
                        <p>
                          {{ 'ADMINISTRATION.TAX_RATES.DESCRIPTION' | translate }}
                        </p>
                      </div>
                    </div>
                  </mat-grid-tile>
                }
              </mat-grid-list>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="card-container">
          <mat-card appearance="outlined">
            <mat-card-header>
              <mat-card-title>{{ 'ADMINISTRATION.ADVANCED' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <mat-grid-list [cols]="displayColumns" rowHeight="84px" gutterSize="24px">
                <mat-grid-tile>
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>api</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="api-documentation" href="https://developers.vendasta.com/" target="_blank"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.DEVELOPER_APIS.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.DEVELOPER_APIS.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile>
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>settings_input_component</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="app-integrations" routerLink="/integrations"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.INTEGRATIONS.TITLE' | translate }}
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.INTEGRATIONS.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="pageData.appConfig.userIsAdmin">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>fingerprint</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="service-accounts" (click)="onServiceAccountsAppPressed()"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.SERVICE_ACCOUNTS.TITLE' | translate }}
                        <div class="upgrade-icon">
                          <mat-icon
                            >{{ (hasAccessToServiceAccounts$ | async) === false ? 'arrow_circle_up' : '' }}
                          </mat-icon>
                        </div>
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.SERVICE_ACCOUNTS.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="pageData.appConfig.userIsAdmin && pageData.vigAdapter">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>account_tree</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="sso" (click)="handleRouteToSSOPage()"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.SINGLE_SIGN_ON.TITLE' | translate }}
                        @if (!hasAccessToSingleSignOn()) {
                          <div class="upgrade-icon">
                            <mat-icon>arrow_circle_up</mat-icon>
                          </div>
                        }
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.SINGLE_SIGN_ON.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
                <mat-grid-tile *ngIf="(translatePortalEnabled$ | async) === true">
                  <div class="settings-container">
                    <div class="icon">
                      <mat-icon>language</mat-icon>
                    </div>
                    <div class="settings">
                      <a data-action="translate" (click)="handleRouteToTranslatePage()"></a>
                      <div class="title">
                        {{ 'ADMINISTRATION.TRANSLATION_PORTAL.TITLE' | translate }}
                        @if (!hasAccessToTranslate()) {
                          <div class="upgrade-icon">
                            <mat-icon>arrow_circle_up</mat-icon>
                          </div>
                        }
                      </div>
                      <p>
                        {{ 'ADMINISTRATION.TRANSLATION_PORTAL.DESCRIPTION' | translate }}
                      </p>
                    </div>
                  </div>
                </mat-grid-tile>
              </mat-grid-list>
            </mat-card-content>
          </mat-card>
        </div>
      </ng-container>
      <ng-template #loading>
        <mat-card appearance="outlined">
          <mat-card-content>
            <mat-grid-list [cols]="displayColumns" rowHeight="84px" gutterSize="24px">
              <mat-grid-tile *ngFor="let x of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]">
                <div class="grid-list-shimmer-container">
                  <span class="settings stencil-shimmer"></span>
                </div>
              </mat-grid-tile>
            </mat-grid-list>
          </mat-card-content>
        </mat-card>
      </ng-template>
    </ng-container>
  </glxy-page-wrapper>
</glxy-page>
