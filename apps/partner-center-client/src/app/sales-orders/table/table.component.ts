import { Component, Inject } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { FeatureFlagService } from '@galaxy/partner';
import { AccountGroup } from '@vendasta/account-group';
import { BusinessSelectorDialogComponent } from '@vendasta/businesses';
import { IAMService, UserIdentifier } from '@vendasta/iamv2';
import { CreateOrderDialogService } from '@vendasta/orders';
import { AppliedValueService, FilterService } from '@vendasta/va-filter2';
import { VaPresetViewService } from '@vendasta/va-filter2-table';
import { combineLatest, Observable, of } from 'rxjs';
import { map, shareReplay, switchMap, take, withLatestFrom } from 'rxjs/operators';
import { AppConfig, AppConfigService } from '../../app-config.service';
import { AccessService, Feature, StubAccessService } from '../../core/access';
import { FeatureFlags } from '../../core/features';
import { SalespersonService } from '../../core/salesperson.service';
import { SalespersonServiceInterface } from '../interfaces/salesperson-service-interface';
import { CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';

@Component({
  selector: 'app-table',
  templateUrl: './table.component.html',
  providers: [AppliedValueService, FilterService, VaPresetViewService],
  standalone: false,
})
export class TableComponent {
  canCreateOrders$: Observable<boolean> = this.partnerId$.pipe(
    switchMap((partnerId) => {
      return this.featureFlagService.batchGetStatus(partnerId, '', ['no_admin_order_creation']);
    }),
    map((featureFlagStatus) => featureFlagStatus['no_admin_order_creation']),
    shareReplay(1),
  );

  userIsPartnerAdmin$: Observable<boolean> = this.getUserIsPartnerAdmin$();

  // This use case is for TRUDON as they are strict on restricting from Admin to Creating order
  // Additionally they don't need logic of #orderButton as well

  noAdminOrderCreation$: Observable<boolean> = this.crmConfig.canPartnerAdminCreateOrdersFeatureFlag$;

  userSalespersonId$: Observable<string>;
  canAccessSalesTeams$ = this.accessService.hasAccessToFeature(Feature.salesTeam);

  constructor(
    @Inject(SalespersonService) private salesPersonService: SalespersonServiceInterface,
    @Inject('PARTNER_ID') private partnerId$: Observable<string>,
    @Inject(FeatureFlags.CREATE_SALES_ORDER) public readonly hasAccessToSalesOrders$: Observable<boolean>,
    private readonly router: Router,
    private readonly dialog: MatDialog,
    private readonly featureFlagService: FeatureFlagService,
    private readonly appConfigService: AppConfigService,
    private readonly iamService: IAMService,
    @Inject(StubAccessService) private readonly accessService: AccessService,
    private readonly createOrderDialogService: CreateOrderDialogService,
    @Inject(CrmInjectionToken) private readonly crmConfig: CrmDependencies,
  ) {
    this.salesPersonService.loadAllSalespeople();
  }

  onActivateProducts(): void {
    this.partnerId$
      .pipe(
        switchMap((partnerId) => {
          return this.dialog
            .open(BusinessSelectorDialogComponent, {
              width: '500px',
              data: {
                partnerId: partnerId,
              },
            })
            .afterClosed();
        }),
        take(1),
      )
      .subscribe((business: AccountGroup) => {
        if (business) {
          this.router.navigate(['businesses/accounts', business.accountGroupId, 'activation'], {
            queryParams: { navigationSource: 'orders' },
          });
        }
      });
  }

  onCreateSalesOrder(): void {
    combineLatest([this.partnerId$, this.appConfigService.config$])
      .pipe(
        switchMap(([partnerId, config]) => {
          const hideCreateBusiness = !config.userCanAccessAccounts;
          const marketId = config.accessibleMarketIds?.length ? config.accessibleMarketIds : undefined;
          return this.dialog
            .open(BusinessSelectorDialogComponent, {
              width: '500px',
              data: {
                partnerId: partnerId,
                marketId: marketId,
                hideCreateBusiness: hideCreateBusiness,
              },
            })
            .afterClosed();
        }),
        withLatestFrom(this.appConfigService.config$),
        switchMap(([business, config]: [AccountGroup, AppConfig]) => {
          if (!business) {
            return of(null);
          }

          const [partnerId, marketId, businessId, userId] = [
            business.accountGroupExternalIdentifiers.partnerId,
            business.accountGroupExternalIdentifiers.marketId,
            business.accountGroupId,
            config.unifiedUserId,
          ];
          return this.createOrderDialogService.createOrderAndRedirect(
            partnerId,
            marketId,
            businessId,
            userId,
            (businessId, orderId) => `/order-management/${businessId}/view/${orderId}`,
          );
        }),
        take(1),
      )
      .subscribe();
  }

  private getUserIsPartnerAdmin$(): Observable<boolean> {
    const config = this.appConfigService.config;
    if (config.isSuperAdmin) {
      return of(true);
    }
    const id = config.unifiedUserId || config.userId;
    const currentUser$ = this.iamService.getUser(new UserIdentifier({ userId: id }));
    return combineLatest([currentUser$, this.partnerId$]).pipe(
      map(([user, partnerId]) => {
        if (user.partnerId === partnerId) {
          return 'partner' in user.roles;
        }
      }),
    );
  }
}
