<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-title>
      {{ 'SALES_ORDERS.ORDERS' | translate }}
    </glxy-page-title>
    <glxy-page-actions>
      <ng-container *ngIf="hasAccessToSalesOrders$ | async; else noAccessToSalesOrders">
        <ng-container *ngIf="canCreateOrders$ | async; else createSalesOrderOnly">
          <ng-container
            *ngIf="userIsPartnerAdmin$ | async; then ordersButton; else createSalesOrderOnly"
          ></ng-container>
        </ng-container>
      </ng-container>
    </glxy-page-actions>
  </glxy-page-toolbar>
  <glxy-page-wrapper [maxWidth]="'full'">
    <app-tabs-with-tables></app-tabs-with-tables>
  </glxy-page-wrapper>
</glxy-page>

<ng-template #noAccessToSalesOrders>
  <ng-container *ngIf="canCreateOrders$ | async; then activateProductsOnly"></ng-container>
</ng-template>

<ng-template #ordersButton>
  <glxy-button-group>
    <button mat-flat-button color="primary" type="button" (click)="onCreateSalesOrder()">
      {{ 'SALES_ORDERS.CREATE_SALES_ORDER' | translate }}
    </button>
    <button
      class="dropdown-icon"
      mat-flat-button
      color="primary"
      type="button"
      [matMenuTriggerFor]="orderButtonDropdown"
    >
      <mat-icon>arrow_drop_down</mat-icon>
    </button>
  </glxy-button-group>
</ng-template>

<mat-menu #orderButtonDropdown>
  <button mat-menu-item type="button" (click)="onActivateProducts()">
    {{ 'SALES_ORDERS.ORDER_PRODUCTS' | translate }}
  </button>
</mat-menu>

<ng-template #createSalesOrderOnly>
  @if (noAdminOrderCreation$ | async) {
    <button mat-flat-button color="primary" type="button" (click)="onCreateSalesOrder()">
      {{ 'SALES_ORDERS.CREATE_SALES_ORDER' | translate }}
    </button>
  }
</ng-template>

<ng-template #activateProductsOnly>
  <button mat-flat-button color="primary" type="button" (click)="onActivateProducts()">
    {{ 'SALES_ORDERS.ORDER_PRODUCTS' | translate }}
  </button>
</ng-template>
