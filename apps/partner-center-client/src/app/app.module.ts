import { CommonModule, LOCATION_INITIALIZED } from '@angular/common';
import { provideHttpClient, withInterceptorsFromDi, withXsrfConfiguration } from '@angular/common/http';
import { APP_INITIALIZER, Inject, Injectable, Injector, NgModule } from '@angular/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouteReuseStrategy, UrlSerializer } from '@angular/router';

import { AiKnowledgeModule } from '@galaxy/ai-knowledge';
import { ATLAS_CONFIG_TOKEN, AtlasModule } from '@galaxy/atlas';
import {
  AutomataI18nModule,
  AUTOMATION_BUSINESS_ENTRY_URL_SERVICE,
  AUTOMATION_CONTEXT_INJECTION_TOKEN,
  AUTOMATION_DEFAULT_CURRENCY_CODE,
  AUTOMATION_IS_USER_SUPERADMIN$,
  AUTOMATION_MARKET_ID_INJECTION_TOKEN$,
  AUTOMATION_NAMESPACE_INJECTION_TOKEN$,
  AUTOMATION_PARTNER_ID_INJECTION_TOKEN$,
  AUTOMATION_VARIABLE_MENU_DIALOG,
  AUTOMATIONS_ACCESS_SERVICE,
  AUTOMATIONS_MARKETS_SERVICE,
  AUTOMATIONS_ROUTE_PREFIX$,
} from '@galaxy/automata/shared';
import { BILLING_MARKETS_SERVICE } from '@galaxy/billing';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { CONFIG_TOKEN } from '@galaxy/campaign/shared/tokens';

// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  CONVERSATION_IMAGE_SERVICE_TOKEN,
  ConversationCoreModule,
} from '@galaxy/conversation/core';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { InboxAIButtonComponent, InboxButtonComponent, InboxComponent } from '@galaxy/conversation/inbox';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { ConversationUIModule } from '@galaxy/conversation/ui';
import { EnvironmentService } from '@galaxy/core';
import { CrmPlatformApiService } from '@galaxy/crm/static';
import { SENDER_ID_TOKEN } from '@galaxy/email-ui/email-activity/src/dependencies';
import {
  DYNAMIC_COMPONENT_DATA_TOKEN,
  EMAIL_BUILDER_APP_LISTING_SERVICE_TOKEN,
  EMAIL_BUILDER_ENABLED_RECIPES,
  PLACEHOLDER_LOGO_URL_TOKEN,
  TEMPLATE_HYDRATION_DATA_TOKEN,
} from '@galaxy/email-ui/email-builder';
import {
  VariableMenuItem,
  VariableMenuItemOptions,
} from '@galaxy/email-ui/email-builder/src/components/dynamic-component-selector';
import { buildSenderData, SENDER_INJECTION_TOKEN } from '@galaxy/email-ui/email-builder/src/shared';
import { SENDER_EMAIL_LIB_TOKEN } from '@galaxy/email-ui/email-library/dependencies';
import { TranslationModule as FormBuilderI18NModule } from '@galaxy/form-builder';
import { LEXICON_TRANSLATE_PORTAL_URL, LexiconModule } from '@galaxy/lexicon';
import { FeatureFlagService, PartnerServiceInterfaceToken } from '@galaxy/partner';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_ORIGIN_INJECTION_TOKEN,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
  PlatformIntegrationsI18nModule,
} from '@galaxy/platform-integrations/shared';
import { ReportingCoreModule } from '@galaxy/reports/static';
import { SmsContextToken, SmsInjectionToken, SmsModule } from '@galaxy/sms';
import { DEFAULT_LANGUAGE, TranslateModule, TranslateService, TranslateStore } from '@ngx-translate/core';
import { AccountGroupApiService } from '@vendasta/account-group';
import { Context } from '@vendasta/automata';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { AuxDataAccountsTableActionModule } from '@vendasta/auxiliary-data-components';
import { ActionListsServiceModule, BusinessProfileModule } from '@vendasta/businesses';
import { CountryStateServiceInterfaceToken } from '@vendasta/country-state';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyI18NModule, getLocale } from '@vendasta/galaxy/i18n';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyNavModule } from '@vendasta/galaxy/nav';
import { GalaxyDefaultProviderOverrides } from '@vendasta/galaxy/provider-default-overrides';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { SupportedContexts } from '@vendasta/platform-integrations';
import { ProductAnalyticsModule } from '@vendasta/product-analytics';
import { UserPipelinesModule } from '@vendasta/sales-ui';
import { OwnerType, SmsApiService } from '@vendasta/smsv2';
import { TaxonomyServiceInterfaceToken } from '@vendasta/taxonomy';
import { DateRangeSelectorModule, UIKitModule } from '@vendasta/uikit';
import { UsersModule, UsersServiceInterfaceToken } from '@vendasta/users';
import { DragulaModule } from 'ng2-dragula';
import { combineLatest, Observable, of } from 'rxjs';
import { distinctUntilChanged, filter, map, shareReplay, switchMap } from 'rxjs/operators';
import baseTranslation from '../assets/i18n/en_devel.json';
import { AccessModule } from './access';
import { AccessDeniedComponent } from './access-denied/access-denied.component';
import { AccountModule } from './account/account.module';
import { AccountsModule } from './accounts';
import { AI_KNOWLEDGE_CONFIG } from './ai-knowledge/providers';
import { AnalyticsModule } from './analytics';
import { AppConfig, AppConfigService } from './app-config.service';
import { AppNavItemComponent } from './app-nav-item.component';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { AssignedSalespersonModule } from './assigned-salesperson/assigned-salesperson.module';
import { BillingProviderModule } from './billing/billing-provider/billing-provider.module';
import { VendastaPaymentsGuard } from './billing/vendasta-payments.guard';
import { BulkImportModule } from './bulk-import/bulk-import.module';
import { CampaignConfigFactory } from './campaigns/providers';
import { AddressCellModule } from './common/address-cell';
import { HelpMenuComponent } from './common/help-menu';
import { LevelBadgeModule } from './common/level-badge/level-badge.module';
import { PricePipe } from './common/pipes/price.pipe';
import { VaAlertComponent } from './common/va-alert/va-alert.component';
import { VaDialogService } from './common/va-dialog.service';
import { VariableMenuMatDialogComponent } from './common/variable-menu/variable-menu-dialog/variable-menu-mat-dialog.component';
import { PartnerIdToken, WEBLATE_COMPONENT_NAME } from './constants';
import { StubAccessService } from './core/access';
import { Views } from './core/access/interface';
import { BusinessCenterService } from './core/business-center.service';
import { CountryStateService } from './core/country-state.service';
import { featureFlagFactory, FeatureFlags } from './core/features';
import { InboxModalGuard } from './core/guards/inbox-modal.guard';
import { MarketsService } from './core/markets/markets.service';
import { PartnerService } from './core/partner.service';
import { TaxonomyService } from './core/taxonomy.service';
import { ForAppEngineUrlSerializer } from './core/url-serializer.service';
import { UrlService } from './core/url.service';
import { ImageService } from './core/utils/image.service';
import { PartnerCrmModule } from './crm/partner-crm.module';
import { DashboardModule } from './dashboard';
import { EmailBuilderSandboxComponentModule } from './email-builder-sandbox/email-builder-sandbox.component';
import { EmailSettingsModule } from './email-settings/email-settings.module';
import { EmbedModule } from './embed/embed.module';
import { FEATURE_BULK_ACTIONS_V2 } from './flags';
import { HelpCenterModule } from './help-center/help-center.module';
import { HelpSideDrawerTemplateRefService } from './help-center/help-side-drawer/help-side-drawer-template-ref.service';
import { ConversationPartnerCenterService } from './inbox/conversation-partner-center.service';
import { imageServiceFactory } from './inbox/external-providers';
import { INBOX_CONFIG_FOR_CONVERSATION } from './inbox/providers';
import { SmsRegistrationLibraryInjectionToken } from './inbox/sms-configuration/providers';
import { IdentityProviderConfigModule } from './integrations/identity-provider-config/identity-provider-config.module';
import { RecipientProviderService } from './invoice/recipients/recipient-provider.service';
import { LocalLoginModule } from './local-login';
import {
  ActionListsLibraryInjectionToken,
  AuxDataAccountsActionInjectionToken,
} from './manage_accounts/filterby-custom-fields-page.component';
import { AppListingService } from './marketing/base-template-editing/app-listing.service';
import { buildHydrationData } from './marketing/base-template-editing/base-template-editing.module';
import { LogoSource } from './marketing/base-template-editing/template-editor-page/partner-logo';
import {
  AccountGroupSearcherComponent,
  OmniSearchComponent,
  PartnerOverviewComponent,
  PartnerSwitcherComponent,
  UserSwitcherComponent,
} from './omni-search/';
import { OmniAppIconComponent } from './omni-search/omni-app-icon.component';
import { ProductOrderFormProxyComponent } from './product-order-form-proxy/product-order-form-proxy.component';
import { PARTNER_CENTER_REPORTING_CONFIG_TOKEN } from './reporting/providers';
import { CurrencyService } from './saas-metrics/currency.service';
import { TermsOfServiceModule } from './terms-of-service';
import { NotificationPreferencesModule } from './user-notifications/notification-preferences.module';
import { UserBulkActionsModule } from './users/user-bulk-actions/user-bulk-actions.module';
import { WhitelabelModule } from './whitelabel/whitelabel.module';
import { AddUserService } from './work-order-details/add-user.service';
import { AI_ASSISTANTS_CONFIG } from './ai-assistant/providers';
import { AiAssistantModule } from '@galaxy/ai-assistant';
import { MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$, MS_CONTEXT } from '@galaxy/meeting-scheduler';
import { CachedRouteReuseStrategy } from './app.routing.strategy';
import {
  MEETING_ANALYSIS_NAMESPACE_INJECTION_TOKEN$,
  MEETING_ANALYSIS_ROUTE_PREFIX$,
  MeetingAnalysisI18nModule,
  ScheduleBotComponent,
} from '@galaxy/meeting-analysis/static';
import { ConfigManagerModule, ConfigManagerI18nModule } from '@vendasta/config-manager';
import { CONFIG_MANAGER_CONFIG } from './config-manager/providers';
import { MeetingSourceOrigin } from '@vendasta/meetings';

const unauthedRoutes = ['/signup', '/sign-up', '/login', '/forgot-password', '/login-two', '/forgot-password-two'];

@Injectable()
export class BulkActionFeatureCache {
  readonly enabled$: Observable<boolean>;

  constructor(@Inject('PARTNER_ID') partnerId$: Observable<string>, flagSvc: FeatureFlagService) {
    this.enabled$ = partnerId$.pipe(
      switchMap((partnerId) => flagSvc.batchGetStatus(partnerId, null, [FeatureFlags.BULK_ACTIONS_V2])),
      map((v) => v[FeatureFlags.BULK_ACTIONS_V2]),
      shareReplay(1),
    );
  }
}

export function isEnabled(cache: BulkActionFeatureCache): Observable<boolean> {
  return cache.enabled$;
}

const MAT_MODULES = [
  MatProgressBarModule,
  MatIconModule,
  MatInputModule,
  MatListModule,
  MatDialogModule,
  MatDialogModule,
  MatButtonModule,
  MatFormFieldModule,
  MatToolbarModule,
  MatTooltipModule,
  MatDividerModule,
  MatMenuModule,
  MatProgressSpinnerModule,
];

export function configFactory(config: AppConfigService, injector: Injector): () => Promise<boolean> {
  // IE10+ takes too long to initialize the location, we need to load the config after it is initialized
  function waitForLocationAndLoadConfig(resolve: any): Promise<boolean> {
    const locationInitialized: Promise<any> = injector.get(LOCATION_INITIALIZED, Promise.resolve(null));
    return locationInitialized.then(() => {
      if (unauthedRoutes.some((route) => window.location.href.indexOf(route) >= 0)) {
        return resolve(true);
      } else {
        return config
          .load()
          .then(() => resolve(true))
          .catch(() => {
            window.location.href = '/login/';
          });
      }
    });
  }

  return () => new Promise(waitForLocationAndLoadConfig);
}

const hasAccessToWeblateFactory = (accessService: StubAccessService) => {
  return accessService.hasAccessToViews([Views.partnerAll]).pipe(
    map((response) => {
      const hasAccess = response.get(Views.partnerAll);
      if (hasAccess) {
        return '/translate';
      }
      return null;
    }),
  );
};

const dynamicComponents: VariableMenuItem[] = [
  {
    textKey: 'EMAIL_BUILDER.DYNAMIC_COMPONENT_SELECTOR.CONTACT_DETAILS',
    replacementTags: ['crmCampaignContactFields'],
    options: {
      nestedDynamicSelector: true,
    } as VariableMenuItemOptions,
  },
  {
    textKey: 'EMAIL_BUILDER.DYNAMIC_COMPONENT_SELECTOR.PRIMARY_COMPANY_DETAILS',
    replacementTags: ['crmCampaignCompanyFields'],
    options: {
      nestedDynamicSelector: true,
    } as VariableMenuItemOptions,
  },
  {
    textKey: 'EMAIL_BUILDER.DYNAMIC_COMPONENT_SELECTOR.ACCOUNT_DETAILS',
    replacementTags: [
      'businessName',
      'businessWebsite',
      'customerIdentifier',
      'accountIdentifier',
      'firstName',
      'lastName',
      'greetingName',
      'phoneNumber',
    ],
  },
  {
    textKey: 'EMAIL_BUILDER.DYNAMIC_COMPONENT_SELECTOR.PARTNER_DETAILS',
    replacementTags: [
      'partnerName',
      'partnerWebsite',
      'partnerEmail',
      'partnerPhone',
      'partnerStreetAddress',
      'partnerStreetAddress2',
      'partnerCity',
      'partnerState',
      'partnerCountry',
      'partnerPostalCode',
    ],
  },
  {
    textKey: 'EMAIL_BUILDER.DYNAMIC_COMPONENT_SELECTOR.SALESPERSON_DETAILS',
    replacementTags: [
      'salesPersonContactCard',
      'salesPersonFirstName',
      'salesPersonLastName',
      'salesPersonEmail',
      'salesPersonPhoneNumber',
      'salesPersonJobTitle',
      'salesPersonLoginUrl',
      'meetingSchedulerName',
      'meetingSchedulerUrl',
    ],
  },
  {
    textKey: 'EMAIL_BUILDER.DYNAMIC_COMPONENT_SELECTOR.BUSINESS_APP_DETAILS',
    replacementTags: ['businessAppName', 'businessAppUrl', 'businessAppStoreUrl', 'businessAppPreviewUrl'],
  },
  {
    textKey: 'EMAIL_BUILDER.DYNAMIC_COMPONENT_SELECTOR.SNAPSHOT_REPORT',
    replacementTags: [
      'gradeReview',
      'gradeSocial',
      'gradeListing',
      'gradeWebsite',
      'gradeAdvertising',
      'gradeEcommerce',
      'gradeSEO',
      'snapshotReportUrl',
    ],
  },
  {
    textKey: 'EMAIL_BUILDER.DYNAMIC_COMPONENT_SELECTOR.PRODUCT_DETAILS',
    replacementTags: ['productName', 'productPreviewUrl', 'productUrl'],
    options: {
      nestedDynamicSelector: true,
    } as VariableMenuItemOptions,
  },
];

@NgModule({
  exports: [],
  declarations: [
    AppComponent,
    OmniSearchComponent,
    AccountGroupSearcherComponent,
    OmniAppIconComponent,
    PartnerSwitcherComponent,
    UserSwitcherComponent,
    ProductOrderFormProxyComponent,
    AppNavItemComponent,
    PartnerOverviewComponent,
    AccessDeniedComponent,
  ],
  bootstrap: [AppComponent],
  imports: [
    HelpMenuComponent,
    BrowserModule,
    LevelBadgeModule,
    CommonModule,
    DragulaModule.forRoot(),
    ProductAnalyticsModule,
    AnalyticsModule,
    ...MAT_MODULES,
    AccountsModule,
    BrowserAnimationsModule,
    DashboardModule,
    AppRoutingModule,
    AccountModule,
    TermsOfServiceModule,
    BusinessProfileModule,
    GoogleMapsModule,
    UserBulkActionsModule,
    NotificationPreferencesModule,
    AccessModule,
    TranslateModule,
    LexiconModule.forRoot({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    UIKitModule,
    LocalLoginModule,
    AtlasModule,
    EmbedModule,
    EmailSettingsModule,
    BulkImportModule,
    AssignedSalespersonModule,
    IdentityProviderConfigModule,
    DateRangeSelectorModule,
    GalaxySnackbarModule,
    GalaxyNavModule,
    GalaxyBadgeModule,
    GalaxyLoadingSpinnerModule,
    GalaxyI18NModule,
    GalaxyTooltipModule,
    WhitelabelModule,
    HelpCenterModule,
    AddressCellModule,
    EmailBuilderSandboxComponentModule,
    UserPipelinesModule,
    ConversationUIModule,
    ConversationCoreModule.forRoot({ config: INBOX_CONFIG_FOR_CONVERSATION }),
    ReportingCoreModule.forRoot({ config: PARTNER_CENTER_REPORTING_CONFIG_TOKEN }),
    InboxComponent,
    InboxButtonComponent,
    ActionListsServiceModule.forRoot(ActionListsLibraryInjectionToken),
    AuxDataAccountsTableActionModule.forRoot(AuxDataAccountsActionInjectionToken),
    PartnerCrmModule,
    BillingProviderModule,
    AutomataI18nModule,
    MeetingAnalysisI18nModule,
    UsersModule,
    InboxAIButtonComponent,
    SmsModule,
    VaAlertComponent,
    FormBuilderI18NModule,
    AiKnowledgeModule.forRoot({ config: AI_KNOWLEDGE_CONFIG }),
    AiAssistantModule.forRoot({ config: AI_ASSISTANTS_CONFIG }),
    PlatformIntegrationsI18nModule,
    ConfigManagerI18nModule,
    ConfigManagerModule.forRoot({ config: CONFIG_MANAGER_CONFIG }),
    ScheduleBotComponent,
  ],
  providers: [
    provideHttpClient(
      withInterceptorsFromDi(),
      withXsrfConfiguration({
        headerName: 'x-blast-shield',
        cookieName: 'x-blast-shield',
      }),
    ),
    VendastaPaymentsGuard,
    ...GalaxyDefaultProviderOverrides,
    AppConfigService,
    {
      provide: APP_INITIALIZER,
      useFactory: configFactory,
      deps: [AppConfigService, Injector],
      multi: true,
    },
    {
      provide: DEFAULT_LANGUAGE,
      useFactory: () => getLocale(),
    },
    StubAccessService,
    { provide: 'AccessService', useExisting: StubAccessService },
    {
      provide: 'PARTNER_ID',
      useFactory: (appConfigService: AppConfigService) =>
        appConfigService.config$.pipe(
          map((config) => config.partnerId),
          filter((partnerId) => !!partnerId),
          distinctUntilChanged(),
          shareReplay(1),
        ),
      deps: [AppConfigService],
    },
    {
      provide: PartnerIdToken,
      useExisting: 'PARTNER_ID',
      deps: ['PARTNER_ID'],
    },
    {
      provide: SENDER_ID_TOKEN,
      useFactory: (appConfigService: AppConfigService) =>
        appConfigService.config$.pipe(
          map((config) => config.partnerId),
          filter((partnerId) => !!partnerId),
          distinctUntilChanged(),
          shareReplay(1),
        ),
      deps: [AppConfigService],
    },
    {
      provide: SENDER_INJECTION_TOKEN,
      useFactory: buildSenderData,
    },
    {
      provide: MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$,
      useValue: MS_CONTEXT.MS_CONTEXT_PARTNER,
    },
    {
      provide: SENDER_EMAIL_LIB_TOKEN,
      useFactory: buildSenderData,
    },
    {
      provide: DYNAMIC_COMPONENT_DATA_TOKEN,
      useValue: dynamicComponents,
    },
    {
      provide: CONFIG_TOKEN,
      useFactory: CampaignConfigFactory,
    },
    {
      provide: 'MARKET_ID',
      useFactory: (marketService: MarketsService) => {
        return marketService.currentMarket$.pipe(
          map((market) => market.market_id),
          distinctUntilChanged(),
          shareReplay(1),
        );
      },
      deps: [MarketsService],
    },
    {
      provide: 'USER_ACCESSIBLE_MARKETS',
      useFactory: (marketsService: MarketsService) =>
        marketsService.userAccessibleMarkets$.pipe(distinctUntilChanged(), shareReplay(1)),
      deps: [MarketsService],
    },
    TranslateService,
    TranslateStore,
    {
      provide: 'USER_ID',
      useFactory: (a: AppConfigService) => {
        return a.config$.pipe(map((cfg: AppConfig) => cfg.unifiedUserId));
      },
      deps: [AppConfigService],
    },
    {
      provide: 'IS_IMPERSONATING',
      useFactory: (a: AppConfigService) => {
        return a.config$.pipe(map((cfg: AppConfig) => cfg.isImpersonated));
      },
      deps: [AppConfigService],
    },
    {
      provide: ATLAS_CONFIG_TOKEN,
      useFactory: (urlService: UrlService, appConfigService: AppConfigService) => {
        const partnerId$ = appConfigService.config$.pipe(map((config) => config.partnerId));
        const isSuperAdminRoute$ = urlService.isSuperAdminRoute$;
        return combineLatest([isSuperAdminRoute$, partnerId$]).pipe(
          map(([superadmin, partnerId]) => (superadmin ? 'VA' : partnerId)),
          map((partnerId) => ({ partnerId: partnerId, serviceProviderId: 'AA' })),
        );
      },
      deps: [UrlService, AppConfigService],
    },
    { provide: 'PartnerSdkFeatureFlagsService', useClass: FeatureFlagService },
    { provide: FeatureFlagService, useClass: FeatureFlagService },
    BulkActionFeatureCache,
    {
      provide: FEATURE_BULK_ACTIONS_V2,
      deps: [BulkActionFeatureCache],
      useFactory: isEnabled,
    },
    {
      provide: FeatureFlags.FRESHDESK_INTEGRATION,
      useFactory: featureFlagFactory(FeatureFlags.FRESHDESK_INTEGRATION),
      deps: [FeatureFlagService, 'PARTNER_ID', 'MARKET_ID'],
    },
    {
      provide: FeatureFlags.MANAGE_PLATFORM_INTEGRATIONS,
      useFactory: featureFlagFactory(FeatureFlags.MANAGE_PLATFORM_INTEGRATIONS),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.WEBLATE_TRANSLATION_PORTAL,
      useFactory: featureFlagFactory(FeatureFlags.WEBLATE_TRANSLATION_PORTAL),
      deps: [FeatureFlagService, 'PARTNER_ID', 'MARKET_ID'],
    },
    {
      provide: FeatureFlags.CUSTOM_DATA_FILTER_TABLE,
      useFactory: featureFlagFactory(FeatureFlags.CUSTOM_DATA_FILTER_TABLE),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.INVOICE_MULTI_FREQUENCY_WARNING,
      useFactory: featureFlagFactory(FeatureFlags.INVOICE_MULTI_FREQUENCY_WARNING),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.INBOX_AI_ATLAS_LINK,
      useFactory: featureFlagFactory(FeatureFlags.INBOX_AI_ATLAS_LINK),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.FULLSCREEN_INBOX_VIEW,
      useFactory: featureFlagFactory(FeatureFlags.FULLSCREEN_INBOX_VIEW),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.MEETINGS_AUTOMATIC_ANALYSIS,
      useFactory: featureFlagFactory(FeatureFlags.MEETINGS_AUTOMATIC_ANALYSIS),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.CRM_COMPANY,
      useFactory: featureFlagFactory(FeatureFlags.CRM_COMPANY),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.CREATE_SALES_ORDER,
      useFactory: featureFlagFactory(FeatureFlags.CREATE_SALES_ORDER),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.SALESPERSON_MARKET_ACCESS,
      useFactory: featureFlagFactory(FeatureFlags.SALESPERSON_MARKET_ACCESS),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.SALESPERSON_ORDER_APPROVAL_ACCEPTS_PAYMENT,
      useFactory: featureFlagFactory(FeatureFlags.SALESPERSON_ORDER_APPROVAL_ACCEPTS_PAYMENT),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.UNIFIED_MARKET_SELECTOR,
      useFactory: featureFlagFactory(FeatureFlags.UNIFIED_MARKET_SELECTOR),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.SCHEDULED_DEACTIVATIONS,
      useFactory: featureFlagFactory(FeatureFlags.SCHEDULED_DEACTIVATIONS),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.SUBSCRIPTION_TIERS_2024,
      useFactory: featureFlagFactory(FeatureFlags.SUBSCRIPTION_TIERS_2024),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.MARKETPLACE_SELL_PRODUCTS,
      useFactory: featureFlagFactory(FeatureFlags.MARKETPLACE_SELL_PRODUCTS),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.SNAP_VLC_WELCOME_EMAIL,
      useFactory: featureFlagFactory(FeatureFlags.SNAP_VLC_WELCOME_EMAIL),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.SHOW_SALES_AND_SUCCESS_CENTER,
      useFactory: featureFlagFactory(FeatureFlags.SHOW_SALES_AND_SUCCESS_CENTER),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: FeatureFlags.SHOW_MORE_SALESPERSON_PERMISSIONS,
      useFactory: featureFlagFactory(FeatureFlags.SHOW_MORE_SALESPERSON_PERMISSIONS),
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
    {
      provide: LEXICON_TRANSLATE_PORTAL_URL,
      useFactory: hasAccessToWeblateFactory,
      deps: [StubAccessService],
    },
    SnackbarService,
    HelpSideDrawerTemplateRefService,
    {
      provide: CONVERSATION_IMAGE_SERVICE_TOKEN,
      useFactory: imageServiceFactory,
      deps: [ImageService],
    },
    {
      provide: 'CURRENCY',
      useFactory: (currencyService: CurrencyService) => {
        return currencyService.currency$;
      },
      deps: [CurrencyService],
    },
    {
      provide: 'PARTNER_MARKET',
      useFactory: (partnerId$: Observable<string>, marketId$: Observable<string>) => {
        return combineLatest([partnerId$, marketId$]);
      },
      deps: ['PARTNER_ID', 'MARKET_ID'],
    },
    {
      provide: 'kFormat',
      useFactory: (currencyService: CurrencyService) => {
        return currencyService.kFormat$;
      },
      deps: [CurrencyService],
    },
    { provide: 'ACCOUNT_DETAILS_URL', useValue: '/businesses/accounts/${account_group_id}/details' },
    InboxModalGuard,
    AddUserService,
    {
      provide: UsersServiceInterfaceToken,
      useExisting: AddUserService,
    },
    { provide: TaxonomyServiceInterfaceToken, useExisting: TaxonomyService },
    {
      provide: CountryStateServiceInterfaceToken,
      useExisting: CountryStateService,
    },
    { provide: PartnerServiceInterfaceToken, useExisting: PartnerService },
    { provide: UrlSerializer, useClass: ForAppEngineUrlSerializer },
    PricePipe,
    VaDialogService,
    { provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN, useClass: ConversationPartnerCenterService },
    {
      provide: TEMPLATE_HYDRATION_DATA_TOKEN,
      useFactory: buildHydrationData,
      deps: ['PARTNER_ID', 'MARKET_ID', EnvironmentService, AccountGroupApiService],
    },
    { provide: EMAIL_BUILDER_APP_LISTING_SERVICE_TOKEN, useClass: AppListingService },
    { provide: EMAIL_BUILDER_ENABLED_RECIPES, useValue: ['product-button', 'meeting-scheduler-button'] },
    LogoSource,
    {
      provide: PLACEHOLDER_LOGO_URL_TOKEN,
      useFactory: (src: LogoSource) => src.logoURL$ as Observable<string>,
      deps: [LogoSource],
    },
    {
      provide: AUTOMATION_NAMESPACE_INJECTION_TOKEN$,
      useFactory: (partnerId$: Observable<string>) => partnerId$.pipe(distinctUntilChanged()),
      deps: ['PARTNER_ID'],
    },
    {
      provide: PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
      useFactory: (partnerId$: Observable<string>) => partnerId$.pipe(distinctUntilChanged()),
      deps: ['PARTNER_ID'],
    },
    {
      provide: PLATFORM_INTEGRATIONS_ORIGIN_INJECTION_TOKEN,
      useValue: MeetingSourceOrigin.PartnerCenter,
    },
    {
      provide: PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
      useFactory: (partnerId$: Observable<string>) => partnerId$.pipe(distinctUntilChanged()),
      deps: ['PARTNER_ID'],
    },
    {
      provide: AUTOMATION_PARTNER_ID_INJECTION_TOKEN$,
      useFactory: (partnerId$: Observable<string>) => partnerId$.pipe(distinctUntilChanged()),
      deps: ['PARTNER_ID'],
    },
    {
      provide: AUTOMATION_CONTEXT_INJECTION_TOKEN,
      useValue: Context.AUTOMATION_CONTEXT_PARTNER,
    },
    {
      provide: AUTOMATION_MARKET_ID_INJECTION_TOKEN$,
      useFactory: (marketsService: MarketsService) =>
        marketsService.userAccessibleMarkets$.pipe(
          distinctUntilChanged(),
          map((markets) => {
            // Use the default market if it exists, otherwise use the first (alphabetical) market
            const defaultMarket = markets.find((market) => market.market_id === 'default');
            return defaultMarket ? defaultMarket.market_id : markets[0].market_id;
          }),
        ),
      deps: [MarketsService],
    },
    {
      provide: PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
      useFactory: (marketsService: MarketsService) =>
        marketsService.userAccessibleMarkets$.pipe(
          distinctUntilChanged(),
          map((markets) => markets[0].market_id),
        ),
      deps: [MarketsService],
    },
    {
      provide: AUTOMATION_BUSINESS_ENTRY_URL_SERVICE,
      useClass: BusinessCenterService,
    },
    {
      provide: AUTOMATIONS_ACCESS_SERVICE,
      useExisting: StubAccessService,
    },
    {
      provide: AUTOMATIONS_MARKETS_SERVICE,
      useExisting: MarketsService,
    },
    {
      provide: AUTOMATION_IS_USER_SUPERADMIN$,
      useFactory: (appConfigService: AppConfigService) => appConfigService.config$.pipe(map((c) => c.isSuperAdmin)),
      deps: [AppConfigService],
    },
    {
      provide: AUTOMATION_DEFAULT_CURRENCY_CODE,
      useFactory: (currencyService: CurrencyService) => {
        return currencyService.currency$;
      },
      deps: [CurrencyService],
    },
    {
      provide: AUTOMATIONS_ROUTE_PREFIX$,
      useValue: of(''),
    },
    {
      provide: AUTOMATION_VARIABLE_MENU_DIALOG,
      useValue: VariableMenuMatDialogComponent,
    },
    {
      provide: MEETING_ANALYSIS_ROUTE_PREFIX$,
      useValue: of(''),
    },
    {
      provide: MEETING_ANALYSIS_NAMESPACE_INJECTION_TOKEN$,
      useFactory: (partnerId$: Observable<string>) => partnerId$.pipe(distinctUntilChanged()),
      deps: ['PARTNER_ID'],
    },
    {
      provide: PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
      useValue: SupportedContexts.PI_CONTEXT_PARTNER,
    },
    {
      provide: BILLING_MARKETS_SERVICE,
      useExisting: MarketsService,
    },
    {
      provide: SmsContextToken,
      useFactory: (partnerId$: Observable<string>) => ({
        ownerId$: partnerId$,
        ownerType: OwnerType.OWNER_TYPE_PARTNER,
        featureFlagInfo$: partnerId$.pipe(map((partnerId) => ({ partnerId }))),
        sendsViaBroadly$: of(false),
      }),
      deps: ['PARTNER_ID'],
    },
    CrmPlatformApiService,
    RecipientProviderService,
    SmsApiService,
    {
      provide: SmsInjectionToken,
      useExisting: SmsRegistrationLibraryInjectionToken,
    },
    {
      provide: RouteReuseStrategy,
      useClass: CachedRouteReuseStrategy,
    },
  ],
})
export class AppModule {}
