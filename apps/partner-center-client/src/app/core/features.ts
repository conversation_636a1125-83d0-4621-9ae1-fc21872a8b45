import { FeatureFlagService, FeatureFlagStatusInterface } from '@galaxy/partner';
import { combineLatest, Observable, of } from 'rxjs';
import { map, publishReplay, refCount, switchMap } from 'rxjs/operators';

export class FeatureFlags {
  public static readonly SALES_TEAM_DELETE = 'st_delete_sales_teams';
  public static readonly SAAS_METRICS = 'saas_metrics';
  public static readonly BULK_ACTIONS_V2 = 'bulk_actions_v2';
  public static readonly PCC_LOOP = 'pcc_loop';
  public static readonly SHOPPING_CART = 'business_center_shopping_cart'; // released to all but 1 partner is on the exclude list
  public static readonly VIG_ADAPTER = 'vig_adapter';
  public static readonly FACEBOOK_AD_LEADS_FOR_CLOUD_BROKERS = 'facebook_ad_leads_for_cloud_brokers';
  public static readonly CUSTOMIZE_CAMPAIGN_SEND_FROM = 'customize_campaign_send_from';
  public static readonly CUSTOMIZE_BUSINESS_APP = 'customize_business_app';
  public static readonly MEETING_SCHEDULER_BUSINESS_APP = 'meeting_scheduler_business_app';
  public static readonly CAMPAIGNS_TESSERACT_STATS = 'campaigns_tesseract_backend';
  public static readonly LEGACY_BRANDS = 'legacy_brands_access';
  public static readonly FRESHDESK_INTEGRATION = 'freshdesk_integration';
  public static readonly MANAGE_PLATFORM_INTEGRATIONS = 'manage_integrations_for_partner_center';
  public static readonly SNAPSHOT_LISTING_SCAN_PCC_SCC = 'snapshot_listing_scan_pcc_scc';
  public static readonly NEW_CUSTOMIZE_DESIGN = 'new_customize_design';
  public static readonly WEBLATE_TRANSLATION_PORTAL = 'i18n_pcc_weblate';
  public static readonly SUPPORT_HIDING_UPGRADE_CTA = 'support_hiding_upgrade_cta';
  public static readonly AUTOMATIONS_ORDER_WORKFLOW = 'automations_order_workflow';
  public static readonly CUSTOM_DATA_FILTER_TABLE = 'custom_account_data_filter';
  public static readonly INBOX_IN_PCC = 'inbox_tab_pcc';
  public static readonly INBOX_SMB_CAN_MESSAGE_PARTNER = 'inbox_smb_message_partner';
  public static readonly INVOICE_MULTI_FREQUENCY_WARNING = 'invoice_multi_frequency_warning';
  public static readonly CUSTOM_SALESPERSON_ACTIONS = 'sales_orders_custom_actions';
  public static readonly SCHEDULED_DEACTIVATIONS = '05_2024_scheduled_deactivations';
  public static readonly SUBSCRIPTION_TIERS_2024 = '2024_subscription_tiers';
  public static readonly MARKETPLACE_SELL_PRODUCTS = 'enable_marketplace_sell_products';
  public static readonly ACTIVATION_FLOW_CONTRACT_TERMS = '2024_05_pcc_activation_workflow_contract_terms';
  public static readonly ORDERS_BILLING_PERIODS = '2024_01_sales_opportunities_billing_periods';
  public static readonly UNIFIED_ORDERS_PAGE = '2024_07_pcc_unified_orders_page';
  public static readonly SNAP_VLC_WELCOME_EMAIL = 'snap_vlc_welcome_email';
  public static readonly EMAIL_TRIGGER_CRM_CONTACT = 'email_trigger_crm_contact';
  public static readonly CAN_WHITE_LABEL_SM = '10_2024_social_marketing_can_be_white_labeled';
  public static readonly BCC_CUSTOM_OBJECTS = 'bcc_crm_custom_objects';
  public static readonly INBOX_AI_SUPPORT_FEATURE_ID = 'inbox_ai_support';

  //Email system flags
  public static readonly CONVERT_LEGACY = 'convert_legacy_email';

  public static readonly INBOX_AI_ATLAS_LINK = 'inbox_ai_tab_pcc';
  public static readonly MEETINGS_AUTOMATIC_ANALYSIS = 'automatic_meeting_analysis';
  public static readonly INBOX_SMS = 'pion_partner_inbox_sms';
  public static readonly FULLSCREEN_INBOX_VIEW = 'fullscreen_inbox_view';

  public static readonly CRM_COMPANY = 'company_partner_center';
  public static readonly CRM_COMPANY_COMPANY_ASSOCIATION = 'crm_company_company_association';
  public static readonly CRM_OPPORTUNITY_BUSINESS_APP = 'crm_opportunity_business_app';
  public static readonly BUSINESS_APP_DYNAMIC_LISTS = 'business_app_dynamic_lists';
  public static readonly BUSINESS_APP_SALES_FEATURES = 'sales_feature_business_app';
  public static readonly CREATE_SALES_ORDER = 'create_sales_order';
  public static readonly OPPORTUNITY_CRM_ACTIVITY = 'opportunity_crm_activity';
  public static readonly BUSINESS_APP_PRO_BANNER = 'business_app_pro_banner';
  public static readonly SALESPERSON_MARKET_ACCESS = 'salespeople_market_access';
  public static readonly SALESPERSON_ORDER_APPROVAL_ACCEPTS_PAYMENT = `02_2024_collect_customer_payment_information`;
  public static readonly UNIFIED_MARKET_SELECTOR = 'unified_team_member_market_selector';
  public static readonly AUTOMATIONS_OAUTH = 'oauth2_automations';
  public static readonly SHOW_SALES_AND_SUCCESS_CENTER = 'show_sales_and_success_center';
  public static readonly BILLING_SERVICE_PERIODS = 'billing_service_periods';
  public static readonly DYNAMIC_LISTS = 'dynamic_lists';
  public static readonly SHOW_MORE_SALESPERSON_PERMISSIONS = 'additional_salesperson_permissions';
  public static readonly ACCOUNT_CONFIGURATIONS = 'account_configurations';
  public static readonly PCC_CUSTOM_OBJECTS = 'pcc_crm_custom_objects';

  //TRUD
  public static readonly NO_ADMIN_ORDER_CREATION = 'no_admin_order_creation';
}

/*
Generic function to wrap any feature flag factory for a feature flag injection token.
Usage:
In the app.module.ts providers or your modules providers add:
    {
      provide: FeatureFlags.WSP_SIDE_DRAWER_HELP,
      useFactory: featureFlagFactory(FeatureFlags.WSP_SIDE_DRAWER_HELP),
      deps: [FeatureFlagService, 'PARTNER_ID', 'MARKET_ID'],
    },

In your component's constructor add:
    @Inject(FeatureFlags.WSP_SIDE_DRAWER_HELP) public readonly showWordpressBasedHelp$: Observable<boolean>,
 */
export function featureFlagFactory(
  featureFlagId: string,
): (
  featureFlagService: FeatureFlagService,
  partnerId$: Observable<string>,
  marketId$?: Observable<string>,
) => Observable<boolean> {
  return function (
    featureFlagService: FeatureFlagService,
    partnerId$: Observable<string>,
    marketId$?: Observable<string>,
  ): Observable<boolean> {
    return combineLatest([partnerId$, marketId$ || of('')]).pipe(
      switchMap(([pid, marketId]) => {
        return featureFlagService.batchGetStatus(pid, marketId, [featureFlagId]);
      }),
      map((res: FeatureFlagStatusInterface) => res[featureFlagId]),
      publishReplay(1),
      refCount(),
    );
  };
}
