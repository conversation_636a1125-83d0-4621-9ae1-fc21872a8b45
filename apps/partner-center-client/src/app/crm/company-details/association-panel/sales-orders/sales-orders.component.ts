import { CommonModule } from '@angular/common';
import { Component, Inject, inject, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { COMPANY_PROFILE_CARD_DATA_TOKEN, CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { CreateOrderDialogService } from '@vendasta/orders';
import {
  ListSalesOrderRequestFilters,
  ListSalesOrderRequestSortDirection,
  ListSalesOrderRequestSortField,
  ListSalesOrderRequestSortOption,
  SalesOrdersService,
  Status,
} from '@vendasta/sales-orders';
import { Badge, StatusToBadgeMapping } from '@vendasta/sales-ui';
import { BehaviorSubject, combineLatest, map, Observable, of, switchMap, take, tap } from 'rxjs';
import { ALL_SALES_ORDERS, buildOrdersPageForBusinessQuery, SALES_ORDER_DETAILS } from '../../../../sales-orders/urls';
import { CompanyAccountService } from '../../../company-account.service';

interface DisplayOrder {
  orderId: string;
  businessId: string;
  badge: Badge;
  requestedActivation: Date;
}

@Component({
  selector: 'app-sales-orders',
  imports: [
    CommonModule,
    MatExpansionModule,
    MatButtonModule,
    GalaxyLoadingSpinnerModule,
    TranslateModule,
    MatIconModule,
    MatCardModule,
    GalaxyBadgeModule,
  ],
  templateUrl: './sales-orders.component.html',
  styleUrls: ['../association-panel-base-styles.component.scss'],
})
export class SalesOrdersComponent implements OnInit {
  private readonly data = inject(COMPANY_PROFILE_CARD_DATA_TOKEN);
  accountGroupId$ = this.accountCreationService.accountGroupId$;
  protected createSalesOrderButtonLoading$ = this.accountCreationService.createAccountGroupLoading$(
    this.data.company_id,
  );

  protected noAdminOrderCreation$: Observable<boolean> = this.crmConfig.canPartnerAdminCreateOrdersFeatureFlag$;
  protected hasMoreOrders$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  protected orders$: Observable<DisplayOrder[]>;

  constructor(
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    @Inject('MARKET_ID') private readonly marketId$: Observable<string>,
    @Inject('USER_ID') private readonly userId$: Observable<string>,
    private router: Router,
    private readonly accountCreationService: CompanyAccountService,
    private readonly snackbarService: SnackbarService,
    private readonly salesOrdersService: SalesOrdersService,
    private readonly createOrderDialogService: CreateOrderDialogService,
    @Inject(CrmInjectionToken) private readonly crmConfig: CrmDependencies,
  ) {}

  ngOnInit(): void {
    this.accountCreationService.setAccountGroupId(this.data.account_group_id ?? '');
    this.setupOrdersFetching();
  }

  openOrderDetails(order: DisplayOrder): void {
    this.router.navigate([SALES_ORDER_DETAILS(order.businessId, order.orderId)]);
  }

  viewMoreOrders(): void {
    this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
      const params = buildOrdersPageForBusinessQuery(accountGroupId);
      this.router.navigate([ALL_SALES_ORDERS], { queryParams: params });
    });
  }

  onCreateSalesOrderButtonClicked(): void {
    combineLatest([
      this.accountCreationService.fetchAccountGroupId$(this.data.company_id),
      this.partnerId$,
      this.marketId$,
      this.userId$,
    ])
      .pipe(
        switchMap(([accountGroupId, partnerId, marketId, userId]) => {
          if (accountGroupId && partnerId) {
            return this.createOrderDialogService.createOrderAndRedirect(
              partnerId,
              marketId,
              accountGroupId,
              userId,
              (businessId, orderId) => `/order-management/${businessId}/view/${orderId}`,
              true,
            );
          } else {
            this.snackbarService.openErrorSnack('CRM.ERROR.UNABLE_TO_CREATE_SALES_ORDER');
          }
        }),
        take(1),
      )
      .subscribe();
  }

  private setupOrdersFetching(): void {
    this.orders$ = combineLatest([this.accountGroupId$, this.partnerId$]).pipe(
      switchMap(([accountGroupId, partnerId]) => {
        if (!accountGroupId) {
          return of(null);
        }
        return this.salesOrdersService.list(
          partnerId,
          new ListSalesOrderRequestFilters({ businessId: accountGroupId }),
          new ListSalesOrderRequestSortOption({
            field: ListSalesOrderRequestSortField.CREATED,
            direction: ListSalesOrderRequestSortDirection.DESCENDING,
          }),
          '',
          5,
        );
      }),
      tap((resp) => this.hasMoreOrders$$.next(resp?.hasMore || false)),
      map((resp) => {
        const results = resp?.results || [];
        return results.map((order) => {
          return {
            businessId: order?.businessId || '',
            orderId: order?.orderId || '',
            badge: StatusToBadgeMapping.get(order?.status || Status.SUBMITTED),
            requestedActivation: order?.requestedActivation,
          } as DisplayOrder;
        });
      }),
    );
  }
}
