<mat-expansion-panel class="panel">
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'SALES_ORDERS.ORDERS' | translate }}
    </mat-panel-title>
  </mat-expansion-panel-header>
  <ng-container *ngIf="createSalesOrderButtonLoading$ | async; else createSalesOrderButtonActive">
    <button mat-stroked-button color="primary" class="card-button" type="button" disabled>
      <glxy-loading-spinner [size]="'small'"></glxy-loading-spinner>
    </button>
  </ng-container>

  <ng-container *ngIf="orders$ | async as orders; else loadingSpinner">
    <mat-card *ngFor="let order of orders" appearance="outlined" class="association-card-contents">
      <mat-card-title>
        <a (click)="openOrderDetails(order)">
          {{ order.orderId }}
        </a>
      </mat-card-title>
      <mat-card-subtitle>
        <span *ngIf="!!order.requestedActivation">
          {{ 'SALES_ORDERS.CONTRACT_START' | translate }}: {{ order.requestedActivation | date }}
          <br />
        </span>
        <div class="status-badge">
          <glxy-badge [color]="order.badge.color">
            {{ order.badge.text | translate }}
          </glxy-badge>
        </div>
      </mat-card-subtitle>
    </mat-card>
    <button
      *ngIf="hasMoreOrders$$ | async"
      mat-stroked-button
      color="primary"
      class="card-button"
      type="button"
      (click)="viewMoreOrders()"
    >
      {{ 'COMMON.ACTION_LABELS.VIEW_MORE' | translate }}
    </button>
  </ng-container>
</mat-expansion-panel>

<ng-template #loadingSpinner>
  <glxy-loading-spinner id="spinner-container" [fullWidth]="true" [fullHeight]="true"></glxy-loading-spinner>
</ng-template>
<ng-template #createSalesOrderButtonActive>
  @if (noAdminOrderCreation$ | async) {
    <button
      mat-stroked-button
      color="primary"
      class="card-button"
      type="button"
      (click)="onCreateSalesOrderButtonClicked()"
    >
      <mat-icon>add</mat-icon>
      {{ 'SALES_ORDERS.CREATE_SALES_ORDER' | translate }}
    </button>
  }
</ng-template>
