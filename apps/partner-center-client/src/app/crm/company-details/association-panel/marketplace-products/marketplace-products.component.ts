import { CommonModule } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { COMPANY_PROFILE_CARD_DATA_TOKEN, CrmInjectionToken } from '@galaxy/crm/static';
import { MatCardModule } from '@angular/material/card';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { ActiveItemsModule } from '@galaxy/inventory-ui';
import { CrmMarketplaceProductsAssociationCardService } from './marketplace-products.service';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { catchError, firstValueFrom, of, switchMap } from 'rxjs';
import { map } from 'rxjs/operators';
import { Router } from '@angular/router';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { AppConfigService } from '../../../../app-config.service';
import { CompanyAccountService } from '../../../company-account.service';

const defaultMarket = 'default';

@Component({
  selector: 'app-pcc-crm-marketplace-products-association-card',
  imports: [
    CommonModule,
    TranslateModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatCardModule,
    GalaxyBadgeModule,
    ActiveItemsModule,
    GalaxyAvatarModule,
    GalaxyLoadingSpinnerModule,
  ],
  templateUrl: './marketplace-products.component.html',
  styleUrls: ['./marketplace-products.component.scss'],
  providers: [CrmMarketplaceProductsAssociationCardService],
})
export class CrmMarketplaceProductsAssociationCardComponent {
  private readonly service = inject(CrmMarketplaceProductsAssociationCardService);
  private readonly data = inject(COMPANY_PROFILE_CARD_DATA_TOKEN);
  private readonly config = inject(CrmInjectionToken);
  private readonly appConfigService = inject(AppConfigService);
  private readonly companyAccountService = inject(CompanyAccountService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly router = inject(Router);

  private readonly canNavigateToProducts$ = this.config.namespace$.pipe(
    switchMap(
      (partnerId) =>
        this.config.services?.accessService?.canAccessProductPage$?.(partnerId, this.data.group_id || defaultMarket) ??
        of(false),
    ),
    catchError(() => of(false)),
  );

  protected readonly products = toSignal(this.service.products$);
  protected readonly isLoaded = computed(() => this.products() !== undefined);
  protected readonly hasProducts = computed(() => !!this.products()?.length);
  protected readonly hasFailed = toSignal(this.service.getProductsFailed$);
  protected readonly canNavigateToProducts = toSignal(this.canNavigateToProducts$);
  protected readonly canEnabledApps = toSignal(
    this.appConfigService.config$.pipe(map((config) => config.canEnableApps)),
  );
  protected readonly isAccountGroupLoading = toSignal(
    this.companyAccountService.createAccountGroupLoading$(this.data.company_id),
  );

  protected readonly noAdminOrderCreation = toSignal(this.config.canPartnerAdminCreateOrdersFeatureFlag$);

  constructor() {
    this.config.namespace$.pipe(takeUntilDestroyed()).subscribe((partnerId) => this.service.setPartnerId(partnerId));
    this.service.setAccountGroupId(this.data.account_group_id ?? '');
    this.service.setMarketId(this.data.group_id || defaultMarket);
  }

  protected handleProductClick(url: string): void {
    const parsedUrl = this.injectAccountId(url, this.data.account_group_id ?? '');
    window.open(parsedUrl, '_blank');
  }

  private injectAccountId(url: string, accountGroupId: string): string {
    return url.replaceAll('<accountId>', accountGroupId).replaceAll('%3CaccountId%3E', accountGroupId);
  }

  open(): void {
    const companyId = this.data.company_id;
    firstValueFrom(this.companyAccountService.fetchAccountGroupId$(companyId)).then((accountGroupId) => {
      if (accountGroupId) {
        this.router.navigate([`/businesses/accounts/${accountGroupId}/activation`], {
          queryParams: {
            navigationSource: 'crm',
            navigationSourceId: companyId,
          },
        });
      } else {
        this.snackbarService.openErrorSnack('CRM.ERROR.ACCOUNT_GROUP_CREATION_FAILED');
      }
    });
  }
}
