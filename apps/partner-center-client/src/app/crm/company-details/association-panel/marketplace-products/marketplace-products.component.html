<mat-expansion-panel class="panel">
  <mat-expansion-panel-header>
    <mat-panel-title>{{ 'ASSOCIATIONS.PRODUCTS.TITLE' | translate }}</mat-panel-title>
  </mat-expansion-panel-header>
  @if (canEnabledApps()) {
    @if (isAccountGroupLoading()) {
      <button mat-stroked-button color="primary" class="button-order-products" type="button" disabled>
        <glxy-loading-spinner [size]="'small'"></glxy-loading-spinner>
      </button>
    } @else {
      @if (noAdminOrderCreation()) {
        <button mat-stroked-button color="primary" class="button-order-products" (click)="open()">
          <mat-icon>add</mat-icon>
          {{ 'BUSINESS.ORDER_PRODUCT' | translate }}
        </button>
      }
    }
  }
  @defer (when isLoaded()) {
    @if (hasProducts()) {
      @for (product of products(); track product.productId) {
        <mat-card appearance="outlined" class="card">
          <mat-card-content>
            <ng-container *ngTemplateOutlet="productView; context: { product }"></ng-container>
            @for (addon of product.addons; track addon.productId) {
              <div class="addon">
                <ng-container *ngTemplateOutlet="productView; context: { product: addon }"></ng-container>
              </div>
            }
          </mat-card-content>
        </mat-card>
      }
    } @else if (hasFailed()) {
      <div class="empty">
        {{ 'ASSOCIATIONS.PRODUCTS.ERROR' | translate }}
      </div>
    } @else {
      <div class="empty">{{ 'ASSOCIATIONS.PRODUCTS.NO_PRODUCTS' | translate }}</div>
    }
  } @placeholder {
    <glxy-loading-spinner></glxy-loading-spinner>
  }
</mat-expansion-panel>

<ng-template #productView let-product="product">
  <div class="product-title">
    <glxy-avatar [name]="product.name" [src]="product.iconUrl" [width]="28"></glxy-avatar>
    @if (canNavigateToProducts() && product.entryUrl) {
      <a (click)="handleProductClick(product.entryUrl)">{{ product.name }}</a>
    } @else {
      {{ product.name }}
    }
    @if (product.quantity > 1) {
      <span>({{ product.quantity }})</span>
    }
  </div>
  <div class="product-details">
    @if (product.isTrial) {
      @if (product.trialEnded) {
        <div>
          {{ 'ASSOCIATIONS.PRODUCTS.TRIAL_UPGRADE' | translate }}
        </div>
        <div class="product-tags">
          <glxy-badge [color]="'red'">{{ 'ASSOCIATIONS.PRODUCTS.STATUS.TRIAL_ENDED' | translate }}</glxy-badge>
        </div>
      } @else {
        <div>
          {{ 'ASSOCIATIONS.PRODUCTS.TRIAL_SINCE' | translate }}: {{ product.activationDate | date: 'mediumDate' }}
        </div>
        <div>
          {{ 'ASSOCIATIONS.PRODUCTS.ENDS' | translate }}: {{ product.trialExpirationDate | date: 'mediumDate' }} ({{
            'ASSOCIATIONS.PRODUCTS.DAYS_REMAINING' | translate: { days: product.trialDaysRemaining }
          }})
        </div>
        <div class="product-tags">
          <glxy-badge [color]="'blue'">{{ 'ASSOCIATIONS.PRODUCTS.STATUS.TRIAL' | translate }}</glxy-badge>
        </div>
      }
    } @else {
      <div>
        {{ 'ASSOCIATIONS.PRODUCTS.ACTIVE_SINCE' | translate }}: {{ product.activationDate | date: 'mediumDate' }}
      </div>
      @if (product.renewalDate) {
        <div>{{ 'ASSOCIATIONS.PRODUCTS.RENEWS' | translate }}: {{ product.renewalDate | date: 'mediumDate' }}</div>
      }
      @if (product.commitmentDate) {
        <div>
          {{ 'ASSOCIATIONS.PRODUCTS.COMMITTED_UNTIL' | translate }}: {{ product.commitmentDate | date: 'mediumDate' }}
        </div>
      }
    }
  </div>
</ng-template>
