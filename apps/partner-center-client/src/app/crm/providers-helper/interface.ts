import { Router } from '@angular/router';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { AddContactsToCampaignRedirectService } from '@galaxy/campaign/shared';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { InboxNavigationService } from '@galaxy/conversation/core';
import { AutomationActionsService } from '@galaxy/crm/integrations/automation';
import {
  CrmAccessService,
  CompanyService,
  CrmFieldService,
  CrmObjectDisplayService,
  SidePanelContentService,
  TranslateForCrmObjectService,
} from '@galaxy/crm/static';
import { FeatureFlagService, PartnerService, PartnerSubscriptionService, WhitelabelService } from '@galaxy/partner';
import { WhitelabelTranslationService } from '@galaxy/snapshot';
import { TranslateService } from '@ngx-translate/core';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { SalespersonService } from '@vendasta/sales';
import { OpportunityService } from '@vendasta/sales-ui';
import { SSOService } from '@vendasta/sso';
import { Observable } from 'rxjs';
import { AppConfigService } from '../../app-config.service';
import { BusinessCenterService } from '../../core/business-center.service';
import { FeatureFlags } from '../../core/features';
import { SnapshotService } from '../../core/snapshot.service';
import { SnapshotSidepanelService } from '../company-table/snapshot-sidepanel/snapshot-sidepanel.service';
import { FeatureAccessService } from '../feature-access.service';
import { PartnerCRMOpportunityService } from '../opportunity.service';
import { CRMUserService } from '../user.service';
import { PartnerCRMSalespersonMarketService } from '../salesperson-market.service';
import { AccountGroupService } from '@galaxy/account-group';
import { MatDialog } from '@angular/material/dialog';
import { HostService } from '@vendasta/meetings';
import { MeetingEventService } from '@galaxy/crm/integrations/meeting-event';
import { CRMFieldSchemaApiService } from '@vendasta/crm';
import { ListActionsService } from '@galaxy/crm/integrations/dynamic-lists-actions';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ExportActionsService } from '@galaxy/crm/dynamic';
import { AtlasLanguageService } from '@galaxy/atlas/core';
import { CampaignActionsService } from '@galaxy/crm/integrations/yesware';
import { StubAccessService } from '../../core/access';

export interface Services {
  accessService: StubAccessService;
  crmAccessService: CrmAccessService;
  appConfigService: AppConfigService;
  automationActionsService: AutomationActionsService;
  businessCenterService: BusinessCenterService;
  companyService: CompanyService;
  contactCampaignService: AddContactsToCampaignRedirectService;
  crmFieldService: CrmFieldService;
  crmFieldSchemaApiService: CRMFieldSchemaApiService;
  crmUserService: CRMUserService;
  exportActionsService: ExportActionsService;
  featureAccessService: FeatureAccessService;
  featureFlagService: FeatureFlagService;
  inboxNavigationService: InboxNavigationService;
  marketsService: PartnerCRMSalespersonMarketService;
  opportunityService: OpportunityService;
  partnerCRMOpportunityService: PartnerCRMOpportunityService;
  partnerService: PartnerService;
  partnerSubscriptionService: PartnerSubscriptionService;
  productAnalyticsService: ProductAnalyticsService;
  router: Router;
  salespersonService: SalespersonService;
  sidePanelContentService: SidePanelContentService;
  snapshotService: SnapshotService;
  snapshotSidepanelService: SnapshotSidepanelService;
  ssoService: SSOService;
  translationService: TranslateService;
  whitelabelService: WhitelabelService;
  whitelabelTranslationService: WhitelabelTranslationService;
  translateForObjectService: TranslateForCrmObjectService;
  accountGroupService: AccountGroupService;
  dialogService: MatDialog;
  listActionsService: ListActionsService;
  yeswareCampaignActionsService: CampaignActionsService;
  meetingService: HostService;
  meetingEventService: MeetingEventService;
  snackbar: SnackbarService;
  languageService: AtlasLanguageService;
  crmObjectDisplayService: CrmObjectDisplayService;
}

export interface Config {
  canAccessMarketingAutomation$: Observable<boolean>;
  canStartManualAutomation$: Observable<boolean>;
  canAddToList$: Observable<boolean>;
  currentUserId$: Observable<string>;
  locale$: Observable<string>;
  featureFlags$: {
    [FeatureFlags.CRM_COMPANY_COMPANY_ASSOCIATION]: Observable<boolean>;
    [FeatureFlags.INBOX_SMS]: Observable<boolean>;
    [FeatureFlags.PCC_CUSTOM_OBJECTS]: Observable<boolean>;
    [FeatureFlags.ACCOUNT_CONFIGURATIONS]: Observable<boolean>;
    [FeatureFlags.NO_ADMIN_ORDER_CREATION]: Observable<boolean>;
  };
  isUserSuperAdmin$: Observable<boolean>;
  partnerId$: Observable<string>;
  salespersonId$: Observable<string>;
  isMeetingSchedulerConfigured$: Observable<boolean>;
  hasMeetingFeatureFlag$: Observable<boolean>;
}
