import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { AUTOMATION_IS_USER_SUPERADMIN$ } from '@galaxy/automata/shared';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { AddContactsToCampaignRedirectService } from '@galaxy/campaign/shared';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { InboxNavigationService } from '@galaxy/conversation/core';
import { AutomationActionsService } from '@galaxy/crm/integrations/automation';
import {
  CompanyService,
  CRM_ACCESS_SERVICE_TOKEN,
  CrmFieldService,
  CrmObjectDisplayService,
  ObjectType,
  PresetFilter,
  SidePanelContentService,
  TranslateForCrmObjectService,
} from '@galaxy/crm/static';
import { FeatureFlagService, PartnerService, PartnerSubscriptionService, WhitelabelService } from '@galaxy/partner';
import { WhitelabelTranslationService } from '@galaxy/snapshot';
import { Salesperson } from '@galaxy/types';
import { TranslateService } from '@ngx-translate/core';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { SalespersonService } from '@vendasta/sales';
import { Opportunity as ApiOpportunity } from '@vendasta/sales-opportunities';
import {
  MARKET_SELECTOR_SERVICE_TOKEN,
  OpportunityService,
  OrdersUrl,
  OrdersUrlFunction,
  SalesUIOpportunityConfig,
  UserPipelines,
  UserPipelinesServiceConfig,
} from '@vendasta/sales-ui';
import { SSOService } from '@vendasta/sso';
import { Feature } from 'marketplace-ui';
import { combineLatest, Observable, of } from 'rxjs';
import { catchError, distinctUntilChanged, filter, map, shareReplay, startWith, switchMap } from 'rxjs/operators';
import { AppConfigService } from '../../app-config.service';
import { BusinessCenterService } from '../../core/business-center.service';
import { FeatureFlags } from '../../core/features';
import { SnapshotService } from '../../core/snapshot.service';
import { SalespeopleService } from '../../manage_accounts/salespeople.service';
import { BusinessInfoService } from '../business-info.service';
import { SnapshotSidepanelService } from '../company-table/snapshot-sidepanel/snapshot-sidepanel.service';
import { FeatureAccessService } from '../feature-access.service';
import { PartnerCRMOpportunityService } from '../opportunity.service';
import { CRMUserService } from '../user.service';
import { Config, Services } from './interface';
import { PartnerCRMSalespersonMarketService } from '../salesperson-market.service';
import { MatDialog } from '@angular/material/dialog';
import { HostService, IsCalendarConfiguredRequestInterface } from '@vendasta/meetings';
import { MeetingEventService } from '@galaxy/crm/integrations/meeting-event';
import { CRMFieldSchemaApiService } from '@vendasta/crm';
import { ListActionsService } from '@galaxy/crm/integrations/dynamic-lists-actions';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { CacheFactory, CacheInjectionOptions, CacheStrategy } from '@galaxy/crm/components/cache';
import { ExportActionsService } from '@galaxy/crm/dynamic';
import { AtlasLanguageService } from '@galaxy/atlas/core';
import { CampaignActionsService } from '@galaxy/crm/integrations/yesware';
import { StubAccessService } from '../../core/access';
import { GalaxyDateDefault, GalaxyFilterInterface, GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';

export function servicesFactory(): Services {
  const cacheStrategy: CacheStrategy = { maxAge: 20000, observable: { share: true }, autoCleanup: true };
  const crmUserServiceCacheOptions: CacheInjectionOptions<CRMUserService> = {
    methods: [{ method: 'getMultiUsers', strategy: cacheStrategy }],
  };
  const userService = CacheFactory.inject(CRMUserService, crmUserServiceCacheOptions);

  return {
    accessService: inject(StubAccessService),
    crmAccessService: inject(CRM_ACCESS_SERVICE_TOKEN),
    appConfigService: inject(AppConfigService),
    automationActionsService: inject(AutomationActionsService),
    businessCenterService: inject(BusinessCenterService),
    companyService: inject(CompanyService),
    contactCampaignService: inject(AddContactsToCampaignRedirectService),
    crmFieldService: inject(CrmFieldService),
    crmFieldSchemaApiService: inject(CRMFieldSchemaApiService),
    crmUserService: userService,
    exportActionsService: inject(ExportActionsService),
    featureAccessService: inject(FeatureAccessService),
    featureFlagService: inject(FeatureFlagService),
    inboxNavigationService: inject(InboxNavigationService),
    marketsService: inject(PartnerCRMSalespersonMarketService),
    opportunityService: inject(OpportunityService),
    partnerCRMOpportunityService: inject(PartnerCRMOpportunityService),
    partnerService: inject(PartnerService),
    partnerSubscriptionService: inject(PartnerSubscriptionService),
    productAnalyticsService: inject(ProductAnalyticsService),
    router: inject(Router),
    salespersonService: inject(SalespersonService),
    sidePanelContentService: inject(SidePanelContentService),
    snapshotService: inject(SnapshotService),
    snapshotSidepanelService: inject(SnapshotSidepanelService),
    ssoService: inject(SSOService),
    translationService: inject(TranslateService),
    whitelabelService: inject(WhitelabelService),
    whitelabelTranslationService: inject(WhitelabelTranslationService),
    translateForObjectService: inject(TranslateForCrmObjectService),
    dialogService: inject(MatDialog),
    listActionsService: inject(ListActionsService),
    yeswareCampaignActionsService: inject(CampaignActionsService),
    meetingService: inject(HostService),
    meetingEventService: inject(MeetingEventService),
    snackbar: inject(SnackbarService),
    languageService: inject(AtlasLanguageService),
    crmObjectDisplayService: inject(CrmObjectDisplayService),
  } as Services;
}

export function configFactory(services: Services): Config {
  const partnerId$ = services.partnerService.getPartnerId();
  const isUserSuperAdmin$ = inject(AUTOMATION_IS_USER_SUPERADMIN$);
  const flags = [
    FeatureFlags.CRM_COMPANY_COMPANY_ASSOCIATION,
    FeatureFlags.INBOX_SMS,
    FeatureFlags.DYNAMIC_LISTS,
    FeatureFlags.EMAIL_TRIGGER_CRM_CONTACT,
    FeatureFlags.ACCOUNT_CONFIGURATIONS,
    FeatureFlags.PCC_CUSTOM_OBJECTS,
    FeatureFlags.NO_ADMIN_ORDER_CREATION,
  ];
  const featureFlags$ = partnerId$.pipe(
    switchMap((partnerId) => services.featureFlagService.batchGetStatus(partnerId, '', flags)),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  const automationsOauth$ = featureFlags$.pipe(map((flags) => flags[FeatureFlags.AUTOMATIONS_OAUTH]));
  const canStartManualAutomation$ = combineLatest([
    automationsOauth$,
    services.featureAccessService.hasAccessToFeature(Feature.automations),
    services.crmAccessService.canAccessAutomations$,
  ]).pipe(
    map(([useOauth, hasAccess, viewAccess]) => {
      return hasAccess && viewAccess && !useOauth;
    }),
  );
  const currentUserId$ = services.appConfigService.config$.pipe(
    map((config) => config.unifiedUserId),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  const locale$ = services.languageService.language$;
  const salespersonId$ = combineLatest([partnerId$, currentUserId$]).pipe(
    switchMap(([partnerId, userId]) => services.salespersonService.getSalespersonByUserId(partnerId, userId)),
    map((salesperson) => salesperson.salespersonId),
    shareReplay({ bufferSize: 1, refCount: true }),
    catchError(() => of('')),
  );
  const canAccessMarketingAutomation$ = services.partnerService
    .getPartnerId()
    .pipe(
      switchMap((partnerId) =>
        services.partnerSubscriptionService.canAccessFeature(Feature.marketingAutomation, partnerId),
      ),
    );
  const canAddToList$ = featureFlags$.pipe(map((flags) => flags[FeatureFlags.DYNAMIC_LISTS]));

  const isMeetingSchedulerConfigured$ = combineLatest([partnerId$, currentUserId$]).pipe(
    map(([namespace, currentUser]) => {
      const request: IsCalendarConfiguredRequestInterface = {
        applicationContextProperties: {
          partner_id: namespace,
          user_context: 'PARTNER',
        },
        userId: currentUser,
      };
      return request;
    }),
    switchMap((request) => services.meetingService.IsCalendarConfigured(request)),
    map((response) => {
      return response.isConfigured === true;
    }),
  );

  const hasMeetingFeatureFlag$ = featureFlags$.pipe(
    map((featureFlagStatus) => featureFlagStatus[FeatureFlags.EMAIL_TRIGGER_CRM_CONTACT] ?? false),
    startWith(false),
  );

  return {
    canAccessMarketingAutomation$: canAccessMarketingAutomation$,
    canStartManualAutomation$: canStartManualAutomation$,
    canAddToList$: canAddToList$,
    currentUserId$: currentUserId$,
    locale$: locale$,
    featureFlags$: {
      [FeatureFlags.CRM_COMPANY_COMPANY_ASSOCIATION]: featureFlags$.pipe(
        map((flags) => flags[FeatureFlags.CRM_COMPANY_COMPANY_ASSOCIATION]),
      ),
      [FeatureFlags.INBOX_SMS]: featureFlags$.pipe(map((flags) => flags[FeatureFlags.INBOX_SMS])),
      [FeatureFlags.PCC_CUSTOM_OBJECTS]: featureFlags$.pipe(map((flags) => flags[FeatureFlags.PCC_CUSTOM_OBJECTS])),
      [FeatureFlags.ACCOUNT_CONFIGURATIONS]: featureFlags$.pipe(
        map((flags) => flags[FeatureFlags.ACCOUNT_CONFIGURATIONS]),
      ),
      [FeatureFlags.NO_ADMIN_ORDER_CREATION]: featureFlags$.pipe(
        map((flags) => flags[FeatureFlags.NO_ADMIN_ORDER_CREATION]),
      ),
    },
    isUserSuperAdmin$: isUserSuperAdmin$,
    partnerId$: partnerId$,
    salespersonId$: salespersonId$,
    isMeetingSchedulerConfigured$: isMeetingSchedulerConfigured$,
    hasMeetingFeatureFlag$: hasMeetingFeatureFlag$,
  } as Config;
}

export function userPipelinesFactory(): UserPipelinesServiceConfig {
  const appConfigService = inject(AppConfigService);

  const partnerId$ = appConfigService.config$.pipe(
    map((config) => config.partnerId),
    filter((partnerId) => !!partnerId),
    distinctUntilChanged(),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  return <UserPipelinesServiceConfig>{ partnerId$: partnerId$ };
}

export function opportunityServiceConfigFactory(): SalesUIOpportunityConfig {
  const appConfigService = inject(AppConfigService);
  const userPipelines = inject(UserPipelines);
  const salespersonService = inject(SalespersonService);
  const featureFlagService = inject(FeatureFlagService);
  const salespeopleService = inject(SalespeopleService);
  const businessInfoService = inject(BusinessInfoService);
  const partnerConfigService = inject(WhitelabelService);
  const marketSelectorService = inject(MARKET_SELECTOR_SERVICE_TOKEN);

  const partnerId$ = appConfigService.config$.pipe(
    map((config) => config.partnerId),
    filter((partnerId) => !!partnerId),
    distinctUntilChanged(),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  const userId$ = appConfigService.config$.pipe(
    map((config) => config.unifiedUserId),
    filter((userId) => !!userId),
    distinctUntilChanged(),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  const salesperson$ = combineLatest([partnerId$, userId$]).pipe(
    switchMap(([partnerId, userId]) => salespersonService.getSalespersonByUserId(partnerId, userId)),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  const salespersonId$ = salesperson$.pipe(
    map((salesperson) => salesperson.salespersonId),
    catchError((_) => of('')),
  );

  const featureFlags$ = partnerId$.pipe(
    switchMap((partnerId) => {
      const flags = [FeatureFlags.NO_ADMIN_ORDER_CREATION, FeatureFlags.OPPORTUNITY_CRM_ACTIVITY];
      return featureFlagService.batchGetStatus(partnerId, '', flags);
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  const canCreateOrders$ = featureFlags$.pipe(
    map((featureFlagStatus) => featureFlagStatus[FeatureFlags.NO_ADMIN_ORDER_CREATION]),
  );

  const getOrdersUrlFn: OrdersUrlFunction = (opportunity: ApiOpportunity): OrdersUrl => {
    return <OrdersUrl>{
      route: `/order-management/${opportunity.accountGroupId}/sales-order/create/${opportunity.opportunityId}`,
    };
  };

  const marketId$ = marketSelectorService.selectedMarketId$;

  const partnerMarket$ = combineLatest([partnerId$, marketId$]).pipe(
    map(([partnerId, marketId]) => ({
      partnerId,
      marketId,
    })),
    distinctUntilChanged(isPartnerMarketSame),
  );

  const salespeople$ = combineLatest([partnerId$, marketId$]).pipe(
    distinctUntilChanged(),
    switchMap(([partnerId, marketId]) => salespeopleService.getSalespeople(partnerId, marketId)),
    map((salespeople) => Salesperson.convertSalespeopleFromSalesResponse(salespeople)),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  const bypassQuantityRestrictionsPartnerIds = ['VMF'];
  const bypassQuantityRestrictions$ = partnerId$.pipe(
    map((partnerId) => bypassQuantityRestrictionsPartnerIds.includes(partnerId)),
  );

  const salesCenterCurrency$ = partnerMarket$.pipe(
    switchMap((pm) => {
      return partnerConfigService.getConfiguration(pm.partnerId, pm.marketId);
    }),
    map((config) => {
      return config?.salesConfiguration?.stDefaultDisplayCurrency || 'USD';
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  const showActivities$ = featureFlags$.pipe(
    map((featureFlagStatus) => featureFlagStatus[FeatureFlags.OPPORTUNITY_CRM_ACTIVITY]),
  );

  const cfg: SalesUIOpportunityConfig = {
    salespersonId$: salespersonId$,
    pipelines$: userPipelines.partnerPipelines$,
    currentPipeline$: userPipelines.getCurrentPipeline(),
    canCreateOrders$: canCreateOrders$,
    getOrdersUrl: getOrdersUrlFn,
    partnerId$: partnerId$,
    salespeople$: salespeople$,
    marketId$: marketId$,
    bypassQuantityRestrictions$: bypassQuantityRestrictions$,
    businessInfoService: businessInfoService,
    hasAccessToAllAccountsInMarket$: of(true),
    defaultCurrency$: salesCenterCurrency$,
    showActivities$,
  };

  return cfg;
}

interface PartnerMarket {
  partnerId: string;
  marketId: string;
}

const isPartnerMarketSame = (prev: PartnerMarket, curr: PartnerMarket): boolean => {
  return prev?.partnerId === curr?.partnerId && prev?.marketId === curr?.marketId;
};

export function objectViewPresets$(
  translationService: TranslateService,
  translateByObjectService: TranslateForCrmObjectService,
  ownerId$: Observable<string>,
  objectType: ObjectType,
  lastEngagedFieldId: string,
  ownerFieldId: string,
): Observable<PresetFilter[]> {
  const myFilterName$ = translateByObjectService.getTranslationForCrmObject(objectType, 'MY_OBJECTS');
  const myFilter$ = combineLatest([ownerId$, myFilterName$]).pipe(
    map(([userId, filterName]) => {
      if (userId) {
        const filter: GalaxyFilterInterface = {
          fieldId: ownerFieldId,
          operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
          values: [{ string: userId }],
        };
        return {
          name: filterName,
          filters: [filter],
          id: 'owned-objects',
        } as PresetFilter;
      }
      return undefined;
    }),
  );

  const allFilter$ = translateByObjectService.getTranslationForCrmObject(objectType, 'ALL_OBJECTS').pipe(
    map((filterName) => {
      return {
        name: filterName,
        filters: [],
        id: 'all-objects',
      } as PresetFilter;
    }),
  );

  return combineLatest([myFilter$, allFilter$]).pipe(
    map(([myFilter, allFilter]) => {
      const filters: PresetFilter[] = [];
      if (myFilter) {
        filters.push(myFilter);
      }
      if (allFilter) {
        filters.push(allFilter);
      }

      // recently engaged
      const lastEngagedFilter = {
        name: translationService.instant('CRM.TABLE.PRESET_FILTERS.RECENTLY_ENGAGED'),
        filters: [
          {
            fieldId: lastEngagedFieldId,
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS_AFTER,
            values: [{ dateDefault: GalaxyDateDefault.DATE_DEFAULT_LAST_WEEK }],
          },
        ],
        id: 'recently-engaged',
      } as PresetFilter;
      filters.push(lastEngagedFilter);

      return filters;
    }),
  );
}
