<div class="restricted-content">
  <div class="top section">
    <div class="left">
      <div class="title">{{ featureInfo.featureTitle | translate }}</div>
      <div class="description">{{ featureInfo.description | translate }}</div>
      <ul class="top-points">
        <span *ngFor="let point of featureInfo.topPoints">
          <li><mat-icon>check_circle_outline</mat-icon> {{ point | translate }}</li>
          <br />
        </span>
        <li><mat-icon>check_circle_outline</mat-icon> {{ featureInfo.availabilityOnTier | translate }}</li>
      </ul>
      <a
        mat-flat-button
        color="primary"
        class="button upgrade"
        (click)="trackEvent('restricted-content-upgrade:' + featureInfo.featureId)"
        [routerLink]="['/upgrade-subscription-tier']"
      >
        <ng-container *ngIf="isFreeTier$ | async; else paidTier">
          {{ 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE' | translate }}
        </ng-container>
        <mat-icon class="button-link" iconPositionEnd>arrow_circle_up</mat-icon>
      </a>
      <ng-template #paidTier>
        {{ 'RESTRICTED_FEATURE_CONTENT_V2.COMPARE' | translate }}
      </ng-template>
      <a
        *ngIf="featureInfo.featureId && featureInfo.learnMoreLink"
        mat-stroked-button
        color="secondary"
        (click)="trackEvent('restricted-content-learnMore:' + featureInfo.featureId)"
        class="button learn-more"
        href="{{ featureInfo.learnMoreLink }}"
        target="_blank"
      >
        {{ 'RESTRICTED_FEATURE_CONTENT_V2.EXPLORE' | translate }}
        <mat-icon class="button-link" iconPositionEnd>open_in_new</mat-icon>
      </a>
      <div *ngIf="dialogConfig$ | async as dialogConfig; else noSalesperson">
        <p>
          <a (click)="openSalespersonDialog(dialogConfig)">{{
            'RESTRICTED_FEATURE_CONTENT_V2.TALK_TO_SALES' | translate
          }}</a>
          {{ 'RESTRICTED_FEATURE_CONTENT_V2.OR_CALL_US' | translate }}
          <a (click)="trackEvent('restricted-content-phoneNumber-' + featureInfo.featureId)" href="tel:18559555585"
            >**************</a
          >
        </p>
      </div>
      <ng-template #noSalesperson>
        <p>{{ 'RESTRICTED_FEATURE_CONTENT_V2.CALL_US' | translate }} <a href="tel:18559555585">**************</a></p>
      </ng-template>
    </div>
    @if (featureInfo.image) {
      <div class="img-container">
        <img class="access-image" src="{{ featureInfo.image | getImageSrc }}" />
      </div>
    }
  </div>
  <div *ngIf="featureInfo.moreInfoTitle" class="more-info section">
    <div class="title">{{ featureInfo.moreInfoTitle | translate }}</div>
    <div class="row row-gutters card-container">
      <ng-container *ngFor="let section of featureInfo.moreInfoSections">
        <div
          [ngClass]="featureInfo.moreInfoSections.length === 2 ? 'col col-xs-12 col-md-6' : 'col col-xs-12 col-md-4'"
        >
          <mat-card appearance="outlined">
            <mat-card-content>
              <glxy-empty-state [size]="'medium'" appearance="outlined">
                <glxy-empty-state-hero>
                  <mat-icon>{{ section.icon }}</mat-icon>
                </glxy-empty-state-hero>
                <glxy-empty-state-title>
                  {{ section.title | translate }}
                </glxy-empty-state-title>
                <p>{{ section.description | translate }}</p>
                <glxy-empty-state-actions>
                  <a
                    *ngIf="section.link"
                    mat-stroked-button
                    color="secondary"
                    href="{{ section.link }}"
                    target="_blank"
                    (click)="trackEvent('restricted-content-learnMore:{{section.title | translate}}')"
                  >
                    {{ 'RESTRICTED_FEATURE_CONTENT_V2.LEARN_MORE' | translate }}
                    <mat-icon class="button-link" iconPositionEnd>open_in_new</mat-icon>
                  </a>
                </glxy-empty-state-actions>
              </glxy-empty-state>
            </mat-card-content>
          </mat-card>
        </div>
      </ng-container>
    </div>
  </div>
  <div
    *ngIf="featureInfo.videoId"
    class="video section"
    (click)="trackEvent('restricted-content-play-video:' + featureInfo.featureId)"
  >
    <div class="wistia_responsive_padding">
      <div class="wistia_responsive_wrapper">
        <div class="wistia_embed wistia_async_{{ featureInfo.videoId }} seo=false videoFoam=true">
          <div class="wistia_swatch">
            <img
              src="https://fast.wistia.com/embed/medias/{{ featureInfo.videoId }}/swatch"
              alt=""
              aria-hidden="true"
              onload="this.parentNode.style.opacity=1;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="featureInfo.faqs" class="faq section">
    <div class="title">{{ 'RESTRICTED_FEATURE_CONTENT_V2.FAQ' | translate }}</div>
    <mat-accordion>
      <mat-expansion-panel *ngFor="let faq of featureInfo.faqs">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{ faq.question | translate }}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <p class="answer" [innerHTML]="faq.answer | translate"></p>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>
