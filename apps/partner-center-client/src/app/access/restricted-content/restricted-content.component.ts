import { CommonModule } from '@angular/common';
import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatDialog, MatDialogConfig, MatDialogModule } from '@angular/material/dialog';
import {
  MatAccordion,
  MatExpansionPanel,
  MatExpansionPanelHeader,
  MatExpansionPanelTitle,
} from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { SubscriptionTiersService } from '@galaxy/partner';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AssignedSalespersonDialogComponent } from '../../assigned-salesperson/assigned-salesperson-dialog/assigned-salesperson-dialog.component';
import { AssignedSalespersonService } from '../../assigned-salesperson/assigned-salesperson.service';
import { PartnerService } from '../../core/partner.service';
import { FeatureInfo, GetImageSrcPipe } from './shared';

const materialComponents = [MatIconModule, MatButtonModule, MatDialogModule];

@Component({
  selector: 'app-restricted-content',
  templateUrl: './restricted-content.component.html',
  styleUrls: ['./restricted-content.component.scss'],
  imports: [
    CommonModule,
    RouterModule,
    ...materialComponents,
    TranslateModule,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    MatExpansionPanelTitle,
    MatCard,
    MatCardContent,
    GalaxyEmptyStateModule,
    GetImageSrcPipe,
  ],
})
export class RestrictedContentComponent implements OnInit, OnDestroy {
  @Input() featureInfo: FeatureInfo;

  isFreeTier$: Observable<boolean>;
  dialogConfig$: Observable<MatDialogConfig<any>>;
  private embeddedScripts = [];
  private observer: MutationObserver;
  constructor(
    private readonly partnerService: PartnerService,
    private readonly dialog: MatDialog,
    private readonly assignedSalespersonService: AssignedSalespersonService,
    private readonly productAnalyticsService: ProductAnalyticsService,
  ) {}

  ngOnInit(): void {
    this.isFreeTier$ = this.partnerService.subscriptionTier$.pipe(map((s) => SubscriptionTiersService.isFree(s)));
    this.dialogConfig$ = this.assignedSalespersonService.dialogConfig$;
    if (this.featureInfo.videoId) {
      this.embedWistia(this.featureInfo.videoId);
    }
  }

  openSalespersonDialog(dialogConfig: MatDialogConfig): void {
    this.dialog.open(AssignedSalespersonDialogComponent, dialogConfig);
    this.trackEvent('sales-dialog');
  }

  embedWistia(videoId: string): void {
    const firstScript = document.createElement('script');
    const secondScript = document.createElement('script');
    this.embeddedScripts = [firstScript, secondScript];

    firstScript.type = 'text/javascript';
    secondScript.type = 'text/javascript';
    firstScript.src = `https://fast.wistia.com/embed/medias/${videoId}.jsonp`;
    secondScript.src = 'https://fast.wistia.com/assets/external/E-v1.js';
    document.body.appendChild(firstScript);
    document.body.appendChild(secondScript);
  }

  trackEvent(name: string): void {
    this.productAnalyticsService.trackEvent(name, 'restricted-content', 'click');
  }

  ngOnDestroy(): void {
    this.embeddedScripts.forEach((child) => document.body.removeChild(child));
    this.observer?.disconnect();
  }
}
