import { Feature } from '../../core/access';
import { FeatureInfo, getAvailabilityOnTier } from './shared';

export function getFeatureInfo(featureId: string, nextTier: string): FeatureInfo {
  const restrictedContent = {
    ...RESTRICTED_CONTENT.filter((f) => f.featureId.filter((id) => id === featureId).length > 0)[0],
  };
  restrictedContent.availabilityOnTier = getAvailabilityOnTier(nextTier);
  return restrictedContent;
}

/**
 * Content for upgrade page for restricted features.
 */
const RESTRICTED_CONTENT: FeatureInfo[] = [
  // Vendasta Payments
  {
    featureId: [Feature.vendastaPayments],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.TITLE',
    image: 'images/restricted/vendasta-payments.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/4406961189271-Setting-up-Vendasta-Payments',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.TOP_POINTS_2',
      'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.TOP_POINTS_3',
    ],
    moreInfoTitle: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.MORE_INFO_TITLE',
    moreInfoSections: [
      {
        icon: 'receipt_long',
        title: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.MORE_INFO_1_TITLE',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.MORE_INFO_1_DESCRIPTION',
        link: 'https://support.vendasta.com/hc/en-us/articles/16683150168599-FAQs-for-Retail-Subscription-Management',
      },
      {
        icon: 'repeat',
        title: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.MORE_INFO_2_TITLE',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.MORE_INFO_2_DESCRIPTION',
        link: 'https://support.vendasta.com/hc/en-us/articles/4406960017815-Create-and-send-invoices',
      },
    ],
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_1_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_2_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_2_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_3_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_3_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_4_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_4_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_5_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_5_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_6_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.VENDASTA_PAYMENTS.FAQ_6_ANSWER',
      },
    ],
  },
  // Multi-location
  {
    featureId: [Feature.brands],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.TITLE',
    image: 'images/restricted/multi-location.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/4406951013399-Multi-Location-Business-App-Overview',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.TOP_POINTS_2',
    ],
    moreInfoTitle: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.MORE_INFO_TITLE',
    moreInfoSections: [
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.MORE_INFO_1_TITLE',
        icon: 'storefront',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.MORE_INFO_1_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.MORE_INFO_2_TITLE',
        icon: 'insights',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.MORE_INFO_2_DESCRIPTION',
      },
    ],
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_1_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_2_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_2_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_3_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_3_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_4_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_4_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_5_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_5_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_6_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_6_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_7_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MULTI_LOCATION.FAQ_7_ANSWER',
      },
    ],
  },
  // Automations
  {
    featureId: [Feature.automations],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.TITLE',
    image: 'images/restricted/automations.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/4406961819543-Getting-started-with-automations',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.TOP_POINTS_2',
    ],
    moreInfoTitle: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.MORE_INFO_TITLE',
    moreInfoSections: [
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.MORE_INFO_1_TITLE',
        icon: 'storefront',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.MORE_INFO_1_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.MORE_INFO_2_TITLE',
        icon: 'insights',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.MORE_INFO_2_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.MORE_INFO_3_TITLE',
        icon: 'api',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.MORE_INFO_3_DESCRIPTION',
      },
    ],
    videoId: 'kb5xjssm46',
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.FAQ_1_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.FAQ_2_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.AUTOMATIONS.FAQ_2_ANSWER',
      },
    ],
  },
  // Fulfillment / Concierge
  {
    featureId: [Feature.concierge],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.TITLE',
    image: 'images/restricted/fulfillment-task-manager.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.UPGRADE_BUTTON_TEXT',
    learnMoreLink:
      'https://support.vendasta.com/hc/en-us/articles/16864870432407-Getting-Started-with-Projects-in-Task-Manager',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.TOP_POINTS_2',
    ],
    moreInfoTitle: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.MORE_INFO_TITLE',
    moreInfoSections: [
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.MORE_INFO_1_TITLE',
        icon: 'task',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.MORE_INFO_1_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.MORE_INFO_2_TITLE',
        icon: 'bolt',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.MORE_INFO_2_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.MORE_INFO_3_TITLE',
        icon: 'hub',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.MORE_INFO_3_DESCRIPTION',
      },
    ],
    videoId: '8roh06qgml',
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.FAQ_1_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.FAQ_2_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.FAQ_2_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.FAQ_3_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.FAQ_3_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.FAQ_4_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.FAQ_4_ANSWER',
      },
    ],
  },
  // Marketing
  {
    featureId: [Feature.marketingAutomation, Feature.snapshotWidget],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.TITLE',
    image: 'images/restricted/marketing.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.TOP_POINTS_2',
    ],
    moreInfoTitle: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.MORE_INFO_TITLE',
    moreInfoSections: [
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.MORE_INFO_1_TITLE',
        icon: 'send',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.MORE_INFO_1_DESCRIPTION',
        link: 'https://support.vendasta.com/hc/en-us/articles/6980140383255-Email-Builder-Overview',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.MORE_INFO_2_TITLE',
        icon: 'dynamic_form',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.MORE_INFO_2_DESCRIPTION',
        link: 'https://support.vendasta.com/hc/en-us/articles/4406958650519-Acquisition-Widget-overview',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.MORE_INFO_3_TITLE',
        icon: 'description',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.MORE_INFO_3_DESCRIPTION',
        link: 'https://support.vendasta.com/hc/en-us/articles/4406960330519-Create-email-campaigns#h_30b810d3-e293-403b-bde3-6f61f958e4f7',
      },
    ],
    videoId: 'mfwr8rw3pd',
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.FAQ_1_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.FAQ_2_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.FAQ_2_ANSWER',
      },
    ],
  },
  // Sales lockscreen
  {
    featureId: [Feature.salesAndSuccessCenterAccess, Feature.pipeline],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.TITLE',
    image: 'images/restricted/sales.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/19457209256343-CRM-in-Partner-Center-Overview',
    topPoints: ['RESTRICTED_FEATURE_CONTENT_V2.SALES.TOP_POINTS_1', 'RESTRICTED_FEATURE_CONTENT_V2.SALES.TOP_POINTS_2'],
    moreInfoTitle: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.MORE_INFO_TITLE',
    moreInfoSections: [
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.MORE_INFO_1_TITLE',
        icon: 'check_circle',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.MORE_INFO_1_DESCRIPTION',
        link: 'https://support.vendasta.com/hc/en-us/articles/20262711512087-Sales-tasks-in-the-CRM',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.MORE_INFO_2_TITLE',
        icon: 'paid',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.MORE_INFO_2_DESCRIPTION',
        link: 'https://support.vendasta.com/hc/en-us/articles/4406962110615-Sales-Pipeline-Overview',
      },
    ],
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.FAQ_1_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.FAQ_2_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.FAQ_2_ANSWER',
      },
    ],
  },
  // Sales teams and goals lockscreen
  {
    featureId: [Feature.salesTeam, Feature.goals, Feature.leaderboard],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.TITLE',
    image: 'images/restricted/sales-teams.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.TOP_POINTS_2',
    ],
    moreInfoTitle: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.MORE_INFO_TITLE',
    moreInfoSections: [
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.MORE_INFO_1_TITLE',
        icon: 'people',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.MORE_INFO_1_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.MORE_INFO_2_TITLE',
        icon: 'leaderboard',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.MORE_INFO_2_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.MORE_INFO_3_TITLE',
        icon: 'leaderboard',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.MORE_INFO_3_DESCRIPTION',
        link: 'https://support.vendasta.com/hc/en-us/articles/22709380842519-Leaderboard-Overview',
      },
    ],
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.FAQ_1_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.FAQ_2_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.SALES_TEAMS.FAQ_2_ANSWER',
      },
    ],
  },
  // Lists lockscreen
  {
    featureId: [Feature.lists],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.TITLE',
    image: 'images/restricted/lists.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/4406952250519-Lists-Overview',
    topPoints: ['RESTRICTED_FEATURE_CONTENT_V2.LISTS.TOP_POINTS_1', 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.TOP_POINTS_2'],
    moreInfoSections: [
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.MORE_INFO_1_TITLE',
        icon: 'list',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.MORE_INFO_1_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.MORE_INFO_2_TITLE',
        icon: 'filter_list',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.MORE_INFO_2_DESCRIPTION',
      },
      {
        title: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.MORE_INFO_3_TITLE',
        icon: 'add',
        description: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.MORE_INFO_3_DESCRIPTION',
      },
    ],
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.FAQ_1_ANSWER',
      },
    ],
  },
  // Guides
  {
    featureId: [Feature.contentLibrary],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.GUIDES.TITLE',
    image: 'images/restricted/guides.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.GUIDES.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.GUIDES.UPGRADE_BUTTON_TEXT',
    learnMoreLink:
      'https://support.vendasta.com/hc/en-us/articles/4406951128599-Business-App-Add-your-own-custom-guides',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.GUIDES.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.GUIDES.TOP_POINTS_2',
    ],
    moreInfoSections: [],
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.GUIDES.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.GUIDES.FAQ_1_ANSWER',
      },
    ],
  },
  // Customize business app
  {
    featureId: [Feature.customizeBusinessApp],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/10139745934359-Customize-Business-App',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.TOP_POINTS_2',
    ],
  },
  // CRM lead scoring
  {
    featureId: [Feature.crmLeadScore],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.TOP_POINTS_2',
      'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.TOP_POINTS_3',
    ],
  },
  // Service accounts
  {
    featureId: [Feature.serviceAccounts],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.SERVICE_ACCOUNTS.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.SERVICE_ACCOUNTS.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://developers.vendasta.com/platform/ZG9jOjEwMTkzMDg4-overview',
    topPoints: ['RESTRICTED_FEATURE_CONTENT_V2.SERVICE_ACCOUNTS.TOP_POINTS_1'],
  },
  // Custom fields
  {
    featureId: [Feature.customFields],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOM_FIELDS.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOM_FIELDS.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/*************-Custom-Fields',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.CUSTOM_FIELDS.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.CUSTOM_FIELDS.TOP_POINTS_2',
    ],
  },
  // Inbox AI WebChat
  {
    featureId: [Feature.inboxAIWebchat],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.DESCRIPTION',
    image: 'images/restricted/inbox-ai-webchat.png',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.TOP_POINTS_2',
    ],
    faqs: [
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.FAQ_1_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.FAQ_1_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.FAQ_2_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.FAQ_2_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.FAQ_3_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.FAQ_3_ANSWER',
      },
      {
        question: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.FAQ_4_QUESTION',
        answer: 'RESTRICTED_FEATURE_CONTENT_V2.INBOX_AI_WEBCHAT.FAQ_4_ANSWER',
      },
    ],
  },
];
