import { Pipe, PipeTransform } from '@angular/core';
import { Feature } from '../../core/access';
import { getImageSrc } from '../../core/images';
import {
  VBP2024_PREMIUM_SUBSCRIPTION_ID,
  VBP2024_PROFESSIONAL_SUBSCRIPTION_ID,
  VBP2024_STARTER_SUBSCRIPTION_ID,
} from '@galaxy/partner';

export interface FAQ {
  question: string;
  answer: string;
}
export interface MoreInfoSection {
  icon: string;
  title: string;
  description: string;
  link?: string;
}
export interface FeatureInfo {
  featureId: Feature[];
  featureTitle: string;
  image?: string;
  minUpgradeLevel?: string;
  description?: string;
  upgradeButtonText?: string;
  learnMoreLink?: string;
  topPoints?: string[];
  moreInfoTitle?: string;
  moreInfoSection1Title?: string;
  moreInfoSection1Icon?: string;
  moreInfoSection1Description?: string;
  moreInfoSection1Link?: string;
  moreInfoSection2Title?: string;
  moreInfoSection2Icon?: string;
  moreInfoSection2Description?: string;
  moreInfoSection2Link?: string;
  moreInfoSections?: MoreInfoSection[];
  videoId?: string;
  faqs?: FAQ[];
  availabilityOnTier?: string;
}

export function getAvailabilityOnTier(nextTier: string): string {
  const availabilityOnTier = {
    [VBP2024_STARTER_SUBSCRIPTION_ID]: 'RESTRICTED_FEATURE_CONTENT_V2.AVAILABLE_ON_ALL_PAID_VENDASTA_PLANS',
    [VBP2024_PROFESSIONAL_SUBSCRIPTION_ID]: 'RESTRICTED_FEATURE_CONTENT_V2.AVAILABLE_ON_PROFESSIONAL_PLANS_AND_UP',
    [VBP2024_PREMIUM_SUBSCRIPTION_ID]: 'RESTRICTED_FEATURE_CONTENT_V2.AVAILABLE_ON_PREMIUM_PLANS_AND_UP',
  };
  return availabilityOnTier[nextTier];
}

/**
 * Get the image source for the given image name pipe.
 */
@Pipe({
  standalone: true,
  name: 'getImageSrc',
})
export class GetImageSrcPipe implements PipeTransform {
  transform(imageName: string): string {
    return getImageSrc(imageName);
  }
}
