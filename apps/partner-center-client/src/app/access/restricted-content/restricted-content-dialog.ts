import { Feature } from '../../core/access';
import { FeatureInfo, getAvailabilityOnTier } from './shared';

export function getFeatureInfo(featureId: string, nextTier: string): FeatureInfo {
  const restrictedContent = {
    ...RESTRICTED_CONTENT.filter((f) => f.featureId.filter((id) => id === featureId).length > 0)[0],
  };
  restrictedContent.availabilityOnTier = getAvailabilityOnTier(nextTier);
  return restrictedContent;
}

/**
 * Content upgrade for dialog / modal.
 */
const RESTRICTED_CONTENT: FeatureInfo[] = [
  // Customize business app dialog
  {
    featureId: [Feature.customizeBusinessApp],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.DESCRIPTION',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/10139745934359-Customize-Business-App',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.CUSTOMIZE_BUSINESS_APP.TOP_POINTS_2',
    ],
  },
  // Custom fields dialog
  {
    featureId: [Feature.customFields],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOM_FIELDS.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.CUSTOM_FIELDS.DESCRIPTION',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/*************-Custom-Fields',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.CUSTOM_FIELDS.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.CUSTOM_FIELDS.TOP_POINTS_2',
    ],
  },
  // Setvice accounts dialog
  {
    featureId: [Feature.serviceAccounts],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.SERVICE_ACCOUNTS.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.SERVICE_ACCOUNTS.DESCRIPTION',
    learnMoreLink: 'https://developers.vendasta.com/platform/ZG9jOjEwMTkzMDg4-overview',
  },
  // Start selling dialog
  {
    featureId: [Feature.marketplaceSellProducts],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.START_SELLING.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.START_SELLING.DESCRIPTION',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/**************-Marketplace-Start-Selling-Products',
    image: 'images/restricted/marketplace.png',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.START_SELLING.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.START_SELLING.TOP_POINTS_2',
    ],
  },
  // CRM lead scoring
  {
    featureId: [Feature.crmLeadScore],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.TOP_POINTS_2',
      'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_SCORING.TOP_POINTS_3',
    ],
  },
  // Sales lockscreen
  {
    featureId: [Feature.salesAndSuccessCenterAccess, Feature.pipeline, Feature.crmAISummary],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.TITLE',
    image: 'images/restricted/sales.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.SALES.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/19457209256343-CRM-in-Partner-Center-Overview',
    topPoints: ['RESTRICTED_FEATURE_CONTENT_V2.SALES.TOP_POINTS_1', 'RESTRICTED_FEATURE_CONTENT_V2.SALES.TOP_POINTS_2'],
  },
  // Manage pipeline
  {
    featureId: [Feature.managePipeline],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.MANAGE_PIPELINE.TITLE',
    image: 'images/restricted/manage-pipeline.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.MANAGE_PIPELINE.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.MANAGE_PIPELINE.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.MANAGE_PIPELINE.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.MANAGE_PIPELINE.TOP_POINTS_2',
    ],
  },
  // SSO feature
  {
    featureId: [Feature.singleSignOn],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.SSO.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.SSO.DESCRIPTION',
    topPoints: ['RESTRICTED_FEATURE_CONTENT_V2.SSO.TOP_POINTS_1', 'RESTRICTED_FEATURE_CONTENT_V2.SSO.TOP_POINTS_2'],
  },
  // Translate feature
  {
    featureId: [Feature.translate],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.TRANSLATE.TITLE',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.TRANSLATE.DESCRIPTION',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/4406962234391-Translate-and-Customize-with-Weblate',
    topPoints: ['RESTRICTED_FEATURE_CONTENT_V2.TRANSLATE.TOP_POINTS_1'],
  },
  {
    featureId: [Feature.lists],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.TITLE',
    image: 'images/restricted/lists.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.UPGRADE_BUTTON_TEXT',
    learnMoreLink: 'https://support.vendasta.com/hc/en-us/articles/4406952250519-Lists-Overview',
    topPoints: ['RESTRICTED_FEATURE_CONTENT_V2.LISTS.TOP_POINTS_1', 'RESTRICTED_FEATURE_CONTENT_V2.LISTS.TOP_POINTS_2'],
  },
  {
    featureId: [Feature.marketingAutomation],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.TITLE',
    image: 'images/restricted/marketing.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.TOP_POINTS_2',
    ],
    moreInfoTitle: 'RESTRICTED_FEATURE_CONTENT_V2.MARKETING.MORE_INFO_TITLE',
  },
  {
    featureId: [Feature.metricsInsights],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.METRICS.TITLE',
    image: 'images/restricted/metrics.svg',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.METRICS.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.METRICS.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.METRICS.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.METRICS.TOP_POINTS_2',
    ],
  },
  // CRM Lead Prospector OR "Find nearby business"
  {
    featureId: [Feature.crmLeadProspector],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_PROSPECTOR.TITLE',
    image: 'images/restricted/lists.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_PROSPECTOR.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_PROSPECTOR.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_PROSPECTOR.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.CRM_LEAD_PROSPECTOR.TOP_POINTS_2',
    ],
  },
  // Task Queue
  {
    featureId: [Feature.taskQueue],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.TASK_QUEUE.TITLE',
    image: 'images/restricted/task-queue.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.TASK_QUEUE.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.TASK_QUEUE.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.TASK_QUEUE.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.TASK_QUEUE.TOP_POINTS_2',
    ],
  },
  {
    featureId: [Feature.concierge],
    featureTitle: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.TITLE',
    image: 'images/restricted/fulfillment-task-manager.png',
    description: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.DESCRIPTION',
    upgradeButtonText: 'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.UPGRADE_BUTTON_TEXT',
    topPoints: [
      'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.TOP_POINTS_1',
      'RESTRICTED_FEATURE_CONTENT_V2.FULFILLMENT.TOP_POINTS_2',
    ],
  },
];
