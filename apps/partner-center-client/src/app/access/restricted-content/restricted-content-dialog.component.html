<mat-dialog-content class="restricted-dialog">
  <div class="restricted-content">
    <div class="title">{{ featureInfo.featureTitle | translate }}</div>
    <div class="top section">
      <div class="left">
        <div class="description">{{ featureInfo.description | translate }}</div>
        <ul class="top-points">
          <span *ngFor="let point of featureInfo.topPoints">
            <li><mat-icon>check_circle_outline</mat-icon> {{ point | translate }}</li>
            <br />
          </span>
          <li><mat-icon>check_circle_outline</mat-icon> {{ featureInfo.availabilityOnTier | translate }}</li>
        </ul>
        <button
          mat-flat-button
          mat-dialog-close
          color="primary"
          class="button upgrade"
          (click)="trackEvent('restricted-content-upgrade:' + featureInfo.featureId)"
          [routerLink]="['/upgrade-subscription-tier']"
        >
          <ng-container *ngIf="isFreeTier$ | async; else paidTier">
            {{ 'RESTRICTED_FEATURE_CONTENT_V2.UPGRADE' | translate }}
          </ng-container>
          <mat-icon class="button-link" iconPositionEnd>arrow_circle_up</mat-icon>
        </button>
        <ng-template #paidTier>
          {{ 'RESTRICTED_FEATURE_CONTENT_V2.COMPARE' | translate }}
        </ng-template>
        <a
          *ngIf="featureInfo.featureId && featureInfo.learnMoreLink"
          mat-stroked-button
          color="secondary"
          (click)="trackEvent('restricted-content-learnMore:' + featureInfo.featureId)"
          class="button learn-more"
          href="{{ featureInfo.learnMoreLink }}"
          target="_blank"
        >
          {{ 'RESTRICTED_FEATURE_CONTENT_V2.EXPLORE' | translate }}
          <mat-icon class="button-link" iconPositionEnd>open_in_new</mat-icon>
        </a>
        <div *ngIf="dialogConfig$ | async as dialogConfig; else noSalesperson">
          <p>
            <a (click)="openSalespersonDialog(dialogConfig)">{{
              'RESTRICTED_FEATURE_CONTENT_V2.TALK_TO_SALES' | translate
            }}</a>
            {{ 'RESTRICTED_FEATURE_CONTENT_V2.OR_CALL_US' | translate }}
            <a (click)="trackEvent('restricted-content-phoneNumber-' + featureInfo.featureId)" href="tel:18559555585"
              >**************</a
            >
          </p>
        </div>
        <ng-template #noSalesperson>
          <p>{{ 'RESTRICTED_FEATURE_CONTENT_V2.CALL_US' | translate }} <a href="tel:18559555585">**************</a></p>
        </ng-template>
      </div>
      <div *ngIf="featureInfo.image" class="img-container">
        <img class="access-image" src="{{ featureInfo.image | getImageSrc }}" />
      </div>
    </div>
  </div>
</mat-dialog-content>
