import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { getFeatureInfo } from './restricted-content/restricted-content-dialog';
import { FeatureInfo } from './restricted-content/shared';
import { FeatureInfoService } from './feature-info.service';
import { map, Observable } from 'rxjs';

@Component({
  templateUrl: './access-dialog.component.html',
  styleUrls: ['./access-dialog.component.scss'],
  standalone: false,
})
export class AccessRestrictedDialogComponent implements OnInit {
  public static readonly DEFAULT_WIDTH = '710px';
  public static readonly DEFAULT_MAX_WIDTH = '90vw';

  public info$: Observable<FeatureInfo>;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { featureId: string },
    public dialog: MatDialog,
    private readonly featureInfoService: FeatureInfoService,
  ) {}

  ngOnInit(): void {
    const featureId = this.data.featureId;
    this.info$ = this.featureInfoService
      .getNextSubscriptionTierWithFeature(featureId)
      .pipe(map((tier) => getFeatureInfo(featureId, tier)));
  }

  ctaClicked(): void {
    this.dialog.closeAll();
  }
}
