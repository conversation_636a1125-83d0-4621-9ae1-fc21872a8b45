import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { getFeatureInfo } from './restricted-content/restricted-content';
import { FeatureInfo } from './restricted-content/shared';
import { FeatureInfoService } from './feature-info.service';

@Component({
  templateUrl: './access.component.html',
  styleUrls: ['./access.component.scss'],
  standalone: false,
})
export class AccessRestrictedComponent implements OnInit {
  featureInfo$: Observable<FeatureInfo>;
  constructor(
    private readonly route: ActivatedRoute,
    private readonly featureInfoService: FeatureInfoService,
  ) {}
  ngOnInit(): void {
    // we need to pipe from the route snapshot params so the featureId is
    // updated when we switch to a different lock screen
    this.featureInfo$ = this.route.params.pipe(
      switchMap((params) =>
        this.featureInfoService
          .getNextSubscriptionTierWithFeature(params.featureId)
          .pipe(map((nextTier) => ({ params, nextTier }))),
      ),
      map(({ params, nextTier }) => getFeatureInfo(params.featureId, nextTier)),
    );
  }
}
