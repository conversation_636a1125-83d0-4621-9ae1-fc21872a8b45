import { Injectable } from '@angular/core';
import { SubscriptionTierInterface, SubscriptionTiersService, VBP2024_VERSION } from '@galaxy/partner';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { PartnerService } from '../core/partner.service';

@Injectable({
  providedIn: 'root',
})
export class FeatureInfoService {
  constructor(
    private readonly partnerService: PartnerService,
    private readonly subscriptionTiersService: SubscriptionTiersService,
  ) {}

  getNextSubscriptionTierWithFeature(featureId: string): Observable<string> {
    return combineLatest([
      this.subscriptionTiersService.subscriptionTierList$,
      this.partnerService.subscriptionTier$,
    ]).pipe(
      map(([tiers, currentTier]) => {
        const availableTiersSorted = tiers
          .filter(
            (t) =>
              t.enabledFeatures.some((feature) => feature === featureId) &&
              // only show the most recent subscription tiers that we are actively promoting
              t.tierVersion === VBP2024_VERSION &&
              !t.archived,
          )
          .sort((a: SubscriptionTierInterface, b: SubscriptionTierInterface) => a.rank - b.rank);

        // Return first tier with a higher rank than current tier OR return the highest tier we have
        return (
          availableTiersSorted.filter((t) => Math.floor(t.rank / 100) >= Math.floor(currentTier.rank / 100))[0]?.id ||
          availableTiersSorted[availableTiersSorted.length - 1]?.id
        );
      }),
    );
  }
}
