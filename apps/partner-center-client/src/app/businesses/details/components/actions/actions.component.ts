import { HttpErrorResponse } from '@angular/common/http';
import { Component, computed, Inject, Input, OnDestroy, OnInit, Signal, signal, WritableSignal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { AccountGroup } from '@galaxy/account-group';
import { CrmDependencies, CrmInjectionToken, CrmObjectService } from '@galaxy/crm/static';
import { FeatureFlagService } from '@galaxy/partner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';

import { CreateOrderDialogService } from '@vendasta/orders';
import { Salesperson, SalespersonService } from '@vendasta/sales';
import { BehaviorSubject, combineLatest, Observable, of, Subscription } from 'rxjs';
import { catchError, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { AccessRestrictedDialogComponent } from '../../../../access';
import { AppConfigService } from '../../../../app-config.service';
import { ConciergeAccountService } from '../../../../concierge/core/concierge-account.service';
import { AccessService, Feature, StubAccessService } from '../../../../core/access';
import { BusinessCenterService, VBCEntryInfo } from '../../../../core/business-center.service';
import { FeatureFlags } from '../../../../core/features';

/**
 * The actions component provides the action buttons to perform actions on a business.
 */
@Component({
  // This defines the attribute selector "[app-business-nav-actions]" so it can be used like this in the parent:
  //
  // <glxy-page-actions app-business-nav-actions></glxy-page-actions>
  //
  // This prevent the wrapping component name from being rendered in the DOM allowing this template to respect the
  // Galaxy styles provided for action buttons. Otherwise the Galaxy page elements will end up with
  // <app-business-nav-actions></app-business-nav-actions> rendered inside it, and the styling will be broken.
  selector: 'app-business-nav-actions, [app-business-nav-actions]',
  templateUrl: './actions.component.html',
  styleUrls: ['./actions.component.scss'],
  standalone: false,
})
export class ActionsComponent implements OnInit, OnDestroy {
  /**
   * The business the actions are performed against.
   *
   * This input is required.
   */
  @Input() business: AccountGroup;

  /**
   * This is the loading state for when an action is performed from the "New" drop down. This is only used if we need to
   * make an API call, such as creating a new invoice because the invoice must exist before we can route to it.
   */
  newButtonLoading$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  newButtonLoading$: Observable<boolean> = this.newButtonLoading$$.asObservable();

  /**
   * This is the whitelabeled information for the Partner's Business App. It contains the name and URL to link to it.
   */
  businessAppDetails$: Observable<VBCEntryInfo>;

  /**
   * Indicates whether or not the Sales and Success Center button should be shown. It's only shown if the user has
   * a Salesperson user in the market for this account group.
   *
   * If the user does not have a salesperson in this partner, it will show the button because they can create one.
   * However, they have to pick the market it's created in.
   */
  sscButtonVisible$: Observable<boolean>;

  /**
   * Indicates whether or not the Company button should be shown. It's only shown if the associated company is
   * not deleted
   */
  companyButtonVisible$: Observable<boolean>;

  // Determines whether the "Open in Task Manager" creates an account or links directly to the account.
  hasTaskManagerAccount: WritableSignal<boolean> = signal(false);
  isCreatingTaskManagerAccount: WritableSignal<boolean> = signal(false);

  /**
   * Determines whether the user has access to retail billing
   */
  canAccessRetailBilling$: Observable<boolean>;

  canCreateOrders$: Observable<boolean> = this.partnerId$.pipe(
    switchMap((partnerId) => this.featureFlagService.batchGetStatus(partnerId, '', ['no_admin_order_creation'])),
    map((featureFlagStatus) => featureFlagStatus['no_admin_order_creation']),
    shareReplay(1),
  );

  protected noAdminOrderCreation$: Observable<boolean> = this.crmConfig.canPartnerAdminCreateOrdersFeatureFlag$;

  // generic processing signal to indicate something is going on within the "Open in" button. Currently only used to
  // indicate task manager account creation
  openInProcessing: Signal<boolean> = computed(() => this.isCreatingTaskManagerAccount());
  subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    public dialog: MatDialog,
    private snackbarService: SnackbarService,
    private businessCenterService: BusinessCenterService,
    private salespersonService: SalespersonService,
    private crmObjectService: CrmObjectService,
    @Inject('PARTNER_ID') private partnerId$: Observable<string>,
    @Inject('USER_ID') private userId$: Observable<string>,
    @Inject('MARKET_ID') readonly marketId$: Observable<string>,
    private conciergeAccountService: ConciergeAccountService,
    private appConfig: AppConfigService,
    @Inject(StubAccessService) private accessService: AccessService,
    @Inject(FeatureFlags.SHOW_SALES_AND_SUCCESS_CENTER)
    public readonly showSalesAndSuccessCenter$: Observable<boolean>,
    private featureFlagService: FeatureFlagService,
    private createOrderDialogService: CreateOrderDialogService,
    @Inject(CrmInjectionToken) private readonly crmConfig: CrmDependencies,
  ) {}

  ngOnInit(): void {
    this.businessAppDetails$ = this.businessCenterService.getVBCEntryUrl(this.business.accountGroupId);

    const salespersonCanViewBusinessMarket$ = combineLatest([this.partnerId$, this.userId$]).pipe(
      switchMap(([partnerId, userId]) => this.salespersonService.getSalespersonByUserId(partnerId, userId)),
      map((salesperson: Salesperson) => salesperson?.marketId === this.business?.externalIdentifiers?.marketId),
      catchError((err: HttpErrorResponse) => {
        // Show the button if we've received a 404 because this will allow them to create a salesperson.
        return of(err.status === 404);
      }),
    );

    this.sscButtonVisible$ = combineLatest([salespersonCanViewBusinessMarket$, this.showSalesAndSuccessCenter$]).pipe(
      map(([salespersonCanViewBusinessMarket, showSalesAndSuccessCenter]) => {
        return salespersonCanViewBusinessMarket && showSalesAndSuccessCenter;
      }),
    );

    this.companyButtonVisible$ = this.crmObjectService
      .getMultiObject('Company', [this.business.externalIdentifiers['platformCrmCompanyId']])
      .pipe(
        map((response) => {
          // Show button if the associated company is not deleted
          return !!response.crmObjects.find((crmObject) => crmObject.crmObjectId);
        }),
      );

    this.subscriptions.push(
      // we want to know if they have a task manager account right away, so they don't have to wait requests if they
      // click the Open in Task Manager button
      this.partnerId$
        .pipe(
          switchMap((partnerId: string) =>
            this.conciergeAccountService.getPurchasingPartnerConciergeAccount(partnerId, this.business.accountGroupId),
          ),
        )
        .subscribe({
          next: () => {
            this.hasTaskManagerAccount.set(true);
          },
          // there's nothing to do for the error here, this is just to stop the response from being printed to the console
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          error: () => {},
        }),
    );

    this.canAccessRetailBilling$ = this.appConfig.config$.pipe(map((config) => config?.userCanAccessRetailBilling));
  }

  get hasAccessToLists$(): Observable<boolean> {
    return this.accessService.hasAccessToFeature(Feature.lists);
  }

  get hasAccessToTaskManager$(): Observable<boolean> {
    return this.accessService.hasAccessToFeature(Feature.concierge);
  }

  /**
   * Create a sales order for the current business.
   */
  createSalesOrder(): void {
    if (this.business.accountGroupId) {
      combineLatest([this.partnerId$, this.marketId$, this.userId$])
        .pipe(
          switchMap(([partnerId, marketId, userId]) => {
            const businessId = this.business.accountGroupId;
            return this.createOrderDialogService.createOrderAndRedirect(
              partnerId,
              marketId,
              businessId,
              userId,
              (businessId, orderId) => `/order-management/${businessId}/view/${orderId}`,
            );
          }),
          take(1),
        )
        .subscribe();
    } else {
      this.snackbarService.openErrorSnack('SALES_ORDERS.ERRORS.GENERIC_ERROR');
    }
  }

  /**
   * Present the user a confirmation dialog to delete the business.
   */
  delete(): void {
    this.router.navigate([
      {
        outlets: {
          action: ['businesses', 'accounts', this.business?.accountGroupId, 'delete', 'redirect'],
        },
      },
    ]);
  }

  /**
   * Present the user a dialog to add the account to a list.
   */
  addToList(): void {
    this.hasAccessToLists$.pipe(take(1)).subscribe((hasAccessToList) => {
      if (hasAccessToList) {
        this.router.navigate([
          {
            outlets: {
              action: ['businesses', 'accounts', this.business?.accountGroupId, 'addToList'],
            },
          },
        ]);
      }
    });
  }

  /**
   * Opens the account in Task Manager in a new tab
   */
  handleTaskManagerClick(): void {
    if (!this.isCreatingTaskManagerAccount()) {
      if (this.hasTaskManagerAccount()) {
        combineLatest([this.partnerId$, this.hasAccessToTaskManager$])
          .pipe(
            switchMap(([partnerId, hasAccessToTaskManager]) => {
              if (!hasAccessToTaskManager) {
                this.openAccessRestrictedDialog(Feature.concierge);
                return of(null);
              }
              return this.conciergeAccountService.getUrlForAccountDetailsPage(partnerId, this.business.accountGroupId);
            }),
            take(1),
          )
          .subscribe((url: string) => {
            if (url) {
              window.open(url, '_blank', 'noopener');
            }
          });
      } else {
        this.partnerId$
          .pipe(
            switchMap((partnerId: string) => {
              this.isCreatingTaskManagerAccount.set(true);
              return this.conciergeAccountService.createAccount(partnerId, this.business.accountGroupId).pipe(
                catchError((err) => {
                  if (err instanceof HttpErrorResponse) {
                    if (err.status === 409) {
                      // 409 means the account already exists, return null to open the account page
                      return of(null);
                    }
                    throw err;
                  }
                  throw err;
                }),
                switchMap(() =>
                  this.conciergeAccountService.getUrlForAccountDetailsPage(partnerId, this.business.accountGroupId),
                ),
              );
            }),
            take(1),
          )
          .subscribe({
            next: (url: string) => {
              this.isCreatingTaskManagerAccount.set(false);
              window.open(url, '_blank', 'noopener');
            },
            error: () => {
              this.isCreatingTaskManagerAccount.set(false);
              this.snackbarService.openErrorSnack('BUSINESS.SOMETHING_WENT_WRONG');
            },
          });
      }
    }
  }

  private openAccessRestrictedDialog(featureId: string): void {
    this.dialog.closeAll();
    this.dialog.open(AccessRestrictedDialogComponent, {
      width: AccessRestrictedDialogComponent.DEFAULT_WIDTH,
      maxWidth: AccessRestrictedDialogComponent.DEFAULT_MAX_WIDTH,
      data: { featureId: featureId },
    });
  }

  navigatePage() {
    let crmURL = '/crm/company/list';
    const companyID = this.business.externalIdentifiers['platformCrmCompanyId'];
    if (companyID) {
      crmURL = `/crm/company/profile/${companyID}`;
    }
    this.router.navigateByUrl(crmURL);
  }

  ngOnDestroy() {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
}
