<ng-container *ngIf="(companyButtonVisible$ | async) === true">
  <a (click)="navigatePage()" mat-stroked-button color="primary" class="go-company-button no-border">
    {{ 'BUSINESS.VIEW_COMPANY' | translate }}
  </a>
</ng-container>

<button
  mat-stroked-button
  [disabled]="openInProcessing()"
  [matMenuTriggerFor]="openInMenu"
  data-action="pc:business-details:actions:view-in"
>
  <glxy-button-loading-indicator [isLoading]="openInProcessing()">
    <span class="center-action-btn-txt">
      {{ 'BUSINESS.OPEN_IN' | translate }}
      <mat-icon iconPositionEnd class="menu-button-right-icon">arrow_drop_down</mat-icon>
    </span>
  </glxy-button-loading-indicator>
</button>
<mat-menu #openInMenu="matMenu">
  <a
    mat-menu-item
    *ngIf="businessAppDetails$ | async as entryDetails"
    [href]="entryDetails.url"
    target="_blank"
    rel="noopener noreferrer"
    data-action="pc:business-details:actions:open-business-app"
  >
    <div class="external-link">
      <div>{{ entryDetails.name }}</div>
      <div class="spacer"></div>
      <div class="icon-container">
        <mat-icon class="icon">open_in_new</mat-icon>
      </div>
    </div>
  </a>
  <a
    mat-menu-item
    *ngIf="sscButtonVisible$ | async"
    [href]="'/sales/redirect?nextUrl=/info/' + business.accountGroupId"
    target="_blank"
    rel="noopener noreferrer"
    data-action="pc:business-details:actions:open-ssc"
  >
    <div class="external-link">
      <div>{{ 'BUSINESS.SALES_AND_SUCCESS_CENTER' | translate }}</div>
      <div class="spacer"></div>
      <div class="icon-container">
        <mat-icon class="icon">open_in_new</mat-icon>
      </div>
    </div>
  </a>
  <a mat-menu-item (click)="handleTaskManagerClick()" data-action="pc:business-details:actions:open-tm">
    <div class="external-link">
      <div>{{ 'BUSINESS.TASK_MANAGER' | translate }}</div>
      <div class="spacer"></div>
      <div class="icon-container">
        <mat-icon *ngIf="(hasAccessToTaskManager$ | async) === true; else locked" class="icon">open_in_new</mat-icon>
      </div>
      <ng-template #locked>
        <mat-icon class="icon">arrow_circle_up</mat-icon>
      </ng-template>
    </div>
  </a>
</mat-menu>
<button mat-stroked-button [matMenuTriggerFor]="moreMenu" data-action="pc:business-details:actions:more">
  {{ 'BUSINESS.MORE' | translate }}
  <mat-icon iconPositionEnd class="menu-button-right-icon">arrow_drop_down</mat-icon>
</button>
<mat-menu #moreMenu="matMenu">
  <a mat-menu-item [routerLink]="['./edit']" data-action="pc:business-details:actions:edit">
    {{ 'BUSINESS.EDIT' | translate }}
  </a>
  <a
    mat-menu-item
    (click)="addToList()"
    data-action="pc:business-details:actions:add-to-list"
    class="center-menu-item-txt-btn"
    [disabled]="(hasAccessToLists$ | async) === false"
  >
    <mat-icon *ngIf="(hasAccessToLists$ | async) === false">arrow_circle_up</mat-icon>
    <span>{{ 'BUSINESS.ADD_TO_LIST' | translate }}</span>
  </a>
  <mat-divider></mat-divider>
  <a mat-menu-item class="warn" (click)="delete()" data-action="pc:business-details:actions:delete">
    {{ 'BUSINESS.DELETE' | translate }}
  </a>
</mat-menu>
@if (noAdminOrderCreation$ | async) {
  <button
    mat-flat-button
    color="primary"
    [matMenuTriggerFor]="newMenu"
    [disabled]="newButtonLoading$ | async"
    data-action="pc:business-details:actions:new"
  >
    <glxy-button-loading-indicator [isLoading]="newButtonLoading$ | async">
      <span class="center-action-btn-txt">
        {{ 'COMMON.ACTIONS' | translate }}
        <mat-icon iconPositionEnd class="menu-button-right-icon">arrow_drop_down</mat-icon>
      </span>
    </glxy-button-loading-indicator>
  </button>
  <mat-menu #newMenu="matMenu">
    <a
      *ngIf="canCreateOrders$ | async"
      mat-menu-item
      [routerLink]="['./activation']"
      [queryParams]="{ navigationSource: 'account-details' }"
      data-action="pc:business-details:actions:new-order"
    >
      {{ 'BUSINESS.ORDER_PRODUCTS' | translate }}
    </a>
    <ng-container *ngIf="canAccessRetailBilling$ | async">
      <a mat-menu-item (click)="createSalesOrder()" data-action="pc:business-details:actions:new-sales-order">
        {{ 'SALES_ORDERS.CREATE_SALES_ORDER' | translate }}
      </a>
    </ng-container>
  </mat-menu>
}
