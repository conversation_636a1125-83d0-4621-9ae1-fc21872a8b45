import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Inject,
  On<PERSON>estroy,
  OnInit,
  QueryList,
  ViewChildren,
} from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountGroup, AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { StartManualAutomationDialogComponent } from '@galaxy/automata/shared';
import { SNAPSHOT_REPORT_REFRESH_SKU } from '@galaxy/billing';
import { FeatureFlagService, WhitelabelService } from '@galaxy/partner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BrandMetadata, MultiLocationService } from '@vendasta/multi-location';
import { BillingFrequency, ChangeSpendRequestStatus, OrderFulfillmentService } from '@vendasta/order-fulfillment';
import { Breadcrumbs } from '@vendasta/uikit';
import {
  BehaviorSubject,
  combineLatest,
  endWith,
  firstValueFrom,
  lastValueFrom,
  Observable,
  of,
  Subscription,
} from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  filter,
  first,
  map,
  pluck,
  publishReplay,
  refCount,
  shareReplay,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs/operators';
import { AccessRestrictedDialogComponent } from '../../../../access';
import { AddUserDialogComponent } from '../../../../accounts/add-user-dialog.component';
import { CampaignsDialogComponent } from '../../../../accounts/campaigns-dialog.component';
import { AppConfigService } from '../../../../app-config.service';
import { UnifiedTOSDialogComponent } from '../../../../common/tos-unified/unified-tos-dialog.component';
import { BusinessCenterService, VBCEntryInfo } from '../../../../core/business-center.service';
import { FeatureFlags } from '../../../../core/features';
import { UnifiedTOSService } from '../../../../core/tos-unified.service';
import {
  ManageSubscriptionsService,
  SubscriptionLimit,
} from '../../../../subscription-tier/utils/manage-subscriptions.service';
import { ResendWelcomeEmailDialogComponent } from '../../../../users/resend-welcome-email-dialog/resend-welcome-email-dialog.component';
import { CreateSnapshotComponent } from './components/create-snapshot/create-snapshot.component';
import { ExecutiveReportSenderComponent } from './components/executive-report-sender/executive-report-sender.component';
import { FileActionOption, FilesDialog } from './components/files';
import {
  ChangeSpendRequestEventDisplayContainer,
  SpendChangeRequestEventsPage,
} from './components/spend-change-history/spend-change-history.component';
import { UserActionOptionButton } from './components/users-box-content/users-box-content.component';

import { Context, EntityType } from '@vendasta/automata';
import { DomainService } from '@vendasta/domain';
import { CreateOrderDialogService } from '@vendasta/orders';
import { newAccountServiceContext, SSOService } from '@vendasta/sso';
import { addDays, differenceInDays } from 'date-fns';
import moment from 'moment';
import { CheckoutDialogComponent } from '../../../../checkout/checkout-dialog.component';
import { VaDialogService } from '../../../../common/va-dialog.service';
import { AccessService, Feature, StubAccessService } from '../../../../core/access';
import { Account } from '../../../../core/account';
import { AccountDetails, AccountService } from '../../../../core/account.service';
import { FileGroup } from '../../../../core/filegroup';
import { FileGroupService } from '../../../../core/filegroup.service';
import { MarketsService } from '../../../../core/markets/markets.service';
import { PartnerService } from '../../../../core/partner.service';
import { SnapshotService } from '../../../../core/snapshot.service';
import { User } from '../../../../core/user';
import { DynamicOpenCloseTemplateRefService } from '../../../../side-drawer/dynamic-open-close-template-ref.service';
import { calculateEffectiveDate } from './components/spend-change-history/spend-change-utils';
import { CannotProvisionReason } from '@vendasta/snapshot';
import { TrialBannerService } from '../../../../trial-banner/trial-banner.service';
import { CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';

const agid_key = 'businessId';
const SNAPSHOT_SKU = 'ST';

@Component({
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class OverviewComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChildren(CreateSnapshotComponent) createSnapshotComponents: QueryList<CreateSnapshotComponent>;
  private createSnapshotComponent: CreateSnapshotComponent;
  private whitelabelSnapshotName$: Observable<string>;

  subscriptions: Subscription[] = [];

  dialogRef: MatDialogRef<AddUserDialogComponent>;
  campaignRef: MatDialogRef<CampaignsDialogComponent>;
  filesDialogRef: MatDialogRef<FilesDialog>;

  details$: Observable<AccountDetails>;
  accountGroup$: Observable<AccountGroup>;
  noAdminOrderCreation$: Observable<boolean>;

  loadingUsers$: Observable<boolean>;

  cursor: string;
  market: string;
  marketId: string;
  breadcrumbs$: Observable<Breadcrumbs[]>;
  canAccessOrders$: Observable<boolean>;
  loadingSnapshot$: Observable<boolean>;
  snapshotCreated$: Observable<boolean>;
  tosDialog: MatDialogRef<UnifiedTOSDialogComponent>;
  reportSenderDialog: MatDialogRef<ExecutiveReportSenderComponent>;
  showTOSDialog: boolean;
  accessRestrictedDialogRef: MatDialogRef<AccessRestrictedDialogComponent>;

  snapshotIsExpired$: Observable<boolean>;
  snapshotIsRefreshed$: Observable<boolean>;
  snapshotBecomingReady$: Observable<boolean>;
  snapshotIsReadyAt$: Observable<Date>;
  snapshotRefreshTooltip$: Observable<string>;
  hasAccessToCampaign$: Observable<boolean>;
  hasAccessToMarkets$: Observable<boolean>;
  hasAccessToSsc$: Observable<boolean>;
  hasAccessToLists$: Observable<boolean>;
  userActions$: Observable<{ label: string; locked?: boolean }[]>;
  showInboxPaneCard$: Observable<boolean>;
  displaySnapshotListingScan$: Observable<boolean>;

  private isImpersonated: boolean;

  showCTAs = false;
  showAddUserDialog = false;

  execReportUrl$: Observable<VBCEntryInfo>;

  get partnerId(): string {
    return this.appConfig.config.partnerId;
  }

  get userId(): string {
    return this.appConfig.config.unifiedUserId;
  }

  public get accountGroupId(): string {
    return this.route.snapshot.params[agid_key];
  }

  snapshotSubscriptionLimits$: Observable<SubscriptionLimit>;
  snapshotsRemaining$: Observable<number>;
  disableRefreshButton$: Observable<boolean>;

  brands$: Observable<BrandMetadata[]>;
  spendChangeHistoryNextPageCursor: string;
  spendChangeHistoryHasMoreRecords: boolean;
  spendChangeHistoryLoadMore$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  spendChangeRequestHistoryIsLoading: boolean;
  spendChangeRequestEvents$$: BehaviorSubject<ChangeSpendRequestEventDisplayContainer[]> = new BehaviorSubject<
    ChangeSpendRequestEventDisplayContainer[]
  >(null);
  isOnTrial$: Observable<boolean>;

  primaryIcon = 'primary';
  greyIcon = undefined;

  orderUrlCallback = (orderId: string) =>
    this.accountService.account$.pipe(
      map((account) => `/order-management/${account.account_group_id}/info/${orderId}`),
    );

  fulfillmentFormUrlCallback = (orderId: string) =>
    this.accountService.account$.pipe(
      map((account) => `/order-management/${account.account_group_id}/work-order/${orderId}`),
    );

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private accountService: AccountService,
    private fileGroupService: FileGroupService,
    private snackbarService: SnackbarService,
    private marketsService: MarketsService,
    public dialog: MatDialog,
    private snapshotService: SnapshotService,
    private dialogService: VaDialogService,
    private tosService: UnifiedTOSService,
    private accountGroupService: AccountGroupService,
    private appConfig: AppConfigService,
    @Inject(StubAccessService) private accessService: AccessService,
    private matIconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
    private businessCenterService: BusinessCenterService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly manageSubscriptionsService: ManageSubscriptionsService,
    multiLocationService: MultiLocationService,
    private orderFulfillmentService: OrderFulfillmentService,
    private partnerService: PartnerService,
    private dynamicOpenCloseService: DynamicOpenCloseTemplateRefService,
    private ssoService: SSOService,
    private domainService: DomainService,
    private readonly whitelabelService: WhitelabelService,
    private createOrderDialogService: CreateOrderDialogService,
    private readonly trialBannerService: TrialBannerService,
    @Inject(CrmInjectionToken) private readonly crmConfig: CrmDependencies,
  ) {
    this.brands$ = multiLocationService.listBrandsForBusiness(this.partnerId, this.accountGroupId);
    this.loadingUsers$ = this.accountService.loadingUsers$;
  }

  ngOnInit(): void {
    this.whitelabelSnapshotName$ = this.accountService.account$.pipe(
      switchMap((account) => this.whitelabelService.getConfiguration(this.partnerId, account.market_id)),
      map((whiteLabelConfig) => whiteLabelConfig.snapshotConfiguration.snapshotReportName),
      catchError(() => 'Snapshot report'),
    );

    /* tslint:disable:no-string-literal */
    this.showCTAs = this.route.snapshot.queryParams['show_ctas'] === 'true';
    this.isImpersonated = this.appConfig.config.isImpersonated;
    this.subscriptions.push(
      this.tosService.tosDataLoaded$.subscribe((isReady) => {
        if (isReady) {
          if (!this.tosService.hasAcceptedTOS && !this.tosService.isClassicSubscription) {
            this.showTOSDialog = true;
          }
        }
      }),
    );

    this.details$ = this.route.params.pipe(
      pluck(agid_key),
      filter((id) => !!id),
      tap((id: string) =>
        this.accountService.setCurrentAccountID(id, true, [
          'email',
          'firstName',
          'lastName',
          'userId',
          'unifiedUserId',
        ]),
      ),
      switchMap(() => {
        return this.accountService.accountDetails$;
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.subscriptions.push(
      this.spendChangeHistoryLoadMore$$.subscribe((_) => {
        this.loadMoreSpendChangeHistoryItems();
      }),
    );

    //Check if we should show the add user dialog
    this.showAddUserDialog = this.route.snapshot.queryParams['add_users'] === 'true';
    if (this.showAddUserDialog) {
      this.details$.pipe(take(1)).subscribe((details) => {
        this.presentAddUsersDialog(details?.users);
      });
    }

    this.breadcrumbs$ = combineLatest([this.route.queryParams, this.accountService.account$]).pipe(
      map(([params, account]) => [...this.extractBreadcrumbsFromParams(params), { text: account.company_name }]),
    );

    const market$ = this.accountService.account$.pipe(
      switchMap((account) => {
        return this.marketsService.getMarket(account.market_id);
      }),
      filter((market) => !!market),
      take(1),
      publishReplay(1),
      refCount(),
    );
    this.accountGroup$ = this.accountService.accountID$.pipe(
      switchMap((accountGroupID) =>
        this.accountGroupService.get(
          accountGroupID,
          new ProjectionFilter({ status: true, snapshotReports: true, accountGroupExternalIdentifiers: true }),
        ),
      ),
    );
    this.noAdminOrderCreation$ = this.crmConfig.canPartnerAdminCreateOrdersFeatureFlag$;

    const userCanAccessOrders$ = this.appConfig.config$.pipe(map((config) => config?.userCanAccessOrders));

    this.canAccessOrders$ = combineLatest([
      this.accessService.hasAccessToFeature(Feature.salesOrders),
      userCanAccessOrders$,
    ]).pipe(map(([partnerAccess, userAccess]) => partnerAccess && userAccess));

    const features$ = this.accountService.account$.pipe(
      switchMap((account) => {
        return this.featureFlagService.batchGetStatus(account.partner_id, account.market_id, [
          FeatureFlags.SNAPSHOT_LISTING_SCAN_PCC_SCC,
          FeatureFlags.INBOX_SMB_CAN_MESSAGE_PARTNER,
        ]);
      }),
    );

    this.displaySnapshotListingScan$ = features$.pipe(
      map((status) => status[FeatureFlags.SNAPSHOT_LISTING_SCAN_PCC_SCC]),
    );

    this.showInboxPaneCard$ = features$.pipe(map((status) => status[FeatureFlags.INBOX_SMB_CAN_MESSAGE_PARTNER]));

    this.subscriptions.push(
      market$.subscribe((market) => {
        this.market = market ? market.name : '';
      }),
    );

    this.subscriptions.push(
      this.fileGroupService.cursor.subscribe((cursor) => {
        this.cursor = cursor;
      }),
    );

    this.hasAccessToCampaign$ = this.accessService
      .hasAccessToFeature(Feature.marketingAutomation)
      .pipe(shareReplay({ refCount: true, bufferSize: 1 }));
    this.hasAccessToMarkets$ = this.accessService.hasAccessToFeature(Feature.markets);
    this.hasAccessToSsc$ = this.accessService.hasAccessToFeature(Feature.salesAndSuccessCenterAccess);
    this.hasAccessToLists$ = this.accessService.hasAccessToFeature(Feature.lists);

    this.userActions$ = this.hasAccessToCampaign$.pipe(
      map((enabled) => {
        return [
          { label: 'Edit Permissions' },
          { label: 'Edit Notifications' },
          { label: 'Campaigns', locked: !enabled },
          { label: 'Resend Welcome Email' },
          { label: 'Impersonate' },
          { label: 'Remove User' },
        ];
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.matIconRegistry.addSvgIcon(
      'listing-scan',
      this.sanitizer.bypassSecurityTrustResourceUrl('/static/images/listing-scan.svg'),
    );

    this.execReportUrl$ = this.accountGroup$.pipe(
      switchMap((accountGroup) =>
        this.businessCenterService.getVBCEntryUrl(accountGroup.accountGroupId, 'executive-report/monthly'),
      ),
    );

    this.isOnTrial$ = this.trialBannerService.getTrialDetails$().pipe(map((trialDetails) => trialDetails.onTrial));

    this.setupSnapshotOverview();
  }

  ngAfterViewInit(): void {
    if (!this.createSnapshotComponent) {
      this.createSnapshotComponent = this.createSnapshotComponents.first;
    }
    this.subscriptions.push(
      this.createSnapshotComponents.changes.pipe(take(1)).subscribe((c: QueryList<CreateSnapshotComponent>) => {
        this.createSnapshotComponent = c.first;
      }),
    );
  }

  private extractBreadcrumbsFromParams(params: { breadcrumbs?: string[] }): Breadcrumbs[] {
    if (params.breadcrumbs) {
      return params.breadcrumbs.map((crumb) => JSON.parse(crumb));
    } else {
      return [{ url: '/manage-accounts', text: 'Manage Accounts' }];
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
    this.accountService.clearAccountDetails();
  }

  performActionOnFile(action: FileActionOption, file: FileGroup): void {
    switch (action) {
      case 'Edit':
        this.gotoFileEdit(file);
        break;
      case 'Remove File':
        this.presentFileDeleteModal(file);
        break;
      case 'Show':
        this.presentFileModal(file);
        break;
    }
  }

  gotoFileEdit(file: FileGroup): void {
    this.router.navigateByUrl(`/manage-accounts/files?agid=${this.accountGroupId}&fgid=${file.file_group_id}`);
  }

  createOrder(): void {
    if (this.accountGroupId) {
      this.createOrderDialogService
        .createOrderAndRedirect(
          this.partnerId,
          this.marketId,
          this.accountGroupId,
          this.userId,
          (businessId, orderId) => `/order-management/${businessId}/view/${orderId}`,
          true,
        )
        .subscribe();
    } else {
      this.snackbarService.openErrorSnack('SALES_ORDERS.ERRORS.GENERIC_ERROR');
    }
  }

  loadMoreFiles(): void {
    this.fileGroupService.getMoreFileGroups();
  }

  presentFileDeleteModal(file: FileGroup): void {
    this.subscriptions.push(
      this.dialogService
        .delete('Are you sure?', `Are you sure you want to delete the file ${file.title}?`, 'Delete')
        .pipe(
          switchMap((result) => {
            if (result === 'yes') {
              return this.fileGroupService.deleteFileGroup(file);
            } else {
              return of(false);
            }
          }),
        )
        .subscribe(
          () => this.snackbarService.openSuccessSnack('FILES.FILES_DELETED'),
          () => this.snackbarService.openErrorSnack('FILES.FILES_ALREADY_DELETED'),
        ),
    );
  }

  presentFileModal(file: FileGroup): void {
    this.filesDialogRef = this.dialog.open(FilesDialog, { width: '620px' });
    this.filesDialogRef.componentInstance.fileGroup = file;
    this.filesDialogRef.componentInstance.show_delete = false;
  }

  presentAddFile(): void {
    this.router.navigateByUrl(`manage-accounts/files?agid=${this.accountGroupId}`);
  }

  async performActionOnUser(action: UserActionOptionButton, user: User): Promise<void> {
    const isOnTrial = await firstValueFrom(this.isOnTrial$);
    switch (action.label) {
      case 'Edit Permissions':
        this.presentEditPermissionsForUser(user);
        break;
      case 'Edit Notifications':
        this.presentEditNotificationsForUser(user);
        break;
      case 'Edit User':
        this.presentEditForUser(user);
        break;
      case 'Campaigns':
        if (action.locked || isOnTrial) {
          this.openAccessRestrictedDialog(Feature.marketingAutomation);
        } else {
          this.presentCampaignsForUser(user);
        }
        break;
      case 'Impersonate':
        this.impersonateUser(user);
        break;
      case 'Remove User':
        this.presentDeleteUserDialog(user);
        break;
      case 'Resend Welcome Email':
        this.presentSendWelcomeEmailDialog(user);
        break;
    }
  }

  private openAccessRestrictedDialog(featureId: string): void {
    this.dialog.closeAll();
    this.accessRestrictedDialogRef = this.dialog.open(AccessRestrictedDialogComponent, {
      width: AccessRestrictedDialogComponent.DEFAULT_WIDTH,
      maxWidth: AccessRestrictedDialogComponent.DEFAULT_MAX_WIDTH,
      data: { featureId: featureId },
    });
    this.subscriptions.push(
      this.accessRestrictedDialogRef.afterClosed().subscribe(() => (this.accessRestrictedDialogRef = null)),
    );
  }

  presentEditPermissionsForUser(user: User): void {
    this.router.navigateByUrl(`/bc-admin/users/${user.userId}/permissions`);
  }

  presentEditNotificationsForUser(user: User): void {
    this.router.navigateByUrl(`/bc-admin/users/${user.userId}/notifications`);
  }

  presentEditForUser(user: User): void {
    this.router.navigateByUrl(`bc-admin/user/${user.userId}/edit?return=${this.router.url}`);
  }

  presentCampaignsForUser(user: User): void {
    this.accountService.account$
      .pipe(
        map((account) => {
          this.campaignRef = this.dialog.open(CampaignsDialogComponent, { width: '620px' });
          this.campaignRef.componentInstance.partnerId = this.partnerId;
          this.campaignRef.componentInstance.accountGroupId = this.accountGroupId;
          this.campaignRef.componentInstance.user = user;
          this.campaignRef.componentInstance.marketId = account.market_id;
        }),
        take(1),
      )
      .subscribe();
  }

  impersonateUser(user: User): void {
    this.ssoService
      .getEntryUrl('VBC', newAccountServiceContext(this.accountGroupId), {
        impersonation: user.unifiedUserId,
        path: `/account/location/${this.accountGroupId}/dashboard`,
      })
      .subscribe({
        next: (url) => {
          window.open(url, '_blank', 'noopener');
        },
        error: () => this.snackbarService.openErrorSnack('MANAGE_ACCOUNTS.IMPERSONATE_USER_DIALOG.ERROR'),
      });
  }

  presentSendWelcomeEmailDialog(user: User): void {
    this.details$.pipe(take(1)).subscribe((accountDetails) => {
      this.partnerService
        .getBusinessCenterHostname()
        .pipe(take(1))
        .subscribe((hostName) =>
          this.dialog.open(ResendWelcomeEmailDialogComponent, {
            data: {
              userID: user.unifiedUserId,
              partnerID: this.partnerService.partnerId,
              marketId: accountDetails.account.market_id,
              loginUrl: hostName + '/login/',
            },
            width: '600px',
          }),
        );
    });
  }

  presentDeleteUserDialog(user: User): void {
    this.subscriptions.push(
      this.dialogService
        .delete('Are you sure?', `Are you sure you want to remove ${user.fullName} from this account?`, 'Remove')
        .subscribe((result) => {
          if (result === 'yes') {
            this.removeUser(user);
          }
        }),
    );
  }

  removeUser(user: User): void {
    this.accountService.removeUserFromAccount(this.accountGroupId, user.userId);
  }

  presentAddUsersDialog(users: User[]): void {
    this.dialogRef = this.dialog.open(AddUserDialogComponent, { width: '620px' });
    this.dialogRef.componentInstance.accountGroupId = this.accountGroupId;
    this.dialogRef.componentInstance.partnerId = this.partnerId;
    this.dialogRef.componentInstance.existingUsers = users;
    this.subscriptions.push(
      this.dialogRef
        .afterOpened()
        .pipe(
          switchMap(() => this.accountService.accountDetails$),
          switchMap((accountDetails) => {
            if (this.dialogRef.componentInstance) {
              this.dialogRef.componentInstance.existingUsers = accountDetails.users || [];
              this.dialogRef.componentInstance.accountGroupId = accountDetails.account.account_group_id;
              this.dialogRef.componentInstance.partnerId = accountDetails.account.partner_id;
            }
            return this.dialogRef.afterClosed();
          }),
        )
        .subscribe((result) => {
          if (result === 'create-new-user') {
            this.router.navigateByUrl(
              `bc-admin/user/create/?from=account-details&accountGroupId=${this.accountGroupId}`,
            );
          }
        }),
    );
  }

  openUrlInNewTab(url: string): void {
    window.open(url, '_blank', 'noopener');
  }

  public openTOSDialog(
    callback: (accountGroup: AccountGroup, verb: string) => void,
    accountGroup?: AccountGroup,
    verb?: string,
  ): void {
    if (this.tosDialog) {
      return;
    }
    this.tosDialog = this.dialog.open(UnifiedTOSDialogComponent, UnifiedTOSDialogComponent.config());
    this.subscriptions.push(
      this.tosDialog.afterClosed().subscribe(() => {
        this.tosDialog = null;
        if (this.tosService.hasAcceptedTOS) {
          this.showTOSDialog = false;
          callback(accountGroup, verb);
        }
      }),
    );
  }

  public async openReportSenderDialog(): Promise<void> {
    const account = await firstValueFrom(this.accountService.account$);
    this.reportSenderDialog = this.dialog.open(
      ExecutiveReportSenderComponent,
      ExecutiveReportSenderComponent.config(this.accountGroupId, this.partnerId, account.company_name),
    );
    await lastValueFrom(this.reportSenderDialog.afterClosed());
    this.reportSenderDialog = null;
  }

  createSnapshotCTA(accountGroup: AccountGroup): void {
    this.createSnapshotComponent.onSnapshotCreatePressed(accountGroup);
  }

  viewSnapshotCTA(): void {
    const snapshotURL$ = this.createSnapshotComponent.currentSnapshot$.pipe(
      map((snapshot) => {
        return snapshot.path;
      }),
      take(1),
    );

    const domain$ = this.getDomain();

    combineLatest([snapshotURL$, domain$])
      .pipe(
        map(([url, domain]) => {
          if (url && domain && url !== '') {
            window.open(`${domain}${url}`, '_blank', 'noopener');
          } else {
            this.dynamicOpenCloseService.open(CreateSnapshotComponent.scorecardTemplateID);
          }
        }),
        take(1),
        catchError((_) => {
          this.dynamicOpenCloseService.open(CreateSnapshotComponent.scorecardTemplateID);
          return null;
        }),
      )
      .subscribe();
  }

  private getDomain(): Observable<string> {
    return this.domainService.getDomainByIdentifier(`/application/ST/partner/${this.partnerId}`).pipe(
      map((resp) => {
        const scheme = resp.primary.secure ? 'https' : 'http';
        return `${scheme}://${resp.primary.domain}`;
      }),
      take(1),
    );
  }

  goToBusinessSearch(): void {
    this.router.navigateByUrl('business/search');
  }

  addUserToAccountCTA(users: User[]): void {
    this.presentAddUsersDialog(users);
  }

  goToProductActivate(accountGroupId: string): void {
    if (this.showTOSDialog && !this.isImpersonated) {
      this.openTOSDialog(this.navigateToActivate.bind(this), undefined);
      this.openTOSDialog(undefined);
    } else {
      this.navigateToActivate(accountGroupId);
    }
  }

  public navigateToActivate(accountGroupId: string): void {
    this.router.navigate([`/businesses/accounts/${accountGroupId}/activation`], {
      queryParams: { navigationSource: 'account-details' },
    });
  }

  startManualAutomation(account: Account): void {
    this.dialog
      .open(StartManualAutomationDialogComponent, {
        data: {
          companyName: account.company_name,
          namespace: account.partner_id,
          entities: new Map<EntityType, string[]>([[EntityType.ENTITY_TYPE_ACCOUNT_GROUP, [account.account_group_id]]]),
          context: Context.AUTOMATION_CONTEXT_PARTNER,
        },
      })
      .afterClosed()
      .pipe(take(1))
      .subscribe();
  }

  loadMoreSpendChangeHistoryItems(): void {
    const spendChangeListResponse$ = this.details$.pipe(
      filter((accountDetails) => {
        return !!(accountDetails?.account?.account_group_id || '');
      }),
      map((accountDetails) => {
        return accountDetails.account.account_group_id;
      }),
      distinctUntilChanged(),
      switchMap((accountGroupId) => {
        return this.orderFulfillmentService.listChangeSpendRequestHistory(
          10,
          this.spendChangeHistoryNextPageCursor,
          [],
          [accountGroupId],
          [],
          [],
        );
      }),
      publishReplay(1),
      refCount(),
    );

    const spendChangeRequestEventsPage$ = combineLatest([spendChangeListResponse$]).pipe(
      map(([scrs]) => {
        const spendChangeRequestEvents = (scrs?.changeSpendRequestEvents || []).map((request) => {
          const scr: ChangeSpendRequestEventDisplayContainer = request as ChangeSpendRequestEventDisplayContainer;
          const shortBillingFrequency = this.formatShortBillingFrequency(scr.externalData?.billingFrequency);
          scr.statusName = this.formatSpendChangeRequestStatus(scr.status);
          scr.appName = scr.externalData?.itemName;
          scr.newSpendAmountFormatted =
            '$' +
            (scr?.externalData?.requestedSpendAmount / 100 || 0).toFixed(2) +
            (shortBillingFrequency ? '/' + shortBillingFrequency : '');
          scr.requestDateFormatted = this.formatDate(scr.eventTime);
          scr.rejectedNote = scr.externalData?.note;
          scr.effectiveDateFormatted = this.formatCondensedDate(
            calculateEffectiveDate(scr.externalData?.renewalDate, scr.eventTime, scr.externalData?.billingFrequency),
          );
          return scr;
        });

        return {
          requests: spendChangeRequestEvents,
          hasMore: scrs.hasMore,
          nextCursor: scrs.nextCursor,
        } as SpendChangeRequestEventsPage;
      }),
    );

    this.spendChangeRequestHistoryIsLoading = true;
    spendChangeRequestEventsPage$
      .pipe(
        first(),
        catchError(() => {
          return of([]);
        }),
      )
      .subscribe((scrPage: SpendChangeRequestEventsPage) => {
        if (!scrPage) {
          return;
        }
        this.spendChangeHistoryHasMoreRecords = scrPage.hasMore || false;
        this.spendChangeHistoryNextPageCursor = scrPage.nextCursor || '';
        let currVal = this.spendChangeRequestEvents$$.getValue() || [];
        currVal = currVal.concat(scrPage.requests || []);
        this.spendChangeRequestEvents$$.next(currVal);
        this.spendChangeRequestHistoryIsLoading = false;
      });
  }

  formatSpendChangeRequestStatus(changeRequestStatus: ChangeSpendRequestStatus): string {
    if (changeRequestStatus === ChangeSpendRequestStatus.CHANGE_SPEND_REQUEST_STATUS_CREATED) {
      return 'Submitted';
    }
    if (changeRequestStatus === ChangeSpendRequestStatus.CHANGE_SPEND_REQUEST_STATUS_APPROVED) {
      return 'Approved';
    }
    if (changeRequestStatus === ChangeSpendRequestStatus.CHANGE_SPEND_REQUEST_STATUS_REJECTED) {
      return 'Rejected';
    }
    if (changeRequestStatus === ChangeSpendRequestStatus.CHANGE_SPEND_REQUEST_STATUS_OVERRIDDEN) {
      return 'Overridden';
    }

    return '';
  }

  formatShortBillingFrequency(appBillingFrequency: BillingFrequency): string {
    // map to short display string.
    if (appBillingFrequency === BillingFrequency.BILLING_FREQUENCY_MONTHLY) {
      return 'mo';
    }
    if (appBillingFrequency === BillingFrequency.BILLING_FREQUENCY_YEARLY) {
      return 'yr';
    }

    return '';
  }

  formatDate(timestamp: Date): string {
    return timestamp.toLocaleDateString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  }

  formatCondensedDate(timestamp: Date): string {
    return timestamp.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  }

  onLoadMoreClicked(): void {
    this.spendChangeHistoryLoadMore$$.next(true);
  }

  buildOrdersPageForBusinessQuery(accountGroupId: string): Record<string, string> {
    return {
      'sales-orders': encodeURIComponent(JSON.stringify({ businessId: accountGroupId })),
    };
  }

  refreshButtonClicked() {
    this.openRefreshSnapshotOrTOSDialog();
  }

  openRefreshSnapshotOrTOSDialog(): void {
    if (this.showTOSDialog) {
      this.openTOSDialog(this.openRefreshSnapshotDialog.bind(this));
    } else {
      this.openRefreshSnapshotDialog();
    }
  }

  openRefreshSnapshotDialog(): void {
    this.dialog.closeAll();
    const dialog = combineLatest(this.accountGroup$, this.accountService.account$).pipe(
      switchMap(([accountGroup, account]) => {
        return this.dialog
          .open(CheckoutDialogComponent, {
            width: '480px',
            data: {
              partnerId: account.partner_id,
              title: 'Refresh Snapshot Report',
              successLabel: 'Refresh',
              productSKU: SNAPSHOT_REPORT_REFRESH_SKU,
              description:
                'The snapshot for <b>' +
                account.company_name +
                '</b> expired <b>' +
                moment.utc(accountGroup.getLatestSnapshotReport()?.expiry).fromNow() +
                '</b> and is currently outdated.\n Refresh the report to get the latest data.',
              accountGroupId: account.account_group_id,
              canCheckout$: this.snapshotService
                .canProvisionSnapshot({
                  partnerId: account.partner_id,
                  accountGroupId: account.account_group_id,
                  userId: this.userId,
                })
                .pipe(
                  catchError((err) => {
                    console.error(err);
                    return of({ canProvision: false, reason: CannotProvisionReason.UNKNOWN });
                  }),
                  tap((resp) => {
                    if (!resp.canProvision) {
                      this.snapshotService.showCannotProvisionSnapshotMessage(resp.reason);
                    }
                  }),
                  map((canProvision) => canProvision.canProvision),
                ),
            },
          })
          .afterClosed();
      }),
    );
    dialog.pipe(take(1)).subscribe((response) => {
      if (response.status === 'success') {
        this.refreshSnapshot(response.inferMissingData);
      }
    });
  }

  refreshSnapshot(inferMissingData: boolean): void {
    this.snapshotService.doRefreshSnapshotWork(this.accountGroupId, ['account-details'], inferMissingData);
  }

  private setupSnapshotOverview(): void {
    this.snapshotSubscriptionLimits$ = this.manageSubscriptionsService.getProductDetails(SNAPSHOT_SKU);
    this.snapshotsRemaining$ = this.snapshotSubscriptionLimits$.pipe(
      map((ssl) => (ssl.limitMax - ssl.currentValue < 0 ? 0 : ssl.limitMax - ssl.currentValue)),
    );

    this.snapshotIsExpired$ = this.accountGroup$.pipe(
      map((accountGroup) => accountGroup.getLatestSnapshotReport()?.expired()),
    );

    const snapshotRefreshedEvents$ = this.accountService.accountID$.pipe(
      switchMap((id) => this.snapshotService.snapshotRefreshIsSuccessful$(id)),
    );

    const snapshotIsRefreshing$ = this.accountService.accountID$.pipe(
      switchMap((id) => this.snapshotService.snapshotRefreshLoading$(id)),
    );

    const snapshotIsRefreshed$ = snapshotRefreshedEvents$.pipe(
      filter((is) => is === true),
      take(1),
    );

    this.snapshotIsRefreshed$ = snapshotRefreshedEvents$;

    this.subscriptions.push(
      combineLatest([snapshotIsRefreshing$, this.whitelabelSnapshotName$])
        .pipe(filter(([is, _]) => is === true))
        .subscribe(([_, name]) =>
          this.snackbarService.openSuccessSnack('SNAPSHOT_REPORT.MESSAGE.IS_REFRESHING', {
            interpolateTranslateParams: { snapshotName: name },
          }),
        ),
      combineLatest([snapshotRefreshedEvents$, this.whitelabelSnapshotName$])
        .pipe(filter(([is, _]) => is === false))
        .subscribe(([_, name]) =>
          this.snackbarService.openErrorSnack('SNAPSHOT_REPORT.ERROR.UNABLE_TO_REFRESH', {
            interpolateTranslateParams: { snapshotName: name },
          }),
        ),
    );

    this.disableRefreshButton$ = snapshotIsRefreshing$.pipe(takeUntil(snapshotIsRefreshed$), endWith(true));

    const accountGroupID$ = this.accountService.accountID$.pipe(take(1));

    const snapshotChangedEvents$ = accountGroupID$.pipe(
      switchMap((id) => this.snapshotService.getCurrentSnapshotUpdates$(id)),
    );

    this.snapshotBecomingReady$ = snapshotChangedEvents$.pipe(
      map((snapshot) => {
        return snapshot ? differenceInDays(new Date(), snapshot.created) < 1 : false;
      }),
    );

    this.snapshotIsReadyAt$ = this.accountService.accountID$.pipe(
      switchMap((id) => this.snapshotService.getCurrentSnapshotUpdates$(id)),
      map((snapshot) => addDays(snapshot?.created, 1)),
    );

    this.snapshotRefreshTooltip$ = this.accountGroup$.pipe(
      map((accountGroup) => {
        const latestSnapshot = accountGroup.getLatestSnapshotReport();
        if (latestSnapshot) {
          const fromNow = moment.utc(latestSnapshot.expiry).fromNow();
          const fromText = latestSnapshot.expired() ? 'Snapshot expired ' : 'Snapshot expires ';
          return fromText + fromNow;
        }
      }),
    );

    this.loadingSnapshot$ = this.accountService.accountID$.pipe(
      switchMap((id) => this.snapshotService.currentSnapshotLoading$(id)),
    );

    this.snapshotCreated$ = this.accountService.accountID$.pipe(
      switchMap((id) => this.snapshotService.getCurrentSnapshotSuccess$(id)),
    );

    this.subscriptions.push(
      this.accountService.accountID$
        .pipe(switchMap((id) => this.snapshotService.getCurrentSnapshotV2$(id)))
        .subscribe(),
    );
  }
}
