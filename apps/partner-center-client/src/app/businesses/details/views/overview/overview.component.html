<ng-container *ngIf="details$ | async as d">
  <div *ngIf="(accountGroup$ | async)?.status?.hasAlert" class="business-alert">
    <glxy-alert type="warning">
      <strong>
        {{ 'MANAGE_ACCOUNTS.ACCOUNT_REQUIRES_ATTENTION' | translate }}
      </strong>
      &mdash;
      {{ 'MANAGE_ACCOUNTS.SEE_HIGHLIGHTED_AREAS' | translate }}
    </glxy-alert>
  </div>

  <app-call-to-actions
    [loadingSnapshot]="loadingSnapshot$ | async"
    [expanded]="showCTAs"
    (createSnapshotClicked)="createSnapshotCTA(d.account)"
    [enableSnapshotCTA]="(snapshotCreated$ | async) === false"
    (activateProductClicked)="goToProductActivate(d.account.account_group_id)"
    (addUserClicked)="addUserToAccountCTA(d.users)"
    (createAnotherAccountClicked)="goToBusinessSearch()"
    (viewSnapshotClicked)="viewSnapshotCTA()"
    [accountGroup]="accountGroup$ | async"
  ></app-call-to-actions>
  <div class="account-content">
    <mat-card appearance="outlined" class="location-info-box">
      <mat-card-header>
        <mat-card-title>
          {{ 'MANAGE_ACCOUNTS.BUSINESS_DETAILS' | translate }}
        </mat-card-title>
        <a class="view-data-button" [routerLink]="['../edit']" mat-button color="primary">
          {{ 'MANAGE_ACCOUNTS.EDIT_ACCOUNT_DETAILS' | translate }}
        </a>
      </mat-card-header>
      <mat-card-content>
        <app-account-details-box-content
          [account]="d.account"
          [accountGroup]="accountGroup$ | async"
          [salesperson]="d.salesperson"
          [market]="market"
          [marketsAccess]="hasAccessToMarkets$ | async"
          [listsAccess]="hasAccessToLists$ | async"
          [shouldShowSalesperson]="hasAccessToSsc$ | async"
          [partnerId]="partnerId"
        ></app-account-details-box-content>
      </mat-card-content>
    </mat-card>

    <div class="row row-gutters">
      <div class="col col-xs-12 col-sm-5 col-info">
        <div>
          <app-vbox [titleText]="'MANAGE_ACCOUNTS.REPORTS' | translate">
            <div content>
              <mat-list class="snapshot-report-box">
                <mat-list-item>
                  <div matListItemLine class="snapshot-title">
                    <div class="snapshot-title-box">
                      <app-create-snapshot
                        [accountGroup]="accountGroup$ | async"
                        [originDetails]="'account-details'"
                        [snapshotIsReady]="(snapshotBecomingReady$ | async) === false"
                      ></app-create-snapshot>
                      <p *ngIf="snapshotBecomingReady$ | async" class="snapshot-ready-at-msg">
                        ({{ 'MANAGE_ACCOUNTS.SNAPSHOT_READY_ON' | translate }}
                        {{ snapshotIsReadyAt$ | async | date: 'medium' }})
                      </p>
                    </div>
                    <ng-container *ngIf="snapshotsRemaining$ | async as ssr">
                      <div class="snapshot-limit">
                        {{ 'SUBSCRIPTIONS.FREE_REMAINING' | translate: { freeLeft: ssr } }}
                      </div>
                    </ng-container>
                  </div>
                  <div matListItemMeta class="refresh" *ngIf="snapshotCreated$ | async">
                    <ng-container *ngIf="(snapshotIsExpired$ | async) === true">
                      <button
                        mat-icon-button
                        (click)="refreshButtonClicked()"
                        [matTooltip]="snapshotRefreshTooltip$ | async"
                        [disabled]="disableRefreshButton$ | async"
                      >
                        <mat-icon [color]="(disableRefreshButton$ | async) === true ? greyIcon : primaryIcon">
                          refresh
                        </mat-icon>
                      </button>
                    </ng-container>
                  </div>
                </mat-list-item>
                <mat-list-item *ngIf="displaySnapshotListingScan$ | async">
                  <div
                    matListItemLine
                    (click)="openUrlInNewTab(d.account.snapshot_listing_scan_url)"
                    class="snapshot-title"
                  >
                    <mat-icon svgIcon="listing-scan" class="listing-scan-icon"></mat-icon>
                    <a>
                      {{ 'MANAGE_ACCOUNTS.VIEW_SNAPSHOT_LISTING_SCAN' | translate }}
                    </a>
                  </div>
                </mat-list-item>
                <mat-list-item *ngIf="execReportUrl$ | async as execReportUrl">
                  <div matListItemLine (click)="openUrlInNewTab(execReportUrl.url)" class="exec-report">
                    <mat-icon>timeline</mat-icon>
                    <a>
                      {{ 'MANAGE_ACCOUNTS.VIEW_EXECUTIVE_REPORT' | translate }}
                    </a>
                  </div>
                  <a matListItemMeta (click)="openUrlInNewTab(execReportUrl.url + '?showReviewGradesCard=true')">
                    {{ 'MANAGE_ACCOUNTS.ADMIN_VIEW' | translate }}
                  </a>
                </mat-list-item>
                <mat-list-item *ngIf="execReportUrl$ | async as execReportUrl">
                  <div matListItemLine (click)="openReportSenderDialog()" class="exec-report">
                    <mat-icon>forward_to_inbox</mat-icon>
                    <a>
                      {{ 'MANAGE_ACCOUNTS.RESEND_EXECUTIVE_REPORT' | translate }}
                    </a>
                  </div>
                </mat-list-item>
              </mat-list>
            </div>
          </app-vbox>
        </div>
        <div>
          <inbox-pane-preview
            *ngIf="showInboxPaneCard$ | async"
            [partnerId]="partnerId"
            [accountGroupId]="d.account?.account_group_id"
          ></inbox-pane-preview>
        </div>
        <div>
          <app-vbox [titleText]="'MANAGE_ACCOUNTS.USERS' | translate">
            <a
              mat-button
              color="primary"
              bar-item-right
              id="add-users"
              (click)="presentAddUsersDialog(d.users)"
              *ngIf="d.account?.account_group_id"
            >
              {{ 'MANAGE_ACCOUNTS.ADD_USERS' | translate }}
            </a>
            <ng-container content>
              <app-users-box-content
                *ngIf="d.users?.length > 0; else noUsers"
                [users]="(loadingUsers$ | async) ? null : d.users"
                [userActionOptions]="userActions$ | async"
                [hasAccessToCampaign]="hasAccessToCampaign$ | async"
                (userActionSelected)="performActionOnUser($event.action, $event.user)"
              ></app-users-box-content>
              <ng-template #noUsers>
                <glxy-empty-state #noUsers [size]="'small'">
                  <p>{{ 'MANAGE_ACCOUNTS.NONE_FOUND' | translate }}</p>
                </glxy-empty-state>
              </ng-template>
            </ng-container>
          </app-vbox>
        </div>
        <div>
          <campaign-card
            [partnerId]="partnerId"
            [marketId]="d.account?.market_id"
            [businessId]="d.account?.account_group_id"
            [campaignDetailsUrlPrefix]="'/marketing/campaign/details/'"
          ></campaign-card>
        </div>
        <div>
          <app-vbox [titleText]="'MANAGE_ACCOUNTS.ADMIN_NOTES' | translate">
            <ng-container content>
              <glxy-empty-state *ngIf="!d.account?.admin_notes" [size]="'small'">
                <p>{{ 'MANAGE_ACCOUNTS.NONE_FOUND' | translate }}</p>
              </glxy-empty-state>
              <div class="preserve-linebreaks" [innerHTML]="d.account?.admin_notes | linky"></div>
            </ng-container>
          </app-vbox>
        </div>
        <div>
          <app-vbox [titleText]="'MANAGE_ACCOUNTS.BRANDS' | translate">
            <ng-container content *ngIf="brands$ | glxyAsyncStatus | async as obs">
              <ng-container [ngSwitch]="obs.status">
                <mat-spinner *ngSwitchCase="'loading'" [diameter]="32"></mat-spinner>
                <mat-nav-list *ngSwitchCase="'loaded'">
                  <mat-list-item *ngFor="let brand of obs.value" [routerLink]="['/brands/details', brand.name]">
                    {{ brand.name }}
                  </mat-list-item>
                </mat-nav-list>
                <glxy-empty-state *ngSwitchCase="'empty'" [size]="'small'">
                  <p>{{ 'MANAGE_ACCOUNTS.NONE_FOUND' | translate }}</p>
                </glxy-empty-state>
                <div *ngSwitchCase="'error'">
                  {{ 'MANAGE_ACCOUNTS.AN_ERROR_OCCURRED' | translate }}
                </div>
              </ng-container>
            </ng-container>
          </app-vbox>
        </div>
        <div>
          <app-vbox [titleText]="'AUTOMATIONS.COMMON.AUTOMATIONS' | translate">
            <div bar-item-right>
              <a mat-button color="primary" (click)="startManualAutomation(d.account)">
                {{ 'AUTOMATIONS.COMMON.START_MANUAL_AUTOMATION' | translate | titlecase }}
              </a>
            </div>
            <div content>
              <ng-container *ngIf="!!d.account.partner_id">
                <automata-latest-automations
                  [partnerId]="d.account.partner_id"
                  [marketId]="d.account.market_id"
                  [businessId]="d.account.account_group_id"
                  [provideLink]="true"
                ></automata-latest-automations>
              </ng-container>
            </div>
          </app-vbox>
        </div>
        <div>
          <app-vbox [titleText]="'MANAGE_ACCOUNTS.FILES' | translate">
            <a
              mat-button
              color="primary"
              bar-item-right
              id="add-file"
              (click)="presentAddFile()"
              *ngIf="d.account?.account_group_id"
            >
              {{ 'MANAGE_ACCOUNTS.ADD_FILE' | translate }}
            </a>
            <ng-container content>
              <glxy-empty-state *ngIf="!d.fileGroups?.length" [size]="'small'">
                <p>{{ 'MANAGE_ACCOUNTS.NONE_FOUND' | translate }}</p>
              </glxy-empty-state>
              <app-files-box-content
                *ngIf="d.fileGroups?.length"
                [fileGroups]="d.fileGroups"
                [cursor]="cursor"
                [fileActionOptions]="['Edit', 'Remove File']"
                (loadMore)="loadMoreFiles()"
                (fileActionSelected)="performActionOnFile($event.action, $event.file)"
              ></app-files-box-content>
            </ng-container>
          </app-vbox>
        </div>
      </div>

      <div class="col col-xs-12 col-sm-7 col-products">
        <div *ngIf="details$ | async as details">
          <app-product-box
            [accountGroupId]="details?.account?.account_group_id"
            [accountMarketId]="details?.account?.market_id"
          ></app-product-box>
        </div>

        <div *ngIf="canAccessOrders$ | async">
          <div class="wrapper" *ngIf="accountGroup$ | async as accountGroup">
            <sales-ui-sales-order-short-list
              ordersPageForBusinessUrl="/order-management"
              [ordersPageForBusinessQuery]="buildOrdersPageForBusinessQuery(accountGroup?.accountGroupId)"
              [getOrderDetailsUrl]="orderUrlCallback"
              [getFulfillmentFormUrl]="fulfillmentFormUrlCallback"
              [partnerId]="partnerId"
              [businessId]="accountGroup?.accountGroupId"
              [showCreateButton]="noAdminOrderCreation$ | async"
              (createClicked)="createOrder()"
            ></sales-ui-sales-order-short-list>
          </div>
        </div>

        <div>
          <project-tracker-list-card
            [partnerId]="partnerId"
            [accountGroupId]="accountGroupId"
          ></project-tracker-list-card>
        </div>

        <div
          class="stencil-shimmer history"
          *ngIf="(spendChangeRequestEvents$$.asObservable() | async) === null; else history"
        ></div>

        <ng-template #history>
          <div class="history-container">
            <mat-card
              appearance="outlined"
              class="activation-and-spend-history-card"
              *ngIf="(spendChangeRequestEvents$$.asObservable() | async)?.length > 0; else activationHistoryOnlyLayout"
            >
              <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start">
                <mat-tab label="{{ 'MANAGE_ACCOUNTS.ACTIVATION_HISTORY.ACTIVATION_HISTORY' | translate }}">
                  <div class="activation-history-container">
                    <app-activation-history [accountGroupID]="d.account.account_group_id"></app-activation-history>
                  </div>
                </mat-tab>

                <mat-tab label="{{ 'MANAGE_ACCOUNTS.SPEND_HISTORY' | translate }}">
                  <app-spend-change-history
                    [requests]="spendChangeRequestEvents$$.asObservable()"
                    [spendChangeHistoryHasMoreRecords]="spendChangeHistoryHasMoreRecords"
                    [spendChangeRequestHistoryIsLoading]="spendChangeRequestHistoryIsLoading"
                    (loadMoreClicked)="onLoadMoreClicked()"
                  ></app-spend-change-history>
                </mat-tab>
              </mat-tab-group>
            </mat-card>
          </div>
        </ng-template>

        <ng-template #activationHistoryOnlyLayout>
          <mat-card appearance="outlined">
            <mat-card-header>
              <mat-card-title>
                {{ 'MANAGE_ACCOUNTS.ACTIVATION_HISTORY.ACTIVATION_HISTORY' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <app-activation-history [accountGroupID]="d.account.account_group_id"></app-activation-history>
            </mat-card-content>
          </mat-card>
        </ng-template>
      </div>
    </div>
  </div>
</ng-container>
