```angular2html
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
```

## Simple Example

`<div>{{ phoneNumber | glxyPhoneNumber }}</div>`

## Full Example

```typescript
// snippet from your-component.component.ts

const phoneNumber = '3069009374';
const showCountryCode = true;
const country = 'US'; // Optional: e.g. 'US', 'CA', 'GB', 'AU', etc.
```

```angular2html
<!-- snippet from your-component.component.html -->

<div>{{ phoneNumber | glxyPhoneNumber:showCountryCode:country }}</div>
```

## Properties

| Name                          | Type    | Default | Description                                                                                      |
|-------------------------------|---------|---------|--------------------------------------------------------------------------------------------------|
| **Input**<br> phoneNumber     | string  |         | The phone number to format. <br /> <br /> <b>ex)</b> 3069009374, **************, ******-900-9374 |
| **Input**<br> showCountryCode | boolean | `true`  | If true, the country code will be shown in the formatted phone number.                           |
| **Input**<br> country         | string  | `'US'`  | The country to use when parsing numbers that don't already contain the country code.       |

