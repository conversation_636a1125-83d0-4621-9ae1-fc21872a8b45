import { Injectable } from '@angular/core';
import {
  Alignment,
  AlignmentPeriod,
  AlignmentPeriodCalendar,
  AnalyticsApiService,
  BusinessResourceId,
  CompositeFilter,
  CompositeFilterOperator,
  DateRange,
  FieldFilter,
  FieldFilterOperator,
  FieldFilterOperatorFunction,
  Filter,
  GroupBy,
  GroupByDimension,
  GroupByExcludeCombination,
  GroupByOperator,
  GroupResourceId,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  Order,
  PropertyType,
  QueryMetricsRequest,
  QueryMetricsResponse,
  ResourceId,
  TypedValue,
  UnaryFilter,
  UnaryFilterOperator,
} from '@vendasta/multi-location-analytics';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import moment from 'moment/moment';
import { Observable, combineLatest, of } from 'rxjs';
import { debounceTime, filter, map, shareReplay, startWith, switchMap } from 'rxjs/operators';
import { partnerId } from '../../globals';
import { BrandFilterContainerService } from '../brands/brand-filter-container/brand-filter-container.service';
import { BrandsService } from '../brands/brands.service';
import { LocationsService, isAccountGroup, isBrand } from '../locations';
import { ReputationService } from '../reputation';
import { FilterService } from '../shared/filter-service.service';
import {
  alignUTCDateToLocal,
  generateDates,
  getAlignmentPeriodForRange,
  getGranularityForRange,
  previousPeriodMoment,
} from '../shared/time-range-shared';
import { CUSTOM_TIME_RANGE_KEYS, TimeRangeService } from '../shared/time-range.service';
import { QueryMetricsResponseDelta } from './helpers';
dayjs.extend(utc);

export class StatByTime<T> {
  public localAlignedTime: Date;

  constructor(
    public time: Date,
    public stat: T,
  ) {
    this.localAlignedTime = alignUTCDateToLocal(time);
  }
}

export class BrandContext {
  public readonly sourceIdsSet: Set<string>;

  constructor(
    public resourceIds: ResourceId[],
    public startDate: Date,
    public endDate: Date,
    public timeRange: string,
    public filters: Filter[],
    // The brand's self-declared relevant sources, masking the data potentially present for irrelevant sources
    public activeSources: number[] = [],
    // The sources selected to filter to -- overrides the default of activeSources
    public selectedSourceIds: string[] = [],
    public groupBy: string[],
  ) {
    if (this.selectedSourceIds || this.activeSources) {
      const sourceIds =
        this.selectedSourceIds && this.selectedSourceIds.length
          ? this.selectedSourceIds
          : this.activeSources.map((num) => num + '');
      this.sourceIdsSet = new Set(sourceIds);
    }
  }

  public previousPeriod(): [Date, Date] {
    const prevRange = previousPeriodMoment([dayjs(this.startDate), dayjs(this.endDate)]);
    return [prevRange[0].toDate(), prevRange[1].toDate()];
  }

  public dateRange(forCurrentPeriod: boolean): [Date, Date] {
    if (forCurrentPeriod) {
      return [this.startDate, this.endDate];
    }
    return this.previousPeriod();
  }

  // GMB data is censored on API, this accounts for it
  public GMBDateRange(forCurrentPeriod: boolean): [Date, Date] {
    let [startDate, endDate] = this.dateRange(forCurrentPeriod);
    startDate = dayjs.utc(startDate).toDate();
    if (endDate >= dayjs.utc(dayjs().format('YYYY-MM-DD')).toDate()) {
      endDate = dayjs.utc(endDate).subtract(5, 'days').toDate();
    }
    return [startDate, endDate];
  }

  // Bing data is delayed similar to GMB, this accounts for it
  public BingDateRange(forCurrentPeriod: boolean): [Date, Date] {
    let [startDate, endDate] = this.dateRange(forCurrentPeriod);
    startDate = dayjs.utc(startDate).toDate();
    if (endDate >= dayjs.utc(dayjs().format('YYYY-MM-DD')).toDate()) {
      endDate = dayjs.utc(endDate).subtract(5, 'days').toDate();
    }
    return [startDate, endDate];
  }

  // Review data is typically delayed, this accounts for it
  public ReviewDateRange(forCurrentPeriod: boolean, onlyOffsetStartDate?: boolean): [Date, Date] {
    // don't offset when timeRange is custom, month, quarter
    if (CUSTOM_TIME_RANGE_KEYS.includes(this.timeRange)) {
      return this.dateRange(forCurrentPeriod);
    }
    // TODO: only offset if end is today, and handle displaying that conditionally.
    // TODO: this is currently implicit due to non-custom options always being `Last X`
    let [startDate, endDate] = this.dateRange(forCurrentPeriod);
    startDate = dayjs.utc(startDate).subtract(1, 'days').toDate();
    if (!onlyOffsetStartDate) {
      endDate = dayjs.utc(endDate).subtract(1, 'days').toDate();
    }
    return [startDate, endDate];
  }

  // AdIntel data is delayed, this accounts for it
  public adIntelDateRange(forCurrentPeriod: boolean): [Date, Date] {
    if (this.timeRange === 'custom') {
      let [startDate, endDate] = this.dateRange(forCurrentPeriod);
      startDate = dayjs.utc(startDate).subtract(0, 'days').toDate();
      endDate = dayjs.utc(endDate).subtract(0, 'days').toDate();
      return [startDate, endDate];
    }
    let [startDate, endDate] = this.dateRange(forCurrentPeriod);
    startDate = dayjs.utc(startDate).subtract(0, 'days').toDate();
    endDate = dayjs.utc(endDate).subtract(0, 'days').toDate();
    return [startDate, endDate];
  }

  public alignmentPeriod(): AlignmentPeriodCalendar {
    return getAlignmentPeriodForRange(this.startDate, this.endDate);
  }

  public buildFilter(filters?: Filter[]): Filter {
    if ((!this.filters || this.filters.length === 0) && (!filters || filters.length === 0)) {
      return null;
    }

    if (!filters) {
      filters = [];
    }
    if (this.filters && this.filters.length > 0) {
      filters = filters.concat(this.filters);
    }

    return new Filter({
      compositeFilter: {
        op: CompositeFilterOperator.AND,
        filters: filters,
      },
    });
  }

  public buildActiveSourcesFilter(field = 'source_id'): Filter {
    return new Filter({
      fieldFilter: {
        dimension: field,
        operator: FieldFilterOperator.EQUAL,
        operatorFunction: FieldFilterOperatorFunction.ANY,
        value: {
          value: Array.from(this.sourceIdsSet),
          valueType: PropertyType.PROPERTY_TYPE_INT64,
        },
      },
    });
  }

  public buildActualActiveSourcesFilter(field = 'source_id'): Filter {
    return new Filter({
      fieldFilter: {
        dimension: field,
        operator: FieldFilterOperator.EQUAL,
        operatorFunction: FieldFilterOperatorFunction.ANY,
        value: {
          value: this.activeSources.map((n) => n + ''),
          valueType: PropertyType.PROPERTY_TYPE_INT64,
        },
      },
    });
  }
}

function boolValue(value: boolean): TypedValue {
  return new TypedValue({
    value: value,
    valueType: PropertyType.PROPERTY_TYPE_BOOL,
  });
}

function stringValue(value: string): TypedValue {
  return new TypedValue({
    value: value,
    valueType: PropertyType.PROPERTY_TYPE_STRING,
  });
}

export const isAccurateFilter = new Filter({
  fieldFilter: new FieldFilter({
    dimension: 'match_quality',
    operator: FieldFilterOperator.EQUAL,
    value: new TypedValue({
      value: 'good',
      valueType: PropertyType.PROPERTY_TYPE_STRING,
    }),
  }),
});
export const isInaccurateFilter = new Filter({
  fieldFilter: {
    dimension: 'match_quality',
    operator: FieldFilterOperator.NOT_EQUAL,
    value: {
      value: 'good',
      valueType: PropertyType.PROPERTY_TYPE_STRING,
    },
  },
});
export const isVerifiedFilter = new Filter({
  fieldFilter: new FieldFilter({
    dimension: 'is_verified',
    operator: FieldFilterOperator.EQUAL,
    value: boolValue(true),
  }),
});
export const isNotVerifiedFilter = new Filter({
  fieldFilter: new FieldFilter({
    dimension: 'is_verified',
    operator: FieldFilterOperator.EQUAL,
    value: boolValue(false),
  }),
});
export const isMissingFilter = new Filter({
  unaryFilter: new UnaryFilter({
    dimension: 'listing_id',
    op: UnaryFilterOperator.IS_NULL,
  }),
});
export const isPotentialMissingFilter = new Filter({
  fieldFilter: new FieldFilter({
    dimension: 'is_missing',
    operator: FieldFilterOperator.EQUAL,
    value: boolValue(true),
  }),
});

type RequestBuilder = (brandsContext: BrandContext, curPeriod: boolean, groupBy: string[]) => QueryMetricsRequest;

// Builds measures of error counts by field, error counts by accuracy level, and count of missing
function buildListingCountMeasures(): Measure[] {
  // Count wrong fields
  const fields = [
    'match_detail_name',
    'match_detail_street_address',
    'match_detail_locality',
    'match_detail_region',
    'match_detail_postal_code',
    'match_detail_phone',
    'match_detail_website',
  ];
  let measures = fields.map((field) => {
    const fieldErrorFilter = new Filter({
      fieldFilter: new FieldFilter({
        dimension: field,
        operator: FieldFilterOperator.EQUAL,
        value: stringValue('not match'),
      }),
    });
    return new Measure({
      aggregate: new MeasureAggregate({
        measure: '*',
        aggOp: MeasureAggregateOperator.COUNT,
        filter: new Filter({
          compositeFilter: {
            op: CompositeFilterOperator.AND,
            filters: [fieldErrorFilter, isNotVerifiedFilter, isInaccurateFilter],
          },
        }),
        alias: field,
      }),
    });
  });

  measures = measures.concat([
    new Measure({
      aggregate: new MeasureAggregate({
        measure: '*',
        aggOp: MeasureAggregateOperator.COUNT,
        alias: 'num_accurate',
        filter: {
          compositeFilter: {
            filters: [isAccurateFilter, isVerifiedFilter],
            op: CompositeFilterOperator.OR,
          },
        },
      }),
    }),
    new Measure({
      aggregate: new MeasureAggregate({
        measure: '*',
        aggOp: MeasureAggregateOperator.COUNT,
        alias: 'num_inaccurate',
        filter: new Filter({
          compositeFilter: {
            filters: [isInaccurateFilter, isNotVerifiedFilter],
            op: CompositeFilterOperator.AND,
          },
        }),
      }),
    }),
    new Measure({
      aggregate: new MeasureAggregate({
        measure: '*',
        aggOp: MeasureAggregateOperator.COUNT,
        alias: 'num_missing',
        filter: new Filter({
          unaryFilter: new UnaryFilter({
            dimension: 'listing_id',
            op: UnaryFilterOperator.IS_NULL,
          }),
        }),
      }),
    }),
    new Measure({
      aggregate: new MeasureAggregate({
        measure: '*',
        aggOp: MeasureAggregateOperator.COUNT,
        filter: isPotentialMissingFilter,
        alias: 'num_potential_missing',
      }),
    }),
    new Measure({
      aggregate: new MeasureAggregate({
        measure: '*',
        aggOp: MeasureAggregateOperator.COUNT,
        alias: 'num_ignoring_errors',
        filter: isVerifiedFilter,
      }),
    }),
  ]);
  return measures;
}

@Injectable({ providedIn: 'root' })
export class QueryService {
  // BrandContext which is watched with minimal scoping, to trigger reactive querying of the following
  public brandsContext$: Observable<BrandContext>;

  // TODO: The following react to changes after being subscribed to, regardless of current subscribers, due to shareReplay.

  // GMB: insights and the presence of connections
  readonly insights$s: Map<string, QueryMetricsResponseDelta>;
  readonly insightsTotals$: Map<string, QueryMetricsResponseDelta>;
  readonly searchKeywords: QueryMetricsResponseDelta;
  readonly gmbConnections$: Observable<QueryMetricsResponse>;

  readonly bingViewInsights$s: Map<string, QueryMetricsResponseDelta>;

  readonly bingActionsInsights$s: Map<string, QueryMetricsResponseDelta>;

  readonly bingConnections$: Observable<QueryMetricsResponse>;

  // AdIntel: view, open, click counts
  readonly adIntelStats$: QueryMetricsResponseDelta;

  // Reviews: Comparison counts rolled up by time for location and source. Average rating derived.
  readonly reviews$s: Map<string, QueryMetricsResponseDelta>;
  // ResponseTimes: Review response times
  readonly responseTimes$s: Map<string, QueryMetricsResponseDelta>;
  // Counts by status, source, and rating, for presenting manage scoping
  readonly reviewCounts$: Observable<QueryMetricsResponse>;

  // Listings: Listing Scores Data by time for location and source. Listing Score % derived.
  readonly listingScore$: QueryMetricsResponseDelta;
  // The latest, for presenting now vs then delta
  readonly latestListingScore$: QueryMetricsResponseDelta;
  // Counts by time and source, and by time, source, and quality. Used for presence over time and score over time.
  readonly listingQuality$: QueryMetricsResponseDelta;
  // Count of accurate listing fields by field on current listings
  readonly listingMatchQuality$: Observable<QueryMetricsResponse>;
  readonly listingMatchQualityTotals$: Observable<QueryMetricsResponse>;
  // Counts by source, for filters, for presenting manage scoping
  readonly listingsBySource$: Observable<QueryMetricsResponse>;
  // Counts by source, presence/acccuracy ad field match, for filters, for presenting manage scoping
  readonly listingCounts$: Observable<QueryMetricsResponse>;
  // Counts by account group of listing accuracy
  readonly listingAccuracyCountsByLocation$: QueryMetricsResponseDelta;
  // Counts by  source of listing accuracy
  readonly listingAccuracyCountsBySource$: QueryMetricsResponseDelta;

  readonly listingAccuracyTotals$: QueryMetricsResponseDelta;

  // Account Group: The ids in scope of filters, to be inflated and visualized.
  readonly accountGroup$: Observable<QueryMetricsResponse>;
  // Counts by Geo, for geo filter options
  readonly accountGroupGeography$: Observable<QueryMetricsResponse>;
  // Counts by Taxonomies (sets, not unnested), for taxonomy filter options
  readonly accountGroupTaxonomies$: Observable<QueryMetricsResponse>;

  // Social Connections: Service IDs, auth and token statuses. Filtered to authed and good token statuses.
  readonly socialConnections$: Observable<QueryMetricsResponse>;
  readonly facebookConnections$: Observable<QueryMetricsResponse>;

  readonly reviewRequests: QueryMetricsResponseDelta;
  readonly repReviewRequest$: QueryMetricsResponseDelta;

  readonly resourceIds$ = this.locationsService.currentLocation$.pipe(
    switchMap((location) => {
      if (isBrand(location)) {
        return this.brandsService.path$.pipe(
          map((path) => {
            return [
              new ResourceId({
                groupId: new GroupResourceId({
                  groupPathNodes: path,
                }),
              }),
            ];
          }),
        );
      }
      if (isAccountGroup(location)) {
        return of([
          new ResourceId({
            businessId: new BusinessResourceId({
              businessId: location.accountGroupId,
            }),
          }),
        ]);
      }
      return of([]);
    }),
  );

  constructor(
    private readonly analyticsService: AnalyticsApiService,
    private readonly timeRangeService: TimeRangeService,
    private readonly filterService: FilterService,
    private readonly brandFiltersService: BrandFilterContainerService,
    private readonly reputationService: ReputationService,
    private readonly locationsService: LocationsService,
    private readonly brandsService: BrandsService,
  ) {
    this.brandsContext$ = combineLatest([
      this.resourceIds$,
      this.timeRangeService.utcAlignedDateRange$.pipe(filter(([startDate, endDate]) => !!startDate && !!endDate)),
      this.filterService.filters$,
      this.reputationService.brandSources$,
      this.brandFiltersService.selectedSourceIds$,
      this.timeRangeService.selectedTimeRangeKey$,
    ]).pipe(
      debounceTime(10),
      map(([resourceIds, [startDate, endDate], filters, activatedSources, selectedSourceIds, timeRange]) => {
        return new BrandContext(
          resourceIds,
          startDate,
          endDate,
          timeRange,
          filters,
          activatedSources,
          selectedSourceIds,
          ['source_id'],
        );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.insights$s = new Map<string, QueryMetricsResponseDelta>([
      [
        'account_group_id',
        this.fetchMetric(
          this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'timeRange', 'filters']),
          this.buildGMBInsightsRequest,
          ['account_group_id'],
        ),
      ],
    ]);

    this.bingViewInsights$s = new Map<string, QueryMetricsResponseDelta>([
      [
        'account_group_id',
        this.fetchMetric(
          this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'timeRange', 'filters']),
          this.buildBingInsightsViewRequest,
          ['account_group_id'],
        ),
      ],
    ]);

    this.bingActionsInsights$s = new Map<string, QueryMetricsResponseDelta>([
      [
        'account_group_id',
        this.fetchMetric(
          this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'timeRange', 'filters']),
          this.buildBingInsightsActionsRequest,
          ['account_group_id'],
        ),
      ],
    ]);

    // Bing connections
    this.bingConnections$ = this.buildBrandContext$(['partnerId', 'path']).pipe(
      switchMap((brandsContext: BrandContext) => {
        return this.analyticsService
          .queryMetrics(this.buildBingLocationsRequest(brandsContext))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.insightsTotals$ = new Map<string, QueryMetricsResponseDelta>([
      [
        'account_group_id',
        this.fetchMetric(
          this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'timeRange', 'filters']),
          this.buildGMBInsightsTotalsRequest,
          ['account_group_id'],
        ),
      ],
    ]);

    this.searchKeywords = this.fetchMetric(
      this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'timeRange', 'filters']),
      this.buildGMBSearchKeywordsRequest,
      ['account_group_id'],
    );

    const reviewContext$ = this.buildBrandContext$([
      'partnerId',
      'path',
      'dateRange',
      'timeRange',
      'activeSources',
      'selectedSourceIds',
      'filters',
    ]);
    this.reviews$s = new Map<string, QueryMetricsResponseDelta>([
      ['account_group_id', this.fetchMetric(reviewContext$, this.buildReviewsRequest, ['account_group_id'])],
      [
        'account_group_id|allTime',
        this.fetchMetric(reviewContext$, this.buildAlltimeReviewsRequest, ['account_group_id']),
      ],
      ['source_id', this.fetchMetric(reviewContext$, this.buildReviewsRequest, ['source_id'])],
      [
        'account_group_id|source_id',
        this.fetchMetric(reviewContext$, this.buildReviewsRequest, ['account_group_id', 'source_id']),
      ],
      [
        // 50 is an arbitrary limit, due to doing some processing and filtering on the front end the number of
        // rows received is larger then the rows displayed, so by setting the limit to 50 the queries tended to
        // be fast enough for the preview, while being large enough that the preview usually has a sufficient
        // quantity of data to look good.
        'account_group_id|source_id|preview',
        this.fetchMetric(reviewContext$, this.buildReviewsRequest, ['account_group_id', 'source_id'], 50),
      ],
    ]);

    this.responseTimes$s = new Map<string, QueryMetricsResponseDelta>([
      ['account_group_id', this.fetchMetric(reviewContext$, this.buildResponseTimeRequest, ['account_group_id'])],
      [
        'account_group_id|allTime',
        this.fetchMetric(reviewContext$, this.buildAllTimeResponseTimeRequest, ['account_group_id']),
      ],
      ['source_id', this.fetchMetric(reviewContext$, this.buildResponseTimeRequest, ['source_id'])],
    ]);

    // Does not respond to *source* filters -- provided to give context to filter options which include sources
    this.reviewCounts$ = this.buildBrandContext$([
      'partnerId',
      'path',
      'dateRange',
      'timeRange',
      'activeSources',
      'filters',
    ]).pipe(
      switchMap((brandsContext: BrandContext) => {
        // Only offset the start so that we match
        const [startDate, endDate] = brandsContext.ReviewDateRange(true, true);
        return this.analyticsService.queryMetrics(
          new QueryMetricsRequest({
            partnerId,
            metricName: 'reviews',
            resourceIds: brandsContext.resourceIds,
            timeRange: inclusiveExclusive(startDate, endDate),
            measures: [
              new Measure({
                aggregate: new MeasureAggregate({
                  measure: '*',
                  aggOp: MeasureAggregateOperator.COUNT,
                }),
              }),
            ],
            filter: brandsContext.buildFilter([
              brandsContext.buildActualActiveSourcesFilter(),
              noneDeletedReviewsFilter(),
            ]),
            groupBy: new GroupBy({
              dimension: [
                { dimension: 'action_status' },
                { dimension: 'source_id' },
                { dimension: 'rating' },
                { dimension: 'edited' },
                { dimension: 'deleted_status' },
              ],
              groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
            }),
          }),
        );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    // Does not respond to filters -- provided to give context to filter options
    this.accountGroupGeography$ = this.buildBrandContext$(['partnerId', 'path']).pipe(
      switchMap((brandsContext: BrandContext) => {
        return this.analyticsService
          .queryMetrics(this.buildAccountGroupGeographyRequest(brandsContext))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    // Does not respond to filters -- provided to give context to filter options
    this.accountGroupTaxonomies$ = this.buildBrandContext$(['partnerId', 'path']).pipe(
      switchMap((brandsContext: BrandContext) => {
        return this.analyticsService
          .queryMetrics(this.buildAccountGroupTaxonomyRequest(brandsContext))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.accountGroup$ = this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'filters']).pipe(
      switchMap((brandsContext: BrandContext) => {
        return this.analyticsService
          .queryMetrics(this.buildAccountGroupRequest(brandsContext))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.listingScore$ = this.fetchMetric(
      this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'timeRange', 'filters']),
      this.buildListingScoreRequest,
      [],
    );

    this.latestListingScore$ = this.fetchMetric(
      this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'filters']),
      this.buildLatestListingScoreRequest,
      [],
    );

    this.listingQuality$ = this.fetchMetric(
      this.buildBrandContext$([
        'partnerId',
        'path',
        'dateRange',
        'timeRange',
        'activeSources',
        'selectedSourceIds',
        'filters',
      ]),
      this.buildListingQualityRequest,
      [],
    );

    this.listingMatchQuality$ = this.buildBrandContext$([
      'partnerId',
      'path',
      'activeSources',
      'selectedSourceIds',
      'filters',
    ]).pipe(
      switchMap((brandsContext: BrandContext) => {
        return this.analyticsService
          .queryMetrics(
            this.buildListingMatchQualityRequest(
              brandsContext,
              new GroupBy({
                dimension: [
                  new GroupByDimension({
                    dimension: 'resource__account_group_id',
                  }),
                  new GroupByDimension({
                    dimension: 'join__source_id',
                  }),
                ],
              }),
            ),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.listingMatchQualityTotals$ = this.buildBrandContext$([
      'partnerId',
      'path',
      'activeSources',
      'selectedSourceIds',
      'filters',
    ]).pipe(
      switchMap((brandsContext: BrandContext) => {
        return this.analyticsService
          .queryMetrics(this.buildListingMatchQualityRequest(brandsContext, new GroupBy()))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    // Does not respond to *source* filters -- provided to give context to filter options which include sources
    this.listingCounts$ = this.fetchUnboundedMetric(
      this.buildBrandContext$(['partnerId', 'path', 'activeSources', 'filters']),
      this.buildListingCountRequest,
    );

    this.listingAccuracyCountsByLocation$ = this.fetchMetric(
      this.buildBrandContext$(['partnerId', 'path', 'activeSources', 'filters', 'selectedSourceIds', 'dateRange']),
      this.buildBusinessDataListingCountRequest,
      ['resource__account_group_id'],
    );

    this.listingAccuracyCountsBySource$ = this.fetchMetric(
      this.buildBrandContext$(['partnerId', 'path', 'activeSources', 'filters', 'selectedSourceIds', 'dateRange']),
      this.buildBusinessDataListingCountRequest,
      ['join__source_id'],
    );

    this.listingAccuracyTotals$ = this.fetchMetric(
      this.buildBrandContext$(['partnerId', 'path', 'activeSources', 'filters', 'selectedSourceIds', 'dateRange']),
      this.buildBusinessDataListingCountRequest,
      [],
    );

    this.adIntelStats$ = this.fetchMetric(
      this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'timeRange', 'filters']),
      this.buildAdIntelStatsRequest,
      [],
    );

    // Does not react to filters -- provided solely for review response UI
    this.gmbConnections$ = this.buildBrandContext$(['partnerId', 'path']).pipe(
      switchMap((brandsContext: BrandContext) => {
        return this.analyticsService
          .queryMetrics(this.buildGMBLocationsRequest(brandsContext))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    // Does not react to filters -- provided solely for connection UI
    this.socialConnections$ = this.buildBrandContext$(['partnerId', 'path', 'dateRange']).pipe(
      switchMap((brandsContext) => {
        return this.analyticsService
          .queryMetrics(this.buildSocialRequest(brandsContext, true, false))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.facebookConnections$ = this.buildBrandContext$(['partnerId', 'path', 'dateRange']).pipe(
      switchMap((brandsContext) => {
        return this.analyticsService
          .queryMetrics(this.buildSocialRequest(brandsContext, true, true))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const reviewsRequestContext$ = this.buildBrandContext$(['partnerId', 'path', 'dateRange', 'filters']);
    this.reviewRequests = this.fetchMetric(reviewsRequestContext$, this.buildReviewRequestsRequest, ['event_type']);
    this.repReviewRequest$ = this.fetchMetric(reviewsRequestContext$, this.buildRepReviewRequestsRequest, []);
  }

  fetchMetric(
    brandsContext$: Observable<BrandContext>,
    requestBuilder: RequestBuilder,
    groupBy: string[],
    limit?: number,
  ): QueryMetricsResponseDelta {
    const current = brandsContext$.pipe(
      switchMap((brandsContext: BrandContext) => {
        const req = requestBuilder(brandsContext, true, groupBy);
        req.limit = limit;
        return this.analyticsService.queryMetrics(req);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    const prev = brandsContext$.pipe(
      switchMap((brandsContext: BrandContext) => {
        const req = requestBuilder(brandsContext, false, groupBy);
        req.limit = limit;
        return this.analyticsService.queryMetrics(req);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    return new QueryMetricsResponseDelta(current, prev);
  }

  fetchUnboundedMetric(
    brandsContext$: Observable<BrandContext>,
    requestBuilder: RequestBuilder,
  ): Observable<QueryMetricsResponse> {
    return brandsContext$.pipe(
      switchMap((brandsContext: BrandContext) => {
        return this.analyticsService.queryMetrics(requestBuilder(brandsContext, true, []));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  buildReviewsRequest(brandsContext: BrandContext, forCurrentPeriod: boolean, groupBy: string[]): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.ReviewDateRange(forCurrentPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'reviews',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_one_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '1',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_two_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '2',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_three_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '3',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_four_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '4',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_five_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '5',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_none',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: 'none',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'responded',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              compositeFilter: {
                op: CompositeFilterOperator.OR,
                filters: [
                  new Filter({
                    fieldFilter: {
                      dimension: 'action_status',
                      operator: FieldFilterOperator.EQUAL,
                      value: {
                        value: 'owner-responded',
                      },
                    },
                  }),
                  new Filter({
                    fieldFilter: {
                      dimension: 'action_status',
                      operator: FieldFilterOperator.EQUAL,
                      value: {
                        value: 'digital-agent-responded',
                      },
                    },
                  }),
                ],
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'unresponded',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'action_status',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: 'action-required',
                },
              },
            }),
          }),
        }),
      ],
      filter: brandsContext.buildFilter([brandsContext.buildActiveSourcesFilter(), noneDeletedReviewsFilter()]),
      groupBy: new GroupBy({
        dimension: groupBy
          .map((dim) => {
            return { dimension: dim };
          })
          .concat([{ dimension: 'published' }]),
        groupByOperator: GroupByOperator.OPERATOR_CUBE,
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod: new AlignmentPeriod({
        // This data is not shown over time, so having less unnecessary buckets makes performance better, if
        // higher granularity is needed in the future this can be set to `brandsContext.alignmentPeriod()`
        calendar: AlignmentPeriodCalendar.CALENDAR_YEAR,
      }),
    });
  }

  buildAlltimeReviewsRequest(brandsContext: BrandContext, _: boolean, groupBy: string[]): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'reviews',
      resourceIds: brandsContext.resourceIds,
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_one_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '1',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_two_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '2',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_three_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '3',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_four_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '4',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_five_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '5',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_none',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: 'none',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'responded',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              compositeFilter: {
                op: CompositeFilterOperator.OR,
                filters: [
                  new Filter({
                    fieldFilter: {
                      dimension: 'action_status',
                      operator: FieldFilterOperator.EQUAL,
                      value: {
                        value: 'owner-responded',
                      },
                    },
                  }),
                  new Filter({
                    fieldFilter: {
                      dimension: 'action_status',
                      operator: FieldFilterOperator.EQUAL,
                      value: {
                        value: 'digital-agent-responded',
                      },
                    },
                  }),
                ],
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'unresponded',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'action_status',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: 'action-required',
                },
              },
            }),
          }),
        }),
      ],
      filter: brandsContext.buildFilter([brandsContext.buildActiveSourcesFilter(), noneDeletedReviewsFilter()]),
      groupBy: new GroupBy({
        dimension: groupBy
          .map((dim) => {
            return { dimension: dim };
          })
          .concat([{ dimension: 'published' }]),
        groupByOperator: GroupByOperator.OPERATOR_CUBE,
      }),
    });
  }

  buildResponseTimeRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[],
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.ReviewDateRange(forCurrentPeriod);
    const req = new QueryMetricsRequest({
      partnerId,
      metricName: 'review_response_time',
      resourceIds: brandsContext.resourceIds,
    });
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'response_time',
          aggOp: MeasureAggregateOperator.AVG,
          alias: 'response_time',
        }),
      }),
    ];
    req.groupBy = new GroupBy({
      dimension: groupBy.map((dim) => {
        return { dimension: dim };
      }),
    });
    req.timeRange = inclusiveExclusive(startDate, endDate);
    return req;
  }

  buildAllTimeResponseTimeRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[],
  ): QueryMetricsRequest {
    const req = new QueryMetricsRequest({
      partnerId,
      metricName: 'review_response_time',
      resourceIds: brandsContext.resourceIds,
    });
    req.measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'response_time',
          aggOp: MeasureAggregateOperator.AVG,
          alias: 'response_time',
        }),
      }),
    ];
    req.groupBy = new GroupBy({
      dimension: groupBy.map((dim) => {
        return { dimension: dim };
      }),
    });
    return req;
  }

  buildGMBInsightsRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[] = ['account_group_id'],
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.GMBDateRange(forCurrentPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'gmb_insights',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'views_maps',
            measure: 'views_maps',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'views_search',
            measure: 'views_search',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'actions_website',
            measure: 'actions_website',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'actions_phone',
            measure: 'actions_phone',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'actions_driving_directions',
            measure: 'actions_driving_directions',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'actions_bookings',
            measure: 'actions_bookings',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'actions_conversations',
            measure: 'actions_conversations',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'actions_food_orders',
            measure: 'actions_food_orders',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      filter: brandsContext.buildFilter(null),
      groupBy: new GroupBy({
        dimension: groupBy
          .map((dim) => {
            return { dimension: dim };
          })
          .concat([{ dimension: 'start_time' }]),
        groupByOperator: GroupByOperator.OPERATOR_CUBE,
        excludeCombination: [
          new GroupByExcludeCombination({
            dimensions: ['account_group_id', 'start_time'],
          }),
        ],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod: new AlignmentPeriod({
        calendar: brandsContext.alignmentPeriod(),
      }),
    });
  }

  buildGMBInsightsTotalsRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[] = ['account_group_id'],
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.GMBDateRange(forCurrentPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'gmb_insights',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveInclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'photos_count_customers',
            measure: 'photos_count_customers',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'photos_count_merchant',
            measure: 'photos_count_merchant',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
      ],
      filter: brandsContext.buildFilter(null),
      groupBy: new GroupBy({
        dimension: groupBy
          .map((dim) => {
            return { dimension: dim };
          })
          .concat([
            new GroupByDimension({
              limitDimension: {
                dimension: 'start_time',
                order: Order.ORDER_DESC,
                limit: 4,
              },
            }),
          ]),
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
    });
  }

  buildGMBSearchKeywordsRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[] = ['account_group_id'],
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(forCurrentPeriod);

    const start = moment(startDate).utc().date(1);

    return new QueryMetricsRequest({
      partnerId: partnerId,
      metricName: 'gmb_search_keywords',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveInclusive(start.toDate(), endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'value',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      filter: brandsContext.buildFilter(null),
      groupBy: new GroupBy({
        dimension: groupBy
          .map((dim) => {
            return { dimension: dim };
          })
          .concat([{ dimension: 'month_date' }]),
        groupByOperator: GroupByOperator.OPERATOR_CUBE,
        excludeCombination: [
          new GroupByExcludeCombination({
            dimensions: ['account_group_id', 'month_date'],
          }),
        ],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod: new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_MONTH,
      }),
    });
  }

  buildBingInsightsViewRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[] = ['account_group_id'],
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.BingDateRange(forCurrentPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'bing_insights_view',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'desktop_map_impression',
            measure: 'desktop_map_impression',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'desktop_serp_impression',
            measure: 'desktop_serp_impression',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'mobile_map_impression',
            measure: 'mobile_map_impression',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'mobile_serp_impression',
            measure: 'mobile_serp_impression',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      filter: brandsContext.buildFilter(null),
      groupBy: new GroupBy({
        dimension: groupBy
          .map((dim) => {
            return { dimension: dim };
          })
          .concat([{ dimension: 'start_date' }]),
        groupByOperator: GroupByOperator.OPERATOR_CUBE,
        excludeCombination: [
          new GroupByExcludeCombination({
            dimensions: ['account_group_id', 'start_time'],
          }),
        ],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod: new AlignmentPeriod({
        calendar: brandsContext.alignmentPeriod(),
      }),
    });
  }

  buildBingInsightsActionsRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[] = ['account_group_id'],
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.BingDateRange(forCurrentPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'bing_insights_interaction',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'website_clicks',
            measure: 'website_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'direction_clicks',
            measure: 'direction_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'photo_clicks',
            measure: 'photo_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'phone_clicks',
            measure: 'phone_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'located_at_clicks',
            measure: 'located_at_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'review_clicks',
            measure: 'review_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'menu_clicks',
            measure: 'menu_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'order_online_clicks',
            measure: 'order_online_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'suggest_an_edit_clicks',
            measure: 'suggest_an_edit_clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      filter: brandsContext.buildFilter(null),
      groupBy: new GroupBy({
        dimension: groupBy
          .map((dim) => {
            return { dimension: dim };
          })
          .concat([{ dimension: 'start_date' }]),
        groupByOperator: GroupByOperator.OPERATOR_CUBE,
        excludeCombination: [
          new GroupByExcludeCombination({
            dimensions: ['account_group_id', 'start_time'],
          }),
        ],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod: new AlignmentPeriod({
        calendar: brandsContext.alignmentPeriod(),
      }),
    });
  }

  private buildAccountGroupGeographyRequest(brandContext: BrandContext): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'account_group',
      resourceIds: brandContext.resourceIds,
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          { dimension: 'account_group__country' },
          { dimension: 'account_group__state' },
          { dimension: 'account_group__city' },
        ],
        groupByOperator: GroupByOperator.OPERATOR_ROLLUP,
      }),
    });
  }

  private buildAccountGroupTaxonomyRequest(brandContext: BrandContext): QueryMetricsRequest {
    // TODO: This could be bundled with the Geo query -- we want to count in multiple contexts'
    // TODO: We could accomplish this with a cube and exclude, but it would be good to get proper grouping sets support for it
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'account_group',
      resourceIds: brandContext.resourceIds,
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [{ dimension: 'account_group__tax_ids' }],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
    });
  }

  private buildAccountGroupRequest(brandsContext: BrandContext): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'account_group',
      resourceIds: brandsContext.resourceIds,
      measures: [
        new Measure({
          measure: 'account_group__account_group_id',
        }),
      ],
      filter: brandsContext.buildFilter(null),
    });
  }

  private buildListingScoreRequest(brandsContext: BrandContext, curPeriod: boolean): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(curPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'listing_score',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.AVG,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'timestamp',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      alignmentPeriod: new AlignmentPeriod({
        calendar: brandsContext.alignmentPeriod(),
      }),
      alignment: Alignment.ALIGN_EXACT,
      filter: brandsContext.buildFilter(null),
    });
  }

  private buildLatestListingScoreRequest(brandsContext: BrandContext, curPeriod: boolean): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(curPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'listing_score',
      resourceIds: brandsContext.resourceIds,
      timeRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'source_list',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'quality_list',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'timestamp',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'account_group_id',
          }),
          new GroupByDimension({
            limitDimension: {
              dimension: 'timestamp',
              order: Order.ORDER_DESC,
              limit: 1,
            },
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      filter: brandsContext.buildFilter(null),
    });
  }

  private buildListingQualityRequest(brandsContext: BrandContext, curPeriod: boolean): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(curPeriod);

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'listing_presence_over_time',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'timestamp',
          }),
          new GroupByDimension({
            dimension: 'source',
          }),
          new GroupByDimension({
            dimension: 'quality',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_CUBE,
        excludeCombination: [
          // we need timestamp, source for presence overtime
          // we need timestamp, source, quality for score overtime, could use for accuracy over time
          // right now we're filtering on the frontend so all will require source, and time rollups aren't relevant
          new GroupByExcludeCombination({ dimensions: [] }),
          new GroupByExcludeCombination({ dimensions: ['source'] }),
          new GroupByExcludeCombination({ dimensions: ['quality'] }),
          new GroupByExcludeCombination({ dimensions: ['timestamp'] }),
          new GroupByExcludeCombination({
            dimensions: ['timestamp', 'quality'],
          }),
          new GroupByExcludeCombination({ dimensions: ['source', 'quality'] }),
        ],
      }),
      alignmentPeriod: new AlignmentPeriod({
        calendar: brandsContext.alignmentPeriod(),
      }),
      alignment: Alignment.ALIGN_EXACT,
      // Filter doesn't seem to effect speed but will reduce response size in some cases.
      // Results are also filtered when parsing downstream
      filter: brandsContext.buildFilter([brandsContext.buildActiveSourcesFilter('source')]),
    });
  }

  private buildListingMatchQualityRequest(brandsContext: BrandContext, groupBy: GroupBy): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'business_data_listings',
      resourceIds: brandsContext.resourceIds,
      filter: new Filter({
        compositeFilter: {
          op: CompositeFilterOperator.AND,
          filters: [
            brandsContext.buildActiveSourcesFilter('join__source_id'),
            new Filter({
              fieldFilter: new FieldFilter({
                dimension: 'join__source_id',
                operator: FieldFilterOperator.NOT_EQUAL,
                value: {
                  value: '12000',
                  valueType: PropertyType.PROPERTY_TYPE_STRING,
                },
              }),
            }),
          ],
        },
      }),
      measures: buildListingCountMeasures(),
      groupBy: groupBy,
      timeRange: { end: new Date() },
    });
  }

  private buildListingCountRequest(brandsContext: BrandContext): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'business_data_listings',
      resourceIds: brandsContext.resourceIds,
      filter: new Filter({
        compositeFilter: {
          op: CompositeFilterOperator.AND,
          filters: [
            brandsContext.buildFilter([brandsContext.buildActiveSourcesFilter('join__source_id')]),
            new Filter({
              fieldFilter: new FieldFilter({
                dimension: 'join__source_id',
                operator: FieldFilterOperator.NOT_EQUAL,
                value: {
                  value: '12000',
                  valueType: PropertyType.PROPERTY_TYPE_STRING,
                },
              }),
            }),
          ],
        },
      }),
      measures: buildListingCountMeasures(),
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'join__source_id',
          }),
        ],
      }),
      timeRange: { end: new Date() },
    });
  }

  private buildBusinessDataListingCountRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[],
  ): QueryMetricsRequest {
    const [, endDate] = brandsContext.dateRange(forCurrentPeriod);

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'business_data_listings',
      resourceIds: brandsContext.resourceIds,
      timeRange: new DateRange({
        end: dayjs(endDate).toDate(),
      }),
      filter: brandsContext.buildFilter([
        brandsContext.buildActiveSourcesFilter('join__source_id'),
        new Filter({
          fieldFilter: new FieldFilter({
            dimension: 'join__source_id',
            operator: FieldFilterOperator.NOT_EQUAL,
            value: {
              value: '12000',
              valueType: PropertyType.PROPERTY_TYPE_STRING,
            },
          }),
        }),
      ]),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'accurate',
            filter: {
              compositeFilter: {
                filters: [isAccurateFilter, isVerifiedFilter],
                op: CompositeFilterOperator.OR,
              },
            },
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'inaccurate',
            filter: new Filter({
              compositeFilter: {
                filters: [isInaccurateFilter, isNotVerifiedFilter],
                op: CompositeFilterOperator.AND,
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'missing',
            filter: isMissingFilter,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: groupBy.map((dim) => {
          return { dimension: dim };
        }),
      }),
    });
  }

  buildGMBLocationsRequest(brandsContext: BrandContext): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'gmb_locations_connected',
      resourceIds: brandsContext.resourceIds,
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'account_group_id',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
    });
  }

  private buildAdIntelStatsRequest(brandsContext: BrandContext, curPeriod: boolean): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.adIntelDateRange(curPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'advertising_intel_channel_stats',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'impressions',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'clicks',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'conversions',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'spend_micros',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'account_group_id',
          }),
          new GroupByDimension({
            dimension: 'time_stamp',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_CUBE,
        excludeCombination: [
          new GroupByExcludeCombination({
            dimensions: ['account_group_id', 'time_stamp'],
          }),
        ],
      }),
      alignmentPeriod: new AlignmentPeriod({
        calendar: brandsContext.alignmentPeriod(),
      }),
      alignment: Alignment.ALIGN_DELTA,
      filter: brandsContext.buildFilter(),
    });
  }

  private buildSocialRequest(
    brandsContext: BrandContext,
    groupByAccount: boolean,
    fbPageOnly: boolean,
  ): QueryMetricsRequest {
    const measures = [new Measure({ measure: 'account_group_id' })];
    let groupBy = null;
    if (!groupByAccount) {
      measures.concat([
        new Measure({ measure: 'social_service_id' }),
        new Measure({ measure: 'token_broken' }),
        new Measure({ measure: 'authed_by_user' }),
      ]);
    } else {
      groupBy = new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'account_group_id',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      });
    }
    const filters = [
      new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'token_broken',
          value: new TypedValue({
            value: false,
            valueType: PropertyType.PROPERTY_TYPE_BOOL,
          }),
          operator: FieldFilterOperator.EQUAL,
        }),
      }),
      new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'authed_by_user',
          value: new TypedValue({
            value: true,
            valueType: PropertyType.PROPERTY_TYPE_BOOL,
          }),
          operator: FieldFilterOperator.EQUAL,
        }),
      }),
    ];
    if (fbPageOnly) {
      filters.push(
        new Filter({
          fieldFilter: new FieldFilter({
            dimension: 'is_facebook_page',
            value: new TypedValue({
              value: true,
              valueType: PropertyType.PROPERTY_TYPE_BOOL,
            }),
            operator: FieldFilterOperator.EQUAL,
          }),
        }),
      );
    }
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'social_connections',
      measures,
      resourceIds: brandsContext.resourceIds,
      filter: new Filter({
        compositeFilter: new CompositeFilter({
          op: CompositeFilterOperator.AND,
          filters: filters,
        }),
      }),
      groupBy,
    });
  }

  private buildReviewRequestsRequest(
    brandsContext: BrandContext,
    forCurrentPeriod: boolean,
    groupBy: string[],
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(forCurrentPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'review_request_email_performance',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'notification_id',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      filter: brandsContext.buildFilter(brandsContext.filters),
      groupBy: new GroupBy({
        dimension: groupBy.map((dim) => {
          return { dimension: dim };
        }),
      }),
    });
  }

  private buildRepReviewRequestsRequest(brandsContext: BrandContext, forCurrentPeriod: boolean): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(forCurrentPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'review_request_metrics',
      resourceIds: brandsContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'status',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'requests_sent',
            filter: new Filter({
              fieldFilter: new FieldFilter({
                dimension: 'status',
                operator: FieldFilterOperator.EQUAL,
                value: new TypedValue({
                  value: ['Clicked', 'Opened', 'Sent', 'Delivered'],
                  valueType: PropertyType.PROPERTY_TYPE_STRING,
                }),
                operatorFunction: FieldFilterOperatorFunction.ANY,
              }),
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'status',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'links_clicked',
            filter: new Filter({
              fieldFilter: new FieldFilter({
                dimension: 'status',
                operator: FieldFilterOperator.EQUAL,
                value: new TypedValue({
                  value: ['Clicked', 'Opened'],
                  valueType: PropertyType.PROPERTY_TYPE_STRING,
                }),
                operatorFunction: FieldFilterOperatorFunction.ANY,
              }),
            }),
          }),
        }),
      ],
      filter: brandsContext.buildFilter(brandsContext.filters),
    });
  }

  public buildBrandContext$(fieldMask: string[]): Observable<BrandContext> {
    const fieldMaskObj = fieldMask.reduce((o, f) => {
      o[f] = true;
      return o;
    }, {});
    return combineLatest([
      this.resourceIds$,
      fieldMaskObj['dateRange']
        ? this.timeRangeService.utcAlignedDateRange$.pipe(filter(([startDate, endDate]) => !!startDate && !!endDate))
        : of([null, null]),
      fieldMaskObj['timeRange'] ? this.timeRangeService.selectedTimeRangeKey$ : of(null),
      fieldMaskObj['selectedSourceIds'] ? this.brandFiltersService.selectedSourceIds$ : of(null),
      fieldMaskObj['activeSources'] ? this.reputationService.brandSources$ : of(null),
      fieldMaskObj['filters'] ? this.filterService.filters$ : of(null),
    ]).pipe(
      debounceTime(10),
      map(([resourceIds, [startDate, endDate], timeRange, selectedSourceIds, activatedSources, filters]) => {
        return new BrandContext(
          resourceIds,
          startDate,
          endDate,
          timeRange,
          filters,
          activatedSources,
          selectedSourceIds,
          [],
        );
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  buildBingLocationsRequest(brandsContext: BrandContext): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'bing_insights_view',
      resourceIds: brandsContext.resourceIds,
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [{ dimension: 'account_group_id' }],
      }),
    });
  }
}

function noneDeletedReviewsFilter(): Filter {
  return new Filter({
    unaryFilter: new UnaryFilter({
      dimension: 'deleted',
      op: UnaryFilterOperator.IS_NULL,
    }),
  });
}

export function fillByTime<T>(
  stats: StatByTime<T>[],
  startDate: Date,
  endDate: Date,
  defaultStat: (startDate: Date) => StatByTime<T>,
): StatByTime<T>[] {
  // Exit early if we're missing any of the pieces required
  if (!stats || !startDate || !endDate) {
    return stats;
  }
  // Figure out the expected times
  const granularity = getGranularityForRange(startDate, endDate);
  const expectedTimeBucketStarts = generateDates(startDate, endDate, granularity);

  // Build map of stats by time
  const statsByTime = stats.reduce((obj, stat) => {
    obj[stat.localAlignedTime.valueOf()] = stat;
    return obj;
  }, {});

  // Build filled stats by plucking from map or building a default with provided function
  return expectedTimeBucketStarts.map((start) => {
    return statsByTime[start.valueOf()] || defaultStat(start);
  });
}

export function inclusiveExclusive(start: Date, end: Date): DateRange {
  return new DateRange({ start: start, endExclusive: end });
}

export function inclusiveInclusive(start: Date, end: Date): DateRange {
  return new DateRange({ start: start, end: end });
}
