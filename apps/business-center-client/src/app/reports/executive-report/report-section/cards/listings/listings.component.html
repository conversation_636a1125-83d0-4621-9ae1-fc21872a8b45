<div class="new-performance-row">
  @if (listingScoreConfig$ | async) {
    <bc-new-base-card
      [cardConfig]="listingScoreConfig$ | async"
      [dataSource]="listingScoreData$ | async"
    ></bc-new-base-card>
  }
  @if (listingAccuracyBreakdownTableConfig$ | async) {
    <bc-new-base-card
      [cardConfig]="listingAccuracyBreakdownTableConfig$ | async"
      [dataSource]="listingSourceAccuracyCardData$ | async"
    ></bc-new-base-card>
  }
  @if (listingScoreBreakdownTableConfig$ | async) {
    <bc-new-base-card
      [cardConfig]="listingScoreBreakdownTableConfig$ | async"
      [dataSource]="listingScoreBreakdownData$ | async"
    ></bc-new-base-card>
  }
  <bc-citation-cards class="new-performance-row"></bc-citation-cards>

  @if (bingInsightsFeatureFlag$ | async) {
    <h2>{{ 'EXECUTIVE_REPORT.CATEGORY_TITLE.GOOGLE_MY_BUSINESS' | translate }}</h2>
    <bc-google-my-business-cards class="new-performance-row"></bc-google-my-business-cards>

    <h2>{{ 'PERFORMANCE.LISTINGS.BING_INSIGHTS.TITLE' | translate }}</h2>
    <bc-bing-insights-cards class="new-performance-row"></bc-bing-insights-cards>
  }
</div>
