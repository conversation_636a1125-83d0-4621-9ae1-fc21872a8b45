import { TemplatePortal } from '@angular/cdk/portal';
import { DOCUMENT } from '@angular/common';
import { AfterViewInit, Component, Inject, OnDestroy, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import dayjs from 'dayjs';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { catchError, distinctUntilChanged, map, switchMap } from 'rxjs/operators';
import { partnerId } from '../../../globals';
import { AppConfigService } from '../../app-config.service';
import { BrandsService } from '../../brands/brands.service';
import { FeatureFlagService } from '../../core/feature-flag.service';
import { isAccountGroup, LocationsService } from '../../locations';
import { NavigationComponent } from '../../navigation/navigation.component';
import { PageAccessService, PageId } from '../../page-access';
import { TimeRangeService } from '../../shared/time-range.service';
import { AvailableExecutiveReportSections, ExecutiveReportSection } from './interface';
import { ExecutiveReportSectionInterface } from './report-section/interface';
import { TaskManagerApiService } from '@vendasta/task-manager';

@Component({
  selector: 'bc-executive-report',
  templateUrl: './executive-report.component.html',
  styleUrls: ['./executive-report.component.scss'],
  standalone: false,
})
export class ExecutiveReportComponent implements AfterViewInit, OnDestroy {
  accessToMarketingFunnel$ = this.appConfigService.legacyConfig$.pipe(
    map((appConfig) => appConfig.showMarketingFunnel),
  );
  currentLocation$ = this.locationsService.currentLocation$;
  isCurrentLocationABrand$ = this.locationsService.isCurrentLocationABrand$;
  currentDateRange$ = this.timeRangeService.dateRange$.pipe(
    map(([start, end]) => {
      return this.formatDateRange(start, end);
    }),
  );

  private readonly isConciergeActive$: Observable<boolean> = this.currentLocation$.pipe(
    switchMap((loc) => {
      if (!isAccountGroup(loc)) {
        return of(false);
      }
      return this.taskManagerApi
        .isTaskManagerEnabledForBusiness({ businessId: loc.accountGroupId, partnerId: partnerId })
        .pipe(map((res) => res.isEnabled));
    }),
    catchError(() => of(false)),
  );

  sections$: Observable<ExecutiveReportSectionInterface[]> = this.isCurrentLocationABrand$.pipe(
    switchMap((inBrand) =>
      inBrand ? this.brandsService.disabledExecutiveReportSections$ : of<ExecutiveReportSection[]>([]),
    ),
    switchMap((disabledSections) => {
      return combineLatest([
        this.isConciergeActive$,
        this.pageVisibilityService.isPageAccessible$(PageId.multi_location_social),
        this.featureFlagService
          .checkFeatureFlagsMulti(partnerId, '', [
            'file_groups_exec_report',
            'task_manager_exec_report_cards',
            'google_search_console',
            '2025_crm_exec_report_section',
            'bing_insights_multi_location_busines_app',
          ])
          .pipe(
            map((features) => [
              features['file_groups_exec_report'],
              features['task_manager_exec_report_cards'],
              features['google_search_console'],
              features['2025_crm_exec_report_section'],
              features['bing_insights_multi_location_busines_app'],
            ]),
          ),
        this.isCurrentLocationABrand$,
      ]).pipe(
        map(
          ([
            isConciergeActive,
            socialVisible,
            [filesEnabled, tasksEnabled, gscEnabled, crmEnabled, gmbDisabled],
            isCurrentLocationABrand,
          ]) => {
            if (!crmEnabled && disabledSections.indexOf(ExecutiveReportSection.CUSTOMER_RELATIONS) === -1) {
              disabledSections.push(ExecutiveReportSection.CUSTOMER_RELATIONS);
            }
            if (!socialVisible && disabledSections.indexOf(ExecutiveReportSection.SOCIAL) === -1) {
              disabledSections.push(ExecutiveReportSection.SOCIAL);
            }
            if (!filesEnabled && disabledSections.indexOf(ExecutiveReportSection.FILES) === -1) {
              disabledSections.push(ExecutiveReportSection.FILES);
            }
            if (
              (!tasksEnabled || !isConciergeActive) &&
              disabledSections.indexOf(ExecutiveReportSection.SERVICES) === -1
            ) {
              disabledSections.push(ExecutiveReportSection.SERVICES);
            }

            if (gmbDisabled) {
              disabledSections.push(ExecutiveReportSection.GOOGLE_MY_BUSINESS);
            }
            // ML is temporarily disabled until we can rewrite it for scale
            // TODO: Remove the brand check on the right side of the if statement once ML GSC is re-enabled
            if (!gscEnabled || isCurrentLocationABrand) {
              disabledSections.push(ExecutiveReportSection.SEO);
            }
            // TODO: Remove hardcoded filter for Accounting as there is no handling for accounting in the report section component,
            //  nothing can be displayed right now
            //  check line 10-20 in report-section.component.ts
            disabledSections.push(ExecutiveReportSection.ACCOUNTING);
            disabledSections.push(ExecutiveReportSection.BING_INSIGHTS);
            return disabledSections;
          },
        ),
      );
    }),
    map((disabledSections) => {
      const sectionsToDisplay: ExecutiveReportSectionInterface[] = [];
      Object.keys(AvailableExecutiveReportSections).forEach((key: ExecutiveReportSection) => {
        // Make sure the section should be displayed for the user
        if (disabledSections.indexOf(key) === -1) {
          sectionsToDisplay.push(AvailableExecutiveReportSections[key]);
        }
      });
      return sectionsToDisplay;
    }),
  );

  activeSectionId$$ = new BehaviorSubject<string>('overview');
  activeSection$ = combineLatest([this.sections$, this.activeSectionId$$.pipe(distinctUntilChanged())]).pipe(
    map(([sections, activeSectionId]) => sections.find((s) => s.uniqueId === activeSectionId)),
  );
  sectionsInViewport: Set<string> = new Set();

  @ViewChild('breadcrumbPortalContent') breadcrumbPortalContent: TemplateRef<unknown>;

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly locationsService: LocationsService,
    private readonly brandsService: BrandsService,
    private readonly viewContainerRef: ViewContainerRef,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly pageVisibilityService: PageAccessService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly navigationComponent: NavigationComponent,
    private readonly timeRangeService: TimeRangeService,
    private readonly taskManagerApi: TaskManagerApiService,
  ) {}

  onSectionSelected(sectionId: string): void {
    this.scrollToSection(sectionId);
  }

  onSectionInViewportChanged(sectionId: string, inViewport: boolean): void {
    inViewport ? this.sectionsInViewport.add(sectionId) : this.sectionsInViewport.delete(sectionId);
    let activeSection = this.activeSectionId$$.getValue();
    for (const key of Object.keys(AvailableExecutiveReportSections)) {
      const id = AvailableExecutiveReportSections[key].uniqueId;
      if (this.sectionsInViewport.has(id)) {
        activeSection = id;
        break;
      }
    }
    this.activeSectionId$$.next(activeSection);
  }

  scrollToSection(sectionId: string): void {
    sectionId === 'overview' ? this.scrollToTop() : this.scrollIntoView(sectionId);
  }

  scrollToTop(): void {
    const scrollContainer = this.document.getElementsByClassName('glxy-page-main-content');
    if (scrollContainer.length > 0) scrollContainer[0].scroll({ top: 0, behavior: 'smooth' });
  }

  scrollIntoView(sectionId: string): void {
    const el = this.document.getElementById(sectionId).getElementsByClassName('anchor')[0];
    if (el) {
      el.scrollIntoView({ behavior: 'smooth' });
    }
  }

  ngAfterViewInit(): void {
    window.setTimeout(() => {
      this.navigationComponent.breadcrumbPortalContent.set(
        new TemplatePortal(this.breadcrumbPortalContent, this.viewContainerRef),
      );
    }, 0);
  }

  ngOnDestroy(): void {
    this.navigationComponent.breadcrumbPortalContent.set(null);
  }

  formatDateRange(start: Date, end: Date): string {
    const startDate = dayjs(start).add(1, 'day').format('MMM DD, YYYY');
    const endDate = dayjs(end).format('MMM DD, YYYY');
    return startDate + ' - ' + endDate;
  }
}
