import { inject, NgModule } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterModule, Routes } from '@angular/router';
// todo: remove when we move tokens to a seperate lib to preserve lazyloading
// eslint-disable-next-line @nx/enforce-module-boundaries
import { KnowledgeSourceManagementComponent } from '@galaxy/ai-knowledge';
import { INBOX_TEMPLATE_MESSAGE_ROUTES } from '@galaxy/conversation/inbox';
import { LinkActivityComponent, ActivityTableComponent } from '@galaxy/email-ui/email-activity';
import { EMAIL_LIBRARY_BASE_URL_TOKEN } from '@galaxy/email-ui/email-library/src/shared';
import { TranslateService } from '@ngx-translate/core';
import { pageSublinksRoute, tabbedContainerRoute, tabbedRoute } from '@vendasta/business-nav';
import { map } from 'rxjs/operators';
import { ACCOUNT_GROUP_TOKEN } from '../tokens';
import { AtlasComponent } from './atlas/atlas.component';
import {
  actionButtonMlResolver,
  actionButtonSlResolver,
} from './brands/brand-manage-reviews/manage-reviews/manage-reviews.resolvers';
import { FailedPaymentsRestrictedComponent } from './failed-payments/failed-payments-restricted.component';
import { PageOptions } from './navigation/navigation.interface';
import { AccountGroupGuard } from './page-access/account-group.guard';
import { GroupGuard } from './page-access/group.guard';
import { mobileOpenInBrowserGuard } from './page-access/mobile-open-in-browser.guard';
import { PageAccessGuard } from './page-access/page-access.guard';
import { SessionGuardForChild } from './page-access/session.guard';
import { PageId, PageIdToNavIdMap } from './page-access/page-id.enum';
import { RedirectGuard } from './redirect.guard';
import { UnsavedChangesGuard } from './pending-changes.guard';
import { AI_ASSISTANT_ROUTES } from '@galaxy/ai-assistant';
import { Feature, Views } from './core/access';
import { AccessViewGuard } from './core/guards/access-view-guard.service';
import { AccessFeatureGuard } from './core/guards/access-feature-guard.service';
import { AccessFeatureFlagGuard } from './core/guards/access-feature-flag-guard.service';

const OPTS = PageOptions;

const routes: Routes = [
  {
    path: '',
    component: AtlasComponent,
    canActivateChild: [PageAccessGuard],
    children: [
      {
        path: 'account/location/:agid',
        canActivate: [AccountGroupGuard],
        canActivateChild: [SessionGuardForChild],
        children: [
          {
            path: 'home',
            loadChildren: () => import('./home/<USER>').then((m) => m.HomeModule),
            data: {
              pageId: PageId.home,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
              breadcrumbs: [{ text: 'NAVIGATION.TABS.HOME' }],
            },
          },
          {
            path: 'get-started',
            redirectTo: 'home/get-started',
          },
          {
            path: 'home/get-started',
            loadChildren: () => import('./get-started/get-started.module').then((m) => m.GetStartedModule),
            data: {
              pageId: PageId.get_started,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'projects',
            redirectTo: 'administration/projects',
          },
          {
            path: 'fulfillment',
            redirectTo: 'administration/projects',
          },
          {
            path: 'activity',
            redirectTo: 'home/activity',
          },
          {
            path: 'home/activity',
            loadComponent: () => import('./activity/recent-activity.component'),
            data: {
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          {
            path: 'new-inbox',
            redirectTo: 'inbox',
          },
          {
            path: 'inbox',
            loadChildren: () => import('./inbox/inbox.module').then((m) => m.InboxModule),
            data: {
              pageId: PageId.inbox,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'recommendations',
            redirectTo: 'dashboard',
          },
          {
            path: 'guides',
            redirectTo: 'administration/guides',
          },
          {
            path: 'customer-list',
            loadChildren: () => import('./customer-list/customer-list.module').then((m) => m.CustomerListModule),
            data: { pageId: PageId.customer_list },
          },
          {
            path: 'files',
            redirectTo: 'administration/files',
          },
          {
            path: 'payments',
            loadChildren: () => import('./payments/payments/payments.module').then((mod) => mod.PaymentsModule),
            data: {
              pageId: PageId.payments_payments,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'invoices',
            loadComponent: () => import('./payments/invoices/invoices.component').then((mod) => mod.InvoicesComponent),
            data: {
              pageId: PageId.payments_invoices,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'payouts',
            loadChildren: () => import('./payments/payouts/payouts.module').then((m) => m.PayoutsModule),
            data: {
              pageId: PageId.payments_payouts,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'events-and-meetings',
            pathMatch: 'prefix',
            loadChildren: () =>
              import('./meeting-scheduler/meeting-scheduler.module').then((m) => m.MeetingSchedulerRoutingModule),
            data: {
              pageId: PageId.meeting_scheduler,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'settings',
            redirectTo: 'administration',
          },
          {
            path: 'administration',
            children: [
              pageSublinksRoute(PageIdToNavIdMap[PageId.settings_page], {
                path: '',
                loadComponent: () => import('./settings/settings.component').then((m) => m.SettingsComponent),
                data: {
                  pageId: PageId.settings_page,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                  },
                },
              }),
              {
                path: 'guides',
                loadChildren: () => import('./guides/guides.module').then((m) => m.GuidesModule),
                data: { pageId: PageId.guides },
              },
              {
                path: 'invoices',
                loadComponent: () => import('./invoices/list/list.component').then((m) => m.InvoicesListComponent),
                data: {
                  pageId: PageId.invoices,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'files',
                loadChildren: () => import('./account-files/account-files.module').then((m) => m.AccountFilesModule),
                data: {
                  pageId: PageId.files,
                },
              },
              {
                path: 'orders',
                loadChildren: () => import('./orders/orders.module').then((m) => m.OrdersRouteModule),
                data: {
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'projects',
                loadChildren: () => import('./fulfillment/fulfillment.module').then((m) => m.FulfillmentModule),
                data: { pageId: PageId.fulfillment },
              },
              {
                path: 'invoices',
                loadComponent: () => import('./invoices/list/list.component').then((m) => m.InvoicesListComponent),
                data: {
                  pageId: PageId.invoices,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'payments',
                loadComponent: () =>
                  import('./payments/payment-settings.component').then((mod) => mod.PaymentSettingsComponent),
                data: {
                  pageId: PageId.payments,
                  previousPageTitle: 'SETTINGS.PAGE_TITLE',
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
                resolve: {
                  previousPageUrl: (route: ActivatedRouteSnapshot) => {
                    return `/account/location/${route.params.agid}/settings`;
                  },
                },
              },
              {
                path: 'ai-knowledge',
                component: KnowledgeSourceManagementComponent,
                data: {
                  previousPageTitle: 'SETTINGS.PAGE_TITLE',
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
                resolve: {
                  previousPageUrl: (route: ActivatedRouteSnapshot) => {
                    return `/account/location/${route.params.agid}/administration`;
                  },
                },
              },
              {
                path: 'field-management',
                loadChildren: () =>
                  import('./crm/field-management/crm-field-management.module').then((m) => m.CrmFieldManagementModule),
                data: {
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'profile',
                loadComponent: () => import('./account-group/profile-edit.component'),
                canDeactivate: [UnsavedChangesGuard],
                data: {
                  pageId: PageId.business_profile,
                  pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
                },
              },
              {
                path: 'connections',
                canActivate: [mobileOpenInBrowserGuard],
                loadChildren: () =>
                  import('./social-connections/social-connections.module').then((m) => m.SocialConnectionsModule),
                data: { pageId: PageId.social_connections },
              },
              {
                path: 'integrations',
                canActivate: [mobileOpenInBrowserGuard],
                loadChildren: () =>
                  import('@galaxy/platform-integrations').then((m) => m.PlatformIntegrationsLibModule),
                data: { pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true } },
              },
              {
                path: 'email-history',
                loadChildren: () => import('./email/email.module').then((m) => m.EmailModule),
                data: {
                  pageId: PageId.email_history,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                  },
                },
              },
              {
                path: 'email-configuration',
                loadChildren: () =>
                  import('./email-configuration-v2/email-configuration-v2.module').then(
                    (m) => m.EmailConfigurationV2Module,
                  ),
                data: {
                  pageId: PageId.email_configuration,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                  },
                },
              },
              {
                path: 'sms-configuration',
                loadChildren: () =>
                  import('./sms-configuration/sms-configuration.module').then((m) => m.SmsConfigurationModule),
                data: {
                  pageId: PageId.sms_configuration,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                  },
                },
              },
              {
                path: 'notifications/:subjectId',
                loadChildren: () =>
                  import('./notification-settings/notification-settings.module').then(
                    (m) => m.NotificationSettingsModule,
                  ),
                data: { pageId: PageId.new_notification_settings },
              },
              {
                path: 'billing/:subjectId',
                loadComponent: () => import('./billing-settings/billing-settings.component'),
                data: {
                  pageId: PageId.billing_settings,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                  },
                },
              },
              {
                path: 'billing/:subjectId/settings',
                redirectTo: 'billing/:subjectId',
              },
              {
                path: 'inbox',
                loadChildren: () =>
                  import('./lazy-loaded-lib-modules/conversation-library.module').then(
                    (m) => m.ConversationLazyLoadedModules,
                  ),
              },
              ...INBOX_TEMPLATE_MESSAGE_ROUTES,
              {
                path: 'widgets',
                loadChildren: () =>
                  import('./lazy-loaded-lib-modules/conversation-library.module').then(
                    (m) => m.WidgetsLazyLoadedModule,
                  ),
              },
            ],
          },
          {
            path: 'store',
            loadChildren: () => import('./store/store.module').then((m) => m.StoreModule),
            data: {
              pageId: PageId.store,
              pageOptions: {
                [PageOptions.hideNavToolbar]: true,
                [PageOptions.displayFullScreen]: true,
              },
            },
          },
          {
            path: 'review-cart',
            loadChildren: () => import('./shopping-cart/shopping-cart.module').then((m) => m.ShoppingCartModule),
            data: {
              pageId: PageId.review_cart,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'products',
            loadChildren: () => import('./product/product.module').then((m) => m.ProductModule),
            data: { pageId: PageId.my_products },
          },
          {
            path: 'product',
            redirectTo: 'products',
            pathMatch: 'full',
          },
          {
            path: 'executive-report',
            loadChildren: () =>
              import('./executive-report/executive-report.module').then((m) => m.ExecutiveReportRouteModule),
            data: {
              pageId: PageId.executive_report,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
                [OPTS.subtractToolbarOffset]: false,
              },
            },
          },
          {
            path: 'report',
            redirectTo: 'executive-report',
            pathMatch: 'prefix',
          },
          {
            path: 'orders',
            redirectTo: 'administration/orders',
          },
          {
            path: 'edition-upgrades',
            loadChildren: () =>
              import('./edition-upgrades/edition-upgrades.module').then((m) => m.EditionUpgradesModule),
            data: {
              pageId: PageId.edition_upgrades,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'app/website',
            loadChildren: () => import('./website/website.module').then((m) => m.WebsiteModule),
            data: { pageId: PageId.website },
          },
          {
            path: 'invite',
            loadChildren: () => import('./referral/referral.module').then((m) => m.ReferralModule),
            data: { pageId: PageId.referral },
          },
          {
            path: 'work-order',
            loadChildren: () => import('./work-order/work-order.module').then((m) => m.BusinessAppWorkOrderModule),
            data: {
              pageId: PageId.work_order,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'crm',
            loadChildren: () => import('./crm/business-crm.module').then((m) => m.BusinessCrmModule),
            data: { pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true } },
          },
          {
            path: 'score/Company/:fieldId',
            redirectTo: 'score',
            pathMatch: 'full',
          },
          {
            path: 'score',
            loadChildren: () => import('./crm/business-score.module').then((m) => m.BusinessScoreModule),
            canActivate: [AccessFeatureGuard, AccessViewGuard],
            canMatch: [AccessFeatureFlagGuard],
            data: {
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
              feature: Feature.crmLeadScore,
              view: Views.crmContactEdit,
              featureFlag: 'sales_feature_business_app',
            },
          },
          {
            path: 'lists',
            loadChildren: () => import('./crm/dynamic-lists.module').then((m) => m.DynamicListsModule),
            data: { pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true } },
          },
          {
            path: 'leaderboard',
            loadChildren: () => import('./crm/leaderboard.module').then((m) => m.LeaderboardModule),
            data: {
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
              pageId: PageId.leaderboard,
            },
          },
          {
            path: 'custom-forms',
            loadChildren: () => import('./custom-forms/custom-forms.module').then((m) => m.CustomFormsModule),
            data: {
              pageId: PageId.forms,
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          {
            path: 'ai',
            children: [
              ...AI_ASSISTANT_ROUTES,
              {
                path: 'knowledge-base',
                component: KnowledgeSourceManagementComponent,
              },
            ],
            data: {
              pageId: PageId.ai,
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          {
            path: 'campaigns',
            loadChildren: () =>
              import('./lazy-loaded-lib-modules/campaigns-library.module').then((m) => m.LazyLoadedCampaignsModule),
          },
          {
            path: 'automations',
            loadChildren: () => import('@galaxy/automata/pages').then((m) => m.AutomationsModule),
            data: { pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true } },
          },
          {
            path: 'meetings',
            loadChildren: () => import('@galaxy/meeting-analysis').then((m) => m.MeetingAnalysisModule),
          },
          {
            path: 'link-activity/:attributeKey/:attributeValue',
            component: LinkActivityComponent,
          },
          {
            path: 'email-activity/:attributeKey/:attributeValue',
            component: ActivityTableComponent,
            data: {
              pageId: PageId.emails,
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          {
            path: 'emails',
            loadComponent: () => import('./email-library/page/page.component').then((mod) => mod.PageComponent),
            data: {
              pageId: PageId.emails,
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          {
            path: `emails/editor/:templateId`,
            loadComponent: () => import('./email-library/editor/editor.component').then((mod) => mod.EditorComponent),
            data: {
              pageId: PageId.emails,
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          tabbedRoute(PageIdToNavIdMap[PageId.reputation], {
            path: 'reputation',
            data: {
              pageId: PageId.reputation,
            },
            children: [
              {
                path: 'manage-reviews',
                children: [
                  {
                    resolve: { injectedPageContent: actionButtonSlResolver },
                    path: '',
                    loadChildren: () =>
                      import('./brands/brand-manage-reviews/manage-reviews/manage-reviews.module').then(
                        (m) => m.ManageReviewsModule,
                      ),
                    data: {
                      pageId: PageId.reputation,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                  {
                    path: 'response-templates',
                    children: [
                      {
                        path: '',
                        loadComponent: () =>
                          import(
                            './shared/response-templates/manage-response-templates/manage-response-templates.component'
                          ),
                        title: () => {
                          return inject(TranslateService).stream(
                            'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.RESPONSE_TEMPLATES',
                          );
                        },
                        data: {
                          pageId: PageId.manage_response_templates,
                          pageOptions: {
                            [OPTS.displayFullScreen]: true,
                            [OPTS.hideNavToolbar]: true,
                          },
                        },
                      },
                      {
                        path: 'edit-template',
                        loadComponent: () =>
                          import('./shared/response-templates/edit-response-template/edit-response-template.component'),
                        title: () => {
                          return inject(TranslateService).stream(
                            'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.EDIT_RESPONSE_TEMPLATE',
                          );
                        },
                        data: {
                          pageId: PageId.edit_response_template,
                          pageOptions: {
                            [OPTS.displayFullScreen]: true,
                            [OPTS.hideNavToolbar]: true,
                          },
                        },
                      },
                    ],
                  },
                ],
              },
              {
                path: 'netpromoterscore',
                loadComponent: () => import('./brands/brands-net-promoter-score/brands-net-promoter-score.component'),
                data: {
                  pageId: PageId.net_promoter_score,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'insights',
                loadComponent: () => import('./brands/brand-sentiment-analysis/sentiment-analysis.component'),
                title: () => {
                  return inject(TranslateService).stream('PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS');
                },
                data: {
                  pageId: PageId.reputation,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'google-q-and-a',
                loadComponent: () => import('./brands/brand-gmb/google-questions/google-questions.component'),
                title: () => {
                  return inject(TranslateService).stream('PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A');
                },
                data: {
                  pageId: PageId.google_question_answer,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
            ],
          }),
          {
            path: 'reviews',
            loadChildren: () => import('./brands/brand-reviews/brand-reviews.module').then((m) => m.BrandReviewsModule),
            data: {
              pageId: PageId.multi_location_reviews,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'manage-reviews',
            children: [
              {
                path: '',
                loadChildren: () =>
                  import('./brands/brand-manage-reviews/manage-reviews/manage-reviews.module').then(
                    (m) => m.ManageReviewsModule,
                  ),
                data: {
                  pageId: PageId.manage_multi_location_reviews,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'response-templates',
                children: [
                  {
                    path: '',
                    loadComponent: () =>
                      import(
                        './shared/response-templates/manage-response-templates/manage-response-templates.component'
                      ),
                    title: () => {
                      return inject(TranslateService).stream(
                        'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.RESPONSE_TEMPLATES',
                      );
                    },
                    data: {
                      pageId: PageId.manage_response_templates,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                  {
                    path: 'edit-template',
                    loadComponent: () =>
                      import('./shared/response-templates/edit-response-template/edit-response-template.component'),
                    title: () => {
                      return inject(TranslateService).stream(
                        'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.EDIT_RESPONSE_TEMPLATE',
                      );
                    },
                    data: {
                      pageId: PageId.edit_response_template,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                ],
              },
            ],
          },
          tabbedRoute(PageIdToNavIdMap[PageId.embedded_listings], {
            path: 'listings',
            data: {
              pageId: PageId.embedded_listings,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
            children: [
              {
                path: 'keyword-tracking',
                loadComponent: () =>
                  import('./listings-embedded-keyword-tracking/listings-embedded-keyword-tracking-page.component'),
                data: {
                  pageId: PageId.keyword_tracking,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'listing-sync',
                loadComponent: () => import('./listings-embedded-page/listings-embedded-page.component'),
                data: {
                  pageId: PageId.listing_sync,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
            ],
          }),
          tabbedRoute(PageIdToNavIdMap[PageId.embedded_social], {
            path: 'social',
            data: {
              pageId: PageId.embedded_social,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.subtractToolbarOffset]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadComponent: () => import('./brands/brand-social/brand-social.component'),
                data: {
                  pageId: PageId.embedded_social,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.subtractToolbarOffset]: true,
                  },
                  mapShowing: false,
                },
              },
              {
                path: 'manage-posts',
                loadComponent: () => import('./brands/brand-manage-social/brand-manage-social.component'),
                data: {
                  pageId: PageId.manage_multi_location_social_posts,
                  pageOptions: {
                    [OPTS.displayComposeButton]: true,
                    [OPTS.displayFullScreen]: true,
                    [OPTS.subtractToolbarOffset]: true,
                  },
                },
              },
            ],
          }),
          {
            path: 'google-q-and-a',
            loadComponent: () => import('./brands/brand-gmb/google-questions/google-questions.component'),
            data: {
              pageId: PageId.google_question_answer,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
                [OPTS.subtractToolbarOffset]: false,
              },
            },
          },
          tabbedContainerRoute(PageIdToNavIdMap[PageId.embedded_advertising], {
            path: 'advertising',
            data: {
              pageId: PageId.embedded_advertising,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.subtractToolbarOffset]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadComponent: () => import('./brands/brand-advertising/brand-advertising.component'),
                data: {
                  pageId: PageId.multi_location_advertising,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
            ],
          }),
          {
            path: 'advertising-overview',
            loadComponent: () => import('./brands/brand-advertising/brand-advertising.component'),
            data: {
              pageId: PageId.multi_location_advertising,
              pageOptions: {
                [OPTS.hideNavToolbar]: true,
                [OPTS.displayFullScreen]: true,
              },
            },
          },
          {
            path: 'data-export',
            loadChildren: () =>
              import('./brands/brand-data-export/brand-data-export.module').then((m) => m.BrandDataExportModule),
            data: {
              pageId: PageId.multi_location_data_export,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'switching-location',
            loadComponent: () => import('./core/location-switch-loading/location-switch-loading.component'),
            data: {
              pageOptions: {
                [OPTS.hideNavToolbar]: true,
                [OPTS.displayFullScreen]: true,
              },
            },
          },
          {
            path: '**',
            redirectTo: 'home',
          },
        ],
      },
      {
        path: 'account/brands/:brandname',
        canActivate: [GroupGuard],
        children: [
          {
            path: 'home',
            loadChildren: () =>
              import('./brands/brand-dashboard/brand-dashboard.module').then((m) => m.BrandDashboardModule),
            data: {
              pageId: PageId.multi_location_dashboard,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
              breadcrumbs: [{ text: 'NAVIGATION.TABS.HOME' }],
            },
          },
          {
            path: 'report',
            loadChildren: () => import('./brands/brand-report/brand-report.module').then((m) => m.BrandReportModule),
            data: {
              pageId: PageId.multi_location_report,
              pageOptions: {
                [OPTS.hideNavToolbar]: true,
                [OPTS.displayFullScreen]: true,
              },
            },
          },
          tabbedRoute(PageIdToNavIdMap[PageId.ml_reviews], {
            path: 'reviews',
            data: {
              pageId: PageId.ml_reviews,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadChildren: () =>
                  import('./brands/brand-reviews/brand-reviews.module').then((m) => m.BrandReviewsModule),
                data: {
                  pageId: PageId.multi_location_overview,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'manage-reviews',
                children: [
                  {
                    path: '',
                    resolve: { injectedPageContent: actionButtonMlResolver },
                    loadChildren: () =>
                      import('./brands/brand-manage-reviews/manage-reviews/manage-reviews.module').then(
                        (m) => m.ManageReviewsModule,
                      ),
                    data: {
                      pageId: PageId.manage_multi_location_reviews,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                  {
                    path: 'request-reviews',
                    loadChildren: () =>
                      import('./brands/request-reviews/request-reviews.module').then((m) => m.RequestReviewsModule),
                    data: {
                      pageId: PageId.request_multi_location_reviews,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                  {
                    path: 'response-templates',
                    children: [
                      {
                        path: '',
                        loadComponent: () =>
                          import(
                            './shared/response-templates/manage-response-templates/manage-response-templates.component'
                          ),
                        title: () => {
                          return inject(TranslateService).stream(
                            'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.RESPONSE_TEMPLATES',
                          );
                        },
                        data: {
                          pageId: PageId.manage_response_templates,
                          pageOptions: {
                            [OPTS.displayFullScreen]: true,
                            [OPTS.hideNavToolbar]: true,
                          },
                        },
                      },
                      {
                        path: 'edit-template',
                        loadComponent: () =>
                          import('./shared/response-templates/edit-response-template/edit-response-template.component'),
                        title: () => {
                          return inject(TranslateService).stream(
                            'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.EDIT_RESPONSE_TEMPLATE',
                          );
                        },
                        data: {
                          pageId: PageId.edit_response_template,
                          pageOptions: {
                            [OPTS.displayFullScreen]: true,
                            [OPTS.hideNavToolbar]: true,
                          },
                        },
                      },
                    ],
                  },
                ],
              },
              {
                path: 'insights',
                loadComponent: () => import('./brands/brand-sentiment-analysis/sentiment-analysis.component'),
                title: () => {
                  return inject(TranslateService).stream('PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS');
                },
                data: {
                  pageId: PageId.multi_location_reviews_sentiment,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'google-q-and-a',
                loadComponent: () => import('./brands/brand-gmb/google-questions/google-questions.component'),
                title: () => {
                  return inject(TranslateService).stream('PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A');
                },
                data: {
                  pageId: PageId.multi_location_google_question_answer,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
            ],
          }),
          tabbedRoute(PageIdToNavIdMap[PageId.multi_location_net_promoter_score], {
            path: 'netpromoterscore',
            data: {
              pageId: PageId.multi_location_net_promoter_score,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadComponent: () =>
                  import(
                    './brands/brand-net-promoter-score-overview/brands-net-promoter-score-overview.component'
                  ).then((m) => m.BrandsNetPromoterScoreOverviewComponent),
                data: {
                  pageId: PageId.multi_location_nps_overview,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'nps',
                loadComponent: () => import('./brands/brands-net-promoter-score/brands-net-promoter-score.component'),
                data: {
                  pageId: PageId.nav_brand_reputation_nps,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'team',
                loadComponent: () =>
                  import('./brands/brand-nps-team-overview/brand-nps-team-overview.component').then(
                    (m) => m.BrandNpsTeamOverviewComponent,
                  ),
                data: {
                  pageId: PageId.multi_location_nps_team,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
            ],
          }),
          tabbedRoute(PageIdToNavIdMap[PageId.multi_location_requests], {
            path: 'requests',
            data: {
              pageId: PageId.multi_location_requests,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadComponent: () =>
                  import('./brands/brands-requests-overview/brands-requests-overview.component').then(
                    (m) => m.BrandsRequestsOverviewComponent,
                  ),
                data: {
                  pageId: PageId.multi_location_requests_overview,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
            ],
          }),
          tabbedRoute(PageIdToNavIdMap[PageId.brand_reputation], {
            path: 'reputation',
            data: {
              pageId: PageId.brand_reputation,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
            children: [
              {
                path: 'reviews',
                loadChildren: () =>
                  import('./brands/brand-reviews/brand-reviews.module').then((m) => m.BrandReviewsModule),
                data: {
                  pageId: PageId.multi_location_reviews,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'manage-reviews',
                children: [
                  {
                    path: '',
                    resolve: { injectedPageContent: actionButtonMlResolver },
                    loadChildren: () =>
                      import('./brands/brand-manage-reviews/manage-reviews/manage-reviews.module').then(
                        (m) => m.ManageReviewsModule,
                      ),
                    data: {
                      pageId: PageId.manage_multi_location_reviews,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                  {
                    path: 'request-reviews',
                    loadChildren: () =>
                      import('./brands/request-reviews/request-reviews.module').then((m) => m.RequestReviewsModule),
                    data: {
                      pageId: PageId.request_multi_location_reviews,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                  {
                    path: 'response-templates',
                    children: [
                      {
                        path: '',
                        loadComponent: () =>
                          import(
                            './shared/response-templates/manage-response-templates/manage-response-templates.component'
                          ),
                        title: () => {
                          return inject(TranslateService).stream(
                            'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.RESPONSE_TEMPLATES',
                          );
                        },
                        data: {
                          pageId: PageId.manage_response_templates,
                          pageOptions: {
                            [OPTS.displayFullScreen]: true,
                            [OPTS.hideNavToolbar]: true,
                          },
                        },
                      },
                      {
                        path: 'edit-template',
                        loadComponent: () =>
                          import('./shared/response-templates/edit-response-template/edit-response-template.component'),
                        title: () => {
                          return inject(TranslateService).stream(
                            'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.EDIT_RESPONSE_TEMPLATE',
                          );
                        },
                        data: {
                          pageId: PageId.edit_response_template,
                          pageOptions: {
                            [OPTS.displayFullScreen]: true,
                            [OPTS.hideNavToolbar]: true,
                          },
                        },
                      },
                    ],
                  },
                ],
              },
              {
                path: 'insights',
                loadComponent: () => import('./brands/brand-sentiment-analysis/sentiment-analysis.component'),
                title: () => {
                  return inject(TranslateService).stream('PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS');
                },
                data: {
                  pageId: PageId.multi_location_reviews_sentiment,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'google-q-and-a',
                loadComponent: () => import('./brands/brand-gmb/google-questions/google-questions.component'),
                title: () => {
                  return inject(TranslateService).stream('PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A');
                },
                data: {
                  pageId: PageId.multi_location_google_question_answer,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'nps',
                loadComponent: () => import('./brands/brands-net-promoter-score/brands-net-promoter-score.component'),
                data: {
                  pageId: PageId.nav_brand_reputation_nps,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
            ],
          }),
          {
            path: 'reviews',
            loadChildren: () => import('./brands/brand-reviews/brand-reviews.module').then((m) => m.BrandReviewsModule),
            data: {
              pageId: PageId.multi_location_reviews,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'manage-reviews',
            children: [
              {
                path: '',
                resolve: { injectedPageContent: actionButtonMlResolver },
                loadChildren: () =>
                  import('./brands/brand-manage-reviews/manage-reviews/manage-reviews.module').then(
                    (m) => m.ManageReviewsModule,
                  ),
                data: {
                  pageId: PageId.manage_multi_location_reviews,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'request-reviews',
                loadChildren: () =>
                  import('./brands/request-reviews/request-reviews.module').then((m) => m.RequestReviewsModule),
                data: {
                  pageId: PageId.request_multi_location_reviews,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                  },
                },
              },
              {
                path: 'response-templates',
                children: [
                  {
                    path: '',
                    loadComponent: () =>
                      import(
                        './shared/response-templates/manage-response-templates/manage-response-templates.component'
                      ),
                    title: () => {
                      return inject(TranslateService).stream(
                        'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.RESPONSE_TEMPLATES',
                      );
                    },
                    data: {
                      pageId: PageId.manage_response_templates,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                  {
                    path: 'edit-template',
                    loadComponent: () =>
                      import('./shared/response-templates/edit-response-template/edit-response-template.component'),
                    title: () => {
                      return inject(TranslateService).stream(
                        'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.EDIT_RESPONSE_TEMPLATE',
                      );
                    },
                    data: {
                      pageId: PageId.edit_response_template,
                      pageOptions: {
                        [OPTS.displayFullScreen]: true,
                        [OPTS.hideNavToolbar]: true,
                      },
                    },
                  },
                ],
              },
            ],
          },
          {
            path: 'insights',
            loadComponent: () => import('./brands/brand-sentiment-analysis/sentiment-analysis.component'),
            title: () => {
              return inject(TranslateService).stream('PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS');
            },
            data: {
              pageId: PageId.multi_location_reviews_sentiment,
              pageOptions: {
                [OPTS.displayDateSelector]: true,
                [OPTS.displayFilterContainer]: true,
              },
            },
          },
          tabbedRoute(PageIdToNavIdMap[PageId.multi_location_embedded_listings], {
            path: 'listings',
            data: {
              pageId: PageId.multi_location_embedded_listings,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.subtractToolbarOffset]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadComponent: () => import('./brands/brand-listings/brand-listings.component'),
                data: {
                  pageId: PageId.multi_location_embedded_listings,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
              {
                path: 'manage',
                loadComponent: () => import('./brands/brand-manage-listings/brand-manage-listings.component'),
                data: {
                  pageId: PageId.multi_location_embedded_listings,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
            ],
          }),
          tabbedRoute(PageIdToNavIdMap[PageId.multi_location_keyword_tracking], {
            path: 'keyword-tracking',
            data: {
              pageId: PageId.multi_location_keyword_tracking,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.subtractToolbarOffset]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadComponent: () => import('./brands/brand-keyword-overview/brand-keyword-overview.component'),
                data: {
                  pageId: PageId.multi_location_keyword_tracking,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
              {
                path: 'keywords',
                loadComponent: () => import('./brands/brand-keyword-tracking/brand-keyword-tracking.component'),
                data: {
                  pageId: PageId.multi_location_keyword_tracking,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
            ],
          }),
          {
            path: 'manage-listings',
            loadComponent: () => import('./brands/brand-manage-listings/brand-manage-listings.component'),
            data: {
              pageId: PageId.multi_location_listings,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
                [OPTS.subtractToolbarOffset]: false,
              },
            },
          },
          {
            path: 'listings-overview',
            loadComponent: () => import('./brands/brand-listings/brand-listings.component'),
            data: {
              pageId: PageId.multi_location_listings,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
                [OPTS.subtractToolbarOffset]: false,
              },
            },
          },
          {
            path: 'inbox',
            loadChildren: () => import('./inbox/inbox.module').then((m) => m.InboxModule),
            data: {
              pageId: PageId.inbox_brand,
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          {
            path: 'contacts',
            loadChildren: () =>
              import('./brands/brand-crm/brand-contact.module').then((m) => m.BusinessBrandCrmContactModule),
            data: {
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          {
            path: 'tasks',
            loadChildren: () =>
              import('./brands/brand-crm/brand-task.module').then((m) => m.BusinessBrandCrmTaskModule),
            data: {
              pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
            },
          },
          {
            path: 'keyword-tracking',
            loadComponent: () =>
              import('./listings-embedded-keyword-tracking/listings-embedded-keyword-tracking-page.component'),
            data: {
              pageId: PageId.keyword_tracking,
              pageOptions: {
                [OPTS.displayKeywordOptions]: true,
                [OPTS.displayFullScreen]: true,
              },
            },
          },
          {
            path: 'keyword-tracking-overview',
            loadComponent: () => import('./brands/brand-keyword-overview/brand-keyword-overview.component'),
            data: {
              pageId: PageId.multi_location_keyword,
              pageOptions: {
                [OPTS.displayKeywordOptions]: true,
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
                [OPTS.subtractToolbarOffset]: false,
              },
            },
          },
          {
            path: 'keyword-tracking-keyword',
            loadComponent: () => import('./brands/brand-keyword-tracking/brand-keyword-tracking.component'),
            data: {
              pageId: PageId.multi_location_keyword,
              pageOptions: {
                [OPTS.displayKeywordOptions]: true,
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
                [OPTS.subtractToolbarOffset]: false,
              },
            },
          },
          {
            path: 'listing-sync',
            loadComponent: () => import('./listings-embedded-page/listings-embedded-page.component'),
            data: {
              pageId: PageId.listing_sync,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
              },
            },
          },
          tabbedContainerRoute(PageIdToNavIdMap[PageId.embedded_analytics], {
            path: 'analytics',
            data: {
              pageId: PageId.embedded_analytics,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.subtractToolbarOffset]: true,
              },
            },
            children: [
              {
                path: 'google',
                loadComponent: () => import('./brands/brand-gmb/brand-gmb.component'),
                data: {
                  pageId: PageId.multi_location_analytics,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
              {
                path: 'bing',
                loadComponent: () => import('./brands/brand-bing-places/brand-bing-places.component'),
                data: {
                  pageId: PageId.multi_location_analytics,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
              {
                path: 'google-my-business',
                redirectTo: 'analytics/google',
              },
              {
                path: '',
                redirectTo: 'google',
                pathMatch: 'full',
              },
            ],
          }),
          {
            path: 'google',
            loadComponent: () => import('./brands/brand-gmb/brand-gmb.component'),
            data: {
              pageId: PageId.multi_location_analytics,
              pageOptions: {
                [OPTS.displayDateSelector]: true,
                [OPTS.displayFilterContainer]: true,
              },
            },
          },
          {
            path: 'google-q-and-a',
            loadComponent: () => import('./brands/brand-gmb/google-questions/google-questions.component'),
            data: {
              pageId: PageId.multi_location_google_question_answer,
              pageOptions: {
                [OPTS.displayDateSelector]: true,
                [OPTS.displayFilterContainer]: true,
                [OPTS.displayFullScreen]: true,
              },
            },
          },
          tabbedRoute(PageIdToNavIdMap[PageId.multi_location_embedded_social], {
            path: 'social',
            data: {
              pageId: PageId.multi_location_embedded_social,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.subtractToolbarOffset]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadComponent: () => import('./brands/brand-social/brand-social.component'),
                data: {
                  pageId: PageId.multi_location_social,
                  pageOptions: {
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                  mapShowing: true,
                },
              },
              {
                path: 'manage-posts',
                loadComponent: () => import('./brands/brand-manage-social/brand-manage-social.component'),
                data: {
                  pageId: PageId.manage_multi_location_social_posts,
                  pageOptions: {
                    [OPTS.displayComposeButton]: true,
                    [OPTS.displayFullScreen]: true,
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
            ],
          }),
          {
            path: 'social-overview',
            loadComponent: () => import('./brands/brand-social/brand-social.component'),
            data: {
              pageId: PageId.multi_location_social,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
              mapShowing: true,
            },
          },
          {
            path: 'manage-social-posts',
            loadComponent: () => import('./brands/brand-manage-social/brand-manage-social.component'),
            data: {
              pageId: PageId.manage_multi_location_social_posts,
              pageOptions: {
                [OPTS.hideNavToolbar]: true,
                [OPTS.displayFullScreen]: true,
                [OPTS.subtractToolbarOffset]: false,
              },
            },
          },
          tabbedContainerRoute(PageIdToNavIdMap[PageId.multi_location_advertising], {
            path: 'advertising',
            data: {
              pageId: PageId.embedded_advertising,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.subtractToolbarOffset]: true,
              },
            },
            children: [
              {
                path: 'overview',
                loadComponent: () => import('./brands/brand-advertising/brand-advertising.component'),
                data: {
                  pageId: PageId.multi_location_advertising,
                  pageOptions: {
                    [OPTS.hideNavToolbar]: true,
                    [OPTS.displayFullScreen]: true,
                    [OPTS.subtractToolbarOffset]: false,
                  },
                },
              },
            ],
          }),
          {
            path: 'advertising-overview',
            loadComponent: () => import('./brands/brand-advertising/brand-advertising.component'),
            data: {
              pageId: PageId.multi_location_advertising,
              pageOptions: {
                [OPTS.hideNavToolbar]: true,
                [OPTS.displayFullScreen]: true,
              },
            },
          },
          {
            path: 'data-export',
            loadChildren: () =>
              import('./brands/brand-data-export/brand-data-export.module').then((m) => m.BrandDataExportModule),
            data: {
              pageId: PageId.multi_location_data_export,
              pageOptions: {
                [OPTS.displayFullScreen]: true,
                [OPTS.hideNavToolbar]: true,
              },
            },
          },
          {
            path: 'settings/user-profile/:subjectId',
            loadChildren: () => import('./user-profile/user-settings.module').then((m) => m.UserSettingsModule),
            data: { pageId: PageId.user_profile },
          },
          {
            path: 'switching-location',
            loadComponent: () => import('./core/location-switch-loading/location-switch-loading.component'),
            data: {
              pageOptions: {
                [OPTS.hideNavToolbar]: true,
                [OPTS.displayFullScreen]: true,
              },
            },
          },
          {
            path: '**',
            redirectTo: 'home',
          },
        ],
      },
      {
        path: 'settings/user-profile/:subjectId',
        loadChildren: () => import('./user-profile/user-settings.module').then((m) => m.UserSettingsModule),
      },
      {
        path: 'compose-social-post/:postId',
        outlet: 'action',
        loadComponent: () => import('./social-composer').then((m) => m.SocialComposerComponent),
      },
      {
        path: 'compose-social-post',
        outlet: 'action',
        loadComponent: () => import('./social-composer').then((m) => m.SocialComposerComponent),
      },
      {
        path: 'account/select-location',
        loadComponent: () =>
          import('./user/select-location/select-location.component').then((m) => m.SelectLocationComponent),
      },
      {
        path: 'account/select-location-page',
        loadComponent: () => import('./user/select-location-page/select-location-page.component'),
        data: {
          pageOptions: {
            [OPTS.hideNavToolbar]: true,
            [OPTS.displayFullScreen]: true,
          },
        },
      },
      {
        path: 'account/restricted',
        component: FailedPaymentsRestrictedComponent,
      },
      {
        path: 'product/:productId',
        canActivate: [
          (route: ActivatedRouteSnapshot) => {
            const router = inject(Router);
            return router.createUrlTree([
              `/account/location/${route.queryParams.account}/store/app/${route.params.productId}`,
            ]);
          },
        ],
        children: [],
      },
      {
        path: 'redirect/external',
        canActivate: [mobileOpenInBrowserGuard],
        children: [],
      },
      {
        path: '**',
        canActivate: [RedirectGuard],
        children: [],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [
    {
      provide: EMAIL_LIBRARY_BASE_URL_TOKEN,
      useFactory: () => {
        const accountGroup$ = inject(ACCOUNT_GROUP_TOKEN);
        return accountGroup$.pipe(map((accountGroup) => 'account/location/' + accountGroup.accountGroupId + '/emails'));
      },
    },
  ],
})
export class AuthenticatedAppRouting {}
