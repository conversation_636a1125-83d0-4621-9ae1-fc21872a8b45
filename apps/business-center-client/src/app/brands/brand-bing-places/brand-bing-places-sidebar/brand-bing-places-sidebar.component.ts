import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { partnerId } from '../../../../globals';
import { TimeRangeService } from '../../../shared/time-range.service';
import { BrandSidebarHeaderModule } from '../../brand-sidebar-header/brand-sidebar-header.module';
import { ConnectedCardsModule } from '../../../performance-dashboard/connected-cards/connected-cards.module';

@Component({
  selector: 'bc-brand-bing-places-sidebar',
  templateUrl: './brand-bing-places-sidebar.component.html',
  standalone: true,
  imports: [BrandSidebarHeaderModule, ConnectedCardsModule],
})
export class BrandBingPlacesSidebarComponent {
  partnerId = partnerId;

  private readonly dateRange = toSignal(inject(TimeRangeService).dateRange$);
  readonly startDate = computed(() => this.dateRange()?.[0]);
  readonly endDate = computed(() => this.dateRange()?.[1]);
}
