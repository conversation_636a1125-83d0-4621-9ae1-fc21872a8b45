import { Injectable, OnDestroy } from '@angular/core';
import {
  Alignment,
  AlignmentPeriod,
  AlignmentPeriodCalendar,
  AnalyticsApiService,
  CompositeFilterOperator,
  DateRange,
  FieldFilter,
  FieldFilterOperator,
  FieldFilterOperatorFunction,
  Filter,
  GroupBy,
  GroupByDimension,
  GroupByOperator,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  MetricResult,
  MultiLocationAnalyticsService,
  Order,
  OrderBy,
  PropertyType,
  QueryMetricsRequest,
  QueryMetricsResponse,
  ResourceMetricResult,
  TypedValue,
  UnaryFilter,
  UnaryFilterOperator,
} from '@vendasta/multi-location-analytics';
import { SocialPostsService } from '@vendasta/social-posts';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import moment from 'moment';
import { BehaviorSubject, combineLatest, Observable, of, Subject, Subscription } from 'rxjs';
import { catchError, distinctUntilChanged, filter, map, shareReplay, startWith, switchMap, tap } from 'rxjs/operators';
import { partnerId } from '../../../globals';
import { AccountGroup, AccountGroupService } from '../../account-group';
import { getResourceIds } from '../../core/single-location.service';
import { QueryMetricsResponseDelta } from '../../metrics/helpers';
import { SocialService } from '../../metrics/social-helper.service';
import { AllNetworksPerformanceService } from '../../metrics/social-metrics-v2/all-networks-performance.service';
import { AllNetworksPerformanceServiceV3 } from '../../metrics/social-metrics-v3/all-networks-performance.service';
import { FacebookPerformanceService } from '../../metrics/social-metrics/facebook-performance.service';
import { GoogleMyBusinessPerformanceService } from '../../metrics/social-metrics/gmb-performance.service';
import { InstagramPerformanceService } from '../../metrics/social-metrics/instagram-performance.service';
import { TiktokPerformanceService } from '../../metrics/social-metrics/tiktok-performance.service';
import {
  AllNetworksDateBoundedData,
  CombinedDataSingleSeries,
  FacebookPostStats,
  PostStats,
} from '../../metrics/social-metrics/interface';
import { LinkedinPerformanceService } from '../../metrics/social-metrics/linkedin-performance.service';
import { TwitterPerformanceService } from '../../metrics/social-metrics/twitter-performance.service';
import {
  alignLocalDateToUTC,
  alignStartAndEndDates,
  GBPKeywordPreviousPeriodMoment,
  GMBPeriodMoment,
  getPreviousPeriod,
  granularityFunnelMetricsMonthly,
  granularityFunnelMetricsWeekly,
  previousPeriod,
} from '../../shared/time-range-shared';
import { TimeRangeService } from '../../shared/time-range.service';
import {
  GoogleMyBusinessPostStats,
  InstagramPostStats,
  LinkedinPostStats,
  SocialServiceName,
  TiktokPostStats,
  TopPost,
  TwitterPostStats,
} from './social-stats-v2/interface';

dayjs.extend(utc);

export interface GoogleInsightSearchKeyword {
  keyword: string;
  value: number;
}

// Filters for listings which are deemed relevant to the business (1's are only *possible* listings)
export const bestMatchFilter = new Filter({
  fieldFilter: new FieldFilter({
    dimension: 'good_listing',
    operator: FieldFilterOperator.EQUAL,
    value: {
      value: '2',
      valueType: PropertyType.PROPERTY_TYPE_INT64,
    },
  }),
});

export interface AllNetworksStats extends PostStats {
  reach?: number;
  engagement?: number;
  service?: string;
}

type RequestBuilder = (
  accountGroup: AccountGroup,
  startDate: Date,
  endDate: Date,
  doPreviousPeriod?: boolean,
  limited?: boolean,
  // ignoreStartDate start date of the current range to gather all data up to the end date
  ignoreStartDate?: boolean,
  providerId?: string,
) => QueryMetricsRequest;

export enum CONSTANT_CONTACT_METRIC {
  SENT = 0,
  OPEN,
  CLICK,
}

const BING_INSIGHTS_VIEW_STATS: string[] = [
  'desktop_map_impression',
  'desktop_serp_impression',
  'mobile_map_impression',
  'mobile_serp_impression',
];
@Injectable({ providedIn: 'root' })
export class ConnectedCardsQueryService implements OnDestroy {
  accountGroupDatePickerLatest$: Observable<[AccountGroup, [Date, Date]]>;

  private accountDataChanged$$: Subject<string> = new Subject<string>();
  accountDataChanged$: Observable<string> = this.accountDataChanged$$.asObservable();

  bingHistoricalInsightsViewCard$: Observable<QueryMetricsResponse>;
  bingHistoricalInsightsActionsCard$: Observable<QueryMetricsResponse>;

  // Google My Business
  gmbInsights: QueryMetricsResponseDelta;
  // Historical Information - Goes back 12 months from the passed in startDate
  gmbHistoricalInsights$: Observable<QueryMetricsResponse>;
  // Historical information - respects the timeframe selected
  gmbHistoricalInsightsWithinRange$: Observable<QueryMetricsResponse>;
  gmbHistoricalInsightsWithinRangeTotals$: Observable<QueryMetricsResponse>;
  private gmbLimitHistoricalTo30Days$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  gmbLimitHistoricalTo30Days$ = this.gmbLimitHistoricalTo30Days$$.asObservable();
  bingViewsTotals: QueryMetricsResponseDelta;
  bingPhoneCallTotals: QueryMetricsResponseDelta;
  bingViewsPlacesKeywords$: Observable<QueryMetricsResponse>;

  bingWebsiteClicksTotals: QueryMetricsResponseDelta;
  bingWebsiteClicksPlacesKeywords$: Observable<QueryMetricsResponse>;
  gmbSearchKeywordsData$: Observable<QueryMetricsResponse>;
  gmbSearchKeywordsTotals: QueryMetricsResponseDelta;
  gmbHistoricalSearchKeywords$: Observable<QueryMetricsResponse>;
  // Multi Listings
  listingSyncProStatus$: Observable<QueryMetricsResponse>;
  listingsData$: Observable<QueryMetricsResponse>;

  // Reputation Review
  repReviewCount: QueryMetricsResponseDelta;
  //NPS Volume count
  repNPSVolumeCount: QueryMetricsResponseDelta;

  //Team score card volume count
  repTeamScoreCardVolumeCount: (providerId: string) => QueryMetricsResponseDelta;
  // Historical Information - Goes back 6 months from the passed in startDate
  repReviewCountHistorical$: Observable<QueryMetricsResponse>;
  //Historical Information for NPS volume
  repNPSVolumeHistorical$: Observable<QueryMetricsResponse>;
  //Historical Information for NPS Team volume
  repTeamScoreCardVolumeHistorical$: (providerId: string) => Observable<QueryMetricsResponse>;
  // Historical information - respects the timeframe selected
  repReviewCountHistoricalWithinRange$: Observable<QueryMetricsResponse>;
  repReviewRating: QueryMetricsResponseDelta;
  repReviewHistoricalRating$: Observable<QueryMetricsResponse>;

  // Social Marketing
  smStats$: Observable<AllNetworksDateBoundedData>;
  smCardStats_V2$: Observable<CombinedDataSingleSeries>;
  smTopPosts$: Observable<TopPost[]>;

  // Constant Contact
  emailEngagementTrend$: Observable<QueryMetricsResponse>;
  emailEngagementStats: QueryMetricsResponseDelta;
  emailEngagementStatsWithinRange$: Observable<QueryMetricsResponse>;
  activeCampaignStats$: Observable<QueryMetricsResponse | any>;

  // Advertising Intelligence
  // TODO: Remove $ suffix when not an observable
  adIntelStats: QueryMetricsResponseDelta;
  adIntelStatsForFunnel: QueryMetricsResponseDelta;
  adIntelPhoneStats: QueryMetricsResponseDelta;
  // Historical Information - Goes back 12 months from the passed in startDate
  adIntelStatsHistorical$: Observable<QueryMetricsResponse>;
  // Historical information - respects the timeframe selected
  adIntelStatsHistoricalWithinRange$: Observable<QueryMetricsResponse>;
  adintelROIStats: QueryMetricsResponseDelta;
  adintelROIStatsHistorical$: Observable<QueryMetricsResponse>;

  // Listing LMI Score
  //  Listing score, raw scores
  listingScore: QueryMetricsResponseDelta;
  listingScoreStartedAt$: Observable<QueryMetricsResponse>;
  listingScoreHistorical$: Observable<QueryMetricsResponse>;
  //  Listing score, sources and quality - doesn't get the score back
  listingScoreSourceAndQuality: QueryMetricsResponseDelta;
  listingScoreSourceAndQualityStartedAt$: Observable<QueryMetricsResponse>;
  listingScoreSourceAndQualityHistorical$: Observable<QueryMetricsResponse>;

  private subscriptions = [] as Subscription[];

  constructor(
    private accountGroupService: AccountGroupService,
    private analyticsService: AnalyticsApiService,
    private facebookPerformanceService: FacebookPerformanceService,
    private twitterPerformanceService: TwitterPerformanceService,
    private tiktokPerformanceService: TiktokPerformanceService,
    private gmbPerformanceService: GoogleMyBusinessPerformanceService,
    private linkedinPerformanceService: LinkedinPerformanceService,
    private instagramPerformanceService: InstagramPerformanceService,
    private allNetworksPerformanceService: AllNetworksPerformanceService,
    private allNetworksPerformanceServiceV3: AllNetworksPerformanceServiceV3,
    private socialPostsService: SocialPostsService,
    private timeRangeService: TimeRangeService,
    private multiLocationAnalyticsService: MultiLocationAnalyticsService,
  ) {
    this.accountGroupDatePickerLatest$ = combineLatest([
      this.accountGroupService.currentAccountGroup$,
      this.timeRangeService.dateRange$,
    ]).pipe(
      filter(([accountGroup]) => accountGroup !== null),
      tap(() => {
        this.resetRangeSpecificValues();
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.setUpBingStreams();
    this.setUpBingEngAndLeadStreams();
    this.setUpBingInsightsStreams();
    this.setUpGMBInsightsStreams();
    this.setUpMultiListingsStreams();
    this.setUpReputationReviewStreams();
    this.setUpSocialMarketingStreams();
    this.setUpConstantContactStreams();
    this.setUpAdvertisingIntelligenceStreams();
    this.setUpListingScoreStreams();
    this.setUpGoogleInsightsSearchKeywordsStreams();

    // Listen for changes to the data streams
    this.subscriptions.push(
      this.accountGroupDatePickerLatest$.pipe(distinctUntilChanged()).subscribe({
        next: () => {
          this.accountDataChanged$$.next('changed');
        },
      }),
    );
  }

  resetRangeSpecificValues(): void {
    this.gmbLimitHistoricalTo30Days$$.next(false);
  }

  /*******
   * Bing places Engagement and Leads
   ******/

  private setUpBingEngAndLeadStreams(): void {
    this.bingWebsiteClicksTotals = this.fetchMetric((accountGroup, startDate, endDate, doPreviousPeriod) =>
      this.buildBingInteractionQueryRequest(
        accountGroup.accountGroupId,
        startDate,
        endDate,
        doPreviousPeriod,
        false,
        true,
      ),
    );

    this.bingWebsiteClicksPlacesKeywords$ = combineLatest([
      this.accountGroupService.currentAccountGroup$,
      this.timeRangeService.dateRange$,
    ]).pipe(
      filter(([accountGroup]) => !!accountGroup),
      switchMap(([accountGroup, [startDate, endDate]]) => {
        const request = this.buildBingInteractionQueryRequest(
          accountGroup.accountGroupId,
          startDate,
          endDate,
          false,
          false,
          false,
        );
        return this.analyticsService.queryMetrics(request).pipe(
          catchError(() => of(null as QueryMetricsResponse)),
          startWith(null as QueryMetricsResponse),
        );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private buildBingInteractionQueryRequest(
    accountGroupId: string,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    limited?: boolean,
    isFunnelMetrics?: boolean,
  ): QueryMetricsRequest {
    if (doPreviousPeriod) {
      [startDate, endDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
    }

    let alignmentPeriod: AlignmentPeriod;
    if (isFunnelMetrics) {
      alignmentPeriod = getAlignmentFunnel(startDate, endDate);
    } else {
      alignmentPeriod = new AlignmentPeriod({
        calendar: limited ? AlignmentPeriodCalendar.CALENDAR_DAY : AlignmentPeriodCalendar.CALENDAR_MONTH,
      });
    }

    const measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'website_clicks',
          aggOp: MeasureAggregateOperator.SUM,
        }),
      }),
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'phone_clicks',
          aggOp: MeasureAggregateOperator.SUM,
        }),
      }),
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'direction_clicks',
          aggOp: MeasureAggregateOperator.SUM,
        }),
      }),
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'photo_clicks',
          aggOp: MeasureAggregateOperator.SUM,
        }),
      }),
    ];

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'bing_insights_interaction',
      resourceIds: getResourceIds(accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures,
      groupBy: new GroupBy({ dimension: [{ dimension: 'start_date' }] }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod,
    });
  }

  /*******
   * Bing places Impressions
   ******/

  private setUpBingStreams(): void {
    this.bingViewsTotals = this.fetchMetric((accountGroup, startDate, endDate, doPreviousPeriod) =>
      this.buildBingInsightsQueryRequest(accountGroup.accountGroupId, startDate, endDate, doPreviousPeriod),
    );

    this.bingViewsPlacesKeywords$ = combineLatest([
      this.accountGroupService.currentAccountGroup$,
      this.timeRangeService.dateRange$,
    ]).pipe(
      filter(([accountGroup]) => !!accountGroup),
      switchMap(([accountGroup, [startDate, endDate]]) => {
        const request = this.buildBingInsightsQueryRequest(accountGroup.accountGroupId, startDate, endDate);
        return this.analyticsService.queryMetrics(request).pipe(
          catchError(() => of(null as QueryMetricsResponse)),
          startWith(null as QueryMetricsResponse),
        );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private buildBingInsightsQueryRequest(
    accountGroupId: string,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    limited?: boolean,
    isFunnelMetrics?: boolean,
  ): QueryMetricsRequest {
    if (doPreviousPeriod) {
      [startDate, endDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
    }
    let alignmentPeriod: AlignmentPeriod;
    if (isFunnelMetrics) {
      alignmentPeriod = getAlignmentFunnel(startDate, endDate);
    } else {
      alignmentPeriod = new AlignmentPeriod({
        calendar: limited ? AlignmentPeriodCalendar.CALENDAR_DAY : AlignmentPeriodCalendar.CALENDAR_MONTH,
      });
    }

    const measures = BING_INSIGHTS_VIEW_STATS.map(
      (stat) =>
        new Measure({
          aggregate: new MeasureAggregate({ measure: stat, aggOp: MeasureAggregateOperator.SUM }),
        }),
    );

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'bing_insights_view',
      resourceIds: getResourceIds(accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures,
      groupBy: new GroupBy({ dimension: [{ dimension: 'start_date' }] }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod,
    });
  }

  private setUpBingInsightsStreams(): void {
    this.bingHistoricalInsightsViewCard$ = combineLatest([
      this.accountGroupDatePickerLatest$,
      this.gmbLimitHistoricalTo30Days$,
    ]).pipe(
      switchMap(([[accountGroup, currentReportDate], limitThirtyDays]) => {
        // If we are not limiting to thirty, days check if we are going to be showing a partial month
        //  If we are showing a partial month, go back to the end of the preivous month instead as we
        //  do not want to show a drop off since it wouldn't contain a full months worth of data.

        let endDateToUse = new Date(currentReportDate[1]);
        let startDate = dayjs(currentReportDate[1]).subtract(12, 'months').toDate();
        if (!limitThirtyDays) {
          const endOfMonth = dayjs
            .utc(dayjs(currentReportDate[1].getTime()).format('YYYY-MM-DD'))
            .endOf('month')
            .set('hours', 23)
            .set('minutes', 59);
          if (dayjs.utc(currentReportDate[1]).isBefore(endOfMonth)) {
            endDateToUse = dayjs(currentReportDate[1]).subtract(1, 'month').endOf('month').toDate();
          }
        } else {
          startDate = dayjs(currentReportDate[1]).subtract(30, 'days').toDate();
        }

        return this.analyticsService
          .queryMetrics(
            this.buildBingInsightsQueryRequest(
              accountGroup.accountGroupId,
              startDate,
              endDateToUse,
              false,
              limitThirtyDays,
              false,
            ),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.bingHistoricalInsightsActionsCard$ = combineLatest([
      this.accountGroupDatePickerLatest$,
      this.gmbLimitHistoricalTo30Days$,
    ]).pipe(
      switchMap(([[accountGroup, currentReportDate], limitThirtyDays]) => {
        // If we are not limiting to thirty, days check if we are going to be showing a partial month
        //  If we are showing a partial month, go back to the end of the preivous month instead as we
        //  do not want to show a drop off since it wouldn't contain a full months worth of data.

        let endDateToUse = new Date(currentReportDate[1]);
        let startDate = dayjs(currentReportDate[1]).subtract(12, 'months').toDate();
        if (!limitThirtyDays) {
          const endOfMonth = dayjs
            .utc(dayjs(currentReportDate[1].getTime()).format('YYYY-MM-DD'))
            .endOf('month')
            .set('hours', 23)
            .set('minutes', 59);
          if (dayjs.utc(currentReportDate[1]).isBefore(endOfMonth)) {
            endDateToUse = dayjs(currentReportDate[1]).subtract(1, 'month').endOf('month').toDate();
          }
        } else {
          startDate = dayjs(currentReportDate[1]).subtract(30, 'days').toDate();
        }

        return this.analyticsService
          .queryMetrics(
            this.buildBingInteractionQueryRequest(
              accountGroup.accountGroupId,
              startDate,
              endDateToUse,
              false,
              limitThirtyDays,
              false,
            ),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  /*******
   * Google My Business
   ******/

  private setUpGMBInsightsStreams(): void {
    this.gmbInsights = this.fetchMetric(this.buildInsightsRequest);
    this.gmbHistoricalInsights$ = combineLatest([
      this.accountGroupDatePickerLatest$,
      this.gmbLimitHistoricalTo30Days$,
    ]).pipe(
      switchMap(([[accountGroup, currentReportDate], limitThirtyDays]) => {
        // If we are not limiting to thirty, days check if we are going to be showing a partial month
        //  If we are showing a partial month, go back to the end of the preivous month instead as we
        //  do not want to show a drop off since it wouldn't contain a full months worth of data.

        let endDateToUse = new Date(currentReportDate[1]);
        let startDate = dayjs(currentReportDate[1]).subtract(12, 'months').toDate();
        if (!limitThirtyDays) {
          const endOfMonth = dayjs
            .utc(dayjs(currentReportDate[1].getTime()).format('YYYY-MM-DD'))
            .endOf('month')
            .set('hours', 23)
            .set('minutes', 59);
          if (dayjs.utc(currentReportDate[1]).isBefore(endOfMonth)) {
            endDateToUse = dayjs(currentReportDate[1]).subtract(1, 'month').endOf('month').toDate();
          }
        } else {
          startDate = dayjs(currentReportDate[1]).subtract(30, 'days').toDate();
        }

        return this.analyticsService
          .queryMetrics(this.buildInsightsRequest(accountGroup, startDate, endDateToUse, false, limitThirtyDays))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.gmbHistoricalInsightsWithinRange$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildInsightsRequest(accountGroup, currentReportDate[0], currentReportDate[1], false, true, true),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  setGMBHistoricalLimitToThirtyDays(limit: boolean): void {
    this.gmbLimitHistoricalTo30Days$$.next(limit);
  }

  isLimitingTo30Days(): boolean {
    return this.gmbLimitHistoricalTo30Days$$.getValue();
  }

  private buildInsightsRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    limited?: boolean,
    isFunnelMetrics?: boolean,
  ): QueryMetricsRequest {
    let gmbEndDate;
    let alignmentPeriod;
    if (doPreviousPeriod) {
      [startDate, gmbEndDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
    } else {
      gmbEndDate = GMBPeriodMoment(dayjs(endDate)).toDate();
    }
    if (isFunnelMetrics) {
      alignmentPeriod = getAlignmentFunnel(startDate, endDate);
    } else {
      alignmentPeriod = new AlignmentPeriod({
        calendar: limited ? AlignmentPeriodCalendar.CALENDAR_DAY : AlignmentPeriodCalendar.CALENDAR_MONTH,
      });
    }

    // Caution updating this array of measures as the order needs to stay in sync with the order of multiple items
    // defined in gmb-cards.service.ts
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'gmb_insights',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: gmbEndDate }),
      // TODO: Provide this with the updated proto field. moment's end of day used above
      // TODO: results in a datetime that you could say you want to be inclusive about
      // TODO: or you could switch the above to provide the exclusive boundary
      // timeRange: inclusiveInclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'views_maps',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'views_search',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'actions_website',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'actions_phone',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'actions_driving_directions',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'actions_bookings',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'actions_conversations',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'actions_food_orders',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [{ dimension: 'start_time' }],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod,
    });
  }

  private setUpGoogleInsightsSearchKeywordsStreams(): void {
    this.gmbSearchKeywordsTotals = this.fetchMetric(this.buildGmbSearchKeywordTotalsRequest);
    this.gmbSearchKeywordsData$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(this.buildGmbSearchKeywordRequest(accountGroup, currentReportDate[0], currentReportDate[1]))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.gmbHistoricalSearchKeywords$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildGmbSearchKeywordTotalsRequest(
              accountGroup,
              currentReportDate[0],
              currentReportDate[1],
              false,
              true,
              true,
            ),
          )
          .pipe(
            startWith(null),
            map((r) => {
              if (r) {
                const { monthDiff, dayDiff } = getMonthDiffFromDayJSRange(currentReportDate[0], currentReportDate[1]);
                r.metricResults = spreadMetricsAverageByDays(r.metricResults, monthDiff, dayDiff);
              }
              return r;
            }),
          );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private buildGmbSearchKeywordRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    cursor?: string,
  ): QueryMetricsRequest {
    const measures = [
      new Measure({
        aggregate: new MeasureAggregate({
          measure: 'value',
          aggOp: MeasureAggregateOperator.SUM,
        }),
      }),
    ];

    const start = moment(startDate).utc().date(1);

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'gmb_search_keywords',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({
        start: start.toDate(),
        end: endDate,
      }),
      measures: measures,
      groupBy: new GroupBy({
        dimension: [{ dimension: 'keyword' }],
      }),
      alignment: Alignment.ALIGN_NONE,
      alignmentPeriod: new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_DAY,
      }),
      orderBy: new OrderBy({
        orderBy: [
          {
            column: 'sum_value',
            order: 1,
          },
        ],
      }),
      cursor,
    });
  }

  private buildGmbSearchKeywordTotalsRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    limited?: boolean,
    isFunnelMetrics?: boolean,
  ): QueryMetricsRequest {
    let alignmentPeriod: AlignmentPeriod;
    if (doPreviousPeriod) {
      [startDate, endDate] = getGBPKeywordsPreviousPeriod(startDate, endDate);
    }
    if (isFunnelMetrics) {
      alignmentPeriod = getAlignmentFunnel(startDate, endDate);
    } else {
      alignmentPeriod = new AlignmentPeriod({
        calendar: limited ? AlignmentPeriodCalendar.CALENDAR_DAY : AlignmentPeriodCalendar.CALENDAR_MONTH,
      });
    }

    const start = moment(startDate).utc().date(1);

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'gmb_search_keywords',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({
        start: start.toDate(),
        end: endDate,
      }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'value',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [{ dimension: 'month_date' }],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod: alignmentPeriod,
    });
  }

  parseSearchKeywordsFromQueryMetricsResponse(resp: QueryMetricsResponse): GoogleInsightSearchKeyword[] {
    if (!resp.metricResults?.[0].metrics?.metrics) {
      return [];
    }

    const googleInsightSearchKeywords: GoogleInsightSearchKeyword[] =
      resp.metricResults[0].metrics.metrics.map((metricResult) => {
        let value = 0;
        if (metricResult.measures.length > 0) {
          value = Number(metricResult.measures[0]);
        }
        return {
          value: value,
          keyword: metricResult.dimension,
        };
      }) || [];

    return googleInsightSearchKeywords?.sort((a, b) => (a.value > b.value ? -1 : 1));
  }

  /***********
   * Multi Listings
   **********/
  private setUpMultiListingsStreams(): void {
    this.listingSyncProStatus$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(this.buildListingSyncProRequest(accountGroup, currentReportDate[0], currentReportDate[1]))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.listingsData$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(this.buildListingsRequest(accountGroup, currentReportDate[0], currentReportDate[1]))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private buildListingSyncProRequest(accountGroup: AccountGroup, startDate: Date, endDate: Date): QueryMetricsRequest {
    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'listing_sync_status',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        {
          aggregate: new MeasureAggregate({
            measure: 'status',
            aggOp: MeasureAggregateOperator.MAX, // We only expect one row to be aggregated, the latest
          }),
        },
        {
          aggregate: new MeasureAggregate({
            measure: 'provider',
            aggOp: MeasureAggregateOperator.MAX, // We only expect one row to be aggregated, the latest
          }),
        },
      ],
      groupBy: new GroupBy({
        dimension: [
          { dimension: 'source' },
          new GroupByDimension({
            limitDimension: {
              dimension: 'created',
              order: Order.ORDER_DESC,
              limit: 1,
            },
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
    });
  }

  private buildListingsRequest(
    accountGroup: AccountGroup,
    _startDate: Date,
    _endDate: Date,
    _doPreviousPeriod?: boolean,
    _limited?: boolean,
  ): QueryMetricsRequest {
    // Gets the all time data
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'listings',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      filter: bestMatchFilter,
      measures: [
        new Measure({ measure: 'source_id' }),
        new Measure({ measure: 'score' }),
        new Measure({ measure: 'match_quality' }),
      ],
    });
  }

  /***********
   * Reputation Reviews
   **********/
  private setUpReputationReviewStreams(): void {
    this.repReviewCount = this.fetchMetric(
      (accountGroup: AccountGroup, startDate: Date, endDate: Date, doPreviousPeriod?: boolean, limited?: boolean) => {
        return this.buildRepReviewCountRequest(accountGroup, startDate, endDate, doPreviousPeriod, limited);
      },
    );

    this.repNPSVolumeCount = this.fetchMetric(
      (accountGroup: AccountGroup, startDate: Date, endDate: Date, doPreviousPeriod?: boolean, limited?: boolean) => {
        return this.buildRepNPSVolumeCountRequest(accountGroup, startDate, endDate, doPreviousPeriod, limited);
      },
    );
    this.repTeamScoreCardVolumeCount = (providerId: string) =>
      this.fetchMetric(
        (accountGroup: AccountGroup, startDate: Date, endDate: Date, doPreviousPeriod?: boolean, limited?: boolean) => {
          return this.buildRepTeamScoreCardVolumeCountRequest(
            accountGroup,
            startDate,
            endDate,
            doPreviousPeriod,
            limited,
            providerId,
          );
        },
      );
    this.repReviewCountHistorical$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(this.buildRepReviewsHistoricalRequest(accountGroup, currentReportDate[0], currentReportDate[1]))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.repNPSVolumeHistorical$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildRepNPSVolumeHistoricalRequest(accountGroup, currentReportDate[0], currentReportDate[1]),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.repTeamScoreCardVolumeHistorical$ = (providerId: string) =>
      this.accountGroupDatePickerLatest$.pipe(
        switchMap(([accountGroup, currentReportDate]) => {
          return this.analyticsService
            .queryMetrics(
              this.buildRepTeamScoreCardVolumeHistoricalRequest(
                accountGroup,
                currentReportDate[0],
                currentReportDate[1],
                providerId,
              ),
            )
            .pipe(startWith(null as QueryMetricsResponse));
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );
    this.repReviewCountHistoricalWithinRange$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildRepReviewsHistoricalRequest(accountGroup, currentReportDate[0], currentReportDate[1], true),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.repReviewRating = this.fetchMetric(this.buildRepReviewRatingRequest, false, true);
    this.repReviewHistoricalRating$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildRepReviewRatingRequest(accountGroup, currentReportDate[0], currentReportDate[1], false, true),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private buildRepReviewCountRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    _limited?: boolean,
  ): QueryMetricsRequest {
    if (doPreviousPeriod) {
      [startDate, endDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
    }
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'reviews',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [],
      groupBy: new GroupBy({
        dimension: [
          {
            dimension: 'review_id',
          },
        ],
      }),
      filter: new Filter({
        unaryFilter: new UnaryFilter({
          dimension: 'deleted',
          op: UnaryFilterOperator.IS_NULL,
        }),
      }),
    });
  }

  private buildRepReviewsHistoricalRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    isFunnelMetrics?: boolean,
  ): QueryMetricsRequest {
    let alignmentPeriod: AlignmentPeriod;
    if (isFunnelMetrics) {
      alignmentPeriod = getAlignmentFunnel(startDate, endDate);
    } else {
      startDate = dayjs(startDate).subtract(6, 'months').toDate();
      [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
      [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
      alignmentPeriod = new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_WEEK,
      });
    }

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'reviews',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          {
            dimension: 'published',
          },
        ],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod,
      filter: new Filter({
        unaryFilter: new UnaryFilter({
          dimension: 'deleted',
          op: UnaryFilterOperator.IS_NULL,
        }),
      }),
    });
  }

  private buildRepTeamScoreCardVolumeCountRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    _limited?: boolean,
    provider_id?: string,
  ): QueryMetricsRequest {
    if (doPreviousPeriod) {
      [startDate, endDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
    }

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'multi_location_team_score_details',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'nps_volume',
          }),
        }),
      ],
      filter: new Filter({
        compositeFilter: {
          op: CompositeFilterOperator.AND,
          filters: [
            new Filter({
              fieldFilter: new FieldFilter({
                dimension: 'provider_id',
                operator: FieldFilterOperator.EQUAL,
                value: new TypedValue({
                  value: provider_id,
                }),
              }),
            }),
          ],
        },
      }),
    });
  }

  private buildRepNPSVolumeCountRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    _limited?: boolean,
  ): QueryMetricsRequest {
    if (doPreviousPeriod) {
      [startDate, endDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
    }
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'net_promoter_score',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'nps_volume',
          }),
        }),
      ],
    });
  }

  private buildRepNPSVolumeHistoricalRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
  ): QueryMetricsRequest {
    startDate = dayjs(startDate).subtract(6, 'months').toDate();
    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
    const alignmentPeriod = new AlignmentPeriod({
      calendar: AlignmentPeriodCalendar.CALENDAR_WEEK,
    });

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'net_promoter_score',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          {
            dimension: 'score_left_time',
          },
        ],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod,
    });
  }

  private buildRepTeamScoreCardVolumeHistoricalRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    provider_id?: string,
  ): QueryMetricsRequest {
    startDate = dayjs(startDate).subtract(6, 'months').toDate();
    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
    const alignmentPeriod = new AlignmentPeriod({
      calendar: AlignmentPeriodCalendar.CALENDAR_WEEK,
    });

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'multi_location_team_score_details',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          {
            dimension: 'score_date',
          },
        ],
      }),
      filter: provider_id
        ? new Filter({
            fieldFilter: new FieldFilter({
              dimension: 'provider_id',
              operator: FieldFilterOperator.EQUAL,
              value: new TypedValue({
                value: provider_id || '',
              }),
            }),
          })
        : undefined,
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod,
    });
  }

  private buildRepReviewRatingRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    limited?: boolean,
    ignoreStartDate?: boolean,
  ): QueryMetricsRequest {
    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
    if (doPreviousPeriod) {
      [startDate, endDate] = previousPeriod([startDate, endDate]);
    }
    const request = new QueryMetricsRequest({
      partnerId,
      metricName: 'reviews',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({
        start: ignoreStartDate ? null : startDate,
        end: endDate,
      }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_one_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '1',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_two_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '2',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_three_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '3',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_four_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '4',
                },
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            alias: 'rating_five_star',
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
            filter: new Filter({
              fieldFilter: {
                dimension: 'rating',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: '5',
                },
              },
            }),
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [],
      }),
      filter: new Filter({
        unaryFilter: new UnaryFilter({
          dimension: 'deleted',
          op: UnaryFilterOperator.IS_NULL,
        }),
      }),
    });
    // If limited, we are trying to get the trendline for ratings
    if (limited) {
      startDate = dayjs(startDate).subtract(6, 'months').toDate();
      [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
      request.dateRange = new DateRange({ start: startDate, end: endDate });
      request.groupBy = new GroupBy({
        dimension: [{ dimension: 'published' }],
      });
      request.alignment = Alignment.ALIGN_DELTA;
      request.alignmentPeriod = new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_WEEK,
      });
    }
    return request;
  }

  /***********
   * Social Marketing
   **********/
  private setUpSocialMarketingStreams(): void {
    this.smCardStats_V2$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        const agid = accountGroup.accountGroupId;
        const startDate = new Date(currentReportDate[0].getTime());
        const endDate = new Date(currentReportDate[1].getTime());
        return this.allNetworksPerformanceServiceV3.getAllBrandPostStatsData(agid, startDate, endDate);
      }),
    );

    this.smStats$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        const agid = accountGroup.accountGroupId;
        const startDate = currentReportDate[0];
        const endDate = currentReportDate[1];
        return this.allNetworksPerformanceService.getBrandPostStatsData(agid, startDate, endDate);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.smTopPosts$ = this.accountGroupDatePickerLatest$.pipe(
      filter((x) => !!x),
      switchMap(([accountGroup, currentReportDate]) => {
        const agid = accountGroup.accountGroupId;
        return combineLatest([
          of(agid),
          this.facebookPerformanceService.getTopPosts([agid], currentReportDate[0], currentReportDate[1]),
          this.gmbPerformanceService.getTopPosts([agid], currentReportDate[0], currentReportDate[1]),
          this.instagramPerformanceService.getTopPosts([agid], currentReportDate[0], currentReportDate[1]),
          this.linkedinPerformanceService.getTopPosts([agid], currentReportDate[0], currentReportDate[1]),
          this.twitterPerformanceService.getTopPosts([agid], currentReportDate[0], currentReportDate[1]),
          this.tiktokPerformanceService.getTopPosts([agid], currentReportDate[0], currentReportDate[1]),
        ]);
      }),
      map(([agid, fbPosts, gmbPosts, igPosts, liPosts, twPosts, tktPosts]) => {
        const allNetworkStats = this.compileRawPostStats(fbPosts, SocialService.FACEBOOK);
        allNetworkStats.push(...this.compileRawPostStats(gmbPosts, SocialService.GOOGLE_MY_BUSINESS));
        allNetworkStats.push(...this.compileRawPostStats(igPosts, SocialService.INSTAGRAM));
        allNetworkStats.push(...this.compileRawPostStats(liPosts, SocialService.LINKEDIN));
        allNetworkStats.push(...this.compileRawPostStats(twPosts, SocialService.TWITTER));
        allNetworkStats.push(...this.compileRawPostStats(tktPosts, SocialService.TIKTOK));
        return [agid, allNetworkStats];
      }),
      switchMap(([agid, allPostStats]: [string, AllNetworksStats[]]) => {
        allPostStats = allPostStats
          .sort((s1, s2) => {
            let sortValue1 = s1['reach'];
            let sortValue2 = s2['reach'];
            if (sortValue1 === sortValue2) {
              sortValue1 = s1['engagement'];
              sortValue2 = s2['engagement'];
            }
            return sortValue1 > sortValue2 ? -1 : 1;
          })
          .slice(0, 4);
        const postMap = [] as AllNetworksStats[];
        let topReach = 0;
        allPostStats.forEach((post) => {
          topReach = topReach < post.reach ? post.reach : topReach;
          postMap.push(post);
        });

        const postIds = allPostStats.map((stat) => stat.postId) as string[];
        if (!postIds || postIds.length === 0) {
          return of([]);
        }
        return this.socialPostsService.getMultiSocialPosts(agid, postIds).pipe(
          map((posts) => {
            if (posts.socialPosts) {
              return posts.socialPosts.map((post) => {
                const postDimensions = postMap.find((ele) => ele.postId === post.socialPostId);
                return {
                  image: post.imageUrls && post.imageUrls.length ? post.imageUrls[0] : post.imageUrl,
                  text: post.postText,
                  posted: post.posted.toString(),
                  engagement: postDimensions.engagement,
                  peopleReached: postDimensions.reach,
                  topReach: topReach,
                  deletedPost: false,
                  service: postDimensions.service,
                  serviceName: SocialServiceName.FACEBOOK,
                  serviceIcon: this.serviceIconPath(postDimensions.service),
                } as TopPost;
              });
            } else {
              return [] as TopPost[];
            }
          }),
        );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  /***********
   * Constant Contact
   **********/
  private setUpConstantContactStreams(): void {
    this.emailEngagementTrend$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(this.buildConstantContactTrendline(accountGroup, currentReportDate[0], currentReportDate[1]))
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.emailEngagementStats = this.fetchMetric(this.buildConstantContactStatsRequest);
    this.emailEngagementStatsWithinRange$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildConstantContactStatsRequest(
              accountGroup,
              currentReportDate[0],
              currentReportDate[1],
              false,
              true,
            ),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.activeCampaignStats$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildConstantContactCampaignStatsRequest(accountGroup, currentReportDate[0], currentReportDate[1]),
          )
          .pipe(
            map((campaignsResponse) => {
              if (typeof campaignsResponse.metricResults[0].metrics.metrics === 'undefined') {
                return null;
              }

              const campaignIds: string[] = [];
              campaignsResponse.metricResults[0].metrics.metrics.forEach((metric) => {
                if (
                  metric.measures[CONSTANT_CONTACT_METRIC.SENT] > 0 ||
                  metric.measures[CONSTANT_CONTACT_METRIC.OPEN] > 0 ||
                  metric.measures[CONSTANT_CONTACT_METRIC.CLICK] > 0
                ) {
                  campaignIds.push(String(metric.dimension));
                }
              });
              let campaignFilter = null;
              if (campaignIds.length > 0) {
                campaignFilter = new Filter({
                  compositeFilter: {
                    op: CompositeFilterOperator.OR,
                    filters: campaignIds.map((campaignId) => {
                      return {
                        fieldFilter: {
                          dimension: 'campaign_id',
                          operator: FieldFilterOperator.EQUAL,
                          value: {
                            value: campaignId,
                            valueType: PropertyType.PROPERTY_TYPE_STRING,
                          },
                        },
                      };
                    }),
                  },
                });
              }
              return campaignFilter;
            }),
            switchMap((campaignFilter) => {
              return this.analyticsService.queryMetrics(
                this.buildConstantContactCampaignStatsRequest(
                  accountGroup,
                  dayjs(currentReportDate[0]).subtract(6, 'months').toDate(),
                  currentReportDate[1],
                  campaignFilter,
                ),
              );
            }),
          );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private buildConstantContactTrendline(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
  ): QueryMetricsRequest {
    startDate = dayjs(startDate).subtract(12, 'months').toDate();
    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'constant_contact_campaigns',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'open_count',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'click_count',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [{ dimension: 'status_timestamp' }],
      }),
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod: new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_MONTH,
      }),
    });
  }

  private buildConstantContactStatsRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    isFunnelMetrics?: boolean,
  ): QueryMetricsRequest {
    let groupBy: GroupBy | undefined;
    let alignmentPeriod: AlignmentPeriod;
    if (isFunnelMetrics) {
      groupBy = new GroupBy({
        dimension: [{ dimension: 'status_timestamp' }],
      });
      alignmentPeriod = getAlignmentFunnel(startDate, endDate);
    } else {
      [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
      [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
      if (doPreviousPeriod) {
        [startDate, endDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
      }
      alignmentPeriod = new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_DAY,
      });
    }

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'constant_contact_campaigns',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'open_count',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'click_count',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      groupBy,
      alignment: Alignment.ALIGN_DELTA,
      alignmentPeriod,
    });
  }

  private buildConstantContactCampaignStatsRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    campaignFilter?: Filter,
  ): QueryMetricsRequest {
    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'constant_contact_campaigns',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'send_count',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'open_count',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'click_count',
            aggOp: MeasureAggregateOperator.SUM,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [{ dimension: 'campaign_id' }],
      }),
      filter: campaignFilter,
    });
  }

  /***********
   * Advertising Intelligence
   **********/
  private setUpAdvertisingIntelligenceStreams(): void {
    this.adIntelStats = this.fetchMetric(
      this.buildAdIntelStatsRequest(false, [
        'Adwords',
        'Facebook_ads',
        'LocalAds',
        'Microsoft_ads',
        'Simplifi',
        'TikTok',
        'Amazon',
        'Linkedin',
        'Gam',
      ]),
    );
    this.adIntelStatsForFunnel = this.fetchMetric(
      this.buildAdIntelStatsRequest(true, [
        'Adwords',
        'Facebook_ads',
        'LocalAds',
        'Microsoft_ads',
        'Simplifi',
        'TikTok',
        'Amazon',
        'Linkedin',
        'Gam',
      ]),
    );
    this.adIntelPhoneStats = this.fetchMetric(this.buildAdIntelStatsRequest(true, ['Phone']));
    this.adIntelStatsHistorical$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildAdIntelStatsRequest(false)(
              accountGroup,
              dayjs(currentReportDate[0]).subtract(12, 'months').toDate(),
              currentReportDate[1],
              false,
              true,
            ),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.adIntelStatsHistoricalWithinRange$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildAdIntelStatsRequest(true)(accountGroup, currentReportDate[0], currentReportDate[1], false, true),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.adintelROIStats = this.fetchMetric(this.buildAdIntelROIStatsRequest);
    this.adintelROIStatsHistorical$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildAdIntelROIStatsRequest(accountGroup, currentReportDate[0], currentReportDate[1], false, true),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private buildAdIntelStatsRequest(
    isFunnelMetrics: boolean,
    channelTypeFilters?: string[],
  ): (
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    limited?: boolean,
  ) => QueryMetricsRequest {
    return function (
      accountGroup: AccountGroup,
      startDate: Date,
      endDate: Date,
      doPreviousPeriod?: boolean,
      limited?: boolean,
    ): QueryMetricsRequest {
      let groupByRequest;
      let alignment;
      let channelFilter: Filter;
      let alignmentPeriod = new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_DAY,
      });
      if (limited) {
        groupByRequest = new GroupBy({
          dimension: [{ dimension: 'channel_type' }, { dimension: 'time_stamp' }],
        });
        alignment = Alignment.ALIGN_DELTA;
        if (isFunnelMetrics) {
          alignmentPeriod = getAlignmentFunnel(startDate, endDate);
        } else {
          if (dayjs(endDate).diff(startDate, 'days') <= 90) {
            alignmentPeriod = new AlignmentPeriod({
              calendar: AlignmentPeriodCalendar.CALENDAR_DAY,
            });
          } else {
            alignmentPeriod = new AlignmentPeriod({
              calendar: AlignmentPeriodCalendar.CALENDAR_WEEK,
            });
          }
        }
      }
      if (channelTypeFilters) {
        channelFilter = new Filter({
          fieldFilter: new FieldFilter({
            dimension: 'channel_type',
            operator: FieldFilterOperator.EQUAL,
            operatorFunction: FieldFilterOperatorFunction.ANY,
            value: new TypedValue({ valueType: PropertyType.PROPERTY_TYPE_STRING, value: channelTypeFilters }),
          }),
        });
      }

      if (doPreviousPeriod) {
        [startDate, endDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
      }
      return new QueryMetricsRequest({
        partnerId,
        metricName: 'advertising_intel_channel_stats',
        resourceIds: getResourceIds(accountGroup.accountGroupId),
        dateRange: new DateRange({ start: startDate, end: endDate }),
        filter: channelFilter,
        measures: ['impressions', 'clicks', 'conversions'].map(
          (measure) =>
            new Measure({
              aggregate: new MeasureAggregate({
                measure,
                aggOp: MeasureAggregateOperator.SUM,
              }),
            }),
        ),
        groupBy: groupByRequest,
        alignment,
        alignmentPeriod,
      });
    };
  }

  private buildAdIntelROIStatsRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    limited?: boolean,
  ): QueryMetricsRequest {
    let groupByRequest;
    let alignment;
    let alignmentPeriod;
    if (limited) {
      startDate = dayjs(startDate).subtract(12, 'months').toDate();
      groupByRequest = new GroupBy({
        dimension: [{ dimension: 'channel_type' }, { dimension: 'time_stamp' }],
      });
      alignment = Alignment.ALIGN_DELTA;
      alignmentPeriod = new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_WEEK,
      });
    }

    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
    if (doPreviousPeriod) {
      [startDate, endDate] = getPreviousPeriod(startDate, endDate, viewingMonthlyExecReport());
    }
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'advertising_intel_channel_stats',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: ['spend_micros', 'conversions'].map(
        (measure) =>
          new Measure({
            aggregate: new MeasureAggregate({
              measure,
              aggOp: MeasureAggregateOperator.SUM,
            }),
          }),
      ),
      groupBy: groupByRequest,
      alignment,
      alignmentPeriod,
    });
  }

  /***********
   * Listing Score
   **********/
  private setUpListingScoreStreams(): void {
    this.listingScore = this.fetchMetric(this.buildListingScoreRequest);
    this.listingScoreStartedAt$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildListingScoreRequest(accountGroup, currentReportDate[0], currentReportDate[1], false, false, true),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.listingScoreHistorical$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildListingScoreRequest(accountGroup, currentReportDate[0], currentReportDate[1], false, true),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.listingScoreSourceAndQuality = this.fetchMetric(this.buildListingScoreSourceAndQualityRequest);
    this.listingScoreSourceAndQualityStartedAt$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildListingScoreSourceAndQualityRequest(
              accountGroup,
              currentReportDate[0],
              currentReportDate[1],
              false,
              false,
              true,
            ),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.listingScoreSourceAndQualityHistorical$ = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            this.buildListingScoreSourceAndQualityRequest(
              accountGroup,
              currentReportDate[0],
              currentReportDate[1],
              false,
              true,
            ),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private buildListingScoreRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    getMulti?: boolean,
    reverse?: boolean,
  ): QueryMetricsRequest {
    let orderBy = Order.ORDER_DESC;
    let customDimension;
    let alignment;
    let alignmentPeriod;

    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
    if (doPreviousPeriod) {
      [startDate, endDate] = previousPeriod([startDate, endDate]);
    }

    if (reverse) {
      orderBy = Order.ORDER_ASC;
    }
    if (!getMulti) {
      customDimension = new GroupByDimension({
        limitDimension: {
          dimension: 'timestamp',
          order: orderBy,
          limit: 1,
        },
      });
    } else {
      customDimension = new GroupByDimension({
        dimension: 'timestamp',
      });
      alignment = Alignment.ALIGN_EXACT;
      alignmentPeriod = new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_WEEK,
      });
    }
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'listing_score',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [customDimension],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      alignment,
      alignmentPeriod,
    });
  }

  private buildListingScoreSourceAndQualityRequest(
    accountGroup: AccountGroup,
    startDate: Date,
    endDate: Date,
    doPreviousPeriod?: boolean,
    getMulti?: boolean,
    reverse?: boolean,
  ): QueryMetricsRequest {
    let orderBy = Order.ORDER_DESC;
    let customDimension;
    let alignment;
    let alignmentPeriod;

    [startDate, endDate] = alignStartAndEndDates(startDate, endDate);
    [startDate, endDate] = [alignLocalDateToUTC(startDate), alignLocalDateToUTC(endDate)];
    if (doPreviousPeriod) {
      [startDate, endDate] = previousPeriod([startDate, endDate]);
    }

    if (reverse) {
      orderBy = Order.ORDER_ASC;
    }
    if (!getMulti) {
      customDimension = new GroupByDimension({
        limitDimension: {
          dimension: 'timestamp',
          order: orderBy,
          limit: 1,
        },
      });
    } else {
      customDimension = new GroupByDimension({
        dimension: 'timestamp',
      });
      alignment = Alignment.ALIGN_EXACT;
      alignmentPeriod = new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_WEEK,
      });
    }
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'listing_score',
      resourceIds: getResourceIds(accountGroup.accountGroupId),
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'source_list',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'quality_list',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'timestamp',
            aggOp: MeasureAggregateOperator.MAX,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [customDimension],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      alignment,
      alignmentPeriod,
    });
  }

  private compileRawPostStats(stats: PostStats[], service: SocialService): AllNetworksStats[] {
    const returnStats: AllNetworksStats[] = [];
    returnStats.push(
      ...stats.map((postStats: PostStats) => {
        const allNetworkStats = {
          postId: postStats.postId,
          imageUrl: postStats.imageUrl,
          published: postStats.published,
          businessId: postStats.businessId,
          service: service,
        } as AllNetworksStats;
        switch (service) {
          case SocialService.FACEBOOK: {
            const fbStats = postStats as FacebookPostStats;
            allNetworkStats.engagement = fbStats.reactions + fbStats.shares + fbStats.comments;
            allNetworkStats.reach = fbStats.reach;
            break;
          }
          case SocialService.GOOGLE_MY_BUSINESS: {
            const gmbStats = postStats as GoogleMyBusinessPostStats;
            allNetworkStats.engagement = gmbStats.clicks;
            allNetworkStats.reach = gmbStats.views;
            break;
          }
          case SocialService.INSTAGRAM: {
            const igStats = postStats as InstagramPostStats;
            allNetworkStats.engagement = igStats.like_count + igStats.saves + igStats.comments_count;
            allNetworkStats.reach = igStats.reach;
            break;
          }
          case SocialService.LINKEDIN: {
            const liStats = postStats as LinkedinPostStats;
            allNetworkStats.engagement = liStats.likes + liStats.shares + liStats.clicks + liStats.comments;
            allNetworkStats.reach = liStats.impressions;
            break;
          }
          case SocialService.TWITTER: {
            const twStats = postStats as TwitterPostStats;
            allNetworkStats.engagement = twStats.favourites + twStats.retweets;
            break;
          }
          case SocialService.TIKTOK: {
            const tktStats = postStats as TiktokPostStats;
            allNetworkStats.engagement = tktStats.engagement;
            allNetworkStats.reach = tktStats.reachcount;
            break;
          }
        }
        return allNetworkStats;
      }),
    );
    return returnStats;
  }

  private serviceIconPath(service: string): string {
    // This is to make vstatic work. When vstatic supports assets, remove this
    const prefix = 'https://vstatic-prod.apigateway.co/social-marketing-client/assets/social-icons/';
    switch (service) {
      case SocialServiceName.FACEBOOK:
        return `${prefix}facebook.png`;
      case SocialServiceName.TWITTER:
        return `${prefix}X.png`;
      case SocialServiceName.LINKEDIN:
      case SocialServiceName.LINKEDIN_COMPANY:
        return `${prefix}linkedin.png`;
      case SocialServiceName.INSTAGRAM:
        return `${prefix}instagram.png`;
      case SocialServiceName.TIKTOK:
        return `${prefix}tiktok_round.png`;
      default:
        return '';
    }
  }

  /***********
   * Shared Functions
   **********/
  fetchMetric(requestBuilder: RequestBuilder, limited?: boolean, ignoreStartDate = false): QueryMetricsResponseDelta {
    const current = this.accountGroupDatePickerLatest$.pipe(
      // filter(([, currentReportDate]) => currentReportDate !== null),
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            requestBuilder(accountGroup, currentReportDate[0], currentReportDate[1], false, limited, ignoreStartDate),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const prev = this.accountGroupDatePickerLatest$.pipe(
      switchMap(([accountGroup, currentReportDate]) => {
        return this.analyticsService
          .queryMetrics(
            requestBuilder(accountGroup, currentReportDate[0], currentReportDate[1], true, false, ignoreStartDate),
          )
          .pipe(startWith(null as QueryMetricsResponse));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    return new QueryMetricsResponseDelta(current, prev);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}

function viewingMonthlyExecReport(): boolean {
  return window.location.href.indexOf('/executive-report/monthly') > -1;
}

function getGBPKeywordsPreviousPeriod(startDate, endDate: Date): [Date, Date] {
  let prevRange;
  if (viewingMonthlyExecReport()) {
    prevRange = GBPKeywordPreviousPeriodMoment([dayjs(startDate), dayjs(endDate)]);
  } else {
    prevRange = GBPKeywordPreviousPeriodMoment([dayjs(startDate), dayjs(endDate)]);
  }
  return [prevRange[0].toDate(), prevRange[1].toDate()];
}

// Get the alignment period for the Marketing Funnel
function getAlignmentFunnel(startDate: Date, endDate: Date): AlignmentPeriod {
  const alignmentPeriod = new AlignmentPeriod({
    calendar: AlignmentPeriodCalendar.CALENDAR_DAY,
  });

  if (dayjs(endDate).diff(startDate, 'days') > granularityFunnelMetricsMonthly) {
    alignmentPeriod.calendar = AlignmentPeriodCalendar.CALENDAR_MONTH;
  } else if (dayjs(endDate).diff(startDate, 'days') >= granularityFunnelMetricsWeekly) {
    alignmentPeriod.calendar = AlignmentPeriodCalendar.CALENDAR_WEEK;
  }
  return alignmentPeriod;
}

function getMonthDiffFromDayJSRange(startDate: Date, endDate: Date) {
  const monthDiff = dayjs(endDate).diff(startDate, 'months');
  const dayDiff = dayjs(endDate).diff(startDate, 'days');
  return { monthDiff, dayDiff };
}

// spreadMetricsAverageByDays spreads the total metric value by day for a single month
// using the average value per day on the specified month. This takes in the number of
// month and days of a specific metric.
function spreadMetricsAverageByDays(
  metricResult: ResourceMetricResult[],
  months: number,
  days: number,
): ResourceMetricResult[] {
  if (!metricResult) {
    return metricResult;
  }
  if (months > 1 || metricResult.length < 1) {
    return metricResult;
  }

  const metrics = metricResult[0]?.metrics?.metrics;
  if (!metrics) {
    return metricResult;
  }

  const newMetrics: MetricResult[] = [];

  const counter = metrics[0].measures[0] < days ? metrics[0].measures[0] : days;
  const avgValue = metrics[0].measures[0] / days;
  let date = dayjs(metrics[0].dimension);
  for (let x = 0; x < counter; x++) {
    newMetrics.push(
      new MetricResult({
        dimension: offsetToUTCZero(date).toISOString(),
        measures: [Math.ceil(avgValue)],
        results: {},
      }),
    );

    date = date.add(1, 'day');
  }

  metricResult[0].metrics.metrics = newMetrics;
  return metricResult;
}

export function offsetToUTCZero(date: dayjs.Dayjs): Date {
  const offset = date.utcOffset();
  return dayjs.utc(date).subtract(offset, 'm').toDate();
}
