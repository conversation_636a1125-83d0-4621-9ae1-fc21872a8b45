@if (!noDataFound && isLocalSEOActive) {
  <div class="anchor-link-position" id="bing-places"></div>
  @if (!hideHeader) {
    <h2>{{ 'EXECUTIVE_REPORT.CATEGORY_TITLE.BING_INSIGHTS' | translate }}</h2>
  }
  <div class="new-performance-row">
    @if (viewsMetricsData$ | async) {
      <bc-new-base-card [cardConfig]="viewsMetricsConfig" [dataSource]="viewsMetricsData$ | async"></bc-new-base-card>
    }
    @if (actionsMetricsData$ | async) {
      <bc-new-base-card
        [cardConfig]="actionsMetricsConfig"
        [dataSource]="actionsMetricsData$ | async"
      ></bc-new-base-card>
    }
  </div>
} @else if (isLocalSEOActive && noDataFound) {
  <div class="cta-container">
    <bc-call-to-action-card
      [cardData]="{
        uniqueId: 'cta_bing_connect',
        title: 'EXECUTIVE_REPORT.BING.TITLE' | translate,
        description: 'EXECUTIVE_REPORT.BING.PRESCRIPTION' | translate,
        templateType: 'CALL_TO_ACTION',
        nextUrlLabel: 'Connect now',
        nextUrl: '/edit/account/' + accountGroupId + '/google-insights/',
        width: 'FULL',
      }"
      appId="MS"
      [buildLink]="buildLinkWithAppId"
    ></bc-call-to-action-card>
  </div>
}
