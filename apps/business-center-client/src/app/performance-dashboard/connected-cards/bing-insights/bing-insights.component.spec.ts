import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { BingInsightsComponent } from './bing-insights.component';
import { BingInsightsService } from './bing-insights.service';
import { ProductService } from '../../../core/product.service';
import { AccountGroupService } from '../../../account-group';
import { LocationsService } from '../../../locations';
import { ListingProductsApiService } from '../../../core/listing-products-api.service';
import { FeatureFlagService } from '../../../core/feature-flag.service';

describe('BingInsightsComponent', () => {
  let component: BingInsightsComponent;
  let fixture: ComponentFixture<BingInsightsComponent>;
  let mockBingInsightsService: jest.Mocked<BingInsightsService>;
  let mockProductService: jest.Mocked<ProductService>;
  let mockAccountGroupService: jest.Mocked<AccountGroupService>;
  let mockLocationsService: jest.Mocked<LocationsService>;
  let mockListingProductsService: jest.Mocked<ListingProductsApiService>;
  let mockFeatureFlagService: jest.Mocked<FeatureFlagService>;

  const mockAccountGroup = {
    accountGroupId: 'AG-123',
    name: 'Test Account Group',
  };

  const mockBrandLocation = {
    accountGroupId: 'AG-123',
    groupNodes: ['test-brand'],
    isBrand: true,
  };

  const mockSingleLocation = {
    accountGroupId: 'AG-456',
    isBrand: false,
  };

  const mockProducts = [
    {
      productId: 'MS',
      name: 'Local SEO',
      iconUrl: 'test-icon-url',
    },
  ];

  beforeEach(async () => {
    // Create mocks
    mockBingInsightsService = {
      getViewsMetrics: jest.fn().mockReturnValue(of(null)),
      getActionsMetrics: jest.fn().mockReturnValue(of(null)),
    } as any;

    mockProductService = {
      activeAccessibleProducts$: of(mockProducts),
    } as any;

    mockAccountGroupService = {
      currentAccountGroup$: of(mockAccountGroup),
    } as any;

    mockLocationsService = {
      currentLocation$: of(mockBrandLocation),
    } as any;

    mockListingProductsService = {
      getListingProducts: jest.fn().mockReturnValue(of([])),
    } as any;

    mockFeatureFlagService = {
      isFeatureEnabled: jest.fn().mockReturnValue(of(true)),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [BingInsightsComponent],
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
      ],
      providers: [
        { provide: BingInsightsService, useValue: mockBingInsightsService },
        { provide: ProductService, useValue: mockProductService },
        { provide: AccountGroupService, useValue: mockAccountGroupService },
        { provide: LocationsService, useValue: mockLocationsService },
        { provide: ListingProductsApiService, useValue: mockListingProductsService },
        { provide: FeatureFlagService, useValue: mockFeatureFlagService },
        TranslateService,
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(BingInsightsComponent);
    component = fixture.componentInstance;
    component.startDate = new Date('2025-05-04');
    component.endDate = new Date('2025-06-02');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component on ngOnInit', () => {
    component.ngOnInit();

    expect(component.isLocalSEOActive).toBe(true);
    expect(component.lbSourceConfig.sourceIconUrl).toBe('test-icon-url');
    expect(component.lbSourceConfig.sourceTooltip).toBe('Local SEO');
  });

  it('should generate correct brand context URL', () => {
    component.ngOnInit();

    const expectedUrl = '/account/brands/test-brand/analytics/bing';
    expect(component.lbSourceConfig.sourceUrl).toContain(encodeURIComponent(expectedUrl));
  });

  it('should generate correct single location context URL', () => {
    mockLocationsService.currentLocation$ = of(mockSingleLocation);
    
    TestBed.overrideProvider(LocationsService, { useValue: mockLocationsService });
    fixture = TestBed.createComponent(BingInsightsComponent);
    component = fixture.componentInstance;
    component.startDate = new Date('2025-05-04');
    component.endDate = new Date('2025-06-02');

    component.ngOnInit();

    const expectedUrl = '/edit/account/AG-123/app/analytics/bing';
    expect(component.lbSourceConfig.sourceUrl).toContain(encodeURIComponent(expectedUrl));
  });

  it('should build link with app ID correctly', () => {
    const testLink = '/test-link';
    const testAppId = 'TEST-APP';
    component.accountGroupId = 'AG-123';

    const result = component.buildLinkWithAppId(testLink, testAppId);

    expect(result).toContain('accountGroupId=AG-123');
    expect(result).toContain('productId=TEST-APP');
    expect(result).toContain(encodeURIComponent(testLink));
  });

  it('should handle missing Local SEO product', () => {
    mockProductService.activeAccessibleProducts$ = of([]);
    
    TestBed.overrideProvider(ProductService, { useValue: mockProductService });
    fixture = TestBed.createComponent(BingInsightsComponent);
    component = fixture.componentInstance;

    component.ngOnInit();

    expect(component.isLocalSEOActive).toBe(false);
  });

  it('should emit cardDataLoaded event', () => {
    spyOn(component.cardDataLoaded, 'emit');
    
    const testData = 'test-card-data';
    component.cardDataLoaded.emit(testData);

    expect(component.cardDataLoaded.emit).toHaveBeenCalledWith(testData);
  });

  it('should render views metrics card when data is available', () => {
    const mockViewsData = {
      data: { views: 100, viewsChange: 10 },
    };
    
    mockBingInsightsService.getViewsMetrics.mockReturnValue(of(mockViewsData));
    component.ngOnInit();
    fixture.detectChanges();

    const viewsCard = fixture.debugElement.nativeElement.querySelector('bc-new-base-card');
    expect(viewsCard).toBeTruthy();
  });

  it('should render actions metrics card when data is available', () => {
    const mockActionsData = {
      data: { actions: 50, actionsChange: 5 },
    };
    
    mockBingInsightsService.getActionsMetrics.mockReturnValue(of(mockActionsData));
    component.ngOnInit();
    fixture.detectChanges();

    // Check that the component structure is correct
    expect(component).toBeTruthy();
  });

  it('should display title when hideHeader is false', () => {
    component.hideHeader = false;
    fixture.detectChanges();

    const title = fixture.debugElement.nativeElement.querySelector('h2');
    expect(title).toBeTruthy();
  });

  it('should hide title when hideHeader is true', () => {
    component.hideHeader = true;
    fixture.detectChanges();

    const title = fixture.debugElement.nativeElement.querySelector('h2');
    expect(title).toBeFalsy();
  });

  it('should have anchor link for navigation', () => {
    fixture.detectChanges();

    const anchorLink = fixture.debugElement.nativeElement.querySelector('#bing-places');
    expect(anchorLink).toBeTruthy();
  });
});
