import { Component } from '@angular/core';
import { ListCustomFieldManagementPageComponent } from '@galaxy/crm/dynamic';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { ObjectType } from '@galaxy/crm/static';

@Component({
  selector: 'app-field-management',
  templateUrl: './crm-field-management.component.html',
  styleUrls: [],
  imports: [TranslateModule, ListCustomFieldManagementPageComponent],
})
export class CrmFieldManagementComponent {
  constructor(private readonly activatedRoute: ActivatedRoute) {}

  protected readonly supportedObjects: ObjectType[] = ['Contact', 'Company', 'Opportunity'];
}
