{
  "APP_NAME": "Business App",
  "NAVIGATION": {
    "TABS": {
      "GET_STARTED": "Get Started",
      "FULFILLMENT": "Projects",
      "FULFILLMENT_DESCRIPTION": "View updates and history on projects fulfilled for your business",
      "HOME": "Home",
      "ACCOUNT_ACTIVITY": "Account activity",
      "INBOX": "Inbox",
      "CAMPAIGNS": "Campaigns",
      "ALL_CAMPAIGNS": "All campaigns",
      "EMAIL_TEMPLATES": "Email templates",
      "RECENT_ACTIVITY": "Recent Activity",
      "EXECUTIVE_REPORT": "Executive Report",
      "MULTI_LOCATION_REPORT": "Executive Report",
      "MULTI_LOCATION_TASKS": "Tasks",
      "MULTI_LOCATION_CONTACTS": "Contacts",
      "RECOMMENDATIONS": "Recommendations",
      "MY_PRODUCTS": "My Products",
      "STORE": "Store",
      "CUSTOMER_LIST": "Customers",
      "GUIDES": "Guides",
      "GUIDES_DESCRIPTION": "Read guides to learn practical digital marketing strategies",
      "ALL": "All",
      "BRANDS": "Groups",
      "GROUPS": "Groups",
      "LOCATIONS": "Locations",
      "FILES": "Files",
      "FILES_DESCRIPTION": "View and download files from your administrator",
      "FILE_NOT_FOUND": "File not found",
      "SETTINGS": "Settings",
      "ADMINISTRATION": "Administration",
      "BUSINESS_PROFILE": "Business Profile",
      "BUSINESS_PROFILE_DESCRIPTION": "Edit the central source of information for your business’s online presence",
      "USER_PROFILE": "User Profile",
      "PROFILE_SETTINGS": "Profile settings",
      "NOTIFICATION_SETTINGS": "Notification settings",
      "NOTIFICATION_SETTINGS_DESCRIPTION": "Manage global settings including app notifications and personal profile and password",
      "PROFILE_SETTINGS_DESCRIPTION": "Manage personal profile and password",
      "EMAIL_HISTORY": "Email history",
      "EMAIL_HISTORY_DESCRIPTION": "Review emails sent to and from your business through the app and connected products",
      "BILLING_SETTINGS": "Billing settings",
      "BILLING_SETTINGS_DESCRIPTION": "Manage billing information and payment methods",
      "INVOICES": "Invoices",
      "INVOICES_DESCRIPTION": "View and download invoices and receipts for purchases",
      "EMAIL_CONFIGURATION": "Email configuration",
      "SMS_CONFIGURATION": "SMS configuration",
      "SMS_CONFIGURATION_DESCRIPTION": "Set up A2P registration to send text messages",
      "SMS_REGISTRATION": "SMS registration",
      "EMAIL_CONFIGURATION_DESCRIPTION": "Set email addresses used for emails sent and received through the app",
      "TEMPLATES_BREADCRUMB": "Templates",
      "EDIT_TEMPLATES_BREADCRUMB": "Edit templates",
      "MANAGE_RECIPIENTS": "Manage Recipients",
      "SOCIAL_CONNECTIONS": "Connections",
      "SOCIAL_CONNECTIONS_DESCRIPTION": "Connect external accounts and services to enable more features, reporting, and insights",
      "MY_LISTING": "My Listing",
      "MEETING_SCHEDULER": "My Meetings",
      "ORDERS": "Orders",
      "ORDERS_DESCRIPTION": "View history of products and features ordered",
      "INVITE_USERS": "Invite Users",
      "REPUTATION": "Reputation",
      "AUTOMATIONS": "Automations",
      "AUTOMATIONS_DESCRIPTION": "Power your business with preset actions to automate routine processes\n",
      "SYSTEM_AUTOMATIONS": "System Automations",
      "MY_AUTOMATIONS": "My Automations",
      "AUTOMATION_TEMPLATES": "Templates",
      "AI_KNOWLEDGE": "AI knowledge base",
      "MARKETING": "Marketing",
      "PAYMENT_SETTINGS": "Payment settings",
      "PAYMENT_SETTINGS_DESCRIPTION": "Manage your Stripe account",
      "PAYMENTS": "Payments",
      "PAYOUTS": "Payouts"
    },
    "MENU": {
      "EDIT_NOTIFICATIONS": "Edit notifications",
      "EDIT_PROFILE": "Edit profile",
      "SIGN_OUT": "Sign out",
      "LOGIN": "Log in",
      "SMB_INVITE": "Invite a business"
    },
    "REQUEST_ASSISTANCE": "Request assistance",
    "NEED_ASSISTANCE": "Need assistance?",
    "SWITCH_LOCATION": "Switch locations",
    "DO_YOU_WANT_TO_LEAVE": "Are you sure you want to leave?",
    "VALUES_WILL_BE_REMOVED": "Leaving now will remove the values you have entered so far.",
    "LEAVE_ANYWAY": "Leave anyway",
    "RETURN": "Return to form",
    "SWITCHING_LOCATIONS": "Switching locations",
    "SWITCHING_LOCATIONS_WENT_WRONG": "Something went wrong while switching locations and we were unable to return you to the same page",
    "TRY_AGAIN": "Try again"
  },
  "PAYMENTS": {
    "COLLECT_PAYMENTS": "Collect payments",
    "GET_PAID": "Get paid faster than ever",
    "SEND_INVOICES": "Effortlessly send invoices to customers over Inbox",
    "CUSTOMERS_PAY": "Make it easy for customers to pay",
    "ACCEPTED_PAYMENT_METHODS": "Accepted payment methods:",
    "TRANSACTION_FEES": "Card transactions as low as 2.9% + $0.30 per transaction",
    "MANAGE_ACCOUNT": "Set up and manage account",
    "ERROR": "Stripe couldn't be reached. Wait a few moments before trying again.",
    "CARDS": {
      "VISA": "Visa",
      "AMERICAN_EXPRESS": "American Express",
      "MASTERCARD": "Mastercard",
      "DISCOVER": "Discover"
    },
    "CUSTOMER_TITLE": "Customer"
  },
  "COMMON": {
    "ADMIN_VIEW": "Admin view",
    "SELECT_DEFAULT": "Choose a default account to land on every time you open {{businessAppName}}.",
    "APPROVED": "Approved",
    "VISIBLE_TO_ADMINS": "Visible to admins only",
    "BETA": "Beta",
    "KEYBOARD": {
      "SHIFT": "Shift",
      "ESC": "Esc"
    },
    "CONTINUE": "Continue",
    "CONFIGURE": "Configure",
    "GENERIC_ERROR": "Sorry, something went wrong. Please try again",
    "AUTHORIZATION_ERROR": "You are not authorized to perform this action",
    "AS_OF_NOW": "as of now",
    "OF": "of",
    "ANALYTICS": "Analytics",
    "RESULTS": "results",
    "ACTION_LABELS": {
      "LOAD_MORE": "Load more",
      "ADD": "Add",
      "CANCEL": "Cancel",
      "NEXT": "Next",
      "BACK": "Back",
      "DELETE": "Delete",
      "EDIT": "Edit",
      "SAVE": "Save",
      "VIEW": "View",
      "SEARCH": "Search",
      "SEND": "Send",
      "DOWNLOAD": "Download",
      "IMPORT": "Import",
      "REVIEW": "Review",
      "CONTACT_US": "Contact us",
      "RESPOND": "Respond",
      "HIDE": "Hide",
      "CONFIRM": "Confirm",
      "CLOSE": "Close",
      "OPEN": "Open",
      "REMOVE": "Remove",
      "STATUS": "Status",
      "MORE": "more",
      "SEE_SOLUTIONS": "See solutions",
      "VIEW_SOLUTIONS": "View solutions",
      "VIEW_NOW": "View now",
      "SEE_PRODUCTS": "See products",
      "VIEW_STORE": "View store",
      "VIEW_GUIDES": "View guides",
      "RESPOND_NOW": "Respond now",
      "REVIEW_NOW": "Review now",
      "VIEW_EXEC_REPORT": "View Executive Report",
      "MANAGE_REVIEWS": "Manage reviews",
      "SEND_REVIEW_REQUEST": "Send review request",
      "SEND_REVIEW_REQUEST_QUESTION": "Send review request?",
      "SEND_REVIEW_REQUESTS": "Send review requests",
      "REQUEST_REVIEWS": "Request reviews",
      "REQUEST": "Request",
      "SELECT_RECIPIENTS": "Select recipients",
      "MAKE_POST": "Make post",
      "SOCIAL_CALENDAR": "Social calendar",
      "COMPOSE_POST": "Compose post",
      "SCHEDULE_POST": "Schedule post",
      "OPEN_CUSTOMER_LIST": "Open Customer List",
      "CORRECT_MY_LISTINGS": "Correct my listings",
      "REVIEW_MY_LISTINGS": "Review my listings",
      "SEE_REVIEWS": "See reviews",
      "CREATE_MY_LISTING": "Create My Listing",
      "CONNECT_NOW": "Connect now",
      "COLUMNS": "Columns",
      "DECLINE": "Decline",
      "OKAY": "Okay",
      "CHANGE_SUBSCRIPTION": "Change subscription",
      "LEARN_HOW": "Learn how",
      "START_SELLING": "Start Selling",
      "ACCEPT": "Accept",
      "GET_STARTED": "Get Started",
      "WARNING_TITLE": "Unsaved Changes",
      "WARNING_MESSAGE": "All unsaved changes will be lost.",
      "CONFIRMATION_MESSAGE": "Discard Changes",
      "CANCEL_BUTTON": "Stay on page"
    },
    "FIELD_LABELS": {
      "FIRST_NAME": "First name",
      "LAST_NAME": "Last name",
      "JOB_TITLE": "Job Title",
      "EMAIL": "Email",
      "NAME": "Name",
      "ZIP/POSTAL": "Zip/postal",
      "WEBSITE": "Website",
      "PHONE_NUMBER": "Phone number",
      "LINKED_IN_PROFILE": "LinkedIn profile",
      "LINKED_IN_PROFILE_HINT": "Enter your LinkedIn profile ID, e.g.: www.linkedin.com/in/{{your-linkedin-profile-id}}",
      "ADDRESS": "Address",
      "CITY": "City",
      "STATE": "State",
      "COUNTRY": "Country",
      "PHONE": "Phone",
      "PACKAGE_NAME": "Package Name",
      "PRODUCT_NAME": "Product Name",
      "QUANTITY": "Quantity",
      "PRICE": "Price",
      "FREQUENCY": "Frequency",
      "PASSWORD": "Password",
      "FROM": "From: {{ from_email }}",
      "PERMISSION_TO_CONTACT": "Permission to contact"
    },
    "LAST_UPDATED": "last updated",
    "LMI_CATEGORIES": {
      "LISTINGS": "Listings",
      "REVIEWS": "Reviews",
      "REQUESTS": "Requests",
      "SOCIAL": "Social",
      "WEBSITE": "Website",
      "SEO": "SEO",
      "ADVERTISING": "Advertising",
      "CONTENT": "Content",
      "CUSTOMER_VOICE": {
        "REVIEW_REQUEST": "Review Requests"
      },
      "RM_REQUESTS": "Requests"
    },
    "RESULT": {
      "SUCCESS": "Success",
      "FAILURE": "Something went wrong, please try again"
    },
    "CONNECT": "Connect",
    "CONNECT_NOW": "Connect now",
    "NOT_CONNECTED": "Not connected",
    "NO_CHANGE": "No change",
    "NOT_APPLICABLE": "N/A",
    "LEGEND": "Legend",
    "SELECTED_DATE_RANGE": "Selected date range",
    "DATE_RANGE": "Date range",
    "SYNCING": "Syncing",
    "ERROR_SYNCING": "Error syncing",
    "WEEK_OF": "Week of",
    "SHOW_ALL": "Show all",
    "SHOW_LESS": "Show less",
    "DISCLAIMER": "All product and company names, logos, brands are trademarks or registered trademarks of their respective holders. Use of them is for identification or reference purposes only and does not imply any affiliation, association, or endorsement by them.",
    "UPDATE": "Update",
    "VIEW_ALL": "View all",
    "CONTACT_SALESPERSON": "Contact {{ salesPersonName }}",
    "LOCATION": "Location",
    "LOCATIONS": "Locations",
    "REVIEW_OR_LISTING_SOURCE": "Source",
    "REVIEW_OR_LISTING_SOURCES": "Sources",
    "EXPORT_TO_CSV": "Export to CSV",
    "TIME_PERIOD": {
      "LAST_7_DAYS": "Last 7 Days",
      "LAST_30_DAYS": "Last 30 Days",
      "LAST_60_DAYS": "Last 60 Days",
      "LAST_90_DAYS": "Last 90 Days",
      "LAST_MONTH": "Last Month",
      "LAST_3_MONTHS": "Last 3 Months",
      "LAST_6_MONTHS": "Last 6 Months",
      "LAST_12_MONTHS": "Last 12 Months",
      "LAST_16_MONTHS": "Last 16 Months",
      "LAST_YEAR": "Last Year",
      "SEASON": "{{ season }}",
      "HOURS": "{{value}} hours"
    },
    "TREND": {
      "30_DAYS": "30 day trend",
      "90_DAYS": "90 day trend",
      "12_MONTHS": "12 month trend"
    },
    "TABLE_SHOWING": "Showing {{page}} of {{total}}",
    "POWERED_BY": "Powered by",
    "ORDINAL": {
      "0": "0",
      "1": "1<sup>st</sup>",
      "2": "2<sup>nd</sup>",
      "3": "3<sup>rd</sup>",
      "4": "4<sup>th</sup>",
      "5": "5<sup>th</sup>",
      "6": "6<sup>th</sup>",
      "7": "7<sup>th</sup>",
      "8": "8<sup>th</sup>",
      "9": "9<sup>th</sup>",
      "10": "10<sup>th</sup>",
      "11": "11<sup>th</sup>",
      "12": "12<sup>th</sup>",
      "13": "13<sup>th</sup>",
      "14": "14<sup>th</sup>",
      "15": "15<sup>th</sup>",
      "16": "16<sup>th</sup>",
      "17": "17<sup>th</sup>",
      "18": "18<sup>th</sup>",
      "19": "19<sup>th</sup>",
      "20": "20<sup>th</sup>",
      "21": "21<sup>st</sup>",
      "22": "22<sup>nd</sup>",
      "23": "23<sup>rd</sup>",
      "24": "24<sup>th</sup>",
      "25": "25<sup>th</sup>",
      "26": "26<sup>th</sup>",
      "27": "27<sup>th</sup>",
      "28": "28<sup>th</sup>",
      "29": "29<sup>th</sup>",
      "30": "30<sup>th</sup>",
      "31": "31<sup>st</sup>",
      "32": "32<sup>nd</sup>",
      "33": "33<sup>rd</sup>",
      "34": "34<sup>th</sup>",
      "35": "35<sup>th</sup>",
      "36": "36<sup>th</sup>",
      "37": "37<sup>th</sup>",
      "38": "38<sup>th</sup>",
      "39": "39<sup>th</sup>",
      "40": "40<sup>th</sup>",
      "41": "41<sup>st</sup>",
      "42": "42<sup>nd</sup>",
      "43": "43<sup>rd</sup>",
      "44": "44<sup>th</sup>",
      "45": "45<sup>th</sup>",
      "46": "46<sup>th</sup>",
      "47": "47<sup>th</sup>",
      "48": "48<sup>th</sup>",
      "49": "49<sup>th</sup>",
      "50": "50<sup>th</sup>",
      "51": "51<sup>st</sup>",
      "52": "52<sup>nd</sup>",
      "53": "53<sup>rd</sup>",
      "54": "54<sup>th</sup>",
      "55": "55<sup>th</sup>",
      "56": "56<sup>th</sup>",
      "57": "57<sup>th</sup>",
      "58": "58<sup>th</sup>",
      "59": "59<sup>th</sup>",
      "60": "60<sup>th</sup>",
      "61": "61<sup>st</sup>",
      "62": "62<sup>nd</sup>",
      "63": "63<sup>rd</sup>",
      "64": "64<sup>th</sup>",
      "65": "65<sup>th</sup>",
      "66": "66<sup>th</sup>",
      "67": "67<sup>th</sup>",
      "68": "68<sup>th</sup>",
      "69": "69<sup>th</sup>",
      "70": "70<sup>th</sup>",
      "71": "71<sup>st</sup>",
      "72": "72<sup>nd</sup>",
      "73": "73<sup>rd</sup>",
      "74": "74<sup>th</sup>",
      "75": "75<sup>th</sup>",
      "76": "76<sup>th</sup>",
      "77": "77<sup>th</sup>",
      "78": "78<sup>th</sup>",
      "79": "79<sup>th</sup>",
      "80": "80<sup>th</sup>",
      "81": "81<sup>st</sup>",
      "82": "82<sup>nd</sup>",
      "83": "83<sup>rd</sup>",
      "84": "84<sup>th</sup>",
      "85": "85<sup>th</sup>",
      "86": "86<sup>th</sup>",
      "87": "87<sup>th</sup>",
      "88": "88<sup>th</sup>",
      "89": "89<sup>th</sup>",
      "90": "90<sup>th</sup>",
      "91": "91<sup>st</sup>",
      "92": "92<sup>nd</sup>",
      "93": "93<sup>rd</sup>",
      "94": "94<sup>th</sup>",
      "95": "95<sup>th</sup>",
      "96": "96<sup>th</sup>",
      "97": "97<sup>th</sup>",
      "98": "98<sup>th</sup>",
      "99": "99<sup>th</sup>",
      "100": "100<sup>th</sup>"
    },
    "ADDRESS": {
      "ADDRESS": "Address",
      "CITY": "City",
      "COUNTRY": "Country",
      "LINE_1": "Address",
      "LINE_2": "Additional Address Line",
      "STATE_PROV": "State / Province",
      "POSTAL_CODE": "Postal / Zip Code"
    }
  },
  "GET_STARTED": {
    "NEW_WELCOME_MESSAGE": "Hi {{ user_name }}, let's get set up",
    "NEW_WELCOME_MESSAGE_UNAUTH": "Hi, let's get set up",
    "CONGRATS": "Congrats!",
    "COMPLETE": "Complete!",
    "NEXT_STEP": "Next: {{ step }}",
    "WIDGET": {
      "QUICK_LINKS": {
        "TITLE": "Engage with your customers",
        "LINK": {
          "NEW_CUSTOMER": "Add new customers",
          "REPLY_REVIEW": "Reply to reviews",
          "SOCIAL_MEDIA": "Engage on social media",
          "REQUEST_REVIEW": "Request a review",
          "SCHEDULE_MEETING": "Schedule a meeting"
        }
      },
      "ACADEMY": {
        "TITLE": "Take your first academy course",
        "VIEW": "Take a free online course",
        "ARTICLE_1": {
          "TITLE": "Discover the Purchasing Path of Your Customers"
        },
        "ARTICLE_2": {
          "TITLE": "Start Selling Online in 60 Minutes"
        },
        "ARTICLE_3": {
          "TITLE": "Take Control of Your Online Reputation"
        }
      },
      "BOOK_MARK": {
        "TITLE": "Bookmark {{ business_app_name }} for easy access",
        "DESCRIPTION": "Most business owners find it easiest to save {{ business_app_name }} as a bookmark, for quick access whenever you need it.",
        "BUTTON_PHONE": "Bookmark on your phone",
        "BUTTON_DESKTOP": "Bookmark on my desktop"
      },
      "TRACKER": {
        "TITLE": "Complete Setup",
        "COMPLETED": "Completed",
        "WELCOME_VIDEO": "Watch the welcome video",
        "CONNECT_ACCOUNT": "Connect an account",
        "QUICKBOOKS": "Integrate your finances",
        "SEND_SMS": "Get your SMS number",
        "NOTIFICATIONS": "Enable notifications"
      },
      "INBOX_SMS": {
        "TITLE": "Communicate with your customers",
        "DESCRIPTION": "Capture more leads and centralize customer communication with Inbox. Message customers via Web Chat, Google Business Message, Facebook Business Messages, Email and more!",
        "BUTTON": {
          "SET_UP_INBOX": "Set up Inbox"
        }
      },
      "NOTIFICATIONS": {
        "TITLE": "Never miss an important notification",
        "DESCRIPTION": "Don't miss important customer messages - manage your notifications to always be in the know",
        "ACTION": "Allow push notifications"
      }
    },
    "SETUP_CARDS": {
      "COMPLETE": "Complete",
      "SKIP": "Skip",
      "SIGN_UP": {
        "TITLE": "Set your password and log in",
        "DESCRIPTION": "You’ve made it! Just a few more steps to get the most out of {{ business_app_name }}."
      },
      "CONNECT_GMB": {
        "TITLE": "Connect Google Business Profile",
        "DESCRIPTION": "Collect and respond to Google Reviews, sync your Business Profile, and more.",
        "ACTION": "Connect Now"
      },
      "WEBCHAT_SETUP": {
        "TITLE": "Set up and install Web Chat",
        "DESCRIPTION": "Instantly engage website visitors with AI trained on your business. Install the chat widget to get leads in Inbox.",
        "ACTION": "Go to Web Chat settings"
      },
      "MANAGE_INTEGRATION": {
        "TITLE": "Connect your business system",
        "DESCRIPTION": "Import existing customers to automatically request reviews and manage relationships all in one place.",
        "ACTION": "Find integrations"
      },
      "REGISTER_SMS": {
        "TITLE": "Register SMS to send business texts",
        "DESCRIPTION": "Register to send SMS messages to your customers.",
        "ACTION": "Register for SMS",
        "ACTION_IN_PROGRESS": "View registration",
        "INFO_IN_PROGRESS": "Registration is in progress",
        "ACTION_NEED_ACTION": "Update business info",
        "ALERT_NEED_ACTION": "Registration needs attention"
      }
    }
  },
  "HOME": {
    "PAGE_TITLE": "Home"
  },
  "DASHBOARD": {
    "ORDER_IN_PROGRESS": "Your recent order is being processed.",
    "ORDER_WAITING_FOR_APPROVAL": "You have an order waiting for your approval.",
    "ORDER_BUTTON": {
      "REVIEW": "Review order",
      "VIEW": "View Order"
    },
    "WELCOME_BANNER": {
      "TITLE": "Welcome to {{ center_name }}!",
      "DESCRIPTION": "Everything you need in one place.",
      "VIEW_EXEC_REPORT": "View your Executive Report",
      "RESTART": "Restart animation",
      "CONNECT_ACCOUNTS": "Connect accounts",
      "WHAT_NEXT_HELP": "What can I do next?",
      "TITLE_BRAND": "Welcome to Multi-Location {{ center_name }}!",
      "WATCH_OVERVIEW_VIDEO": "Watch overview video"
    },
    "WIDGET": {
      "RECENT_ACTIVITY": {
        "NAME": "Recent activity",
        "VIEW": "View all activity"
      },
      "INBOX": {
        "NAME": "Inbox Messages",
        "VIEW": "View inbox messages"
      },
      "PRODUCTS": {
        "NAME": "Products",
        "TOOLTIP": "Choose which products appear here by pinning them on the products page.",
        "NO_PRODCTS_STATE_1": "You currently have no products you can access.",
        "NO_PRODCTS_STATE_2": "Lets change that.",
        "VIEW": "View all products",
        "USER_REQUIRED_MODAL": {
          "TITLE": "Account access required",
          "DESCRIPTION": "Only users belonging to this customer's account can access this product."
        }
      },
      "BUSINESS_PROFILE": {
        "NAME": "Business profile",
        "VIEW": "View business profile",
        "MARKED_AS_SAB": "Marked as a service area business"
      },
      "ACADEMY": {
        "NAME": "Academy",
        "VIEW": "Take a free online course",
        "ARTICLE_1": {
          "TITLE": "Discover the Purchasing Path of Your Customers"
        },
        "ARTICLE_2": {
          "TITLE": "Start Selling Online in 60 Minutes"
        },
        "ARTICLE_3": {
          "TITLE": "Take Control of Your Online Reputation"
        }
      },
      "WEBSITE_SALES": {
        "NAME": "Sales",
        "VIEW": "Manage Orders",
        "ORDERS": "Orders",
        "ITEMS_SOLD": "Items sold",
        "SALES": "Sales",
        "SELL_ONLINE": "Sell products and services on your website",
        "CREATE_STORE": "Create your online store",
        "VIEW_SOLUTION": "See solution",
        "TOTAL": "Total sales"
      },
      "WEBSITE_GET_STARTED_MODAL": {
        "TITLE": "Start Selling Online with Website Express",
        "DESCRIPTION": "Offering your products and services online is an essential ingredient for your business' success and an opportunity to move ahead of the competition. Increase your sales,  develop deeper connections with your customers, and provide the experience your customers expect. Let us guide you through these quick steps to get started:",
        "STEP_1": "Create your site",
        "STEP_2": "Setup your online store",
        "STEP_3": "Make your first sales online",
        "NOTES_TITLE": "Learning resources",
        "NOTE_1": "Want a complete breakdown of e-commerce? <a target=\"_blank\" href=\"{{learn_ecommerce_course_url}}\">Take this course</a>",
        "NOTE_2": "Ready to start selling online? <a target=\"_blank\" href=\"{{build_online_store_url}}\">Start building out your store</a>",
        "BUTTON_TEXT": "Create my site"
      }
    }
  },
  "FUNNEL_METRICS": {
    "TITLE": "Marketing Funnel",
    "LINK_TEXT": "View full report",
    "INFO_TEXT": "The Marketing Funnel arranges key metrics from across the Executive Report to show how your marketing efforts are impacting your brand awareness and resulting in new customers. Data from some sources may take up to 5 days to appear here.",
    "TOOLTIP_DATE_WARNING": "Data may take up to 5 days to appear here",
    "WEEK_OF": "Week of",
    "CONNECT": {
      "TITLE": "Unlock your Marketing Funnel?",
      "MESSAGE": "Connect your Google Business Profile or Facebook to unlock metrics",
      "MESSAGE_NO_ACCESS": "Unlock exclusive insights into your online performance. Reach out to learn how."
    },
    "BREAKDOWN": {
      "SHOW": "Show detailed breakdown",
      "HIDE": "Hide detailed breakdown",
      "CONNECT_ACCOUNTS": "Connect accounts"
    },
    "METRICS": {
      "IMPRESSIONS": {
        "TITLE": "Impressions",
        "TOOLTIP": "The number of views your online content received",
        "SOURCES": {
          "BING": {
            "VIEWS": {
              "TITLE": "Bing"
            }
          },
          "GMB": {
            "VIEWS": {
              "TITLE": "Views in Google"
            },
            "SEARCHES": {
              "TITLE": "Searches",
              "TOOLTIP": "Search counts are gathered monthly. Weekly and daily values are projected based on the average count for each month. If this value is zero, data may still be in the process of being collected. Check back soon."
            }
          },
          "FACEBOOK": {
            "REACH": {
              "TITLE": "Social posts reach"
            }
          },
          "ADINTEL": {
            "IMPRESSIONS": {
              "TITLE": "Ad impressions"
            }
          },
          "CTCT": {
            "EMAILS_OPENED": {
              "TITLE": "Constant Contact: Emails opened"
            }
          }
        }
      },
      "ENGAGEMENT": {
        "TITLE": "Engagement",
        "TOOLTIP": "The number of interactions with your online content",
        "SOURCES": {
          "BING": {
            "WEBSITE_VISIT": {
              "TITLE": "Bing"
            }
          },
          "GMB": {
            "VISITS": {
              "TITLE": "Website visits"
            }
          },
          "RM": {
            "REVIEWS": {
              "TITLE": "Number of new reviews"
            }
          },
          "CTCT": {
            "EMAILS_CLICKED": {
              "TITLE": "Constant Contact: Emails clicked"
            }
          },
          "ADINTEL": {
            "AD_CLICKED": {
              "TITLE": "Ad clicks"
            }
          },
          "FACEBOOK": {
            "ENGAGEMENT": {
              "TITLE": "Social comments, shares, reactions"
            }
          },
          "INBOX": {
            "CONVERSATION": {
              "TITLE": "Conversations created"
            }
          }
        }
      },
      "LEADS": {
        "TITLE": "Leads",
        "TOOLTIP": "Includes all leads captured in your CRM from all sources except bulk import, or manually created.",
        "SOURCES": {
          "INBOX": {
            "TITLE": "Lead Captured"
          },
          "BING": {
            "PHONE_CALLS": {
              "TITLE": "Bing"
            }
          },
          "GMB": {
            "PHONE_CALLS": {
              "TITLE": "Phone calls"
            },
            "DIRECTIONS": {
              "TITLE": "Direction requests"
            },
            "BOOKINGS": {
              "TITLE": "Bookings"
            },
            "CONVERSATIONS": {
              "TITLE": "Messages"
            },
            "FOOD_ORDERS": {
              "TITLE": "Food orders"
            }
          },
          "ADINTEL": {
            "PHONE_CALLS": {
              "TITLE": "Phone calls"
            },
            "AD_CONVERSION": {
              "TITLE": "Ad conversions"
            }
          },
          "SMS": {
            "TITLE": "SMS"
          }
        },
        "DIRECTION_REQUESTS_ALERT": "Google Direction Requests moved to the <a href=\"{{executive_report_url}}\">Executive Report GBP section</a>"
      }
    },
    "EMPTY_STATE": {
      "TITLE": "Connect accounts to collect insights",
      "BODY": "Start by adding your Facebook or Google Business Profile"
    }
  },
  "SWITCH_LOCATION_MODAL": {
    "DEFAULT": "DEFAULT",
    "SELECT_LOCATION": "Select location",
    "MAKE_DEFAULT": "Make default",
    "NO_LOCATIONS": "No locations for:",
    "TO_SELECT": "to select",
    "TO_NAVIGATE": "to navigate",
    "TO_CHANGE_TABS": "to change tabs",
    "TO_CLOSE": "to close",
    "TO_OPEN": "to open",
    "ADMIN_VIEW": "You are an admin, so you may see accounts not available to all users.",
    "SELECT_DEFAULT": "Select the account you'd like to view. You can skip this step in the future by setting an account as your default."
  },
  "REQUEST_ASSISTANCE_MODAL": {
    "HEADER": "Contact us",
    "CONTENT": "Need help? Send me a message and I will contact you shortly",
    "COMPOSE_MESSAGE": "Compose message",
    "PLACEHOLDER_MESSAGE": "Please help me find the right products for our business."
  },
  "REQUEST_SUBSCRIPTION_CHANGE_MODAL": {
    "HEADER": "Change Subscription",
    "CONTENT": "If you would like to change your subscription, please send us a message and we'll get back to you shortly.",
    "COMPOSE_MESSAGE": "Compose message",
    "PLACEHOLDER_MESSAGE": "I would like to change my subscription."
  },
  "RECENT_ACTIVITY": {
    "FILTERS": "Filters",
    "PRESCRIPTION": {
      "TITLE": "Stay informed 24/7",
      "SECONDARY_1": "Keep up with all the online activity that impacts the success of your business. Check back often to see real-time updates from the products you've subscribed to."
    },
    "PUSH_NOTIFICATIONS": {
      "PROMPT": "Would you like to receive push notifications about account activity like new reviews?",
      "YES_ENABLE_NOTIFICATIONS": "Yes, enable notifications",
      "MAYBE_LATER": "Maybe later"
    },
    "NO_ACTIVITY_MESSAGE": "There is currently no recent activity"
  },
  "VENDOR_PRODUCTS": {
    "UBERALL": {
      "LISTINGS": {
        "PROFILE_COMPLETENESS": {
          "MAIN": "Profile Completeness",
          "SUBTITLE": "Score given to your location profile depending on its completeness."
        }
      },
      "REPUTATION": {
        "AVG_RATING": {
          "MAIN": "Average Review Rating",
          "SUBTITLE": "Customer feedback including a star rating."
        },
        "UNREAD_RATING": {
          "MAIN": "Unread Reviews",
          "SUBTITLE": "Customer feedback not yet read and replied to."
        },
        "TOTAL_RATING": {
          "MAIN": "Total Reviews",
          "SUBTITLE": "All customer feedback over time."
        }
      },
      "SOCIAL": {
        "IMPRESSIONS": {
          "MAIN": "Facebook Impressions",
          "SUBTITLE": "Number of views on your Facebook content as of yesterday."
        },
        "CLICKS": {
          "MAIN": "Facebook Clicks",
          "SUBTITLE": "Number of clicks on your Facebook content as of yesterday."
        }
      }
    },
    "AUDIOEYE": {
      "EXECUTIVE_REPORT_CARD": {
        "PRODUCT_NAME": "AudioEye",
        "TITLE": "AudioEye Accessibility Score",
        "SUBTITLE": "Your score reflects both the impact and frequency of issues detected on your site.",
        "TOOLTIP": "As users visit your site AudioEye's automated tests scan for accessibility issues. Your score reflects not only how many issues are found,  but also the severity of those issues."
      }
    }
  },
  "EXECUTIVE_REPORT": {
    "BREAKDOWN": {
      "SHOW": "Show detailed breakdown",
      "HIDE": "Hide detailed breakdown"
    },
    "PAGE_TITLE": "Executive Report",
    "CATEGORY_TITLE": {
      "CUSTOMER_RELATIONS": "Customer Relations",
      "LEADS": "Leads",
      "OVERVIEW": "Overview",
      "REPUTATION": "Reputation",
      "LISTINGS": "Listings",
      "BING_INSIGHTS": "Bing Places",
      "SOCIAL": "Social",
      "WEBSITE": "Website",
      "ADVERTISING": "Advertising",
      "SEO": "SEO",
      "GOOGLE_MY_BUSINESS": "Google Business Profile",
      "FILES": "Files",
      "FULFILLMENT": "Fulfillment",
      "SERVICES": "Services",
      "ECOMMERCE": "Ecommerce",
      "ACCOUNTING": "Accounting"
    },
    "CATEGORY_QUESTION": {
      "CUSTOMER_RELATIONS": "How many new leads and customer interactions are you generating?",
      "LEADS": "How many new leads and customer interactions are you generating?",
      "REPUTATION": "How are people rating your business online?",
      "LISTINGS": "Are you listed accurately on search engines, directories, apps, and GPS?",
      "BING_INSIGHTS": "Are customers discovering and taking action on your Bing profile?",
      "SOCIAL": "Are you engaging with your community & customers online?",
      "WEBSITE": "Is your website attracting potential customers?",
      "ADVERTISING": "Are you reaching everyone?",
      "SEO": "Do you show up where people are searching?",
      "SERVICES": "What is being done for my business?",
      "ECOMMERCE": "What is being done for my business?"
    },
    "INDUSTRY": "{{ business_category }}",
    "LISTINGS": "A listing is an online profile of your business, which provides people with your business name, address, phone number, website, and more. Accurate listings help to ensure that potential customers can find your business online.",
    "REPUTATION": "Your reputation manifests itself as customer feedback on review sites, forums, and other sites around the web. A healthy reputation helps to ensure that potential customers trust you more than your competitors.",
    "SOCIAL": "Social media is one of the best ways to engage with your customers and build your brand. A strong social presence helps to ensure that your customers remain satisfied and loyal.",
    "GMB": {
      "TITLE": "Unlock Google Business Profile Insights",
      "PRESCRIPTION": "Activate Google Business Profile Insights to discover how people interact with your business online. Understand how people are finding you, where they're coming from, and how many of them are clicking on your phone number! Once your GMB account is connected, you will be able to view online business insights on your next report."
    },
    "BING": {
      "TITLE": "Unlock Bing Places Insights",
      "PRESCRIPTION": "Activate Bing Places to discover how people interact with your business online. Understand how people are finding you, where they're coming from, and how many of them are clicking on your phone number! Once your Bing Places account is connected, you will be able to view online business insights on your next report."
    },
    "TWITTER": {
      "TITLE": "New Employee Tweets",
      "PRESCRIPTION": "See what your employees are saying on Twitter",
      "CTA": "Setup employee Twitter searches!"
    },
    "SOCIAL_LINE_CHART": {
      "FACEBOOK_TITLE": "Facebook Likes",
      "TWITTER_TITLE": "Twitter Followers",
      "FACEBOOK_DESCRIPTION": "See how the audience for your business on Facebook is changing",
      "TWITTER_DESCRIPTION": "See how the audience for your business on Twitter is changing"
    },
    "PRESCRIPTION": {
      "TITLE": "Your highlight reel",
      "SECONDARY_1": "See what happened across your digital marketing channels in the last week and month. Check back weekly to see updates from the applicable products you've subscribed to."
    },
    "DATE_PICKER": {
      "WEEKLY": "Weekly",
      "MONTHLY": "Monthly",
      "SEARCH": "Search"
    },
    "NO_DATA": {
      "TITLE": "Nothing Yet",
      "DESCRIPTION": "We'll show this once we have more data."
    },
    "NOT_GENERATED": {
      "CHECK_BACK_NEXT_WEEK": "Check back next week!",
      "REPORT_BEING_GENERATED": "Your report is being generated. Please come back next Tuesday to view your full executive report. Note that you need to have at least one active product to view your report."
    },
    "SHOW_ALL_REMAINING": "Show All (+{{ remaining }})",
    "NO_REPORTS_AVAILABLE": "No {{ frequency }} Executive Reports available."
  },
  "RECOMMENDATIONS": {
    "PERFORMANCE_MESSAGE": "Your performance across the",
    "CATEGORY_HEADERS": {
      "LISTINGS": "Make it easy for people to find you",
      "REPUTATION": "Make it easy for people to trust you",
      "SOCIAL": "Make it easy for customers to like you",
      "WEBSITE": "Make it easy for people to get what they're looking for",
      "SEO": "Make it easy for people to find you",
      "ADVERTISING": "Make it easy for people to find out about you",
      "CONTENT": "Make it easy for people to stay engaged"
    },
    "CATEGORY_DESCRIPTIONS": {
      "LISTINGS": "Claiming your online listings and keeping them up-to-date with accurate information will increase your chances of being found online by potential customers—and being found before your competitors.",
      "REPUTATION": "The more reviews your customers give you, the more trust you build as a local business. Encouraging customers to leave reviews and responding to those reviews will boost your business' star rating and bring new customers to your door.",
      "WEBSITE": "With a fast-loading, informative, and mobile-friendly website, customers can find out everything they need to know about your business. Focus on your website to rank higher in online search, and turn website visitors into customers.",
      "SOCIAL": "Social media is the perfect place to build brand loyalty by entertaining, educating, and engaging with your community. Build a following on social media to position your business as the local expert in your field, and grow your customer base.",
      "SEO": "Ranking higher than your competitors for a variety of keywords ensures that your business is found first when customers search for the products or services you offer. Increase your visibility in online search to widen your customer base.",
      "ADVERTISING": "From search engines and websites to social media and apps, online advertising puts your business in the spotlight. Increase your chances of a sale by building brand awareness, driving traffic to your website, and reaching the right people at the right time.",
      "CONTENT": "People expect local businesses to share interesting content and provide extraordinary service. Give them videos, blogs, text messages, and more to go above and beyond their expectations."
    },
    "PRESCRIPTION": {
      "TITLE": "Take your business to the next level",
      "SECONDARY_1": "Learn what you can do next to improve your online marketing, earn more customers, and grow your business."
    },
    "DASHBOARD": {
      "GUIDES": "Guides",
      "GUIDES_SUBHEADING": "Learn how your {{ category }} can impact your success.",
      "BROWSE_GUIDES": "Browse Guides",
      "AVAILABLE_SOLUTIONS": "Available Solutions",
      "SOLUTIONS_SUBHEADING": "Discover available products and services that will help your business grow.",
      "BROWSE_PACKAGES": "Browse Store"
    },
    "DISRUPT": {
      "CTAS": {
        "SEE_SOLUTIONS": "See solutions",
        "VIEW_NOW": "View now",
        "SEE_PRODUCTS": "See products",
        "VIEW_STORE": "View store",
        "VIEW_GUIDES": "View guides",
        "RESPOND_NOW": "Respond now",
        "REVIEW_NOW": "Review now",
        "VIEW_EXEC_REPORT": "View Executive Report",
        "MANAGE_REVIEWS": "Manage reviews",
        "SEND_REVIEW_REQUEST": "Send review request",
        "REQUEST_REVIEWS": "Request reviews",
        "MAKE_POST": "Make post",
        "SOCIAL_CALENDAR": "Social calendar",
        "COMPOSE_POST": "Compose post",
        "SCHEDULE_POST": "Schedule post",
        "OPEN_CUSTOMER_LIST": "Open Customer List",
        "CORRECT_MY_LISTINGS": "Correct my listings",
        "REVIEW_MY_LISTINGS": "Review my listings",
        "SEE_REVIEWS": "See reviews",
        "CREATE_MY_LISTING": "Create My Listing",
        "CONNECT_NOW": "Connect now"
      },
      "TEXT": {
        "BUSINESS_CENTER": {
          "EXEC_REPORT": "Take a look at your Executive Report to find out how you're doing online.",
          "UNUSED": {
            "HOW_WELL": "Find out how well you're doing online.",
            "IMPROVE": "Improve your scores with state-of-the-art products.",
            "FIND_PRODUCTS": "Find products specifically chosen to fit your needs.",
            "UNLOCK_KNOWLEDGE": "Unlock a wealth of knowledge with guides written by industry experts.",
            "CUSTOMER_LIST": "Keep track of your customers with Customer List."
          }
        },
        "LISTINGS": {
          "UPDATE_HOURS": "Update your hours of operation so customers know when you're open for business.",
          "ENSURE_ACCURACY": "Ensure your business information is accurate across the web.",
          "INACCURATE_INFO": "Nearly 25% of listings on the internet contain business name errors, resulting in lost sales. Is your listing information correct?",
          "UNUSED": {
            "REVIEW_INFO": "Review your business contact information and hours to ensure they are accurate."
          }
        },
        "REPUTATION": {
          "GET_REVIEWS": "92% of consumers are positively influenced to purchase from a business after reading a trusted review. Reach out to your customers to boost your reputation.",
          "CHECK_REVIEWS": "97% of consumers factor customer reviews in their decision to buy from a business. Take a look at your most recent ones.",
          "GENERATE": "Generate more reviews to boost your star rating and increase the amount of customers that walk through your door.",
          "RESPOND": "Responding to your positive reviews can encourage repeat visits to your business. Check out your recent reviews to improve your online reputation.",
          "GENERATE_MORE": "92% of all consumers are less likely to buy from a business without customer reviews. Start generating more reviews.",
          "UNUSED": {
            "ALWAYS_RESPOND": "Don't leave reviews unresponded. It could be hurting your business!"
          }
        },
        "SOCIAL": {
          "ACCESS_IMAGES": "Access millions of royalty-free images for your social media posts, boosting your engagement. Why not make a post now?",
          "GROW_PRESENCE": "Grow your social media presence by easily scheduling posts to Google and Facebook simultaneously. Save time by scheduling posts today.",
          "PLAN_POSTS": "Plan your social media posting schedule with the social calendar.",
          "POST_FREQUENTLY": "The average person spends over 2 hours on social media per day. Posting frequently is a great way to reach them."
        },
        "WEBSITE": {
          "FREE_SITE": "Set up your free website.",
          "SELL_ONLINE": "Sell your products and services online, with an e-commerce enabled website."
        },
        "ADVERTISING": {
          "SEE_AD_CAMPAIGNS": "See how your Google, Facebook, Microsoft, and {{localAdsName}} ad campaigns are performing."
        }
      }
    }
  },
  "STORE": {
    "ALL": "ALL",
    "PRESCRIPTION": {
      "TITLE": "Let's grow",
      "SECONDARY_1": "Browse our world-class products and services to see how we can help you succeed."
    },
    "PACKAGES_FILTER_NAME": "Available upgrades",
    "PACKAGES_FILTER_NAME_WITH_PRODUCT": "Available upgrades for {{ product_name }}",
    "ORDER_FORM": {
      "TITLE": "Order",
      "COMMON_FORM_HEADER_TITLE": "Gather activation information",
      "COMMON_FORM_HEADER_TEXT": "The following information will be provided to the products that require it.",
      "COMMON_FORM_EDITING_HINT": "Editing this information does not update the account",
      "ERROR_MESSAGE_FORM_INVALID": "Looks like some fields are invalid. Please correct the fields highlighted in red.",
      "PRE_REQ_FORM_IS_NOT_VALID": "Some required fields haven't been filled out or are incorrect.",
      "BUTTON_SUBMIT_ORDER_FORM": "Submit form",
      "ERROR_MESSAGE_FORM": "There was an error submitting the form. Please try again.",
      "BUSINESS_SECTION_TITLE": "Business",
      "SALESPERSON_SECTION_TITLE": "Salesperson",
      "CONTACT_SECTION_TITLE": "Contact",
      "BUSINESS_NAME": "Business Name",
      "BUSINESS_ADDRESS": "Business Address",
      "BUSINESS_PHONE_NUMBER": "Business Phone Number",
      "BUSINESS_WEBSITE": "Business Website",
      "BUSINESS_ID": "Business ID",
      "SALESPERSON_NAME": "Salesperson Name",
      "SALESPERSON_PHONE_NUMBER": "Salesperson Phone Number",
      "SALESPERSON_EMAIL": "Salesperson Email",
      "CONTACT_NAME": "Contact Name",
      "CONTACT_PHONE_NUMBER": "Contact Phone Number",
      "CONTACT_EMAIL": "Contact Email",
      "ORDER_SUMMARY": "Order Summary",
      "GET_TRIAL": "Get Trial",
      "NO_LONGER_AVAILABLE": "This order can't be altered. For more info, please contact your sales representative.",
      "UNSAVED_CHANGES_TITLE": "Proceed without changes?",
      "UNSAVED_CHANGES_PROMPT": "There are unsaved changes to the business profile. Do you want to proceed?",
      "PROCEED_WITHOUT_CHANGES": "Discard changes and proceed",
      "REVIEW_CHANGES": "Review changes"
    },
    "DECLINE_ORDER": {
      "TITLE": "Decline Order",
      "DETAILS": "This will send an email to your salesperson to alert them the order has been declined.",
      "MESSAGE_VALIDATION": "You must enter a message as decline reason",
      "SUCCESS": "Order was successfully declined",
      "ERROR": "An error occurred. Please try again"
    },
    "ORDER_CONFIRMATION": {
      "TITLE": "Order confirmation",
      "TOTAL": "Total",
      "SUBMITTED": "Your order was sent successfully.",
      "ERROR_MISSING_FORM": "Missing order form, please retry.",
      "CANNOT_BIY_PACKAGE_WARNING": "Your cart contains one or more items that require additional processing time. Our team will work to get it approved as soon as possible:\n    \u2022 {{package_names}}",
      "EDIT": "Edit",
      "ERROR": "An error occurred. Please try again",
      "APPROVED_MESSAGE": "Order has been approved"
    },
    "INTEREST_IN_PACKAGE": {
      "REQUEST_FOR_PACKAGE": "Request for {{ package_name }}",
      "CONTACT_YOUR_REP": "Contact Your Representative",
      "LEARN_MORE": "If you'd like to learn more or purchase {{ package_name }}, send us a message and we will contact you shortly.",
      "COMPOSE_MESSAGE": "Compose Message",
      "DEFAULT_ACTIVATION_MESSAGE": "I'm interested in {{ productName }}. Please send me more details!",
      "DEFAULT_TRIAL_ACTIVATION_MESSAGE": "I'm interested in trial {{ productName }}. Please send me more details!",
      "INVALID_USER_INPUT": "Invalid user input.",
      "DEFAULT_SUCCESS_MESSAGE": "Your request has been sent and we will contact you shortly!",
      "DEFAULT_ERROR_MESSAGE": "There was an error sending your request."
    },
    "SHOPPING_CART": {
      "TITLE": "Shopping cart",
      "CART": "Cart ({{ number_of_item }})",
      "CART_TITLE_SINGULAR": "1 item in your cart",
      "CART_TITLE_PLURAL": "{{ num_items }} items in your cart",
      "REQUIRES": "Requires {{ parentName }}",
      "ADDON_OF": "Add-on of {{ parentName }}",
      "ORDER_SUMMARY": "Order Summary",
      "SUBTOTAL": "Subtotal",
      "TOTAL": "Total",
      "STARTING_AT": "Starting at",
      "FREE": "Free",
      "PROCEED_TO_CHECKOUT": "Proceed to checkout",
      "REVIEW_AND_PLACE_ORDER": "View Cart",
      "MAX_ITEMS": "Can't add more than {{ max_items }} items in the cart.",
      "ADDED_TO_CART": "Added to cart",
      "REMOVED_FROM_CART": "Removed from cart",
      "CANNOT_ADD_DUPLICATE_ITEM": "Cannot add duplicate item",
      "ITEM_NOT_AVAILABLE_IN_CURRENT_COUNTRY": "Item not available in current country",
      "EMPTY_CART": "Your cart is empty",
      "EMPTY_CART_CONTENT": "Add products from the Store",
      "REVIEW_CONTENT": {
        "TITLE": "Cart Contents",
        "ITEMS": "Item(s)",
        "QUANTITY": "Quantity",
        "PRICE": "Price",
        "TOTAL": "Total",
        "WARNINGS": {
          "DUPLICATE_PRODUCTS_IN_CART": {
            "TITLE": "The following products appear more than once in your cart:",
            "ITEM": "in {{ app_name }}",
            "STANDALONE_ITEM": "on its own"
          },
          "ALREADY_PURCHASED_PRODUCTS_IN_CART": {
            "TITLE": "Your cart contains products that are already active on your account"
          },
          "EDITION_CHANGES_IN_CART": {
            "MESSAGE": "<b>{{ current_item }}</b> is currently active on your account. Purchasing <b>{{ new_item }}</b> will change to to the <b>{{ new_edition_name }}</b> edition."
          }
        }
      },
      "REMOVE_ITEM_DIALOG": {
        "TITLE": "Remove {{itemName}} and its dependent products?",
        "EXPLANATION": "Some items in your cart require {{itemName}} and can't be purchased without it. Removing {{itemName}} will also remove the following items from your cart:"
      }
    },
    "PRODUCT_REQUIREMENTS_DIALOG": {
      "ADDITION": {
        "SINGULAR": {
          "TITLE": "Add required product to cart?",
          "DESCRIPTION": "You'll need the following product before you can start using {{ productName }}:",
          "CONFIRMATION": "Add the above product to your cart as well?"
        },
        "PLURAL": {
          "TITLE": "Add required products to cart?",
          "DESCRIPTION": "You'll need the following products before you can start using {{ productName }}:",
          "CONFIRMATION": "Add the above products to your cart as well?"
        },
        "BUTTONS": {
          "CONFIRM": "Add to cart"
        }
      },
      "REMOVAL": {
        "SINGULAR": {
          "TITLE": "Remove dependent product?",
          "DESCRIPTION": "Without {{ productName }}, you won't be able to activate the following product in your cart:",
          "CONFIRMATION": "Remove the above product from your cart as well?"
        },
        "PLURAL": {
          "TITLE": "Remove dependent products?",
          "DESCRIPTION": "Without {{ productName }}, you won't be able to activate the following products in your cart:",
          "CONFIRMATION": "Remove the above products from your cart as well?"
        },
        "BUTTONS": {
          "CONFIRM": "Remove all"
        }
      }
    },
    "PERMISSION_DENIED": {
      "TITLE": "We can't show you this",
      "BODY": "This order isn't currently accessible. You can still review its contents by contacting your salesperson."
    },
    "MARKET_ACTION_LABELS": {
      "CONTACT_FORM": "Contact Sales",
      "ORDER_FORM_NO_CART": "Get It Now",
      "ORDER_FORM_WITH_CART": "Buy",
      "EXTERNAL_URL": "Buy"
    },
    "ITEM_NOT_FOUND_DIALOG": {
      "TITLE": "This item is unavailable",
      "CONTENT": {
        "PARAGRAPH_ONE": "The item you were looking for is unavailable.",
        "PARAGRAPH_TWO": "Browse the rest of our store, or contact us and let us know what you're looking for."
      },
      "CLOSE_BUTTON": "Browse store"
    },
    "PRODUCT": {
      "ERROR_PRODUCT_LOAD": "Failed to load product"
    }
  },
  "PRODUCTS": {
    "FEEDBACK": {
      "ERROR": "There was a problem submitting feedback, please try again later"
    },
    "APPROVE": {
      "ERROR": "There was a problem submitting approval, please try again later"
    },
    "STORE_LINK": {
      "TEXT": "Add products"
    },
    "PRESCRIPTION": {
      "TITLE": "Everything you need, in one place",
      "SECONDARY_1": "Choose which products appear first by clicking the pin icon."
    },
    "DISABLED_TEXT": {
      "GOOGLE_WORKSPACE": "You must log directly in to {{businessAppName}} and have an active seat to access Google Workspace",
      "OFFICE_365": "You must log directly in to {{businessAppName}} and have an active license to access Office 365",
      "OTHER": "You do not have permission to access this product"
    },
    "FREE_TRIALS": "Start {{ number_of_days }} Day Free Trial",
    "LAUNCH": "Launch",
    "INFO": "Info",
    "BADGES": {
      "NEW": "New!",
      "PREVIEW": "Preview",
      "FREE_TRIAL": "Free Trial"
    },
    "TRIAL_REMAINING": "{{ number_of_days }} Days",
    "TRIAL": "TRIAL",
    "PIN": "Pin",
    "UNPIN": "Unpin",
    "ACTIONS": {
      "UPGRADE_TO_PRO": "Upgrade to Pro",
      "UPGRADE_TO_PAID": "Upgrade to paid",
      "CONSTANT_CONTACT_VIEW_CURRENT_TIER": "Current tier",
      "CANCEL_SERVICE": "Request Cancellation"
    },
    "INFORMATION": {
      "CANCELLATION_REQUESTED": "Cancellation requested"
    },
    "TRIAL_UPGRADE_DIALOG": {
      "TITLE": "Upgrade from trial?",
      "DESCRIPTION": "End your trial and upgrade to the paid version of this product.",
      "ADDITIONAL_INFORMATION": "This product's edition can be changed after upgrading.",
      "ACTION_BUTTON": "Upgrade"
    },
    "TRIAL_UPGRADE_EDITION_MODAL": {
      "TITLE": "Upgrade from trial",
      "SELECT_EDITION_TEXT": "Select product edition:",
      "SELECT_CONTACTS_TEXT": "Select # of contacts:",
      "CURRENT_EDITION": "Current",
      "ACTION_BUTTON": "Upgrade from trial",
      "CANCEL_BUTTON": "Cancel",
      "SNACK_ERROR": "Uh-oh. Looks like something went wrong. Please try again."
    },
    "TRIAL_ORDER_FORM": {
      "ACCOUNT": "Account",
      "SELLER": "Seller"
    }
  },
  "CANCEL_SERVICE_DIALOG": {
    "TITLE": "Request cancellation of {{ productName }}",
    "DESCRIPTION": "Why are you requesting the cancellation of this product?",
    "COMPLETE_CANCELLATION_BUTTON": "Submit",
    "GO_BACK_BUTTON": "Go back",
    "ADDITIONAL_INFORMATION_HINT": "Include any other information here",
    "SUBMIT_CANCELLATION_ERROR_SNACK": "An error occurred when submitting your cancellation request. Please try again",
    "CANCELLATION_REASON": {
      "OTHER": "Other"
    }
  },
  "CUSTOMER_DIALOG": {
    "CREATE_TITLE": "Create Customer",
    "EDIT_TITLE": "Edit Customer",
    "BUTTONS": {
      "CREATE_CUSTOMER": "Create customer"
    }
  },
  "CUSTOMER_FORM": {
    "LABELS": {
      "FIRST_NAME": "First name",
      "LAST_NAME": "Last name",
      "EMAIL": "Email",
      "PHONE_NUMBER": "Phone number",
      "TAGS": "Tags",
      "TAG": "Tag",
      "EMPLOYEE_FIRST_NAME": "Employee first name",
      "EMPLOYEE_LAST_NAME": "Employee last name",
      "PERMISSION_TO_CONTACT": "Permission to contact this customer"
    },
    "BUTTONS": {
      "ADD_EMAIL": "+ Add another email",
      "ADD_PHONE_NUMBER": "+ Add another phone number",
      "ADVANCED": "Advanced",
      "USE_CUSTOMER": "Select this customer",
      "EDIT_CUSTOMER": "Edit this customer"
    },
    "MESSAGES": {
      "ADDED_SUCCESSFULLY": "Customer added successfully",
      "ERROR_ADDING": "Error adding customer",
      "CUSTOMER_EXISTS": "This customer already exists",
      "CONFLICTS_NEED_ATTENTION": "Whoops! You already used that email/phone number to create a customer. Please use a different one",
      "EMAIL_OR_PHONE_REQUIRED": "Either an email or phone number is needed to create a customer. Please enter one"
    }
  },
  "CUSTOMER_LIST": {
    "ADD_CUSTOMER": "Add customer",
    "BULK_ADD_CUSTOMERS": "Bulk add customers",
    "ACTIONS": "Actions",
    "SYNC_WITH_PRODUCT": "Sync with product",
    "STOP_SYNCING": "Stop syncing with this product",
    "ALL_CUSTOMERS": "All customers",
    "LINKED_PRODUCTS": "Linked products",
    "LINKED_PRODUCTS_SYNC": "Linked products sync",
    "PERMISSIONS": "Permissions",
    "PERMISSION_TO_CONTACT_YES": "Yes",
    "PERMISSION_TO_CONTACT_NO": "No",
    "PERMISSION_TO_CONTACT": "I have permission to contact this customer",
    "SYNC_APPS": "Sync customers to products",
    "I_HAVE_PERMISSION": "I have permission to contact these customers",
    "I_HAVE_PERMISSION_SINGULAR": "I have permission to contact this customer",
    "PRODUCT_NEEDS_PERMISSION_TO_CONTACT": "This product requires permission to contact before syncing customers",
    "LIST_WILL_SYNC": "Customer List will sync your contacts to the following products:",
    "SOME_PRODUCTS_REQUIRE_PERMISSION_TO_CONTACT": "Certain products require you to have permission to contact before syncing",
    "NO_CUSTOMERS_FOUND": "No customers found",
    "UPDATE_CUSTOMER": "Update customer",
    "DO_NOT_IMPORT": "Do not import",
    "MAP_FIELDS": "Map fields",
    "MAP_DESCRIPTION": "Map your CSV columns to their corresponding fields. <span>Header Row</span>and<span>First Row</span>reflect what's in your CSV file. Use the<span>Mapping dropdown</span>to select which attribute the column is associated with.",
    "CUSTOMERS_EDITED": "Customers have been updated",
    "NO_CUSTOMERS_YET": "No customers yet",
    "NO_CUSTOMERS_DETAIL": "Manage all of your customer's information in one place. Add customers to the list to keep your data synced across applicable products, or purchase products that push customer data to the list.",
    "CREATE_A_CONTACT": "Create a Contact",
    "IN_PROGRESS": "File Import in Progress",
    "FILTER": {
      "TITLE": "Customer List filters",
      "LABELS": {
        "SHOWING_X_OF_TOTAL": "Showing {{ amount }} of {{ totalResults }}",
        "SHOWING_X": "Showing {{ amount }}",
        "SOURCE": {
          "TITLE": "Source",
          "PLACEHOLDER": "Select source"
        }
      }
    },
    "DELETE_CUSTOMER_MODAL": {
      "HEADER": "Are you sure you want to delete {{ customer_name }}?",
      "CONTENT": "Deleting a customer in your Customer List will delete this customer in all synced products."
    },
    "HEADERS": {
      "CUSTOMER": "Customer",
      "PHONE_NUMER": "Phone number",
      "CREATED": "Created",
      "SOURCE": "Source",
      "HEADER_ROW": "Header row",
      "FIRST_ROW": "First row",
      "MAPPING": "Mapping",
      "PERMISSION_TO_CONTACT": "Permission to contact"
    },
    "BULK_PERSCRIPTIONS": {
      "STEP_1": "Import your customers to Customer List to sync across applicable products. Upload your existing customer data CSV or build your own using <a href=\"https://storage.googleapis.com/bulk-customers-upload/important/bulk-create-customer-template.csv\" download=\"bulk-create-customer-template.csv\">this template.</a>",
      "STEP_2": "Map your CSV columns to their corresponding fields. Header Row and First Row reflect what's in your CSV file. Use the Mapping dropdown to select which attribute the column is associated with.",
      "STEP_3_A": "Importing this will take several minutes depending on the number of customers you are creating. Confirm the details are accurate before starting the import.",
      "STEP_3_B": "Using {{ filename }} to attempt to create {{ number }} customers",
      "REQUIRED_FIELDS": "Required fields:"
    },
    "PRESCRIPTION": {
      "TITLE": "Do more with your customer information",
      "SECONDARY_1": "Add your customers to automatically sync their information to the applicable products you've subscribed to.",
      "EMAIL_AND_COUNTRY_CODE_REQUIRED": "email or phone with country code"
    },
    "BULK_ADD_CUSTOMERS_PAGE": {
      "VALIDATION_ERRORS": "There is {{errorNumber}} row(s) with errors in your CSV. Please address these errors and upload the file again.",
      "NO_CUSTOMERS_ADDED": "No customers have been added.",
      "TOO_MANY_CUSTOMERS": "The file can't contain more than 10,000 rows.",
      "TOO_MANY_CUSTOMERS_ERROR": "Error: The file can't contain more than 10,000 rows.",
      "ROW": "row",
      "ERROR": "error",
      "EMAIL_OR_PHONE_REQUIRED": "Either email address or phone number is required",
      "INVALID_EMAIL": "Invalid or missing email address",
      "INVALID_PHONE": "Invalid phone number. Please include the country code in the phone number.",
      "INVALID_FIRST_NAME": "First name cannot include ',' or ';'",
      "INVALID_LAST_NAME": "Last name cannot include ',' or ';'",
      "BULK_ADD_ERROR": "Error bulk creating Customer",
      "UNKNOWN_ERROR": "Unknown error while bulk adding Customers"
    },
    "COMPOSE": {
      "EMAIL_TITLE": "Compose Email",
      "EMAIL_SUBJECT": "Email Subject",
      "EMAIL_REPLY": "Reply-To address",
      "INFO_BUTTON": "What's this?",
      "CANCEL": "Cancel",
      "SEND": "Send to {{quantity}} of {{total}}",
      "WARNING": "{{quantity}} customers missing email or have not given permission",
      "EMAIL_INFO_TEXT": "Replies to this message will be sent to the Reply-To address. The Reply-To address will also receive a copy of this message."
    },
    "ERROR": {
      "ERROR_TO_GET_CUSTOMER": "Error to get customer: {{message}}"
    }
  },
  "GUIDES": {
    "PRESCRIPTION": {
      "TITLE": "Master your marketing",
      "SECONDARY_1": "Discover practical strategies that will enhance your digital marketing and help you succeed online."
    },
    "FALLBACK": {
      "STAGE_2": "We didn't find any blog posts on <strong>{{ prevUrl }}</strong>, but we did find some on <strong>{{ url }}</strong>. To increase the loading speed of guides, please update the url in Partner Center to <strong>{{ url }}</strong>. <br/><br/>You can review your URL in Partner Center > Administration > Customize > Business App Settings.",
      "STAGE_3": "We didn't find any blog posts on <strong>{{ url }}</strong>. Double-check that the URL entered in Partner Center is valid, or visit the <a href='https://developer.wordpress.org/rest-api/reference/posts/'>Wordpress documentation</a> for more information. <br/><br/>You can review your URL in Partner Center > Administration > Customize > Business App Settings."
    },
    "ERROR_LOADING": "We're having some trouble loading guides. Try refreshing the page, or contact us if this keeps happening.",
    "LOADING_ARTICLE": "Loading Article",
    "RECOMMENDED_PRODUCTS": "Recommended Products"
  },
  "BRANDS": {
    "SOCIAL_POSTS_PENDING": "{{ number_pending }} social post(s) pending.",
    "SELECT_BRAND": "Select group",
    "APPROVE_ALL": "Approve all",
    "REVIEW_POSTS": "Review posts",
    "VIEW_REPORT": "View report",
    "VIEW_LEGACY_REPORT": "View legacy report",
    "COMPOSE_A_POST": "Create",
    "POST_SUCCESS": "Post submitted",
    "POST_SCHEDULED": "Post scheduled",
    "POST_EDIT": "Post edited",
    "RESPOND_TOOLTIP": "Respond to this review",
    "RESPOND_BOX_HINT": "An email response will be sent to this customer",
    "RESPOND_BOX_POST": "Post",
    "GMB_REVIEW_RESPOND_DISABLED": "This review already has a response. To see response click View 1 response.",
    "LOCATIONS_MODAL": {
      "NO_RESULTS": "No results found for your search",
      "SELECTED_LOCATIONS": "Selected locations",
      "NO_LOCATIONS": "No locations selected"
    },
    "ACTIONS": {
      "SEE_MORE": "See more",
      "SEE_LESS": "See less"
    },
    "GOOGLE_MY_BUSINESS": {
      "REPORT": {
        "LINK_TITLE": "Google Business Profile"
      }
    },
    "SENTIMENT_ANALYSIS": {
      "TITLE": "Explore trending keywords",
      "TRENDING_KEYWORDS": "Trending keywords",
      "TRENDING_TOPICS": "Trending topics",
      "ACTIONS": {
        "SHOW_MORE": "Show More",
        "SHOW_LESS": "Show Less"
      },
      "SORTING": {
        "ALL_KEYWORDS": "All keywords",
        "MOST_POSITIVE": "Most positive",
        "MOST_NEGATIVE": "Most negative"
      },
      "SEARCH_KEYWORDS": "Search keywords",
      "SENTIMENT_OVER_TIME": {
        "CARD": {
          "SENTIMENT": "Sentiment",
          "MENTIONS": "Mentions",
          "REVIEWS": "Reviews",
          "POSITIVE": "Positive",
          "NEGATIVE": "Negative",
          "NEUTRAL": "Neutral",
          "TOOLTIP": "These keywords show the most frequently mentioned topics from customer reviews in the past year."
        },
        "GRAPH": {
          "TITLE": "Sentiment over time for {{phrase}}",
          "TITLE_HTML": "Sentiment over time for <span>{{phrase}}</span>",
          "POSITIVE": "Positive",
          "NEGATIVE": "Negative"
        },
        "TOOLTIP": {
          "SENTIMENT_GROWTH": "This keyword's sentiment has risen compared to the previous date range",
          "SENTIMENT_DECLINE": "This keyword's sentiment has fallen compared to the previous date range",
          "MENTIONS_GROWTH": "This keyword has been mentioned more often compared to the previous date range",
          "MENTIONS_DECLINE": "This keyword has been mentioned less often compared to the previous date range"
        }
      },
      "WORD_PAIR": {
        "TITLE": "Customers describe {{phrase}} as",
        "TITLE_HTML": "Customers describe <span>{{phrase}}</span> as",
        "TOOLTIP": "Sentiment: {{score}}",
        "NO_DESCRIPTION_LIST": "No descriptions found for the selected period",
        "NO_DESCRIPTION_LIST_BY_SENTIMENT_HTML": "No <span>{{sentiment}}</span> descriptions for this date range"
      },
      "YOUR_CUSTOMERS_WROTE": "Your customers wrote",
      "DISPLAYED_REVIEW_COUNT": "Showing {{count}} most recent reviews",
      "EXEC_REPORT": {
        "TITLE": "Insights",
        "SUBTITLE": "What your customers are saying about your business",
        "EMPTY_STATE_NO_DATA": "No data was found for the selected period",
        "EMPTY_STATE_NO_REVIEWS_WITH_CONTENT": "Request reviews to discover insights about what your customers are saying"
      },
      "TOP_LOCATIONS": {
        "TITLE": "Your most positive and negative locations",
        "DESCRIPTIONS": "Descriptions",
        "LOCATIONS": "Locations",
        "MOST_POSITIVE": "Most positive",
        "MOST_NEGATIVE": "Most negative"
      }
    },
    "REVIEWS": {
      "MAP_PIN_LABEL": "Review Rating",
      "ACTIONS": {
        "HIDE": "Hide",
        "SHOW": "Show",
        "COMMENT": "response",
        "COMMENTS": "responses",
        "RESPONSE_FROM": "Response from"
      },
      "CHIP_LABELS": {
        "ACTION_REQUIRED": "Action required",
        "NO_ACTION_REQUIRED": "No action required",
        "RESPONDED": "Responded",
        "PUBLISHED": "Published"
      },
      "EXEC_REPORT": {
        "REVIEW_GRADE": {
          "TITLE": "Review grade",
          "CHART_LABEL": "Your Review Grade",
          "DATA_TITLE": "(All time)",
          "TABLE": {
            "TITLE": "Review grade breakdown",
            "ROWS": {
              "AVERAGE_REVIEW_SCORE": "Average review score",
              "NUMBER_OF_REVIEW_SOURCES": "Number of review sources",
              "REVIEWS_FOUND": "Reviews found",
              "NUMBER_OF_REVIEWS_FOUND_PER_MONTH": "Number of reviews found per month"
            },
            "COLUMNS": {
              "GRADE": "Grade",
              "PERCENTILE": {
                "NAME": "Percentile",
                "TOOLTIP_TITLE": "What is a percentile?",
                "TOOLTIP": "Your percentile represents where your Review Grade falls among all other businesses in your industry, relative to the industry leader's Review Grade. For example, being in the 70th percentile means that 70% of businesses in your industry have a lower Review Grade than yours."
              },
              "BUSINESS": "Business",
              "INDUSTRY_AVERAGE": {
                "NAME": "Industry average",
                "TOOLTIP_TITLE": "What is an industry average?",
                "TOOLTIP": "The average across businesses in your industry for each category. The higher you are above the industry average in a given category, the higher your grade for that category will be."
              },
              "INDUSTRY_LEADER": {
                "NAME": "Industry leader",
                "TOOLTIP_TITLE": "What is an industry leader?",
                "TOOLTIP": "The highest score for each category in your industry. The closer you are to the industry leader in a given category, the higher your grade for that category will be."
              }
            }
          },
          "BREAKDOWN": {
            "SHOW": "Show detailed breakdown",
            "HIDE": "Hide detailed breakdown"
          }
        },
        "SOCIAL_GRADE": {
          "TITLE": "Social grade",
          "CHART_LABEL": "Your Social Grade",
          "DATA_TITLE": "(All time)",
          "TABLE": {
            "TITLE": "Social grade breakdown",
            "ROWS": {
              "AVERAGE_FB_LIKES": "Average Likes per Facebook Post",
              "AVG_FB_POSTS_PER_MONTH": "Average Facebook Posts Per Month",
              "AVG_SHARES_PER_FB_POST": "Average Shares per Facebook Post",
              "TOTAL_FB_LIKES": "Total Facebook Post Likes",
              "TOTAL_IG_POSTS": "Total Instagram Posts",
              "TOTAL_IG_FOLLOWERS": "Total Instagram Followers",
              "TOTAL_TWITTER_POSTS": "Total Twitter Posts",
              "TWITTER_FOLLOWERS": "Total Twitter Followers",
              "TWITTER_FOLLOWED": "# of Twitter Accounts Followed"
            },
            "COLUMNS": {
              "GRADE": "Grade",
              "PERCENTILE": {
                "NAME": "Percentile",
                "TOOLTIP_TITLE": "What is a percentile?",
                "TOOLTIP": "Your percentile represents where your Social Grade falls among all other businesses in your industry, relative to the industry leader's Social Grade. For example, being in the 70th percentile means that 70% of businesses in your industry have a lower Social Grade than yours."
              },
              "BUSINESS": "Business",
              "INDUSTRY_AVERAGE": {
                "NAME": "Industry average",
                "TOOLTIP_TITLE": "What is an industry average?",
                "TOOLTIP": "The average across businesses in your industry for each category. The higher you are above the industry average in a given category, the higher your grade for that category will be."
              },
              "INDUSTRY_LEADER": {
                "NAME": "Industry leader",
                "TOOLTIP_TITLE": "What is an industry leader?",
                "TOOLTIP": "The highest score for each category in your industry. The closer you are to the industry leader in a given category, the higher your grade for that category will be."
              }
            }
          },
          "BREAKDOWN": {
            "SHOW": "Show detailed breakdown",
            "HIDE": "Hide detailed breakdown"
          }
        },
        "RECENT_REVIEWS": {
          "TITLE": "Recent reviews",
          "FOOTER_TEXT": "All reviews",
          "SUBTITLE": "What your customers are saying about your business",
          "REVIEW_CARD": {
            "TITLE": "{{reviewer_name}} reviewed your business on {{ review_domain }}"
          }
        },
        "REVIEW_RATING": {
          "TITLE": "Review ratings",
          "FOOTER_TEXT": "Review details",
          "TOTAL_REVIEWS": "{{total_ratings}} Total reviews"
        },
        "AVG_RESPONSE_TIME": {
          "TITLE": "Average response time",
          "TOOLTIPS": "Average response time",
          "DISCLAIMER": "Data shown for Google, Facebook, Yelp & My Listing.",
          "HOURS": "hours",
          "Y_AXIS_LABEL": "Average response time (hrs)",
          "NO_DATA_TITLE": "No data yet",
          "NO_DATA_DESCRIPTION": "Once you've responded to reviews, the average time it takes to respond will show here",
          "FOOTER_TITLE": "Review details"
        },
        "REVIEW_VOLUME": {
          "TITLE": "Review volume",
          "RESPONDED": "Responded to",
          "UNRESPONDED": "No response",
          "THERMOMETER_STAT": {
            "STAT_TEXT": "{{value}} ({{percent}}) Responded to"
          },
          "Y_AXIS_LABEL": "New reviews",
          "FOOTER_TEXT": "Review details"
        },
        "TOP_REVIEWS_SOURCES": {
          "TITLE": "Top review sources",
          "CARD_FOOTER": "Review sources",
          "NUMBER_OF_REVIEWS": "{{number}} reviews"
        },
        "GOOGLE_QUESTIONS": {
          "TITLE": "Google Q&A",
          "SUBTITLE": "Total questions answered by owner",
          "LEGEND_TITLES": {
            "TOTAL_UNANSWERED_QUESTIONS": "Unanswered questions",
            "ANSWERED_BY_OWNERS": "Answered by owner",
            "ANSWERED_ONLY_BY_PUBLIC": "Answered only by public"
          }
        },
        "RECENT_REQUESTS": {
          "TITLE": "Recent requests",
          "SENT": "Sent",
          "OPENED": "Opened",
          "CLICKED": "Clicked",
          "EMAIL": "Email",
          "SMS": "SMS",
          "FOOTER_TEXT": "Review details"
        },
        "REPUTATION_MANAGEMENT": {
          "TITLE": "Reputation Management"
        },
        "SOCIAL_MARKETING": {
          "TITLE": "Social Marketing"
        }
      },
      "RESPONSE_TEMPLATES": {
        "ONLY_AVAILABLE_FOR_BRAND": "Review Response Templates created here are only available for use<br> when using Multi-Location for {{brand_name}}.",
        "MANAGE_TEMPLATES": "Manage templates"
      }
    },
    "LISTINGS": {
      "MAP_PIN_LABEL": "Listing Score",
      "REPORT": {
        "LINK_TITLE": "Listings"
      },
      "EXPORT_DATA": "Export data",
      "SEARCH": "Search for location",
      "SEARCH_FOR_LOCATION": "Business name or address",
      "SHOW_COUNTS": "Showing {{ loadedListing }} of {{ totalListing }}",
      "EXPAND_ALL": "Expand All",
      "COLLAPSE_ALL": "Collapse All"
    },
    "ADVERTISING": {
      "REPORT": {
        "LINK_TITLE": "Advertising"
      }
    },
    "SOCIAL": {
      "ACTIONS": {
        "DELETE": "Delete",
        "RETRY": "Retry",
        "EDIT": "Edit"
      },
      "POSTED_ON": "Posted on ",
      "STORY_POSTED_ON": "Story posted on",
      "STORY_POSTED_ON_IG": "Posted as a story on Instagram",
      "SCHEDULED_ON": "Scheduled on ",
      "STORY_SCHEDULED_FOR": "Story scheduled for",
      "STORY_SCHEDULED_ON_IG": "Scheduled as a story on Instagram",
      "POSTED_AT_TEXT": "at",
      "FAILED_TOOLTIP": "Post failed - Please try reconnecting or retrying.",
      "FAILED_TO_LOAD": "Failed to get Multilocation Posts",
      "REPORT": {
        "LINK_TITLE": "Social"
      },
      "NAVIGATION": {
        "TABS": {
          "RECENT": "Recent",
          "SCHEDULED": "Scheduled",
          "FAILED": "Failed"
        }
      },
      "OTHER_LOCATIONS": {
        "TITLE": "Locations",
        "LOCATIONS": "Locations",
        "NETWORK": "Network",
        "AND": "and ",
        "REMAINING_LOCATIONS": "{{ remaining_locations }} other locations",
        "ALL_LOCATIONS": "all locations",
        "EDITED_DELETED_INFO": "location/s has been edited or deleted and can no longer be included when making bulk edits in Multilocation Social Marketing."
      },
      "DELETE_MLPOST": {
        "DIALOG_TITLE": "Delete post?",
        "DELETE_CONFIRMATION_MESSAGE": "This post will be deleted on [{{ social_media_count }}] social media account[s]. Deleted posts cannot be recovered.",
        "FAILED_TO_DELETE": "Failed to Delete Multilocation Post",
        "DELETE_SUCCESS": "Multilocation Post deleted successfully",
        "ACTIONS": {
          "CANCEL": "Cancel",
          "DELETE_POST": "Delete Post"
        }
      },
      "MANAGE_POSTS": {
        "EMPTY_STATE": {
          "TITLE": "Post to everywhere from one place",
          "DESCRIPTION": "Save time by creating one post for all your social pages.",
          "NO_FAILED_POSTS": "There are no failed posts"
        }
      },
      "CREATE": {
        "CATEGORY": {
          "BLOG": "BLOG",
          "SOCIAL_MEDIA": "SOCIAL MEDIA"
        },
        "PRO": "Pro",
        "COMMON_FLOW": "Post",
        "COMMON_FLOW_DESC": "Text and media content",
        "ML_COMMON_FLOW_DESC": "Create post across multilocation",
        "STORY_FLOW": "Story",
        "STORY_FLOW_DESC": "Temporary content shared for 24 hours only",
        "VIDEO_FLOW": "Long video",
        "VIDEO_FLOW_DESC": "Comprehensive video content",
        "POST_CAMPAIGN": "Post campaign",
        "BULK_CREATE_FLOW": "Import CSV",
        "BULK_CREATE_FLOW_DESC": "Upload to schedule across multilocation",
        "CREATE_BLOG_POSTS": "Blog post",
        "CREATE_BLOG_POSTS_DESC": "Long form content"
      }
    },
    "GOOGLE_ANALYTICS": {
      "REPORT": {
        "LINK_TITLE": "Google Analytics"
      }
    },
    "GOOGLE_ANALYTICS4": {
      "REPORT": {
        "LINK_TITLE": "Google Analytics4"
      }
    },
    "FILE_GROUPS": {
      "EXEC_REPORT": {
        "TITLE": "Files",
        "TITLE_IMAGES": "Latest Images",
        "REPORT": {
          "LINK_TITLE": "See all files",
          "LINK_TITLE_IMAGES": "See all images"
        },
        "CREATED": "Posted on {{ created }}"
      }
    },
    "TASK_MANAGER": {
      "REPORT": {
        "PROJECTS_TITLE": "Projects",
        "PROJECTS_SUBTITLE": "Projects we're doing for you",
        "TASKS_TITLE": "Tasks",
        "TASKS_SUBTITLE": "Tasks we're doing for you",
        "PROJECT_UPDATES_TITLE": "Project updates",
        "PROJECT_UPDATES_SUBTITLE": "{{ count }} projects with recent updates",
        "COUNT_IN_PROGRESS": "{{ count }} in progress",
        "COUNT_COMPLETED": "{{ count }} completed",
        "PROJECTS": "projects",
        "TASKS": "tasks",
        "COMPLETED": "Completed",
        "IN_PROGRESS": "In progress",
        "SERVICES": "Projects",
        "SEE_MORE": "See more",
        "SEE_LESS": "See less",
        "TASK_APPROVAL": "Please review and approve this part of your project.",
        "PERCENT_COMPLETE": "% complete"
      }
    },
    "BRANDS_MOVED": {
      "MOVED_TITLE": "MOVED: Access to Multi-Location Groups reporting is now in the location switcher",
      "MOVED_INSTRUCTIONS": "To navigate to Multi-Location group view, click the dropdown displaying your business name and address in the top-left corner of your screen, select the Group tab, and choose a Group to view."
    }
  },
  "EMAIL_LIBRARY": {
    "TITLE": "Emails"
  },
  "EMAIL_CONFIGURATION": {
    "TITLE": "Email configuration",
    "EXPRESS_WARNING": "Email domain settings are only applicable to the Pro edition of {{ productName }}",
    "QUESTION": "What is it?",
    "INFO_1": "These settings, when properly configured, will allow the applicable products to send emails from your email address using your domain name.",
    "INFO_2": "Example => From: <EMAIL>",
    "INFO_3": "When all of these categories are properly configured in the DNS Settings panel of your domain hosting, emails that you send out will have a lower chance of landing in the recipient's spam folder.",
    "INFO_4a": "Go to the DNS settings of your domain hosting, and enter the correct information in the corresponding fields. Once that is done you can validate the settings here by clicking",
    "INFO_4b": "Run All Validation",
    "INFO_5": "Note: DNS changes may take up to 24 hours to take effect.",
    "DOMAIN": "Domain - ",
    "DOMAIN_SUBTEXT": "Note: DNS changes may take up to 24 hours to take effect.",
    "ERROR_NO_DOMAIN": "Please provide 'Send From' email.",
    "ERROR_ON_SUBMIT": "An unexpected error occurred, please try again",
    "SUCCESS_ON_SUBMIT": "Updated send from information",
    "DKIM": {
      "TITLE": "DKIM Validation",
      "INFO": "A Domain Keys Identified Mail (DKIM) record will help you protect your domain from spamming and phishing.\n            DKIM will validate a domain name associated with a message, proving your\n            authentication and help increase your deliverability rates.",
      "INVALID": "Please update your DNS with the following entries:",
      "VALID": "Your DKIM Records are valid and up to date!",
      "VALIDATE": "Validate DKIM",
      "CREATE": "Create DKIM",
      "RECORD_TYPE": "Record Type",
      "SUBDOMAIN": "Subdomain",
      "VALUE": "Value"
    },
    "SPF": {
      "TITLE": "SPF Validation",
      "INFO": "Sender Policy Framework (SPF) is an email authentication standard that compares the email sender's actual IP address to a list of IP addresses authorized to send mail from that domain. The IP list is published in the domain's DNS record.",
      "VALID_1": "Looks like you have ",
      "VALID_2": " in your SPF record, everything is good to go",
      "INVALID_1": "Please add ",
      "INVALID_2": " to your current SPF Record ",
      "INVALID_3": "Suggestion: ",
      "MISSING_1": "No SPF record found. A TXT record for SPF similar to ",
      "MISSING_2": " will need to be added to your DNS",
      "VALIDATE": "Validate SPF"
    },
    "DMARC": {
      "TITLE": "DMARC Validation",
      "INFO": "A Domain-based Message Authentication, Reporting and Conformance (DMARC) policy allows a sender to indicate that their messages are protected by SPF and/or DKIM, and tells a receiver what to do if neither of those authentication methods passes – such as junk or reject the message.",
      "INVALID_1": "No DMARC record found. A TXT record with the subdomain",
      "INVALID_2": " and a value of",
      "INVALID_3": " will need to be added to your DNS.",
      "VALID": "Looks like you have a valid DMARC record, everything is good to go.",
      "VALIDATE": "Validate DMARC"
    },
    "POSTMASTER": {
      "TITLE": " Postmaster Tools",
      "INFO_1": "Almost 37% of the world's email addresses are hosted by Google, giving\n        them a significant influence on the overall deliverability of email.\n        For this reason, senders of bulk email (including email marketers) can\n        register with  Google's Postmaster Tools to receive feedback and\n        guidelines on best practices for bulk emailing. Google also uses this\n        network to identify bulk email from non-registered senders and mark\n        them as spam automatically.",
      "INFO_2": "To avoid deliverability issues when sending bulk emails, register as a\n        bulk sender at",
      "INFO_3": ". You will receive\n        instructions on the registration process, including setting a DNS record.",
      "INFO_4": "Once you have registered, Google will stop filtering bulk emails that\n        you send, and will provide information on the reputation of your\n        domain and the IPs you use to send an email.",
      "INFO_5": "For any additional help setting this up, contact support at the following:"
    },
    "SEND_AS": {
      "TITLE": "Email <b>Send From</b> Information",
      "EMAIL_HINT": "Email that will be used to send emails",
      "REQUIRED": "This field is required",
      "EMAIL_INVALID": "Must be a valid email address: <EMAIL>",
      "NAME_HINT": "The name users will see when they receive an email",
      "SAVE": "Save",
      "RESET": "Reset"
    },
    "UNSAVED_CHANGES_WARNING": "You have unsaved changes. Press Cancel to go back and save these changes, or OK to lose these changes."
  },
  "BILLING_SETTINGS": {
    "PAGE_TITLE": "Billing settings",
    "PAYMENT_METHOD": "Payment method",
    "HINT_TEXT": "Default payment method used for purchases and subscription renewals",
    "EMPTY_TEXT": "A default payment method has not been selected.",
    "SUBMIT_TEXT": "Save",
    "PAYMENT_METHOD_EMPTY_STATE": "Billing Settings have not been enabled"
  },
  "NOTIFICATION_SETTINGS": {
    "NOTIFICATION_SETTINGS_TITLE": "Notifications",
    "GLOBAL": "Global settings",
    "ALL_DISABLED": "All notifications have been disabled. Emails will not be received.",
    "GLOBALLY_DISABLED": "Globally disabled",
    "INSTANT_EMAIL": "Instant Email Notifications",
    "INSTANT_EMAIL_NOTIFICATIONS": "Instant email and In-App (when available)",
    "DAILY_DIGEST": "Daily Digest",
    "DAILY_DIGEST_EMAIL": "Daily Digest email",
    "UNABLE_TO_FIND": "Unable to find notification settings for {{ recipient_name }}",
    "SETUP_NOTIFICATIONS": "Set up notifications",
    "MANAGE_RECIPIENTS": {
      "ADD_RECIPIENT": "Add recipient"
    },
    "EMAIL_IS_REQUIRED": "Email is required",
    "EMAIL_VALIDATION": "Please enter a valid email address",
    "DELETION_PRESCRIPTION": "If deleted, {{ recipient }} will no longer receive notifications for {{ company_name }}, {{ company_location }}.",
    "ARE_YOU_SURE": "Are you sure?",
    "CONFIRM_DELETE": "Yes, delete recipient",
    "EXECUTIVE_REPORT": "Executive Report",
    "FAILED_TO_UPDATE_SETTINGS": "Failed to update settings"
  },
  "NOTIFICATIONS_UNSUBSCRIBE": {
    "TITLE": "Would you like to unsubscribe from this email notification?",
    "DESCRIPTION": "Unsubscribe from <b>{{ name }}</b> notifications.",
    "UNSUBSCRIBE": "Unsubscribe",
    "SUCCESS_TITLE": "You have been unsubscribed",
    "SUCCESS_DESCRIPTION": "You will no longer receive <b>{{ name }}</b> notifications.",
    "INVALID_LINK": "Link is no longer valid."
  },
  "ACCOUNT_FILES": {
    "TABLE_HEADERS": {
      "FROM": "From",
      "TITLE": "Title",
      "FILES": "Files",
      "DATE_POSTED": "Date Posted",
      "LAST_MODIFIED": "Last Modified"
    },
    "FILES_UNAVAILABLE": "You have no files.",
    "NO_FILES_MESSAGE": "Files that are shared with you will appear in this tab.",
    "DOWNLOAD_AND_VIEW": "Download and view files:"
  },
  "PRESCRIPTION": {
    "DISMISSED_STATE": "What's this?"
  },
  "SOCIAL_CONNECTIONS": {
    "PRESCRIPTION": {
      "TITLE": "Do more with {{ app_name }}",
      "SECONDARY_1": "Connect the external accounts and services you use every day to enable more features of {{ app_name }} and see more reporting and insights"
    },
    "DISCLAIMER": "Connecting and disconnecting these accounts may affect connections within products.",
    "QUICKBOOKS": {
      "DETAILS": "Quickbooks can only be connected by SMB users, and sales data will be viewable in the executive report by only the user that connected it.",
      "LOADING_ERROR": "Error getting connected QuickBooks account"
    },
    "GSC": {
      "DETAILS": "Once connected, 90-days of data about your top search queries will be automatically populated in the {{ business_app }} reporting within a few minutes. You can customize which queries are tracked, up to 20.",
      "DETAILS_NOT_HERE": "Currently, only accounts created with the URL-prefix property are supported. Domain properties are not yet supported and will not appear here. Learn more."
    },
    "GA": {
      "DETAILS": "Currently we support connecting Universal Analytics; Google Analytics v4 accounts are not yet supported.",
      "DETAILS_NOT_HERE": "Currently only Universal Analytics accounts are supported. Google Analytics v4 accounts are not yet supported and will not appear here."
    },
    "MANAGE_ON_WEB_ERROR": "Failed to open the web page."
  },
  "PERFORMANCE": {
    "CUSTOMER_RELATIONS": {
      "CONVERSATIONS": {
        "TITLE": "Conversations",
        "SUBTITLE": "Conversations created by source",
        "TOOLTIP": "Number of conversations created in Inbox, including anonymous web chats that didn’t result in a lead capture."
      },
      "WEB_CHAT_PERFORMANCE_SUBSECTION": {
        "TITLE": "Web Chat Performance",
        "UNIQUE_VISITORS_TITLE": "Unique Visitors",
        "UNIQUE_VISITORS_SUBTITLE": "# of unique visitors that saw your web chat",
        "TOOLTIP": "Visitors are counted only once within a 12-hour window that begins on their first page visit."
      },
      "LEADS": {
        "TITLE": "Leads",
        "SUBTITLE": "Leads created by source",
        "TOOLTIP": "Number of leads captured in the CRM, by source. Includes all sources except manually created or bulk import."
      },
      "SOURCE": {
        "UNKNOWN": "Unknown"
      },
      "UNIQUE_CONVERSATIONS": {
        "TITLE": "Unique Conversations",
        "SUBTITLE": "# of web chat visitors that engaged with the web chat"
      },
      "CAPTURED_LEADS": {
        "TITLE": "Captured Leads",
        "SUBTITLE": "# of web chat conversations captured as actionable leads"
      },
      "MESSAGES": {
        "TITLE": "Messages",
        "SUBTITLE": "# of messages across all channels",
        "SEND_BY_YOUR_TEAM": "Sent by your team",
        "SEND_BY_AI": "Sent by AI receptionist",
        "RECEIVED": "Received"
      },
      "TOTAL_COMPANIES": {
        "TITLE": "Total Companies",
        "SUBTITLE": "Cumulative total companies in your CRM",
        "TOOLTIP": "Total # of companies across all channels"
      },
      "TOTAL_CONTACTS": {
        "TITLE": "Total Contacts",
        "SUBTITLE": "Cumulative total contacts in your CRM",
        "TOOLTIP": "Total # of contacts across all channels"
      }
    },
    "SEO": {
      "KEYWORDS": {
        "TITLE": "Tracking Keywords",
        "NO_KEYWORDS": "You're not tracking any keywords yet",
        "ADD_KEYWORDS": "Add keywords to see reporting"
      },
      "KEYWORD_METRICS": {
        "TITLE": "Keyword Metrics"
      },
      "GOOGLE_SEARCH_CONSOLE": {
        "TITLE": "Google Search Console",
        "CLICKS": {
          "TITLE": "Clicks in Google Search",
          "SUBTITLE": "The number of time your website is clicked in organic Google search results."
        },
        "IMPRESSIONS": {
          "TITLE": "Impressions in Google Search",
          "SUBTITLE": "The number of time your website is seen in organic Google search results."
        },
        "CLICK_THROUGH_RATE": {
          "TITLE": "Click through rate",
          "SUBTITLE": "The number of impressions divided by the number of clicks."
        },
        "POSITION": {
          "TITLE": "Average position",
          "SUBTITLE": "The average position on the page your website showed up in search results."
        },
        "FIRST_PAGE_QUERIES": {
          "TITLE": "Queries on 1st page of Google Search",
          "SUBTITLE": "The number of queries where your website appeared on the first page of Google and was clicked on at least once.",
          "TOOLTIP": "A query is a unique keyword or phrase that someone has typed into Google Search. This graph shows the number of queries that got your website at least one click in the last 30 days, calculated daily."
        },
        "TOP_QUERIES": {
          "TITLE": "Queries in Google Search",
          "SUBTITLE": "Your tracked queries and their website clicks in organic Google search results.",
          "COLUMN_TITLE_CLICKS": "Clicks",
          "COLUMN_TITLE_POSITION": "Avg Position"
        },
        "TOP_PAGES": {
          "TITLE": "Top 10 Pages in Google Search",
          "SUBTITLE": "Your website pages that received the most clicks in organic Google search results.",
          "COLUMN_TITLE_IMPRESSIONS": "Impressions"
        },
        "CTA": {
          "TITLE": "See your Google search keywords",
          "DESCRIPTION": "Connect Google Search Console to see the top keywords that people search for on Google to find your business.",
          "BUTTON_TEXT": "Connect Google Search Console"
        }
      }
    },
    "THERMOMETER": {
      "STARTED_AT": "Started at",
      "NOW_AT": "Now at",
      "TITLE": "Listing percentage",
      "YOU": "YOU",
      "INDUSTRY_AVERAGE": "Industry Average",
      "95TH_PERCENTILE": "95th Percentile",
      "YOU_ARE_HERE": "You Are Here",
      "LETTER_GRADE": {
        "A": "A",
        "B": "B",
        "C": "C",
        "D": "D",
        "F": "F"
      }
    },
    "CARD_FOOTER": {
      "MULTI_LOCATION": {
        "DATA_FROM": "Data from 1 location",
        "DATA_FROM_PLURAL": "Data from {{ location_count }} locations"
      }
    },
    "YELLOW_PAGES": {
      "TRAFFIC": {
        "TITLE": "Yellow Pages Listing",
        "SUBTITLE": "Your Yellow Pages listing performance",
        "CLICKS": "Clicks",
        "CTR": "CTR",
        "VIEWS": "Views",
        "CLICKTHROUGH_RATE": "Clickthrough rate",
        "IMPRESSIONS": {
          "TITLE": "Impressions",
          "INFO": "The number of impressions for your traffic solution is defined by the number of times your business has been displayed on goldenpages.be (in the list of search results and the profile page)."
        },
        "PROFILE_VIEWS": {
          "TITLE": "Profile Views",
          "INFO": "Total number of profile views"
        },
        "URL_CLICKS": {
          "TITLE": "Clicks to website",
          "INFO": "The total number of times a visitor clicked on the website link via pagesdor.be."
        },
        "EMAIL_CLICKS": {
          "TITLE": "Clicks on email",
          "INFO": "The total number of times a visitor clicked on the email via goldenpages.be."
        },
        "PHONE_CLICKS": {
          "TITLE": "Clicks on phone number",
          "INFO": "The total number of times a visitor clicked on the phone number link via goldenpages.be."
        },
        "TOTAL_CLICKS": {
          "TITLE": "Total Clicks",
          "INFO": "The total number of clicks from goldenpages.be, broken down by type of action.",
          "WEB": "Website",
          "EMAIL": "Email",
          "PHONE": "Phone"
        },
        "TOTAL_IMPRESSIONS": {
          "TITLE": "Total Impressions"
        }
      },
      "WEBSITE": {
        "TITLE": "Website",
        "SUBTITLE": "Your website performance",
        "PAGE_VIEWS": {
          "TITLE": "Page Views",
          "INFO": "TBA"
        },
        "UNIQUE_PAGE_VIEWS": {
          "TITLE": "Unique Page Views",
          "INFO": "TBA"
        },
        "SESSIONS": {
          "TITLE": "Sessions",
          "INFO": "TBA"
        },
        "BOUNCE_RATE": {
          "TITLE": "Bounce Rate",
          "INFO": "TBA"
        },
        "DEVICES": {
          "TITLE": "Devices",
          "INFO": "TBA"
        },
        "DEVICES_DESKTOP": {
          "TITLE": "Desktop",
          "INFO": "TBA"
        },
        "DEVICES_MOBILE": {
          "TITLE": "Mobile",
          "INFO": "TBA"
        },
        "DEVICES_TABLET": {
          "TITLE": "Tablet",
          "INFO": "TBA"
        },
        "AVG_VIEWS_PER_VISIT": {
          "TITLE": "Average page views per visit",
          "INFO": "TBA"
        },
        "AVG_SESSION_DURATION": {
          "TITLE": "Average session duration in seconds",
          "INFO": "TBA"
        },
        "TOTAL_CLICKS": {
          "TITLE": "Total Clicks",
          "INFO": "TBA"
        },
        "TOTAL_CLICKS_CONTACT": {
          "TITLE": "Contact Us",
          "INFO": "TBA"
        },
        "TOTAL_CLICKS_MAP": {
          "TITLE": "Map",
          "INFO": "TBA"
        },
        "TOTAL_CLICKS_EMAIL": {
          "TITLE": "Email",
          "INFO": "TBA"
        },
        "TOTAL_CLICKS_CALL": {
          "TITLE": "Click to Call",
          "INFO": "TBA"
        },
        "EMAIL_CLICKS": {
          "TITLE": "Total number of clicks on email",
          "INFO": "TBA"
        },
        "PHONE_CLICKS": {
          "TITLE": "Total number of clicks on/calls to phone",
          "INFO": "TBA"
        },
        "CONTACT_FORM_USAGE": {
          "TITLE": "Total number of usages of contact form",
          "INFO": "TBA"
        },
        "MAP_CLICKS": {
          "TITLE": "Total number of clicks on map",
          "INFO": "TBA"
        },
        "ENTRANCE_SOURCES": {
          "TITLE": "Entrance Sources",
          "INFO": "TBA"
        },
        "ENTRANCE_SOURCES_SITES": {
          "TITLE": "Site",
          "INFO": "TBA"
        },
        "ENTRANCE_SOURCES_VISITS": {
          "TITLE": "Visits",
          "INFO": "TBA"
        },
        "MOST_VISITED_PAGES": {
          "TITLE": "Most Visited Pages",
          "INFO": "TBA"
        },
        "MOST_VISITED_PAGES_PAGE": {
          "TITLE": "Page",
          "INFO": "TBA"
        },
        "MOST_VISITED_PAGES_SESSIONS": {
          "TITLE": "Incoming Sessions",
          "INFO": "TBA"
        },
        "MOST_VISITED_PAGES_PAGE_VIEWS": {
          "TITLE": "Page Views",
          "INFO": "TBA"
        },
        "MOST_VISITED_PAGES_UNIQUE_PAGE_VIEWS": {
          "TITLE": "Unique Page Views",
          "INFO": "TBA"
        },
        "MOST_VISITED_PAGES_BOUNCE_RATE": {
          "TITLE": "Bounce Rate",
          "INFO": "TBA"
        },
        "MOST_VISITED_PAGES_EXIT_RATE": {
          "TITLE": "Exit Rate",
          "INFO": "TBA"
        }
      }
    },
    "SINGLE_LOCATION": {
      "REPORT_SWITCH_BUTTONS": {
        "CUSTOM_DATE_RANGE": "Choose custom date range",
        "FIXED_DATE_RANGE": "Return"
      },
      "CUSTOM_DATE_RANGE_DISCLAIMER": "Cards that don't support custom date ranges are hidden. Return to the most recent reporting period to see all cards.",
      "PAGE_TITLES": {
        "REPORT": "Executive Report"
      }
    },
    "MULTI_LOCATION": {
      "PAGE_TITLES": {
        "HOME": "Home",
        "REPUTATION": "Reputation",
        "MANAGE_REVIEWS": "Manage reviews",
        "INSIGHTS": "Insights",
        "LISTINGS": "Listings",
        "ALL_LISTINGS": "All Listings",
        "MANAGE_LISTINGS": "Manage listings",
        "GOOGLE_MY_BUSINESS": "Google Business Profile",
        "GOOGLE_Q_AND_A": "Google Q&A",
        "SOCIAL": "Social",
        "MANAGE_POSTS": "Manage Posts",
        "ADVERTISING": "Advertising",
        "MANAGE": "Manage",
        "KEYWORD_TRACKING": "Keyword Tracking",
        "KEYWORD_TRACKING_OVERVIEW": "Overview",
        "KEYWORD_TRACKING_KEYWORD": "Keywords",
        "OVERVIEW": "Overview",
        "DATA_EXPORT": "Data exporter",
        "DATA_EXPORT_DESCRIPTION": "Export and download report data",
        "REQUEST_REVIEWS": "Request reviews",
        "REPORT": "Executive Report",
        "REVIEWS": "Reviews",
        "SENTIMENT_ANALYSIS": "Sentiment Analysis",
        "SENTIMENT_OVER_TIME": "Sentiment Over Time",
        "RESPONSE_TEMPLATES": "Response Templates",
        "EDIT_RESPONSE_TEMPLATE": "Edit Response Template",
        "NPS": "NPS",
        "NET_PROMOTER_SCORE": "Net Promoter Score",
        "REQUESTS": "Requests",
        "BING_PLACES": "Bing Places"
        "ANALYTICS": "Google & Bing Analytics"
      },
      "KEYWORD_TRACKING": {
        "LOCATION": "Location",
        "KEYWORD": "Keywords in top 10",
        "AVERAGE_POSITION": "Average Position",
        "TOTAL_KEYWORDS_UP": "Total keywords up",
        "RANKS_GAINED": "Ranks gained",
        "TOTAL_KEYWORDS_DOWN": "Total keywords down",
        "RANK_LOST": "Ranks lost",
        "KEYWORDS_BEING_TRACKED": "Keywords being tracked"
      },
      "KEYWORD_OVERVIEW": {
        "EMPTY": "No Data"
      },
      "COMPARE": {
        "TOGGLE": {
          "LOCATIONS": "Compare locations",
          "GROUPS": "Compare groups"
        },
        "GRADE_LEGEND": {
          "NO_DATA": "No data",
          "BOTTOM_PERFORMING_LOCATIONS": "Bottom performing locations",
          "TOP_PERFORMING_LOCATIONS": "Top performing locations"
        }
      },
      "LISTINGS": {
        "LISTING_SCORE_TAB": "Listing score",
        "LISTING_SCORE_BREAKDOWN_TAB": "Listing score breakdown",
        "LISTING_SCORE": "Listing score",
        "LISTING_SCORE_MAX": "Listing score(max)",
        "DAYS_SINCE_LISTING_SCORE": "Days since listing score",
        "AVERAGE_LISTING_SCORE": "Average listing score",
        "AVERAGE_LISTING_SCORE_OVER_TIME": "Average listing score over time",
        "PRESENCE_TAB": "Presence",
        "TOTAL_PRESENCE_FOR_ALL_ACCOUNTS": "Total presence for all accounts",
        "TOTAL_PRESENCE_OVER_TIME": "Total presence over time",
        "ACCURACY_TAB": "Accuracy",
        "LISTING_ACCURACY_TAB": "Listing Accuracy",
        "LISTING_FOUND": "Found",
        "LISTING_MISSING": "Missing",
        "LISTING_INACCURATE": "Inaccurate",
        "LISTING_ACCURATE": "Accurate",
        "LISTING_PRESENT": "Present",
        "TOTAL_LISTING_SOURCES_PRESENT": "Total present source listings",
        "TOTAL_LISTINGS": "Total listings",
        "DETAILS": "Details",
        "LISTING_SCORE_AT_START": "Score at start ({{ startDate }})",
        "LISTING_SCORE_NOW": "Score now ({{ nowDate }})",
        "LISTING_STATUS": "Status",
        "SYNCED_LISTINGS": "Synced listings",
        "SYNCED_DATA_FIELDS": "Synced fields",
        "SYNCED_DATA_FIELDS_ERRORS": "Data errors",
        "SYNCED_DATA_FIELDS_ACCURATE": "Fields without errors",
        "FIELD_ACCURACY": "Field accuracy",
        "CORRECT_OR_PARTIAL_LISTINGS": "Correct or partially correct listings",
        "MISSING_OR_NO_MATCH_LISTINGS": "Incorrect or missing listings",
        "ACCURACY_THERMOMETER": "Accuracy",
        "ACCURACY_PERCENTAGE": "Accuracy (%)",
        "PRESENCE_THERMOMETER": "Presence",
        "SHOW_METRICS": "Show metrics",
        "SHOW_MAP": "Show map",
        "HIDE_MAP": "Hide map",
        "ACCURACY_INFORMATION": "Accuracy is made up of the accuracy of total locations multiplied by the total listing sources.",
        "SCORE_INFORMATION": "1. This score reveals how present & accurate your listings are across the web. This is an average of all locations in this table. Each source is weighted based on it's importance to online presence; the more important the source, the bigger impact it will have on this score. Adding or removing sources will affect this score. It may take up to 48 hours after an accuracy change happens to be reflected in this score. 2. Listing Score may total differently in Multi-Location vs Single-Location because citations component is not taken into consideration for the score in Multi-Location.",
        "SCORE_DIFFERENCE_STATEMENT": "Listing Score may total differently in Multi-Location vs Single-Location because citations component is not taken into consideration for the score in Multi-Location.",
        "YOUR_SCORE": "Your score",
        "MAX_SCORE": "Max score",
        "PERCENTAGE": "Percentage",
        "SCORE_BREAKDOWN_TOOLTIP": "{{sourceName}} listings are worth a maximum of {{points}} points per location",
        "CURRENT_LISTING_NAME": "Current company name",
        "CURRENT_LISTING_ADDRESS": "Current address",
        "CURRENT_LISTING_CITY": "Current city",
        "CURRENT_LISTING_PROVINCE": "Current province",
        "CURRENT_LISTING_ZIP": "Current zip",
        "CURRENT_LISTING_PHONE": "Current phone number",
        "CURRENT_LISTING_LINK": "Current website link",
        "EXPECTED_NAME": "Expected company name",
        "EXPECTED_ADDRESS": "Expected address",
        "EXPECTED_CITY": "Expected city",
        "EXPECTED_PROVINCE": "Expected province",
        "EXPECTED_ZIP": "Expected zip code",
        "EXPECTED_PHONE": "Expected phone number",
        "EXPECTED_LINK": "Expected website link",
        "SOURCE_NAME": "Source name",
        "SOURCE_URL": "Source url",
        "LAST_UPDATE": "Last Update",
        "SIDEBAR_WARNING": "The listing score data shown here is from the individual account. The score may be different if the sources set at the account level don't match the sources set at the group level. This data updates only once per week."
      },
      "REVIEWS": {
        "AVERAGE_REVIEW_RATING_TAB": "Review rating",
        "TOTAL_REVIEWS_TAB": "Total reviews",
        "REVIEWS_TAB": "Reviews",
        "REVIEWS": "Reviews",
        "REVIEW": "Review",
        "AVERAGE_RESPONSE_TIME": "Average response time",
        "NO_DATA": "There is no data at the moment.",
        "POSITIVE_REVIEWS_SERIES": "Positive",
        "NEUTRAL_REVIEWS_SERIES": "Neutral",
        "NEGATIVE_REVIEWS_SERIES": "Negative",
        "NO_RATING_REVIEW_SERIES": "No rating",
        "5_STARS": "5 stars",
        "4_STARS": "4 stars",
        "3_STARS": "3 stars",
        "2_STARS": "2 stars",
        "1_STAR": "1 star",
        "SENTIMENT": "Rating breakdown",
        "RESPONSE_TIME_HOURS": "Response time (h)",
        "RESPONDED_TO": "Responded to",
        "RESPONSE_RATE": "Response rate",
        "SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE": "You're about to send a review request to {{ contactCount }} contact(s).",
        "SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE_ERROR": "Error sending review requests",
        "SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE_SUCCESS": "Review requests sent",
        "SEND_REVIEWS_ALL_CONFIRMATION_MESSAGE": "You're about to send a review request to all contacts.",
        "SEND_REVIEWS_CONFIRMATION_MESSAGE": "You're about to send a review request to {{ contactName }}.",
        "SEND_REVIEWS_CONFIRMATION_MESSAGE_NO_NAME": "You're about to send a review request.",
        "SEND_REVIEWS_CONFIRMATION_MESSAGE_ERROR": "Error sending review request",
        "SEND_REVIEWS_CONFIRMATION_MESSAGE_SUCCESS": "Review request sent",
        "REVIEW_RESPONDING": "Review responding",
        "REVIEW_RESPONDING_MESSAGE": "Review responding data is only available for Google, Facebook, Yelp, and My Listing. Data for all time.",
        "NO_KEYWORDS": "There are no keywords at the moment"
      },
      "REQUESTS": {
        "CTR": "Clickthrough rate",
        "REQUESTS_SENT": "Requests sent",
        "LINKS_CLICKED": "Links clicked",
        "FILTERS": {
          "SOURCE": "Source",
          "EMAIL": "Email",
          "SMS": "SMS"
        },
        "REQUESTS_CHART_TITLE": "Request clickthrough rate",
        "CLICKED": "{{ clickedCount }} clicked",
        "SENT": "{{ sentCount }} sent"
      },
      "NET_PROMOTER_SCORE": {
        "NPS_SCORE": "NPS score",
        "NPS_BREAKDOWN": "NPS breakdown",
        "NPS_VOLUME": "NPS volume",
        "PROMOTERS": "Promoters",
        "DETRACTORS": "Detractors",
        "PASSIVES": "Passives",
        "TEAM_MEMBER": "Team member"
      },
      "BING_INSIGHTS": {
        "TOTAL_VIEWS": "Views",
        "TOTAL_ACTIONS": "Actions"
      },
      "GMB": {
        "TOTAL_SEARCHES": "Searches",
        "TOTAL_VIEWS": "Views",
        "TOTAL_ACTIONS": "Actions",
        "SEARCHES": "Searches",
        "SEARCHES_TOOLTIP": "This data is collected on a monthly cadence.  Values shown begin at the most recent full month",
        "SEARCH_DATA_BEING_COLLECTED": "Data is still being collected for this time period. Check back soon.",
        "DISCOVERY_SEARCH": "Discovery search",
        "MAP_VIEW": "Map view",
        "SEARCH_VIEW": "Search view",
        "WEBSITE_ACTION": "Visit website",
        "PHONE_ACTION": "Phone",
        "DIRECTIONS_ACTION": "Request directions",
        "BOOKINGS_ACTION": "Book Appointment",
        "CONVERSATIONS_ACTION": "Message",
        "FOOD_ORDERS_ACTION": "Order Food",
        "BRANDED_SEARCH": "Branded Search",
        "LOCAL_POST_ACTIONS_CALL_TO_ACTION": "Local post CTA clicks",
        "LOCAL_POST_VIEWS_SEARCH": "Local post views",
        "TOTAL_PHOTOS_VIEWS": "Photo views",
        "PHOTOS_VIEWS_CUSTOMER": "Views of customer photos",
        "PHOTOS_VIEWS_MERCHANT": "Views of business photos",
        "TOTAL_PHOTOS_COUNTS": "Photo quantity",
        "PHOTOS_COUNT_CUSTOMERS": "Customer photo quantity",
        "PHOTOS_COUNT_MERCHANT": "Business photo quantity",
        "SEARCH_VIEWS": "Search Views",
        "VIEWS_IN_GOOGLE_SEARCH": "Views in Google Search",
        "VIEWS_IN_GOOGLE_MAPS": "Views in Google Maps",
        "ACTIONS_ON_YOUR_GOOGLE_LISTING": "Actions on your Google Listing",
        "GOOGLE_Q_AND_A_IS_MOVED": "Google Q&A has moved to Reputation area",
        "DIRECT_SEARCH": "Direct search"
      },
      "BING": {
        "DELAY_TOOLTIP": "Bing Places statistics are delayed by up to 5 days",
        "TOTAL_VIEWS": "Views",
        "TOTAL_ACTIONS": "Actions",
        "MAP_VIEW": "Map view",
        "WEB_VIEW": "Web view",
        "WEBSITE_ACTION": "Visit website",
        "PHONE_ACTION": "Phone",
        "DIRECTIONS_ACTION": "Request directions",
        "PHOTO_ACTION": "View photos",
        "LOCATED_AT_ACTION": "Located at",
        "REVIEW_ACTION": "Write review",
        "MENU_ACTION": "View menu",
        "ORDER_ONLINE_ACTION": "Order online",
        "SUGGEST_EDIT_ACTION": "Suggest edit",
        "ANALYTICS": "Analytics",
        "VIEWS": "Views",
        "ACTIONS": "Actions",
        "WEBSITES": "Websites",
        "REQUEST_DIRECTIONS": "Request Directions",
        "PHONE_CALLS": "Phone Calls",
        "PHOTO_VIEWS": "Photo Views",
        "OTHERS": "Others"
      },
      "GOOGLE_Q_AND_A": {
        "NAME": "Google Q&A",
        "UNANSWERED": "Unanswered by owner",
        "ANSWERED": "Answered by owner",
        "TOTAL": "Total questions",
        "SHOW_COUNTS": "Showing {{ loadedQuestion }} of {{ totalQuestion }}",
        "EMPTY_STATE_TITLE": "No Google Q&A for the selected time period.",
        "QUESTION_CARD": {
          "TITLE": "{{author}} asked a question on Google",
          "POSTING_PUBLICLY": "Posting Publicly",
          "ANSWER_FIELD_PLACEHOLDER_TEXT": "Answer this question",
          "1_LIKE": "1 Like",
          "2_TO_4_LIKES": "{{likeCount}} Likes",
          "5_PLUS_LIKES": "{{likeCount}} Likes",
          "SHOW": "Show ",
          "HIDE": "Hide ",
          "ANSWER": "additional answer",
          "ANSWERS": "additional answers",
          "MERCHANT": "Owner",
          "LOCAL_GUIDE": "Local Guide",
          "REGULAR_USER": "Google User",
          "SUBMIT": "Answer",
          "ANSWER_STATE": {
            "UNANSWERED_BY_OWNER": "Unanswered by owner",
            "UNANSWERED": "Unanswered",
            "ANSWERED": "Answered"
          },
          "ANSWER_FROM": "Answer from {{author}}",
          "MERCHANT_OWNER": "Merchant (Owner)"
        },
        "NOT_CONNECTED": {
          "PHRASE_1": "Are people asking questions about your business before they become your customers?",
          "PHRASE_2": "Connect your Google Business Profile account to see your Questions & Answers.  Start asking and answering commonly asked questions and help people choose your business."
        },
        "NO_QUESTIONS": {
          "PHRASE_1": "You have no Questions & Answers.",
          "PHRASE_2": "Post helpful questions and answers to let customers know more about your business. Example question: Do you have WiFi? Answer: Yes, we do!"
        },
        "MESSAGES": {
          "ANSWER_SUCCESS": "Your answer has been successfully posted.",
          "ANSWER_ERROR": "Google has made Q&A temporarily unavailable in your region or business category. Service should return shortly."
        },
        "ASK_QUESTION": {
          "ASK_QUESTION": "Ask a question",
          "QUESTION_HINT_TEXT": "Write a frequently asked question.",
          "QUESTION_POSTED": "Your question has been posted!",
          "ERROR_POSTING_QUESTION": "Google has made Q&A temporarily unavailable in your region or business category. Service should return shortly.",
          "NOT_ENOUGH_WORDS": "Error: A question must have at least three words.",
          "NOT_ENOUGH_CHARACTERS": "Error: A question must have at least 10 letters."
        }
      },
      "SOCIAL": {
        "ALL_REACH": "Reach",
        "ALL_ENGAGEMENT": "Total Engagement",
        "ALL_POSTS": "Total Posts",
        "FB_REACH": "Facebook Reach",
        "FB_ENGAGEMENT": "Facebook Engagement",
        "FB_POSTS": "Facebook Posts",
        "TW_ENGAGEMENT": "X Engagement",
        "TW_POSTS": "X Posts",
        "LI_REACH": "LinkedIn Reach",
        "LI_ENGAGEMENT": "LinkedIn Engagement",
        "LI_POSTS": "LinkedIn Posts",
        "IG_REACH": "Instagram Reach",
        "IG_ENGAGEMENT": "Instagram Engagement",
        "IG_POSTS": "Instagram Posts",
        "GMB_REACH": "GBP Reach",
        "GMB_ENGAGEMENT": "GBP Engagement",
        "GMB_POSTS": "GBP Posts",
        "YTC_ENGAGEMENT": "YouTube Engagement",
        "TKT_ENGAGEMENT": "TikTok Engagement",
        "TKT_POSTS": "TikTok Posts",
        "YTC_POSTS": "YouTube Posts",
        "SENTIMENT": "Sentiment",
        "LINKEDIN": "LinkedIn",
        "TIKTOK": "TikTok",
        "FACEBOOK": "Facebook",
        "TWITTER": "X",
        "INSTAGRAM": "Instagram",
        "GMB": "Google Business Profile",
        "AUDIENCE": "Audience",
        "NEW_POSTS": "New posts",
        "NEW_POSTS_SUBTITLE": "New posts on all social networks",
        "NEW_POSTS_VIEW_MORE": "View all new posts",
        "ENGAGEMENT": "Engagement",
        "ENGAGEMENT_SUBTITLE": "Engagement of the posts made this period.",
        "SHARES": "Shares",
        "REACH": "Reach",
        "REACH_SUBTITLE": "Reach of the posts made this period.",
        "VIEW_DETAILS": "View social details",
        "NEW_POSTS_TOOLTIP": "The number of posts you've published on all connected accounts during the selected time period",
        "ENGAGEMENT_TOOLTIP": "The amount of engagements (likes, comments, shares, etc.) that your posts have received during the selected time period",
        "REACH_TOOLTIP": "The amount of people who saw your posts during the selected time period",
        "NEW_POSTS_TREND_TOOLTIP": "You have published {{trend}} posts recently, compared to the last period",
        "ENGAGEMENT_TREND_TOOLTIP": "Your engagement has trended {{trend}}  since the previous time period",
        "REACH_TREND_TOOLTIP": "Your reach has trended {{trend}}  since the previous time period",
        "UP_TREND": "upwards",
        "DOWN_TREND": "downwards",
        "MORE_TREND": "more",
        "FEWER_TREND": "fewer"
      },
      "ADVERTISING": {
        "IMPRESSIONS": "Impressions",
        "CLICKS": "Clicks",
        "CONVERSIONS": "Conversions"
      },
      "LEADS": {
        "LEADS": "Leads",
        "CONVERSATIONS": "Conversations",
        "OVERVIEW": "CRM Contacts"
      },
      "FILTERS": {
        "FILTER": "Filter",
        "FILTERS": "Filters",
        "PAGE_FILTERS": "Page filters",
        "CLEAR_ALL_FILTERS": "Clear all filters",
        "TITLES": {
          "GEOGRAPHY": "Geography",
          "CATEGORY": "Business category",
          "GROUP": "Group",
          "DATE": "Date"
        },
        "LOCATION_COUNT": "({{count}} Locations)",
        "GEOGRAPHY": {
          "LOCATIONS_IN_FILTER": "Locations in Filter",
          "ADD_AREAS": "Add Cities, Postal Codes or other areas",
          "COUNTRIES": "Countries",
          "REGION": "State / Province",
          "CITIES": "Cities"
        },
        "DATE_RANGE": {
          "OFFSET_EXPLANATION": "Some statistics will be offset due to delays in retrieval. Custom dates will not be offset."
        },
        "TEAM_FILTER": {
          "TEAM_MEMBER": "Team member",
          "PLACE_HOLDER": "Select team member.."
        }
      },
      "ACTIONS": {
        "FILTER_TO_THIS_LOCATION": "Filter to this location",
        "MANAGE_THIS_LOCATION": "Manage this location",
        "FILTER_TO_THIS_SOURCE": "Filter to this source",
        "MANAGE_THIS_SOURCE": "Manage this source"
      },
      "CUSTOMER_VOICE": {
        "REQUEST_REVIEWS": "Request Reviews",
        "UPLOAD_CUSTOMERS": {
          "TITLE": "Upload customers",
          "DESCRIPTION": "Upload your customer data CSV or create a new one using the template below. <b>Required fields:</b> email or phone number, and business ID",
          "DOWNLOAD_TEMPLATE": "Download template"
        },
        "MAP_FIELDS": {
          "DESCRIPTION": "Map your CSV columns to their corresponding fields in the <b>Mapping</b> dropdown menus. <b>Header row</b> and <b>First row</b> reflect the information in your CSV file.",
          "FIELDS": {
            "EMPLOYEE_FIRST_NAME": "Employee first name",
            "EMPLOYEE_LAST_NAME": "Employee last name",
            "TAG": "Tag"
          },
          "HEADER": "Header:",
          "FIRST_ROW": "First Row:"
        },
        "REVIEW_UPLOAD": {
          "DESCRIPTION": "Using <b>{{filename}}</b> to create <b>{{numRows}}</b> customers. Importing may take several minutes",
          "SUCCESS": "Your customer information is importing"
        },
        "ERRORS": {
          "PIPELINE_ERROR": "Failed to start processing",
          "UPLOAD_ERROR": "File upload failed",
          "FILE_TOO_SHORT": "It appears your CSV is missing a header row or customer information.\nAdd the required content and try uploading your file again.",
          "MAPPING_ERRORS": {
            "NO_EMAIL_OR_PHONE": "Map a filed to <b>Email</b> or <b>Phone</b> to upload your CSV.",
            "NO_BUSINESS_ID": "Map a field to <b>Business ID</b> to upload your CSV",
            "MULTIPLE_FIRST_NAME": "Map <b>First name</b> to only one field.",
            "MULTIPLE_LAST_NAME": "Map <b>Last name</b> to only one field.",
            "MULTIPLE_BUSINESS_ID": "Map <b>Business ID</b> to only one field.",
            "MULTIPLE_EMAIL": "Map <b>Email</b> to only one field.",
            "MULTIPLE_PHONE": "Map <b>Phone</b> to only one field.",
            "MULTIPLE_EMP_FIRST_NAME": "Map <b>Employee first name</b> to only one field.",
            "MULTIPLE_EMP_LAST_NAME": "Map <b>Employee last name</b> to only one field.",
            "MULTIPLE_TAG": "Map <b>Tag</b> to only one field."
          }
        },
        "REVIEW_REQUEST": {
          "SENT": "Sent",
          "OPENED": "Opened",
          "CLICKED": "Clicked",
          "NEW_REVIEWS": "New reviews"
        }
      },
      "CITATIONS": {
        "CITATIONS": {
          "TITLE": "Citations",
          "SUBTITLE": "The number of sites across the internet where your business information has been found.",
          "TOOLTIP": "Citations are mentions of your business across the web. A citation is your business name along with another piece of business data, like phone number, website or address. Citations are an important part of search engine optimization, by increasing discoverability of your business and upping your business authority in search engines."
        },
        "RECENT_CITATIONS": {
          "TITLE": "Recent Citations",
          "SUBTITLE": "Your business was found on these sites",
          "PERIOD": "Period",
          "SHOW_RECENT_REMAINING": "Show Recent (+{{ remaining }})"
        }
      },
      "DASHBOARD": {
        "FULL_SCREEN_MAP": "Full Screen Map",
        "NUM_LOCATIONS": "{{count}} locations"
      },
      "CLOUDVOICE": {
        "CLOUDVOICE": "Cloud Voice",
        "OUTBOUNDS": "Outbound",
        "INBOUNDS": "Inbound",
        "ONNETS": "On net",
        "CHART": {
          "TITLE": "Total calls historical",
          "DESCRIPTION": "Number of calls each month, for the past 12 months.",
          "TIME_RANGE": "Past 12 months"
        },
        "THERMO": {
          "TITLE": "Total calls",
          "DATE": "Selected date range",
          "DESCRIPTION": "The number of calls you have received by type.",
          "OUTBOUND_HOVER": " outbound calls",
          "INBOUND_HOVER": " inbound calls",
          "ONNET_HOVER": " on net calls"
        },
        "LINE": {
          "OUT": {
            "TITLE": "Outbound calls by status"
          },
          "IN": {
            "TITLE": "Inbound calls by status"
          }
        }
      },
      "LOCAL_ADS": {
        "SHOW_ALL": "Show all",
        "LEADS_TRACKER": {
          "TITLE": "Leads tracker",
          "LEAD_TYPE": "Lead type",
          "NUMBER_OF_LEADS": "Number of leads"
        },
        "TOP_MARKETING_INSIGHTS": {
          "TITLE": "Top Marketing Insights",
          "BEHAVIOR": "Behavior",
          "REACH": "Reach"
        }
      }
    },
    "DATA_HIDDEN": {
      "HEADING": "Data hidden",
      "DESCRIPTION": "This data has been hidden to protect your business information. Log in to view the full report.",
      "CTA_BTN": "Log in to view",
      "IMPERSONATION_DESCRIPTION": "Only the person who connected the product can view this data when logged in."
    },
    "REVIEWS": {
      "AVG": {
        "TITLE": "Average review rating",
        "SUBTITLE": "The average of your total review rating, I guess"
      },
      "NEW_NEGATIVE": {
        "TITLE": "New negative reviews",
        "SUBTITLE": "Just what it says on the tin"
      }
    },
    "LISTINGS": {
      "ALL_LISTINGS": {
        "TITLE": "All listings",
        "SUBTITLE": "The number of sites your data is found on",
        "THERMOMETER": {
          "ACCURATE": "Accurate",
          "INACCURATE": "Inaccurate",
          "MISSING": "Missing"
        },
        "TABLE_TITLE": "Primary sources",
        "TABLE": {
          "PRESENCE": "Presence",
          "ACCURACY": "Accuracy",
          "SYNCING": "Syncing",
          "SYNCING_HOVER": {
            "NOT_SYNCING": "Not available for automatic syncing",
            "SYNCING": "Syncing",
            "ISSUES_SYNCING": "Issues syncing",
            "ERROR_SYNCING": "Error syncing"
          }
        }
      },
      "LSP": {
        "SOURCES_SYNCING": {
          "TITLE": "Sites syncing",
          "SUBTITLE": "The listing sources that are currently receiving your business information"
        },
        "SOURCES_STATUS": {
          "TITLE": "Sync status",
          "SUBTITLE": "The sync status of the listing sources currently receiving your business information",
          "TABLE": {
            "STATUS": "Status"
          }
        }
      },
      "GMB": {
        "INSIGHTS_METRICS_CALCULATION": "Google has changed the way they calculate these metrics",
        "INSIGHTS_METRICS_CALCULATION_EXPLANATION": "a single user is now only counted once within each 24 hour period.  This results in data which more accurately reflects your customer's behavior",
        "SEARCHES": {
          "TITLE": "Search types",
          "SUBTITLE": "How customers find your business on Google",
          "CHART": {
            "CHAIN_TITLE": "Branded – Related brand searches",
            "DIRECT_TITLE": "Direct – Business name searches",
            "INDIRECT_TITLE": "Discovery – Category, product, or service searches"
          }
        },
        "SEARCH_KEYWORDS": {
          "TITLE": "Searches",
          "SUBTITLE": "Search terms that showed your Business Profile in the results. Values \"< 15\" are estimates and not included in the total.",
          "TOOLTIP": "This data is collected on a monthly cadence. Values shown begin at the most recent full month.",
          "CHART": {
            "TITLE": "Searches"
          }
        },
        "VIEWS": {
          "TITLE": "Views",
          "SUBTITLE": "Where customers see your business on Google",
          "CHART": {
            "MAPS_TITLE": "Listing on Maps",
            "SEARCH_TITLE": "Listing on Search"
          }
        },
        "ACTIONS": {
          "TITLE": "Actions",
          "SUBTITLE": "Common actions taken on your Google listing",
          "CHART": {
            "WEBSITE_TITLE": "Visit your website",
            "PHONE_TITLE": "Call you",
            "DIRECTIONS_TITLE": "Request directions",
            "BOOKINGS_TITLE": "Book an appointment",
            "CONVERSATIONS_TITLE": "Start a conversation",
            "FOOD_ORDERS_TITLE": "Order food"
          }
        },
        "PHOTO_VIEWS": {
          "TITLE": "Photo views",
          "SUBTITLE": "The number of times your business photos have been viewed",
          "CHART": {
            "MERCHANT_TITLE": "Your Photos",
            "CUSTOMER_TITLE": "Customer Photos"
          }
        },
        "PHOTO_COUNT": {
          "TITLE": "Photo quantity",
          "SUBTITLE": "The number of photos that appear on your business listing",
          "CHART": {
            "MERCHANT_TITLE": "Your Photos",
            "CUSTOMER_TITLE": "Customer Photos"
          }
        }
      },
      "BING_INSIGHTS": {
        "TITLE": "Bing Places",
        "VIEWS": {
          "TITLE": "Views",
          "SUBTITLE": "Where customers view your business on Bing",
          "CHART": {
            "MAPS_TITLE": "Listing on Maps",
            "WEB_TITLE": "Listing on Web"
          }
        },
        "ACTIONS": {
          "TITLE": "Actions",
          "SUBTITLE": "Customer actions taken on your Bing Places Profile",
          "CHART": {
            "WEBSITE_TITLE": "Website",
            "PHONE_TITLE": "Phone calls",
            "DIRECTIONS_TITLE": "Request directions",
            "PHOTOS_TITLE": "Photos"
          }
        }
      },
      "ACCURACY_AND_ACCEPTANCE_CARD": {
        "TITLE": "Listing details",
        "THERMOMETER_LABELS": {
          "ACCURATE": "Accurate",
          "INACCURATE": "Possible errors",
          "NOT_FOUND": "Not found"
        },
        "TABLE": {
          "COLUMN_HEADERS": {
            "LISTING_SOURCE_DESCRIPTION": "Listing source",
            "ACCURACY": "Accuracy",
            "ACCEPTANCE_STATUS": "Status",
            "LISTING_SCORE": "Listing score",
            "MAXIMUM_POSSIBLE_LISTING_SCORE": "Max score"
          },
          "ACCURACY_STATUS_DESCRIPTIONS": {
            "ACCURATE": "Accurate listing found",
            "INACCURATE": "Found with possible errors",
            "NOT_FOUND": "Not found",
            "REPORTING_NOT_AVAILABLE": "Reporting not available for this source",
            "UNKNOWN": "Listing is in an unknown state"
          },
          "ACCEPTANCE_STATUS_DESCRIPTIONS": {
            "SYNCED": "Synced",
            "SUBMITTED": "Submitted",
            "IN_PROGRESS": "In progress",
            "SYNCING_WITH_WARNINGS": "Syncing with warnings",
            "SYNCING_WITH_ERRORS": "Syncing with errors",
            "DISABLED": "Syncing is disabled",
            "CLAIMED_BY_OTHERS": "Listing is claimed by someone else",
            "SERVICE_AREA_BUSINESS_UNSUPPORTED": "Listing source does not support service area businesses",
            "MANUAL_UPDATE_ONLY": "Manual update only",
            "UNKNOWN": "Status not available"
          }
        }
      }
    },
    "REPUTATION": {
      "AVERAGE_RATING": {
        "TITLE": "Average star rating",
        "SUBTITLE": "Your average rating across all reviews to date",
        "CHART": {
          "TITLE": "Weekly average review rating (past 6 months)",
          "AVERAGE_RATING": "Average rating"
        }
      },
      "NEW_REVIEWS": {
        "TITLE": "New reviews",
        "SUBTITLE": "The number of new reviews you've received",
        "CHART": {
          "TITLE": "Weekly reviews (past 6 months)",
          "AVERAGE_RATING": "Number of reviews"
        }
      },
      "ALL_REVIEWS": {
        "TITLE": "Most recent reviews",
        "LIST": {
          "TITLE": "All reviews this period",
          "REVIEW_ON": "reviewed your business on"
        }
      },
      "NPS_FEEDBACK_VOLUME": {
        "TITLE": "NPS volume",
        "SCORES_RECEIVED": "The number of NPS scores you've received",
        "CHART": {
          "TITLE": "Weekly reviews (past 6 months)"
        }
      },
      "NPS_FEEDBACKS": {
        "TITLE": "NPS feedback",
        "VIEW_MORE": "View more",
        "LIST": {
          "TITLE": "All feedback this period"
        }
      },
      "REQUESTS_DASHBOARD": {
        "SENT": "Sent",
        "CTR": "CTR",
        "CLICKED": "Clicked"
      }
    },
    "SOCIAL": {
      "FACEBOOK_STATS": {
        "TITLE": "Post performance",
        "SUBTITLE": "Your presence across all social networks",
        "TABS": {
          "REACH": {
            "TITLE": "Reach",
            "LEGEND": {
              "REACH": "Reach"
            }
          },
          "ENGAGEMENT": {
            "TITLE": "Engagement",
            "LEGEND": {
              "COMMENTS": "Comments",
              "REACTIONS": "Reactions",
              "SHARES": "Shares"
            }
          },
          "POSTS": {
            "TITLE": "Posts",
            "LEGEND": {
              "POSTS": "Posts"
            }
          },
          "TREND": {
            "90_DAYS": "90 day trend",
            "30_DAYS": "30 day trend",
            "12_MONTHS": "12 month trend"
          }
        }
      },
      "TOP_FACEBOOK_POSTS": {
        "TITLE": "Top-performing posts",
        "SUBTITLE": "Posts with the highest reach and engagement this period",
        "SUBTITLE_2": "Posts with the most reach during the selected date range",
        "SUBTITLE_3": "Posts with the highest engagement during this period",
        "VIEW_MORE": "See more post analytics",
        "VIEW_MORE_2": "View all posts",
        "DELETED_POST": "Deleted post"
      }
    },
    "ADVERTISING": {
      "CTCT": {
        "ENGAGE": {
          "TITLE": "Email engagement",
          "SUBTITLE": "Customer interactions with your campaign emails",
          "CHART": {
            "TITLE-12-MONTHS": "12 month trend",
            "TITLE-30-DAYS": "30 day trend",
            "CLICKS": "Unique clicks",
            "OPENS": "Unique opens"
          }
        },
        "PERFORMANCE": {
          "TITLE": "Campaign performance",
          "SUBTITLE": "Compare the effectiveness of your campaigns",
          "TABLE": {
            "EMAILS_SENT": "Emails sent",
            "CLICK_RATE": "Click rate",
            "OPEN_RATE": "Open rate"
          }
        }
      },
      "ADINTEL": {
        "IMPRESSIONS": {
          "TITLE": "Impressions",
          "SUBTITLE": "How many times your ads were seen"
        },
        "CLICKS": {
          "TITLE": "Clicks",
          "SUBTITLE": "How many times your ads were clicked on"
        },
        "CONVERSIONS": {
          "TITLE": "Conversions",
          "SUBTITLE": "How many people took action from your ads"
        },
        "PHONE_CALLS": {
          "TITLE": "Phone calls",
          "SUBTITLE": "How many times people called your call tracking number"
        },
        "ROI": {
          "TITLE": "ROI",
          "SUBTITLE": "How much money you have made on your ad compared to how much you paid for your ad"
        },
        "SHARED": {
          "LEGEND": {
            "GOOGLE_ADS": "Google Ads",
            "FACEBOOK_ADS": "Facebook Ads",
            "MICROSOFT_ADS": "Microsoft Ads",
            "PHONE_CALLS": "Phone calls",
            "SIMPLIFI_ADS": "Simplifi Ads",
            "TIKTOK_ADS": "TikTok Ads",
            "AMAZON_ADS": "Amazon Ads",
            "LINKEDIN_ADS": "Linkedin Ads",
            "GAM_ADS": "Google Ad Manager"
          }
        },
        "CTA": {
          "ADWORDS": {
            "CONNECT": {
              "TITLE": "Unlock Google Ads performance",
              "DESCRIPTION": "Connect your Google Ads account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect Google Ads"
            },
            "NOT_VALID": {
              "TITLE": "Google Ads has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect Google Ads"
            }
          },
          "SIMPLIFI_ADS": {
            "CONNECT": {
              "TITLE": "Unlock Simplifi Ads performance",
              "DESCRIPTION": "Connect your Simplifi Ads account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect Simplifi Ads"
            },
            "NOT_VALID": {
              "TITLE": "Simplifi Ads has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect Simplifi Ads"
            }
          },
          "AMAZON_ADS": {
            "CONNECT": {
              "TITLE": "Unlock Amazon Ads performance",
              "DESCRIPTION": "Connect your Amazon Ads account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect Amazon Ads"
            },
            "NOT_VALID": {
              "TITLE": "Amazon Ads has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect Amazon Ads"
            }
          },
          "FACEBOOK_ADS": {
            "CONNECT": {
              "TITLE": "Unlock Facebook Ads performance",
              "DESCRIPTION": "Connect your Facebook Ads account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect Facebook Ads"
            },
            "NOT_VALID": {
              "TITLE": "Facebook Ads has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect Facebook Ads"
            }
          },
          "MICROSOFT_ADS": {
            "CONNECT": {
              "TITLE": "Unlock Microsoft Ads performance",
              "DESCRIPTION": "Connect your Microsoft Ads account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect Microsoft Ads"
            },
            "NOT_VALID": {
              "TITLE": "Microsoft Ads has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect Microsoft Ads"
            }
          },
          "LOCAL_ADS": {
            "CONNECT": {
              "TITLE": "Unlock {{appName}} performance",
              "DESCRIPTION": "Connect your {{appName}} account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect {{appName}}"
            },
            "NOT_VALID": {
              "TITLE": "{{appName}} has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect {{appName}}"
            }
          },
          "TIKTOK_ADS": {
            "CONNECT": {
              "TITLE": "Unlock TikTok Ads performance",
              "DESCRIPTION": "Connect your TikTok Ads account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect TikTok Ads"
            },
            "NOT_VALID": {
              "TITLE": "TikTok Ads has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect TikTok Ads"
            }
          },
          "LINKEDIN_ADS": {
            "CONNECT": {
              "TITLE": "Unlock Linkedin Ads performance",
              "DESCRIPTION": "Connect your Linkedin Ads account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect Linkedin Ads"
            },
            "NOT_VALID": {
              "TITLE": "Linkedin Ads has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect Linkedin Ads"
            }
          },
          "GAM_ADS": {
            "CONNECT": {
              "TITLE": "Unlock Google Ad Manager performance",
              "DESCRIPTION": "Connect your Google Ads Manager account to generate live campaign performance reporting.",
              "BUTTON_TEXT": "Connect Google Ads Manager"
            },
            "NOT_VALID": {
              "TITLE": "Google Ad Manager has been disconnected!",
              "DESCRIPTION": "Reconnect your account to ensure Advertising Intelligence data remains up to date.",
              "BUTTON_TEXT": "Reconnect Google Ad Manager"
            }
          }
        }
      }
    },
    "WEBSITE": {
      "GOOGLE_ANALYTICS": {
        "TOTAL_VISITORS": {
          "TITLE": "Users",
          "SUBTITLE": "The number of unique visitors your website has received"
        },
        "PAGE_VIEWS": {
          "TITLE": "Page Views",
          "SUBTITLE": "The number of times a user has viewed a page on your website"
        },
        "PAGES_PER_SESSION": {
          "TITLE": "Pages/Session",
          "SUBTITLE": "The average number of pages viewed in a single session"
        },
        "BOUNCE_RATE": {
          "TITLE": "Bounce Rate",
          "SUBTITLE": "The percentage of users who left your website after viewing only one page"
        },
        "AVERAGE_TIME_ON_WEBSITE": {
          "TITLE": "Average Time on Website",
          "SUBTITLE": "The average length of time a user spends on your website"
        },
        "PERCENT_OF_NEW_VISITS": {
          "TITLE": "% of New Visits",
          "SUBTITLE": "The percentage of visits from users who had never visited before"
        },
        "NEW_USERS": {
          "TITLE": "New Users",
          "SUBTITLE": "The number of users who interacted with your site for the first time"
        },
        "PAGE_VIEWS_PER_SESSION": {
          "TITLE": "Page/Views Session",
          "SUBTITLE": "The number of app screens or web pages your users viewed per session"
        },
        "ENGAGED_SESSIONS": {
          "TITLE": "Engaged Session",
          "SUBTITLE": "The number of sessions that lasted longer than 10 seconds, or had a conversion event, or had 2 or more screen views."
        },
        "TOTAL_VISITS": {
          "TITLE": "Sessions",
          "SUBTITLE": "The number of times your site has been visited"
        },
        "TRAFFIC_SOURCES": {
          "TITLE": "Traffic Sources",
          "COLUMN_TITLE": "Traffic"
        },
        "TOP_REFERRAL_SOURCES": {
          "TITLE": "Top Referral Sources",
          "COLUMN_TITLE": "Traffic"
        }
      },
      "GOOGLE_ANALYTICS4": {
        "ACTIVE_USERS": {
          "TITLE": "Active Users",
          "SUBTITLE": "The number of distinct users who visited your site or app."
        },
        "SESSIONS": {
          "TITLE": "Sessions",
          "SUBTITLE": "The number of sessions that began on your site or app (event triggered: session_start)."
        },
        "ENGAGED_SESSIONS": {
          "TITLE": "Engaged Sessions",
          "SUBTITLE": "The number of sessions that lasted longer than 10 seconds, or had a conversion event, or had 2 or more screen views."
        },
        "PAGE_VIEWS": {
          "TITLE": "Page Views",
          "SUBTITLE": "The number of app screens or web pages your users viewed."
        },
        "AVERAGE_SESSION": {
          "TITLE": "Average Session Duration",
          "SUBTITLE": "The average duration (in seconds) of users' sessions."
        },
        "BOUNCE_RATE": {
          "TITLE": "Bounce rate",
          "SUBTITLE": "The percentage of users who left your website after viewing only one page"
        },
        "EVENT_COUNT": {
          "TITLE": "Event Count",
          "SUBTITLE": "The count of events"
        },
        "CONVERSIONS": {
          "TITLE": "Conversions",
          "SUBTITLE": "The count of conversion events."
        }
      }
    },
    "ERRORS": {
      "INVALID_SERIES": "Invalid series type passed in",
      "INVALID_DATA": "Invalid chart data passed in",
      "NO_DATA": "No historical data available. Check back later.",
      "NO_LIST_DATA": "No data was found for the selected period"
    },
    "ACCOUNTING": {
      "QUICKBOOKS": {
        "TABLE": {
          "TOTAL": "Total",
          "0_TO_30_DAYS": "0-30 days",
          "31_TO_60_DAYS": "31-60 days",
          "61_TO_90_DAYS": "61-90 days",
          "90_PLUS_DAYS": "90+ days",
          "LOADING_ERROR": "Error getting QuickBooks data"
        },
        "WHO_OWES_ME_TABLE": {
          "TITLE": "Who owes me",
          "SUBTITLE": "The top # amounts you are currently owed by your customers",
          "NO_DATA": "No customers have overdue payments"
        },
        "WHO_I_OWE_TABLE": {
          "TITLE": "Who I owe",
          "SUBTITLE": "The top # amounts you currently owe to your suppliers",
          "NO_DATA": "You have no overdue payments"
        },
        "INCOME_TABLE": {
          "TITLE": "Income",
          "NO_DATA_DESCRIPTION": "You have no income for this time period"
        },
        "EXPENSES_TABLE": {
          "TITLE": "Expenses",
          "NO_DATA_DESCRIPTION": "You have no expenses for this time period"
        }
      }
    }
  },
  "LISTINGS": {
    "NAME": "Listings",
    "HELP_DRAWER": {
      "CONTENT_1": "Listings gathers all the locations on the web where your business listing can be found: major search engines, local review sites, online business directories, and social networks. See where your business is listed. Learn where to correct the errors or add listings. Be found online easier than your competitors.",
      "BULLET_1": "See where your business is listed",
      "BULLET_2": "Learn where to correct the errors or add listings",
      "BULLET_3": "Be found online easier than your competitors"
    },
    "PRINT_LISTINGS_REPORT": "Print Listings Report",
    "PRIMARY": {
      "NAME": "Primary Listings",
      "HELP_DRAWER": {
        "VERIFY_LISTINGS": {
          "TITLE": "How to verify your listings",
          "BULLET_1": "Go to the Listings Settings tab and check that the listing sources relevant to your business have been selected",
          "BULLET_2": "Go to the Primary Listings page and review all of the listings that have been found for your business. Here you can check for potential errors and edit the listing, if needed."
        },
        "LISTING_SCORE": {
          "TITLE": "How to increase your listing score",
          "BULLET_1": "Verify your listings",
          "BULLET_2": "Fix any found errors in the Primary Listings tab",
          "BULLET_3": "Create new listings and citations"
        }
      },
      "LISTING_SCORE": {
        "TOOL_TIP": "An evaluation of your business' accurate Primary Listings and Citations. Each listing source is assigned a score based on how popular the site is. For example, having an accurate listing on a popular site like Google will have a greater influence on your Listing Score.",
        "PRESCRIPTION_1": "The Listing Score is an evaluation of how you're performing in regards to establishing accurate Primary Listings and Other Citations. Each site has been assigned a score based on its popularity (i.e. Popular sites like Google will have a greater influence on your Listing Score).",
        "PRESCRIPTION_2": "Keep your score above the industry average to improve your chances of getting found by potential customers in local search. Better yet, keep your score in the top 5 percent to truly stand out from your competition. To increase your Listing Score, create new listings and citations, and make sure to fix any errors highlighted below. Keep in mind that accurate listings will get you more points than listings with potential errors!",
        "COMPARE_MESSAGE": "Compare across"
      },
      "SELECT_REGION": "Select region",
      "SEARCH_ENGINES": "Search engines",
      "REVIEW_ENGINES": "Review sites",
      "DIRECTORIES": "Directories",
      "SOCIAL_ENGINES": "Social sites",
      "LISTING_INFORMATION": "Listing information",
      "NO_LISTINGS_MESSAGE": "No listings to display. Please adjust your filters so that listings will display",
      "SOURCE_TYPE": "Source type",
      "LISTING_STATUS": "Listing status",
      "DISPLAY_COUNT": "Displaying {{numerator}} of {{denominator}} sources",
      "LD_PRESCRIPTION": "With Listing Distribution, you can update your listing information on all four major data providers, which are trusted by 300+ online directories, review sites, search engines, GPS services, and more.",
      "INTEREST_IN_LD": "You may also be interested in Listing Distribution.",
      "HOW_YOU_ARE_DOING_IN_LD": "Find out how your listing appears on data providers.",
      "ADD_LISTING_BY_URL": "Add listing by URL",
      "CREATE_A_NEW": "Create a new {{ source_name }} Listing",
      "LISTING": "Listing",
      "EDIT_ON": "Edit on {{ source_name }}",
      "CREATE_ON": "Create on {{ source_name }}",
      "DETAILS": {
        "COPY_SUCCESS": "Copied to clipboard: ",
        "COPY_FAIL": "An error occurred",
        "CURRENT_LISTING": "Current Listing",
        "CORRECT_BUSINESS_INFO": "Business information",
        "EMPTY_FIELD": "(Field Missing)",
        "COPY": "Copy",
        "ERROR_DETAILS": "There was a problem finding or processing your listing. Please check that the URL is a listing page and resubmit.",
        "PROCESSING_DETAILS": "We have your listing and we process it to determine if there are any inconsistencies in the business information. It could take up to 48 hours.",
        "IGNORING_ERRORS": "Ignoring errors",
        "IGNORE_ERRORS": "Ignore errors",
        "IGNORE_ERRORS_TITLE": "What is \"ignore errors\"?",
        "IGNORE_ERRORS_DETAILS": "If you are comfortable leaving contact information identified as inconsistent on a listing, you can move the listing to the 'ignoring errors' state by clicking the 'ignore errors' link. Note, however, that inconsistent contact information can lead to penalties in search engine ranking.",
        "REMOVE_URL": "Remove URL",
        "ACTIONS": "Actions",
        "SEARCH_LISTINGS": "Search listings",
        "SHOW_DETAILS": "Show details",
        "ADD_NOTES": "Add notes",
        "REFRESH_LISTING": "Refresh listing",
        "REFRESH_PENDING": "Refresh pending",
        "REFRESH_DISABLED_TOOLTIP": "Refresh listing can be requested once every 24 hours.",
        "REFRESH_EXPIRED_LISTING_TOOLTIP": "This feature is unavailable as Reputation Management has expired. Contact your administrator for more information.",
        "REFRESH_SUCCESS": "Refresh listing in process",
        "REFRESH_FAILURE": "Error when attempting to refresh",
        "HIDE_DETAILS": "Hide details",
        "VIEW_POSSIBLE_MATCHES": "View possible matches",
        "HIDE_POSSIBLE_MATCHES": "Hide possible matches",
        "MAKE_MINE": "Make mine",
        "MAKE_MINE_TITLE": "What is \"make mine\"?",
        "MAKE_MINE_DETAILS": "By clicking the 'make mine' link you indicate this listing is yours.\nIf you click this link in error you can always click the 'not mine' link to revert the change.",
        "NOT_MINE": "Not mine",
        "STOP_IGNORING_ERRORS": "Stop ignoring errors",
        "STOP_IGNORING_ERRORS_TITLE": "What is \"stop ignoring errors\"?",
        "STOP_IGNORING_ERRORS_DETAILS": "If you no longer wish to leave inconsistent information on a listing that has been marked as correct, you can move the listing to the 'unverified' state by clicking the 'stop ignoring errors' link.\nClicking 'ignore errors' will revert these changes.",
        "BACK_TO_MATCHES": "Back to matches",
        "NO_POTENTIAL_MATCHES_MESSAGE": "We did not find any potential matches.",
        "USER_SUBMIT_PROMPT": "Do you have a {{source_name}} listing we did not find?",
        "USER_SUBMIT_PLACEHOLDER": "Enter {{source_name}} listing URL here",
        "SEARCH_BUSINESS_PLACEHOLDER": "Search by business name",
        "OR_ADD_BUSINESS": "Or add your business",
        "RESULT_FOUND": "{{result_number}} result(s) found",
        "FIND_LISTING_TITLE": "Find {{source}} listing",
        "NOT_MINE_TITLE": "What is \"not mine\"?",
        "NOT_MINE_DETAILS": "By clicking the 'not mine' link you indicate that we have incorrectly identified this listing as yours. If you click this link in error you can always click the 'make mine' link to revert the change.",
        "PROCESSING_SUBMITTED": "Processing submitted listing.",
        "SUBMIT_SUCCESS": "Successfully submitted listing",
        "SUBMIT_FAIL": "Failed to submit listing",
        "POTENTIALLY_REMOVED": "Potentially Removed",
        "SUSPECTED_MISSING": "We suspect that this listing was removed from {{source_name}}. Confirm that this listing no longer exists.",
        "REMOVE_LISTING": "Remove Listing",
        "LAST_SCRAPED": "last updated {{timeAgo}}"
      }
    },
    "LISTING_SOURCE_DIALOG": {
      "CREATE_LISTING": "Create Listing",
      "CLAIM_EXISTING": "Claim an Existing Listing",
      "EDIT_CONTACT": "Edit Contact Info"
    },
    "LISTING_SNACK_RESULT": {
      "ERRORS_IGNORED": "Listing errors are being ignored",
      "ERRORS_NOT_IGNORED": "Listing errors are no longer being ignored",
      "MARKED_MINE": "Listing has been marked as \"mine\"",
      "MARKED_NOT_MINE": "Listing has been marked as \"not mine\"",
      "EXCEPTION_STOP_IGNORING_ERRORS": "There was a problem trying to stop ignoring listing errors",
      "EXCEPTION_IGNORING_ERRORS": "There was a problem trying to ignore listing errors",
      "EXCEPTION_MARKING_MINE": "Error marking listing \"mine\"",
      "EXCEPTION_MARKING_NOT_MINE": "Error marking listing \"not mine\""
    },
    "LISTING_STATUS_TEXTS": {
      "POSSIBLE_ERRORS": "Inaccurate",
      "PROCESSING": "Processing",
      "NOT_FOUND": "Not found",
      "ERROR_FOUND": "Error",
      "ACCURATE": "Accurate"
    },
    "OTHER_CITATIONS": {
      "NAME": "Other citations",
      "TOOLTIP": {
        "CONTENT": "There are many obvious benefits of having accurate listing information, including consistent branding and accurate contact information so customers can find and connect with businesses. However, Citations, which are mentions of your business name along with another piece of business data (phone number, address, website, or any combination of those) are key in search marketing optimization as well. Google considers these factors:",
        "NUMBER_OF_CITATIONS": "Number of citations",
        "ACCURACY_AND_CONSISTENCY_OF_DATA_IN_CITATIONS": "Accuracy and consistency of data in citations",
        "AUTHORITY_OF_WEBSITES_WHERE_CITATIONS_APPEAR": "Authority of websites where citations appear",
        "AMOUNT_OF_USER_GENERATED_CONTENT_ON_CITATIONS": "Amount of user-generated content on citations",
        "VELOCITY_OF_USER_GENERATED_CONTENT_GENERATION": "Velocity of user-generated content generation"
      },
      "HELP_DRAWER": {
        "CONTENT_1": "Mentions of your business name along with another piece of business data (phone number, address, website, etc) are key in search marketing optimization. Accurate citations help people discover your business, resulting in more web, phone, and foot traffic, which can convert into transactions.",
        "CONTENT_2": "When search engines like Google rank listings, they consider factors such as:",
        "BULLET_1": "Number of citations",
        "BULLET_2": "Accuracy and consistency of data in citations",
        "BULLET_3": "Authority of websites where citations appear",
        "BULLET_4": "Amount of user-generated content on citations"
      },
      "DISPLAY_COUNT": "{{number}} citations found",
      "SOURCE_NAME": "Source name",
      "CITATIONS": "Citations",
      "DATE_FOUND": "Date found"
    },
    "STATISTICS": {
      "LISTING_STATS": {
        "NAME": "Listing stats",
        "TOOLTIP_1": "The Listing Sources statistic shows the number of listing sites you're currently monitoring. There is no graph for this statistic.",
        "TOOLTIP_2": "The Listing Score graph shows how your Listing Score has changed over time. To improve your score, correct your current listing information, create more listings, and generate new citations."
      },
      "CITATION_STATS": {
        "NAME": "Citation stats",
        "TOOLTIP": "The Citations Stats section will monitor the total Citations Found at certain points in time, compare this number to the industry average, and track on a graph to show progress over time."
      },
      "STATS_NOT_FOUND": "Statistics for your business will be available 7 days after account creation."
    },
    "TYPES": {
      "SEARCH_ENGINES": "Search engines",
      "REVIEW_SITES": "Review sites",
      "DIRECTORIES": "Directories",
      "SOCIAL_SITES": "Social sites",
      "INTERNATIONAL": "International"
    },
    "STATUSES": {
      "ACCURATE": "Accurate",
      "FOUND_WITH_POSSIBLE_ERRORS": "Possible errors",
      "NOT_FOUND": "Not found",
      "CORRECT": "Correct"
    },
    "SETTINGS": {
      "PRESCRIPTION": "Select the sources that are relevant to your business by checking or unchecking the boxes below.",
      "SOURCE_REMOVE_DISABLED_TOOLTIP": "To remove this source, you need to mark all {{sourceName}} listings as \"not mine\" under \"Manage Listings\""
    },
    "FILTERS": {
      "IGNORING_ERRORS": "Ignoring errors ({{number}})"
    },
    "EDIT_BUSINESS_PROFILE": "Edit business profile"
  },
  "ADMINISTRATION": {
    "PAGE_TITLE": "Administration"
  },
  "SETTINGS": {
    "PAGE_TITLE": "Settings",
    "NO_SETTINGS_AVAILABLE": "No settings available.",
    "SECTIONS": {
      "ACCOUNT": "Account",
      "PAYMENTS": "Payments",
      "APP_SETTINGS": "App settings",
      "AI_SETTINGS": "AI settings",
      "COMMUNICATION_SETTINGS": "Communication settings",
      "FROM_PARTNER": "From "
    },
    "SOCIAL": {
      "SERVICE_TYPE": {
        "LOCATION": "Location",
        "ACCOUNT": "Account",
        "PAGE": "Page",
        "VIEW": "View",
        "PROPERTY": "Property",
        "SITE": "Site"
      },
      "GINGR": {
        "TITLE": "Connect Gingr",
        "UPDATE_TITLE": "Update Gingr",
        "DESCRIPTION": "Connect Gingr to automatically send review requests to your customers once a reservation checks out.",
        "BROADLY_ID": "Location ID",
        "PLACEHOLDER": "Enter Location ID",
        "HINT_TEXT": "The Location ID should match the one entered in the Admin > Integrations page of the Gingr app.",
        "SUCCESS_MESSAGE": "Successfully connected to Gingr",
        "SUCCESS_UPDATE_MESSAGE": "Successfully updated connection"
      },
      "INSTAGRAM": {
        "CONNECT_CARD_TITLE": "Instagram accounts",
        "CONNECT_CARD_SUBTITLE": "Select the Instagram account to connect to Inbox Pro.",
        "NO_ACCOUNTS_FOUND": "No Instagram accounts found.",
        "FAILED_TO_CONNECT": "Failed to connect Instagram account"
      },
      "RECONNECT": "Reconnect",
      "REMOVE": "Remove",
      "SELECT": "Select",
      "SEARCH_FOR_PAGES": "Search by name or address",
      "SEARCH_FOR_ACCOUNTS": "Search accounts by name",
      "SEARCH_BY_NAME": "Search by name",
      "VERIFIED": "Verified",
      "NOT_VERIFIED": "Not verified",
      "DELETE_DIALOG_TITLE": "Delete confirmation",
      "DELETE_DIALOG_MESSAGE": "This connection will be removed and cannot be recovered. Any status updates scheduled for this connection will not be posted. Any associated monitor data will be permanently deleted. Are you sure?",
      "DELETE_GOOGLE_ANALYTICS_DIALOG_TITLE": "Disconnect Google Analytics?",
      "DELETE_GOOGLE_SEARCH_CONSOLE_DIALOG_TITLE": "Disconnect Google Search Console?",
      "DELETE_GOOGLE_MEET_DIALOG_TITLE": "Disconnect Google Meet account?",
      "DELETE_GOOGLE_ANALYTICS_MESSAGE": "Data from Google Analytics will no longer appear in any associated products or your Executive Report.",
      "DELETE_GOOGLE_SEARCH_CONSOLE_MESSAGE": "Data from Google Search Console will no longer appear in any associated products or your Executive Report.",
      "GOOGLE_SEARCH_CONSOLE_MAX_QUERIES_ERROR": "Max 20 queries",
      "GOOGLE_SEARCH_CONSOLE_DUPLICATE_ERROR": "'{{ label }}' already exists",
      "GOOGLE_SEARCH_CONSOLE_MIN_CHAR_ERROR": "Min 3 characters",
      "DELETE_GOOGLE_MEET_MESSAGE": "This account is used for Google Meet related functionalities like sending Google Meet meeting invites. Disconnecting will temporarily disable those functionalities. You can always come back and connect a Google Meet account.",
      "CONFIRM_DELETE_GOOGLE_ANALYTICS": "Disconnect Google Analytics",
      "CONFIRM_DELETE_GOOGLE_MEET": "Disconnect Google Meet",
      "CONFIRM_DELETE_GOOGLE_SEARCH_CONSOLE": "Disconnect Google Search Console",
      "CANCEL_DELETE_GOOGLE_ANALYTICS": "No, stay connected",
      "CANCEL_DELETE": "No, stay connected",
      "NO_ACCOUNTS": "No accounts connected.",
      "MAX_CONNECTIONS": "Only one {{socialServiceName}} account can be connected at a time.",
      "ADD_ACCOUNT": "Add {{socialServiceName}} connection",
      "NO_LOCATIONS_FOUND": "No locations found.",
      "NO_PAGES_FOUND": "No pages found.",
      "NO_ACCOUNTS_MATCH_SEARCH": "No accounts match your search criteria",
      "NO_MATCH_FOUND": "No matches found",
      "CONNECTING": "Connecting",
      "REMOVED_SUCCESS": "Removed successfully",
      "REMOVED_FAILURE": "Error removing connection, please try again",
      "COULD_NOT_AUTH": "Couldn't get you connected, please try again",
      "BROKEN_TOKEN": "There is a problem with this connection.  Please try reconnecting.",
      "NO_VIEWS": "No Views Available",
      "NO_VIEWS_EXTENDED": "User does not have any Google Analytics account",
      "SET_UP_GA": "Set Up Google Analytics",
      "EDIT_QUERIES": {
        "EDIT_QUERIES": "Edit Queries",
        "TITLE": "Edit Queries in Google Search",
        "SUBTITLE": "Choose up to 20 queries to get reporting on from Google Search Console in your Executive Report.",
        "CANCEL": "Cancel",
        "SAVE": "Save"
      }
    },
    "MY_TEAM": {
      "INFO": {
        "TITLE": "Invite your team",
        "SUBTITLE": "Invite employees and coworkers to your {{businessAppName}}.",
        "ACTION": "Invite team member"
      },
      "INVITE_MEMBER_SIDEBAR": {
        "TITLE": "Add team member",
        "SEND": "Send",
        "CANCEL": "Cancel",
        "CLEAR": "Clear",
        "MEMBER_EXISTS_ERROR": "A user with email '{{email}}' already associated with '{{companyName}}'.",
        "ERROR": "Failed to invite new member.",
        "SUCCESS": "Successfully sent invite to {{email}}."
      }
    }
  },
  "MULTI_LOCATION": {
    "SIDE_PANEL": {
      "VIEW_FULL_EXEC_REPORT": "View full executive report",
      "PRODUCTS": {
        "TITLE": "Products"
      }
    },
    "DATA_EXPORTER": {
      "TABLE_DISCLAIMER": "Shown below is a preview of the columns that will be exported. The exported file will contain all available rows.",
      "DATA_EXPORTING": {
        "DESCRIPTION": "Exporting CSV. This may take a while if there is a large amount of data.",
        "ROWS_EXPORTING": "{{ currentFileCount }} of {{ rowCount }} rows downloaded"
      },
      "TOTAL_LISTINGS": "Total listings: {{ totalListing }}",
      "DATA_SOURCE_SELECTOR": {
        "TITLE": "Data source",
        "REVIEW_DATA": "All reviews",
        "REVIEW_OVERVIEW": "Review summary by source",
        "GMB_TOTAL": "Google Business Profile summary",
        "GMB_BREAKDOWN": "Google Business Profile breakdown",
        "LISTING_DATA": "Listing data"
      },
      "TABLE_ACTIONS": {
        "DOWNLOAD_CSV": "Download CSV",
        "COLUMNS": "Columns"
      },
      "TABLE_COLUMNS": {
        "REVIEW": {
          "DATE_OF_REVIEW": "Date of review",
          "SOURCE": "Source",
          "REVIEW_RATING": "Review rating",
          "REVIEW_TEXT": "Review text",
          "REVIEW_URL": "Review URL",
          "RESPONSE_STATUS": "Response status",
          "RESPONSE": "Response"
        },
        "GMB": {
          "DATE_OF_DATA": "Date"
        },
        "SHARED": {
          "BUSINESS_ID": "Business ID",
          "COMPANY_NAME": "Company name",
          "ADDRESS": "Address",
          "CITY": "City",
          "STATE": "State",
          "ZIP": "Zip"
        }
      },
      "FOOTNOTE": {
        "EXCLUDES_EMPTY": "Exported file will only include locations and sources that received reviews within the selected time period."
      }
    }
  },
  "ERRORS": {
    "403": {
      "USER": "The user",
      "MESSAGE": "does not have sufficient permissions to view this page."
    },
    "NO_ACCESS": {
      "NO_ACCESS": "You don't appear to have access to any accounts. You may need to log in again or contact your administrator to regain access."
    }
  },
  "AUTH_WALL_MODAL": {
    "HEADER": "Log in to {{business_name}}",
    "HEADER_NO_ACCOUNT": "Log in",
    "CONTENT": "You're only a few clicks away from unlocking your personalized {{business_center}}. Take your business to the next level by signing up or logging in below.",
    "CREATE_PROFILE": {
      "SIGN_UP": "Sign up",
      "SUCCESS": "Your profile has been created. Check your email to set your password and log in.",
      "ERROR": "The email address you entered isn't authorized on this account. Try a different email address, or contact {{salesperson_email}} for assistance.",
      "ERROR_NO_SALESPERSON": "The email address you entered isn't authorized on this account. Try a different email address, or contact your rep for assistance.",
      "COMPLETE": "Sign up complete",
      "HEADER_ERROR": "Error signing up",
      "CONTINUE_WITH_GOOGLE": "Continue with Google",
      "LOG_IN_INSTEAD": "Log in instead",
      "SIGN_UP_WITH_GOOGLE": "Sign up with Google"
    },
    "LOGIN": {
      "LOG_IN": "Log in",
      "INVALID_LOGIN": "The username and/or password you entered is invalid. If you do not have an account, sign up instead.",
      "PASSWORD_REQUIRED": "Password is required",
      "LOG_IN_WITH_GOOGLE": "Log in with Google"
    }
  },
  "ORDERS": {
    "ORDER": "Order",
    "APPROVE_ORDER": "Approve Order",
    "ORDER_RECEIVED": "Order received",
    "ORDER_FAILED": "Order failed",
    "ORDER_RECEIVED_MESSAGE": "Thank you for your order, it will be processed shortly.",
    "TRIAL_ORDER_RECEIVED_MESSAGE": "We are currently processing your order. Your trial product will appear in the My Products tab after it's been fully activated.",
    "ORDER_FAILED_MESSAGE": "There was an issue with submitting this order. Try again in a few moments.",
    "ORDER_DECLINED_MESSAGE": "This order has been declined.",
    "ERROR_PERMISSION_DENIED": "You do not have permission to submit this order.",
    "ERROR_EXPIRED": "This order is expired, please contact your salesperson if you would still like to approve this order.",
    "DETAILS_NEEDED_BANNER": "Details Needed",
    "DETAILS_NEEDED_BANNER_DESCRIPTION": "Fulfillment form information is required for an item",
    "VIEW_FORMS": "View forms",
    "CUSTOMER_ATTACHMENTS": "Customer attachments"
  },
  "CONTRACTS": {
    "YOUR_BUSINESS": "Your Business",
    "BUSINESS_CONTACT": "Business Contact",
    "ORDER_SUMMARY": "Order Summary",
    "APPROVE_CONTRACT": "Approve Contract"
  },
  "TABLE": {
    "MEAN": "Average",
    "MEDIAN": "Median",
    "SUM": "Total",
    "COMPARE_SOURCE": "Compare by Source",
    "COMPARE_LOCATION": "Compare by Location",
    "NODE_SOURCE": "Source",
    "NODE_LOCATION": "Location",
    "NODE_SOURCE_ID": "Source ID",
    "NODE_LOCATION_ID": "Location ID",
    "UNKNOWN_ID": "ID",
    "DELTA": "Delta"
  },
  "FAILED_PAYMENTS": {
    "MESSAGE": "Your account has been suspended by {{ partnerName }}. If you believe this is an error, please contact us.",
    "TITLE": "Your account has been suspended"
  },
  "EDITION_UPGRADES": {
    "PAGE_TITLE": "Product upgrade requested",
    "PAGE_OPENING_MESSAGE": "Thank you for your interest in upgrading {{ productName }}. {{ salespersonName }} will be notified and will contact you soon to set up the upgrade. If you would like to provide more information, you can submit a message below.",
    "PAGE_OPENING_MESSAGE_NO_SALESPERSON": "Thank you for your interest in upgrading {{ productName }}. {{ partnerName }} will be notified and will contact you soon to set up the upgrade. If you would like to provide more information, you can submit a message below.",
    "ACTIVATION_MESSAGE": "I'm interested in upgrading to {{ productName }}. Please send me more details!",
    "PRODUCT_INTEREST_SUCCESS_MESSAGE": "Message Sent Successfully",
    "PRODUCT_INTEREST_FAILURE_MESSAGE": "Error occurred while sending message!"
  },
  "ACTIVITY_TITLES": {
    "INBOX_NEW_LEAD": "Inbox New Lead",
    "INSTANT_EMAIL_WHEN_AVAILABLE": "Instant Email and In-App (When Available)",
    "INBOX_MESSAGE_RECEIVED": "Inbox Message Received",
    "AP_REGISTRATION_STATUS_UPDATED": "Status Updated",
    "GOOGLE_MESSAGE_RECEIVED": "Google Message Received",
    "DAILY_DIGEST": "Daily Digest",
    "MONTHLY_CONCIERGE_REPORT": "Monthly Concierge Report",
    "EXECUTIVE_REPORT": "Executive Report",
    "REVIEW": "Review",
    "REVIEW_RESPONSE": "Review Response",
    "NO_REVIEWS": "There are no reviews at the moment.",
    "EDITED_REVIEW": "Edited Review",
    "REMOVED_REVIEW": "Removed Review",
    "REVIEW_RESPONSE_APPROVAL": "Review Response Approval",
    "SOCIAL_ACTIVITY": "Social Activity",
    "LISTING": "Listing",
    "MENTION": "Mention",
    "ACCOUNT_CONNECTION": "Account Connection",
    "CAMPAIGN_STATUS": "Campaign Status",
    "REVIEW_COMMENT": "Review Comment",
    "MONTHLY_EXECUTIVE_REPORT": "Monthly Executive Report",
    "WEEKLY_EXECUTIVE_REPORT": "Weekly Executive Report",
    "ACTIVATION": "Activation",
    "FILE_UPLOAD": "File Upload",
    "SOCIAL_SITES": "Social Sites",
    "IMAGES": "Images",
    "VERY_POSITIVE": "Very Positive",
    "SOMEWHAT_POSITIVE": "Somewhat Positive",
    "NEUTRAL": "Neutral",
    "SOMEWHAT_NEGATIVE": "Somewhat Negative",
    "VERY_NEGATIVE": "Very Negative",
    "MY_PROFILE": "My Profile",
    "EMPLOYEE_PROFILES": "Employee Profiles",
    "CUSTOMER_POST": "Customer Post",
    "LEAD": "Lead",
    "POST_NOTICE": "Post Notice",
    "SOCIAL_CALENDAR": "Social AlignmentPeriodCalendar",
    "AGENT_POST": "Agent Post",
    "WORDPRESS_MAIL": "Wordpress Mail",
    "ACCOUNT_NOTICE": "Account Notice",
    "MESSAGE": "Message",
    "CHATBOT_NOTIFICATION": "Chatbot Notification",
    "CONTACT": "Contact",
    "CALL_NOTIFICATION": "Call Notification",
    "VISITOR_NOTIFICATION": "Visitor Notification",
    "DOMAIN_STATUS": "Domain Status",
    "ACCOUNT_UPDATE": "Account Update",
    "SUBSCRIPTION_STATUS": "Subscription Status",
    "WEBSITE_CONTACT": "Website Contact",
    "VIDEO_RECEIVED": "Video Received",
    "CONTENT_NOTIFICATION": "Content Notification",
    "TESTING": "testing",
    "ACCOUNT_MESSAGE": "Account Message",
    "PROMOTIONS_UPDATE": "Promotions Update",
    "CUSTOMER_ACTIVITY": "Customer Activity",
    "VIDEO_ALERTS": "Video Alerts",
    "WEBSITE_PRODUCTION_UPDATE": "Website Production Update",
    "THREAT_ALERT": "Threat Alert",
    "CAMPAIGN_INFORMATION": "Campaign Information",
    "ORDER_NOTIFICATION": "Order Notification",
    "ACTION_REQUIRED": "Action Required",
    "ALERT": "Alert",
    "EVENT": "Event",
    "REPORT": "Report",
    "WEBSITE_ALERT": "Website Alert",
    "KEYWORD_ALERT": "Keyword Alert",
    "SMS_RECEIVED": "SMS Received",
    "MESSAGE_RECEIVED": "Message Received",
    "STATUS_UPDATE": "Status Update",
    "PRESS_RELEASES": "Press Releases",
    "SUPPORT": "Support",
    "GOOGLE_QUESTION": "Google Question",
    "GOOGLE_ANSWER": "Google Answer"
  },
  "CUSTOMER_JOURNEY": {
    "TITLE": "Customer Journey",
    "STAGE": {
      "NO_STAGE": {
        "DESCRIPTION": "These are the 5 key stages of how people become your customers. Click on a stage for strategies to earn new customers.",
        "GET_STARTED_DESCRIPTION": "The key stages of how people become your customers. Choose a stage for strategies to earn more customers.",
        "ACTION": "Get started"
      },
      "AWARENESS": {
        "TITLE": "Awareness",
        "SUBTITLE": "Do people know about your business?",
        "DESCRIPTION": "Facebook and Google Business Profile are often the first touchpoint a customer has with your business: over 50% of customers say they use social media to research and discover new products. Easily engage with your current customers and make connections with new followers by frequent social posting.",
        "ACTION": {
          "MAKE_A_POST": "Make a post"
        }
      },
      "FINDABILITY": {
        "TITLE": "Findability",
        "SUBTITLE": "Do customers find your business where they are searching?",
        "DESCRIPTION": "When 88% of consumers who do a local search visit or call a business within a day, you need to be found and accurate across high-traffic sources. 46% of all searches are for local information. Having accurate and up-to-date contact info, hours of operation, and more online ensures that customers find your business first.",
        "ACTION": {
          "UPDATE_INFO": "Update my business information",
          "WHERE_AM_I_FOUND": "Where am I found today?"
        }
      },
      "REPUTATION": {
        "TITLE": "Reputation",
        "SUBTITLE": "Do consumers trust your business?",
        "DESCRIPTION": "A positive online reputation is often what makes a consumer choose one business over another. In fact, 95% of consumers use online reviews to make purchase decisions. Monitor and respond to your reviews to rank above the competition and drive more business to your door.",
        "ACTION": {
          "RESPOND_TO_REVIEWS": "Respond to reviews"
        }
      },
      "CONVERSION": {
        "TITLE": "Conversion",
        "SUBTITLE": "Can people easily contact you or buy online?",
        "DESCRIPTION": "When mobile visitors search for your business, they're usually looking for key information: location, store hours, phone numbers and product information. Your website needs that information, and should be easy to find and navigate.",
        "ACTION": {
          "EDIT_WEBSITE": "Edit my website"
        }
      },
      "ADVOCACY": {
        "TITLE": "Advocacy",
        "SUBTITLE": "Do your customers recommend you to others?",
        "DESCRIPTION": "95% of people read reviews before making a purchase decision. Improve the online reviews your potential customers read by asking for reviews from your happy customers. They'll boost your star rating and drive more sales!",
        "ACTION": {
          "EMAIL_CUSTOMERS": "Email my customers"
        }
      }
    }
  },
  "INVITE": {
    "BREADCRUMB": "Invite a business",
    "TITLE": "Enjoying {{ businessAppName }}? Give another business their own app too",
    "DESCRIPTION": "Copy and send the link below to help someone sign up for their very own {{ businessAppName }}. They'll get their own instance of the app, focused on their unique business.",
    "LINK_PLACEHOLDER": "Invite a business link",
    "COPY_TO_CLIPBOARD": "Copy link"
  },
  "SIGN_UP": {
    "TITLE": "Provide your business details to receive access to the #1 business platform",
    "CREATE_AN_ACCOUNT": "Create an account",
    "BUSINESS_NAME": "Business name",
    "CAN_NOT_FIND": "Can't find your business?",
    "MANUAL_CREATE_ACCOUNT": "Click here to manually create an account",
    "CREATE_NEW_ACCOUNT": "Create a new account",
    "STREET_ADDRESS": "Street address",
    "POSTAL/ZIP": "Postal/Zip",
    "PROV/STATE": "Prov/State",
    "BUSINESS_CATEGORY": "Business category",
    "EMAIL_REQUIRED": "Please enter valid email address",
    "REQUIRED_FIELD": "This fields is required",
    "SEARCH_BUSINESS": "Search for your business",
    "REGISTRATION_COMPLETE": {
      "TITLE": "You're all signed up",
      "DESCRIPTION": "Check your email - we've sent you everything you need to set your password and sign in to your new account."
    }
  },
  "TERMS_OF_SERVICE": {
    "TITLE": "Terms of Service"
  },
  "SALES_PROPOSALS": {
    "APPROVE_QUOTE": "Approve Quote",
    "QUOTE_APPROVED": "Quote approved",
    "ALREADY_APPROVED": "This Quote has been approved",
    "DOCUMENT_LOAD_FAILED": "Quote failed to load",
    "SEND_EMAIL": "Send email",
    "CALL": "Call",
    "BOOK_MEETING": "Book a meeting",
    "CONFIRM_IDENTITY": "Please confirm your identity",
    "APPROVED_BY": "By"
  },
  "GALAXY": {
    "INPUT": {
      "PHONE": {
        "VALIDATION_ERROR": "Phone number is invalid"
      },
      "EMAIL": {
        "VALIDATION_ERROR": "Email is invalid"
      },
      "CORE": {
        "VALIDATION_ERROR_REQ": "This field is required"
      }
    }
  },
  "CUSTOMER_SURVEY": {
    "STAR_RATING": {
      "TERRIBLE": "Terrible",
      "BAD": "Bad",
      "OK": "Okay",
      "GOOD": "Good",
      "Great": "Great"
    },
    "FEEDBACK_RECEIVED": "We have received your feedback, thank you!",
    "THANK_YOU": "Thank you for your rating!",
    "MORE_FEEDBACK": "Is there any specific feedback you'd like to add?",
    "FEEDBACK": "Feedback"
  },
  "MEETING_SCHEDULER": {
    "MEETING_LIST.GROUP_GENERAL_LINK_DESCRIPTION": "This link will allow people to choose between all of your businesses services",
    "MEETING_LIST.TEAMS_SECTION_HEADER": "Your business service links",
    "SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.DESCRIPTION_GROUP": "You can customize the URL people use to book meetings with your business. This should be something short and easy to remember."
  },
  "ATLAS": {
    "ONBOARDING_TOUR_BANNER": {
      "DESCRIPTION_DESKTOP": "Your clients can access products, see reporting, and more with {{businessAppName}}.",
      "DESCRIPTION_MOBILE": "{{businessAppName}} is for your clients.",
      "LINK_TITLE": "Next: Go to Partner Center",
      "POP_OVER": {
        "TITLE": "Done exploring {{businessAppName}}?",
        "DESCRIPTION": "Click here to continue to Partner Center, where you can manage your clients.",
        "CLOSE_BUTTON": "Close"
      }
    }
  },
  "PARTNER_ONBOARDING": {
    "WALKTHROUGH_DIALOG": {
      "TITLE": "Welcome to {{businessAppName}}! This is what your clients will see."
    }
  },
  "FULFILLMENT": {
    "FULFILLMENT_FORMS": "Fulfillment forms",
    "TITLE": "Projects",
    "TABS": {
      "IN_PROGRESS": "In progress",
      "COMPLETED": "Completed"
    },
    "EMPTY_STATES": {
      "IN_PROGRESS": {
        "TITLE": "No projects in progress",
        "DESCRIPTION": "Explore the store and find products to supercharge your business.",
        "ACTION": "Discover products"
      },
      "COMPLETED": {
        "TITLE": "No projects completed",
        "DESCRIPTION": "Explore the store and find products to supercharge your business.",
        "ACTION": "Discover products"
      }
    }
  },
  "SALES_ORDERS": {
    "ORDERS": "Sales orders"
  },
  "WORK_ORDERS": {
    "OVERVIEW": "Overview",
    "FULFILLMENT_FORM": "Fulfillment form",
    "FULFILLMENT_FORMS": "Fulfillment forms",
    "PUBLIC_FORM": {
      "ALERT_TITLE": "Details needed",
      "ALERT_SUBTITLE": "Fulfillment form responses needed",
      "COMPLETED": "completed"
    }
  },
  "INVOICES": {
    "EMPTY_STATE": "No invoices found"
  },
  "CONTACT_TABLE_ADD_TO_CAMPAIGN": "Add to campaign",
  "MULTILOCATION": {
    "GBP_CTA": {
      "LEARN_MORE": "Learn More",
      "BOOK": "Book",
      "ORDER": "Order",
      "SHOP": "Buy",
      "SIGN_UP": "Sign Up",
      "GET_OFFER": "Get Offer",
      "CALL": "Call now"
    }
  },
  "CRM": {
    "CUSTOM_FILTERS": {
      "PARENT_COMPANY": "Parent company",
      "PRIMARY_COMPANY": "Primary company",
      "ASSIGNED_TO": "Assigned to",
      "OWNER": "Owner",
      "TEAM_MEMBERS": "Additional team members",
      "PIPELINE": "Pipeline"
    },
    "PRESET_FILTERS": {
      "RECENTLY_ENGAGED": "Recently engaged"
    },
    "CUSTOM_CELLS": {
      "ASSIGNED_TO": "Assigned to"
    },
    "CUSTOM_INPUT": {
      "OWNER": "Owner",
      "TEAM_MEMBERS": "Additional team members"
    },
    "FORMS": {
      "OWNER_IN_ADDITIONAL_TEAM_MEMBERS": "Owner cannot be in additional team members"
    },
    "CONVERSATION_CREATE_ERROR": "Error creating conversation",
    "SEND_MESSAGE": "Send message",
    "ADD_TO_STATIC_LIST": "Add to static list",
    "REQUEST_REVIEWS": "Request reviews",
    "REQUEST_REVIEW": "Request review"
  },
  "CONTACT": {
    "LIST_OBJECTS_TABLE": {
      "SELECT_ALL": {
        "SELECT_ALL_OBJECTS": "select all {{ totalObjects }} contacts",
        "CLEAR_SELECTION": "clear selection",
        "TOTAL_SELECTED": "{{ totalSelected }} selected"
      }
    }
  },
  "COMPANY": {
    "LIST_OBJECTS_TABLE": {
      "SELECT_ALL": {
        "SELECT_ALL_OBJECTS": "select all {{ totalObjects }} companies",
        "CLEAR_SELECTION": "clear selection",
        "TOTAL_SELECTED": "{{ totalSelected }} selected"
      }
    }
  },
  "CAMPAIGNS": {
    "UPDATE_ADDRESS_TITLE": "Update Business Address",
    "ERROR_LOADING": "Failed to load address information. Please try again.",
    "MISSING_ADDRESS": "A complete sender mailing address is required to send campaigns.",
    "REQUIRED_FIELDS": "Please complete all required fields",
    "SAVE_ADDRESS": "Save Address",
    "SAVE_ADDRESS_SUCCESS": "Address successfully updated",
    "SAVE_ADDRESS_FAILURE": "Failed to update address: "
  },
  "BRAND_KEYWORD": {
    "BUSINESS": "Business",
    "MAPS_RANK": "Rank",
    "MAPS_STARTED_AT": "Started at",
    "ORGANIC_RANK": "Rank",
    "ORGANIC_STARTED_AT": "Started at",
    "MAPS": "Maps",
    "ORGANIC": "Organic",
    "BY_RANKS": "by {{value}} ranks",
    "TOP_10": "Locations with keyword in top 10",
    "AVERAGE_POSITION": "Average position",
    "AVERAGE_POSITION_TOOLTIP_TITLE": "Average position",
    "AVERAGE_POSITION_TOOLTIP": "Represents the engagement rate over the period.",
    "KEYWORDS_UP": "Locations moving up",
    "TITLE": "Keyword Tracking",
    "AVERAGE_TOP10": "Average number of keywords in top 10",
    "AVERAGE_TOP10_TOOLTIP": "Keywords in Top 10",
    "AVERAGE_POSITION_BREAKDOWN": "Average position breakdown",
    "LOCAL_RANK_TOOLTIP": "Local Rank",
    "ORGANIC_RANK_TOOLTIP": "Organic Rank",
    "POSITION_TOOLTIP_1_TO_10": "Position 1-10",
    "POSITION_TOOLTIP_11_TO_50": "Position 11-50",
    "POSITION_TOOLTIP_50_PLUS": "Position 50+",
    "LOCATIONS": "{{keyword}} ({{count}} locations)"
  },
  "NPS": {
    "TITLE": "NPS",
    "ADDRESS": "<i>at</i> {{address}}",
    "EMPTY_STATE": "There is no customer feedback for the selected {{ filterType }}",
    "FILTERS": {
      "RATING": "Ratings",
      "SCORES": "Scores",
      "PROMOTERS": "Promoters (9-10)",
      "PASSIVES": "Passives (7-8)",
      "DETRACTORS": "Detractors (0-6)"
    }
  },
  "TEAM": {
    "FOR": "<i>for</i>  {{providername}}",
    "AND": " and ",
    "EMPTY_STATE": "No feedback data available for your team yet"
  },
  "NPS_CARD": {
    "NPS_RESEND_CONFIRMATION": {
      "TITLE": "Are you sure you want to re-send an NPS request?",
      "RESEND_BUTTON": "Re-send",
      "EMAIL_NOT_FOUND": "Add an email to the contact to re-send NPS request."
    },
    "RESEND_REQUEST": "Once you’ve addressed their concerns, you could ask them for another review",
    "RESENT_REQUEST": "Customer was asked to review their experience again on {{resentDate}}",
    "RESEND_NPS_REQUEST": "Re-send NPS request"
  },
  "NPS_OVERALL_CHART": {
    "NAME": "Net Promoter Score (NPS)",
    "NPS_PROMOTER": "Promoter",
    "NPS_PASSIVE": "Passive",
    "NPS_DETRACTOR": "Detractor",
    "NPS_INFO": "Net Promoter Score (NPS) measures your customer satisfaction and loyalty.<br><br> <b>How it's calculated:</b> <br> NPS = % Promoters - % Detractors <br> <br> <b>Explanation:</b> <br> •Promoters: Customers who would recommend your brand. <br> •Detractors: Customers who wouldn't recommend your brand.<br><br> <b>Example:</b> <br> If 70% of your customers are promoters and 10% are detractors, your NPS is 60.",
    "RATINGS": "{{totalScore}} ratings"
  },
  "NPS_ROLLING_AVG_GRAPH": {
    "TITLE": "Score over time",
    "NPS_INFO": "The Net Promoter Score (NPS) shown here is calculated using a 30-day rolling window. This means the score is an average of the feedback received over the past 30 days, updated daily. This method helps smooth out short-term fluctuations and highlights longer-term trends in customer satisfaction."
  }
}
