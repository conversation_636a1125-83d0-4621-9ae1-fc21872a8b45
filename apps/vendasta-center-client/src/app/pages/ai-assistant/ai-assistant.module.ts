import { NgModule } from '@angular/core';
import { AiAssistantRoutingModule } from './ai-assistant-routing.module';
import { AiKnowledgeModule } from '@galaxy/ai-knowledge';
import { AI_KNOWLEDGE_CONFIG, AI_ASSISTANT_CONFIG } from './providers';
import { AiAssistantModule as AiAssistantCoreModule } from '@galaxy/ai-assistant';
import { CommonModule } from '@angular/common';

@NgModule({
  imports: [
    CommonModule,
    AiAssistantRoutingModule,
    AiKnowledgeModule.forRoot({
      config: AI_KNOWLEDGE_CONFIG,
    }),
    AiAssistantCoreModule.forRoot({
      config: AI_ASSISTANT_CONFIG,
    }),
  ],
})
export class AiAssistantModule {}
