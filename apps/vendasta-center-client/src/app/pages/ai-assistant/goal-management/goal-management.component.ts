import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';

import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { Namespace, NamespaceInterface } from '@vendasta/ai-assistants';

import { firstValueFrom } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { GoalManagementService, GoalFilters } from './goal-management.service';
import { Goal } from './goal';
import { GoalEditorComponent } from './goal-editor/goal-editor.component';
import { NamespaceType, NamespaceUtils } from '../shared/utils';

@Component({
  selector: 'app-goal-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDialogModule,
    MatListModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
    GalaxyPageModule,
    GalaxyLoadingSpinnerModule,
    GalaxyAlertModule,
    GalaxyFormFieldModule,
  ],
  templateUrl: './goal-management.component.html',
  styleUrls: ['./goal-management.component.scss'],
  providers: [GoalManagementService],
})
export class GoalManagementComponent implements OnInit {
  private readonly dialog = inject(MatDialog);
  private readonly goalManagementService = inject(GoalManagementService);
  private readonly activeRoute = inject(ActivatedRoute);
  private readonly router = inject(Router);

  protected readonly NamespaceUtils = NamespaceUtils;
  protected readonly NamespaceType = NamespaceType;

  isLoading = false;
  goals: Goal[] = [];
  filteredGoals: Goal[] = [];
  globalNamespace = new Namespace({
    globalNamespace: {},
  });
  systemNamespace = new Namespace({
    systemNamespace: {},
  });
  accountGroupNamespace = new Namespace({
    accountGroupNamespace: {},
  });

  filterForm = new FormGroup({
    namespaceType: new FormControl(NamespaceType.ALL),
    namespaceId: new FormControl(''),
  });

  namespaceTypeOptions = [
    { value: NamespaceType.ALL, label: 'All' },
    { value: NamespaceType.GLOBAL, label: 'Global' },
    { value: NamespaceType.SYSTEM, label: 'System' },
    { value: NamespaceType.ACCOUNT_GROUP, label: 'Account group' },
    { value: NamespaceType.PARTNER, label: 'Partner' },
  ];

  ngOnInit() {
    const editParam = this.activeRoute.snapshot.queryParamMap.get('edit');
    const namespaceTypeParam = this.activeRoute.snapshot.queryParamMap.get('namespaceType');
    const accountGroupIdParam = this.activeRoute.snapshot.queryParamMap.get('accountGroupId');
    const partnerIdParam = this.activeRoute.snapshot.queryParamMap.get('partnerId');

    if (editParam) {
      let namespace;

      if (namespaceTypeParam === 'Global') {
        namespace = new Namespace({ globalNamespace: {} });
      } else if (namespaceTypeParam === 'System') {
        namespace = new Namespace({ systemNamespace: {} });
      } else if (namespaceTypeParam === 'Account Group' && accountGroupIdParam) {
        namespace = new Namespace({
          accountGroupNamespace: { accountGroupId: accountGroupIdParam },
        });
      } else if (namespaceTypeParam === 'Partner' && partnerIdParam) {
        namespace = new Namespace({
          partnerNamespace: { partnerId: partnerIdParam },
        });
      } else {
        namespace = new Namespace({ globalNamespace: {} }); // Default to global
      }

      this.editGoal(editParam, namespace);
    }

    this.refreshGoals();

    // Subscribe to namespace type changes to clear namespace ID when not needed
    this.filterForm.get('namespaceType')?.valueChanges.subscribe((_namespaceType) => {
      if (!this.shouldShowNamespaceIdField()) {
        this.filterForm.get('namespaceId')?.setValue('');
      }
    });

    // Subscribe to filter changes with debounce
    this.filterForm.valueChanges
      .pipe(
        debounceTime(300), // Wait 300ms after the last change before triggering
        distinctUntilChanged(), // Only trigger if the value has actually changed
      )
      .subscribe(() => {
        this.refreshGoals();
      });
  }

  createNewGoal(): void {
    const dialogRef = this.dialog.open(GoalEditorComponent, {
      data: {
        namespace: new Namespace({ globalNamespace: {} }),
      },
    });

    dialogRef.afterClosed().subscribe(() => {
      this.refreshGoals();
    });
  }

  async editGoal(goalId: string, namespace: NamespaceInterface = null): Promise<void> {
    const dialogRef = this.dialog.open(GoalEditorComponent, {
      data: {
        goalId,
        namespace: namespace || new Namespace({ globalNamespace: {} }),
      },
    });

    await this.setQueryParam(goalId, namespace || new Namespace({ globalNamespace: {} }));
    await firstValueFrom(dialogRef.afterClosed());
    await this.setQueryParam(null, null);
    await this.refreshGoals();
  }

  getNamespaceIdLabel(): string {
    const namespaceType = this.filterForm.get('namespaceType')?.value;

    switch (namespaceType) {
      case NamespaceType.ACCOUNT_GROUP:
        return 'Account Group ID';
      case NamespaceType.PARTNER:
        return 'Partner ID';
      default:
        return 'Namespace ID';
    }
  }

  shouldShowNamespaceIdField(): boolean {
    const namespaceType = this.filterForm.get('namespaceType')?.value;
    return namespaceType === NamespaceType.ACCOUNT_GROUP || namespaceType === NamespaceType.PARTNER;
  }

  private async setQueryParam(goalId: string, namespace: NamespaceInterface) {
    const queryParams: any = {
      edit: null,
      namespaceType: null,
      accountGroupId: null,
      partnerId: null,
    };

    if (goalId && namespace) {
      queryParams.edit = goalId;

      if (namespace.globalNamespace) {
        queryParams.namespaceType = 'Global';
      } else if (namespace.systemNamespace) {
        queryParams.namespaceType = 'System';
      } else if (namespace.accountGroupNamespace) {
        queryParams.namespaceType = 'Account Group';
        queryParams.accountGroupId = namespace.accountGroupNamespace.accountGroupId;
      } else if (namespace.partnerNamespace) {
        queryParams.namespaceType = 'Partner';
        queryParams.partnerId = namespace.partnerNamespace.partnerId;
      }
    }

    await this.router.navigate([], {
      relativeTo: this.activeRoute,
      queryParams,
      queryParamsHandling: 'merge',
    });
  }

  protected async refreshGoals(): Promise<void> {
    this.isLoading = true;
    try {
      const filters: GoalFilters = {
        namespaceType: this.filterForm.get('namespaceType')?.value,
        namespaceId: this.filterForm.get('namespaceId')?.value,
      };

      this.goals = await this.goalManagementService.listGoals(filters);
      this.filteredGoals = this.goals;
    } finally {
      this.isLoading = false;
    }
  }
}
