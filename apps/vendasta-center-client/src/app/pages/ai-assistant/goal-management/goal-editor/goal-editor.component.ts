import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTabsModule } from '@angular/material/tabs';
import { MatInputModule } from '@angular/material/input';
import { TextFieldModule } from '@angular/cdk/text-field';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Component, inject, OnInit, signal } from '@angular/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatRadioModule } from '@angular/material/radio';

import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';

import {
  FunctionKeyInterface,
  GoalChannel,
  GoalInterface,
  GoalType,
  Namespace,
  PromptModule,
  PromptModuleApiService,
  PromptModuleKeyInterface,
} from '@vendasta/ai-assistants';

import { firstValueFrom } from 'rxjs';

import { GoalManagementService } from '../goal-management.service';
import { Goal } from '../goal';
import { FunctionSelectorComponent } from '../function-selector/function-selector.component';
import { PromptSelectorComponent } from '../prompt-selector/prompt-selector.component';
import { NamespaceUtils } from '../../shared/utils';

export enum NamespaceType {
  SYSTEM = 'SYSTEM',
  GLOBAL = 'GLOBAL',
  ACCOUNT_GROUP = 'ACCOUNT_GROUP',
  PARTNER = 'PARTNER',
}

@Component({
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatDialogModule,
    MatInputModule,
    MatSidenavModule,
    MatListModule,
    MatTabsModule,
    MatSelectModule,
    MatChipsModule,
    MatIconModule,
    MatTooltipModule,
    MatRadioModule,
    TextFieldModule,
    GalaxyLoadingSpinnerModule,
    GalaxySnackbarModule,
    GalaxyFormFieldModule,
    GalaxySnackbarModule,
    GalaxyBadgeModule,
  ],
  templateUrl: './goal-editor.component.html',
  styleUrls: ['./goal-editor.component.scss'],
  providers: [GoalManagementService],
})
export class GoalEditorComponent implements OnInit {
  isLoading = signal<boolean>(false);
  isEditMode = signal<boolean>(false);
  form = signal<FormGroup | null>(null);
  goal = signal<Goal | null>(null);
  promptModules = signal<PromptModule[] | null>(null);
  promptModuleContent = signal<Record<string, string>>({});

  readonly goalTypes = new Map<number, string>([
    [GoalType.GOAL_TYPE_GOAL, 'Goal'],
    [GoalType.GOAL_TYPE_PERSONALITY, 'Personality'],
    [GoalType.GOAL_TYPE_KNOWLEDGE, 'Knowledge'],
    [GoalType.GOAL_TYPE_CUSTOM, 'Custom'],
  ]);

  readonly goalChannels = new Map<number, string>([
    [GoalChannel.GOAL_CHANNEL_PLATFORM, 'Platform Chat'],
    [GoalChannel.GOAL_CHANNEL_WEBCHAT, 'Web Chat'],
    [GoalChannel.GOAL_CHANNEL_SMS, 'SMS'],
    [GoalChannel.GOAL_CHANNEL_EMAIL, 'Email'],
    [GoalChannel.GOAL_CHANNEL_VOICE, 'Voice'],
    [GoalChannel.GOAL_CHANNEL_FACEBOOK, 'Facebook'],
    [GoalChannel.GOAL_CHANNEL_INSTAGRAM, 'Instagram'],
    [GoalChannel.GOAL_CHANNEL_WHATSAPP, 'WhatsApp'],
    [GoalChannel.GOAL_CHANNEL_AUTOMATION, 'Automation'],
    [GoalChannel.GOAL_CHANNEL_SNAPSHOT, 'Snapshot'],
  ]);

  private readonly dialogRef = inject(MatDialogRef<GoalEditorComponent>);
  private readonly goalManagementService = inject(GoalManagementService);
  private readonly promptModuleService = inject(PromptModuleApiService);
  private readonly data = inject(MAT_DIALOG_DATA);
  private readonly dialog = inject(MatDialog);
  private readonly snackbarService = inject(SnackbarService);
  protected readonly NamespaceUtils = NamespaceUtils;

  ngOnInit() {
    this.isEditMode.set(!!this.data?.goalId);
    this.form.set(this.initForm());
    if (this.isEditMode()) {
      this.loadGoal(this.data.goalId, this.data.namespace);
    } else {
      this.goal.set(new Goal());
    }

    // Add conditional validation for Group Path and Partner ID based on namespace type
    this.form()
      .get('namespaceType')
      ?.valueChanges.subscribe((namespaceType) => {
        const accountGroupIdControl = this.form().get('accountGroupId');

        // Clear all validators first
        accountGroupIdControl?.clearValidators();

        // Set validators based on namespace type
        if (namespaceType === NamespaceType.ACCOUNT_GROUP) {
          accountGroupIdControl?.setValidators([Validators.required]);
        }

        // Update validity
        accountGroupIdControl?.updateValueAndValidity();
      });
  }

  private initForm(): FormGroup {
    return new FormGroup({
      id: new FormControl({ value: this.data?.goalId || '', disabled: this.isEditMode() }, [Validators.required]),
      namespaceType: new FormControl({ value: NamespaceType.GLOBAL, disabled: this.isEditMode() }, [
        Validators.required,
      ]),
      accountGroupId: new FormControl(''),
      partnerId: new FormControl(''),
      name: new FormControl('', [Validators.required]),
      description: new FormControl(''),
      type: new FormControl(null, [Validators.required]),
      supportedChannels: new FormControl([]),
      promptModules: new FormControl([]),
      functions: new FormControl([]),
    });
  }

  async loadGoal(goalId: string, namespace: Namespace) {
    try {
      this.isLoading.set(true);
      const goal = await this.goalManagementService.getGoal(goalId, namespace);
      this.goal.set(goal);

      let accountGroupId = '';

      if (goal.namespace.accountGroupNamespace) {
        const accountGroupNamespace = goal.namespace.accountGroupNamespace;
        accountGroupId = accountGroupNamespace.accountGroupId || '';
      }

      // Determine namespace type from goal namespace
      let namespaceType = NamespaceType.GLOBAL; // Default

      if (goal.namespace.systemNamespace) {
        namespaceType = NamespaceType.SYSTEM;
      } else if (goal.namespace.accountGroupNamespace) {
        namespaceType = NamespaceType.ACCOUNT_GROUP;
      }

      this.form()?.patchValue({
        id: goal.id,
        namespaceType: namespaceType,
        accountGroupId: accountGroupId,
        name: goal.name,
        description: goal.description,
        type: goal.type,
        supportedChannels: goal.supportedChannels || [],
        promptModules: goal.promptModules || [],
        functions: goal.functions || [],
      });
      await this.loadPromptModuleDetails();
    } catch (error) {
      this.snackbarService.openErrorSnack('Failed to load goal');
      console.error('Error loading goal:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  private async loadPromptModuleDetails(): Promise<void> {
    if (!this.goal()?.promptModules?.length) return;

    const loadedModules: PromptModule[] = [];
    const contentMap: Record<string, string> = {};

    for (const moduleKey of this.goal().promptModules) {
      const moduleResponse = await firstValueFrom(
        this.promptModuleService.get({
          id: moduleKey.id,
          namespace: moduleKey.namespace,
        }),
      );

      if (moduleResponse.promptModule) {
        loadedModules.push(moduleResponse.promptModule);

        if (moduleResponse.promptModule.deployedVersion) {
          const versionResponse = await firstValueFrom(
            this.promptModuleService.getVersion({
              id: moduleKey.id,
              version: moduleResponse.promptModule.deployedVersion,
              namespace: moduleKey.namespace,
            }),
          );
          if (versionResponse.promptModuleVersion) {
            contentMap[moduleResponse.promptModule.id] = versionResponse.promptModuleVersion.content;
          }
        }
      }
    }

    this.promptModules.set(loadedModules);
    this.promptModuleContent.set(contentMap);
  }

  getPromptModuleContent(promptModule: PromptModule): string {
    return this.promptModuleContent()[promptModule.id] || '';
  }

  addPromptModule(): void {
    const dialogRef = this.dialog.open(PromptSelectorComponent, {
      width: '500px',
    });

    dialogRef.afterClosed().subscribe(async (result: PromptModuleKeyInterface) => {
      if (result && this.goal()) {
        const updatedGoal = { ...this.goal() };
        if (!updatedGoal.promptModules) {
          updatedGoal.promptModules = [];
        }

        // Only add if not already present
        if (!updatedGoal.promptModules.find((p) => p.id === result.id)) {
          updatedGoal.promptModules.push({
            id: result.id,
            namespace: result.namespace,
          });
          this.goal.set(updatedGoal);
          await this.loadPromptModuleDetails();

          const promptModulesControl = this.form()?.get('promptModules');
          if (promptModulesControl) {
            promptModulesControl.setValue(updatedGoal.promptModules);
            promptModulesControl.markAsDirty();
          }
        }
      }
    });
  }

  removePromptModule(promptModule: PromptModule): void {
    const currentPromptModules = this.goal()?.promptModules || [];
    const updatedGoal = {
      ...this.goal(),
      promptModules: currentPromptModules.filter((p) => p.id !== promptModule.id),
    };
    this.goal.set(updatedGoal);
    this.promptModules.set(this.promptModules()?.filter((p) => p.id !== promptModule.id));

    const updatedContent = { ...this.promptModuleContent() };
    delete updatedContent[promptModule.id];
    this.promptModuleContent.set(updatedContent);

    const promptModulesControl = this.form()?.get('promptModules');
    if (promptModulesControl) {
      promptModulesControl.setValue(updatedGoal.promptModules);
      promptModulesControl.markAsDirty();
    }
  }

  submit(): void {
    if (this.form()?.valid && this.goal()) {
      const formValue = this.form().value;
      let namespace = this.isEditMode() ? this.goal().namespace : { globalNamespace: {} };

      if (!this.isEditMode()) {
        switch (formValue.namespaceType) {
          case NamespaceType.GLOBAL:
            namespace = { globalNamespace: {} };
            break;
          case NamespaceType.SYSTEM:
            namespace = { systemNamespace: {} };
            break;
          case NamespaceType.ACCOUNT_GROUP:
            namespace = { accountGroupNamespace: { accountGroupId: formValue.accountGroupId } };
            break;
          case NamespaceType.PARTNER:
            namespace = { partnerNamespace: { partnerId: formValue.partnerId } };
            break;
        }
      }

      const updatedGoal: GoalInterface = {
        id: this.isEditMode() ? this.goal().id : formValue.id,
        namespace: namespace,
        type: formValue.type,
        name: formValue.name,
        description: formValue.description,
        promptModules: this.goal().promptModules || [],
        functions: this.goal().functions || [],
        supportedChannels: formValue.supportedChannels || [],
        updated: this.goal().updated,
        updatedBy: this.goal().updatedBy,
      };

      this.isLoading.set(true);
      this.goalManagementService
        .upsertGoal(updatedGoal)
        .then(() => {
          this.form()?.markAsPristine();
          this.snackbarService.openSuccessSnack('Goal saved successfully');
        })
        .catch((error) => {
          console.error('Failed to save goal:', error);
          this.snackbarService.openErrorSnack('Failed to save goal');
        })
        .finally(() => {
          this.isLoading.set(false);
        });
    }
  }

  close(): void {
    this.dialogRef.close();
  }

  addFunction(): void {
    const dialogRef = this.dialog.open(FunctionSelectorComponent, {
      width: '500px',
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const currentFunctions = this.goal()?.functions || [];
        if (currentFunctions.find((f) => f.id === result.id)) return;
        const updatedGoal = { ...this.goal(), functions: [...currentFunctions, result] };
        this.goal.set(updatedGoal);

        // Update the form control
        const functionsControl = this.form()?.get('functions');
        if (functionsControl) {
          functionsControl.setValue([...currentFunctions, result]);
          functionsControl.markAsDirty();
        }
      }
    });
  }

  removeFunction(functionToRemove: FunctionKeyInterface): void {
    const currentFunctions = this.goal()?.functions || [];
    const updatedGoal = { ...this.goal(), functions: currentFunctions.filter((f) => f.id !== functionToRemove.id) };
    this.goal.set(updatedGoal);

    const functionsControl = this.form()?.get('functions');
    if (functionsControl) {
      functionsControl.markAsDirty();
    }
  }
}
