import { inject, Injectable } from '@angular/core';
import { Assistant, AssistantApiService, ListAssistantRequest, Namespace } from '@vendasta/ai-assistants';
import { firstValueFrom } from 'rxjs';

export interface AssistantFilter {
  namespaceType?: string;
  namespaceId?: string;
}

@Injectable({
  providedIn: 'root',
})
export class AssistantSelectorService {
  private readonly assistantApiService = inject(AssistantApiService);

  public async listAssistants(filter?: AssistantFilter): Promise<Assistant[]> {
    // Determine which namespaces to query based on filters
    let namespaceToQuery: Namespace = new Namespace();
    switch (filter.namespaceType) {
      case 'GLOBAL':
        namespaceToQuery = new Namespace({ globalNamespace: {} });
        break;
      case 'SYSTEM':
        namespaceToQuery = new Namespace({ systemNamespace: {} });
        break;
      case 'ACCOUNT_GROUP':
        if (filter.namespaceId) {
          namespaceToQuery = new Namespace({
            accountGroupNamespace: { accountGroupId: filter.namespaceId },
          });
        } else {
          namespaceToQuery = new Namespace({ accountGroupNamespace: {} });
        }
        break;
      case 'PARTNER':
        if (filter.namespaceId) {
          namespaceToQuery = new Namespace({
            partnerNamespace: { partnerId: filter.namespaceId },
          });
        } else {
          namespaceToQuery = new Namespace({ partnerNamespace: {} });
        }
        break;
    }

    const response = await firstValueFrom(
      this.assistantApiService.listAssistant(
        new ListAssistantRequest({
          filters: {
            namespace: namespaceToQuery,
          },
          pagingOptions: {
            pageSize: 50,
          },
        }),
      ),
    );

    return response?.assistants || [];
  }
}
