<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>Select AI Assistant</glxy-page-title>
  </glxy-page-toolbar>
  <glxy-page-wrapper>
    <div class="filter-container" [formGroup]="filterForm">
      <div class="filter-row">
        <glxy-form-field class="namespace-type-filter">
          <glxy-label>Namespace Type</glxy-label>
          <mat-select [formControl]="filterForm.get('namespaceType')">
            @for (option of namespaceTypeOptions; track option.value) {
              <mat-option [value]="option">{{ option.label }}</mat-option>
            }
          </mat-select>
        </glxy-form-field>

        @if (filterForm.get('namespaceType')?.value?.hasId) {
          <glxy-form-field class="namespace-id-filter">
            <glxy-label>{{ filterForm.get('namespaceType')?.value?.idLabel }}</glxy-label>
            <input
              type="text"
              matInput
              (keyup.enter)="refresh()"
              formControlName="namespaceId"
              placeholder="Filter by ID"
            />
          </glxy-form-field>
        }
      </div>
    </div>

    <mat-card>
      <mat-card-content>
        @if (isLoading) {
          <div class="loading-container">
            <glxy-loading-spinner />
          </div>
        } @else {
          <mat-list>
            @if (filteredAssistants.length === 0) {
              <mat-list-item>
                <div class="goal">
                  <div class="text">No Assistants found</div>
                </div>
              </mat-list-item>
            } @else {
              @for (assistant of filteredAssistants; track assistant) {
                <mat-list-item>
                  <div class="assistant">
                    <div class="title">
                      @if (assistant.avatarUrl) {
                        <img class="icon" [src]="assistant.avatarUrl" />
                      }
                      <a (click)="selectAssistant(assistant)">
                        <span>[{{ NamespaceUtils.getName(assistant.namespace) }}] </span>
                        @if (NamespaceUtils.getId(assistant.namespace); as namespaceId) {
                          <span>{{ namespaceId }} </span>
                        }
                        <span>{{ assistant.name }}</span>
                      </a>
                    </div>
                  </div>
                </mat-list-item>
              }
            }
          </mat-list>
        }
      </mat-card-content>
    </mat-card>
  </glxy-page-wrapper>
</glxy-page>
