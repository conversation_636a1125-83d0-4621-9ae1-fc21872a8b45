import { Component, inject, OnInit } from '@angular/core';
import { Assistant } from '@vendasta/ai-assistants';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { NamespaceType, NamespaceUtils } from '../shared/utils';
import { AssistantSelectorService } from './assistant-selector.service';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';

interface NamespaceFilter {
  value: NamespaceType;
  label: string;
  hasId?: boolean;
  idLabel?: string;
}

@Component({
  selector: 'app-assistant-selector',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    GalaxyPageModule,
    GalaxyLoadingSpinnerModule,
    GalaxyAlertModule,
    GalaxyFormFieldModule,
    MatButtonModule,
    MatCardModule,
    MatDialogModule,
    MatListModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
  ],
  providers: [AssistantSelectorService],
  standalone: true,
  templateUrl: './assistant-selector.component.html',
  styleUrl: './assistant-selector.component.scss',
})
export class AssistantSelectorComponent implements OnInit {
  assistantService = inject(AssistantSelectorService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  isLoading = false;
  filteredAssistants: Assistant[] = [];

  namespaceTypeOptions: NamespaceFilter[] = [
    { value: NamespaceType.SYSTEM, label: 'System' },
    { value: NamespaceType.GLOBAL, label: 'Global' },
    { value: NamespaceType.ACCOUNT_GROUP, label: 'Account group', hasId: true, idLabel: 'Account group ID' },
    { value: NamespaceType.PARTNER, label: 'Partner', hasId: true, idLabel: 'Partner ID' },
  ];

  filterForm = new FormGroup({
    namespaceType: new FormControl(this.namespaceTypeOptions[0]),
    namespaceId: new FormControl(''),
  });

  protected async refresh(): Promise<void> {
    console.log('Refreshing assistants with filter:', this.filterForm.value);
    this.isLoading = true;
    try {
      this.filteredAssistants = await this.assistantService.listAssistants({
        namespaceType: this.filterForm.get('namespaceType')?.value?.value,
        namespaceId: this.filterForm.get('namespaceId')?.value,
      });
    } finally {
      this.isLoading = false;
    }
  }

  ngOnInit(): void {
    this.refresh();
    this.filterForm.valueChanges
      .pipe(
        debounceTime(300), // Wait 300ms after the last change before triggering
        distinctUntilChanged(), // Only trigger if the value has actually changed
      )
      .subscribe(() => {
        this.refresh();
      });
  }

  selectAssistant(assistant: Assistant): void {
    const type = NamespaceUtils.getType(assistant.namespace);
    let namespaceId = NamespaceUtils.getId(assistant.namespace);
    if (!namespaceId) {
      switch (type) {
        case 'GLOBAL':
          namespaceId = 'Global';
          break;
        case 'SYSTEM':
          namespaceId = 'System';
          break;
      }
    }

    this.router.navigate([`./${type}/${namespaceId}/${assistant.id}`], { relativeTo: this.route });
  }

  protected readonly NamespaceUtils = NamespaceUtils;
}
