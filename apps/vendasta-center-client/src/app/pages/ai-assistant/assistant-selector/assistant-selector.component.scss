@use 'design-tokens' as *;

.filter-container {
  margin: $spacing-3 0;
  padding: $spacing-3;
  background-color: white;
  border: 1px solid $border-color;
  border-radius: $spacing-1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-3;
    align-items: flex-start;
  }

  .namespace-type-filter {
    min-width: 250px;
    max-width: 400px;
    width: 100%;
  }

  .namespace-id-filter {
    min-width: 250px;
    flex: 1;
  }
}

.assistant {
  display: flex;
  flex-direction: column;
  text-align: justify;
}

.title {
  font-size: $font-preset-3-size;
  font-weight: bold;
  margin-bottom: $spacing-1;

  a {
    color: $primary-color;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.icon {
  width: 24px;
  height: 24px;
  margin-right: $spacing-2;
  display: inline-block;
  vertical-align: middle;
  border-radius: 50%;
}
