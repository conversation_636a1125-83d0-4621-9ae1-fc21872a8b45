import { InjectionToken, inject } from '@angular/core';
import { EMPTY, Observable, of } from 'rxjs';
import { AiKnowledgeConfig } from '@galaxy/ai-knowledge';
import { ATLAS_CONFIG_TOKEN } from '@galaxy/atlas';
import { map, shareReplay } from 'rxjs/operators';
import {
  AiAssistant,
  AiAssistantConfig,
  ASSISTANT_ID_SYSTEM,
  AURORA_AVATAR_SVG_ICON,
  NamespaceConfig,
} from '@galaxy/ai-assistant';
import { AssistantType, Namespace } from '@vendasta/ai-assistants';

export const AI_KNOWLEDGE_CONFIG = new InjectionToken<AiKnowledgeConfig>(
  '[Vendasta Center Client]: Token for AI Knowledge config',
  {
    providedIn: 'root',
    factory: function (): AiKnowledgeConfig {
      const partnerId$ = inject(ATLAS_CONFIG_TOKEN).pipe(map((config) => config.partnerId));

      return {
        accountGroupId$: EMPTY,
        businessProfileUrl$: EMPTY,
        marketId$: EMPTY,
        partnerId$: partnerId$,
        showBusinessProfileSource$: EMPTY,
        manageKnowledgeUrl$: of('/ai-assistant/support/knowledge-base'),
        webChatWidgetEditRoute$: EMPTY,
      };
    },
  },
);

export const AI_ASSISTANT_CONFIG = new InjectionToken<AiAssistantConfig>(
  '[Vendasta Center Client]: Token for AI Assistant config',
  {
    providedIn: 'root',
    factory: function (): AiAssistantConfig {
      const partnerId$ = inject(ATLAS_CONFIG_TOKEN).pipe(map((config) => config.partnerId));

      const namespaceConfig$: Observable<NamespaceConfig> = of({
        namespace: new Namespace({
          systemNamespace: {},
        }),
        root: `/ai-assistant`,
      }).pipe(shareReplay({ refCount: true, bufferSize: 1 }));

      return {
        accountGroupId$: of(''),
        marketId$: of(''),
        partnerId$: partnerId$,
        currentUserId$: of(''),
        defaultAIWorkforce$: of(AI_DEFAULT_VENDASTA_ASSISTANT_WORKFORCE),
        namespaceConfig$: namespaceConfig$,
        defaultConnections$: of([]),
        voiceAIAvailable$: of(false),
      };
    },
  },
);

export const AI_DEFAULT_VENDASTA_ASSISTANT_WORKFORCE: AiAssistant[] = [
  {
    assistant: {
      id: ASSISTANT_ID_SYSTEM,
      name: 'AI_ASSISTANT.SETTINGS.SYSTEM_ASSISTANT.DESCRIPTION',
      type: AssistantType.ASSISTANT_TYPE_SYSTEM,
    },
    descriptionKey: 'AI_ASSISTANT.SETTINGS.SYSTEM_ASSISTANT.DESCRIPTION',
    decoration: {
      defaultAvatarIcon: AURORA_AVATAR_SVG_ICON,
      gradientColor: '#22C0CA',
    },
    isDefault: true,
    showConfigButtonOnDefault: true,
    alertInformation: 'AI_ASSISTANT.SETTINGS.SYSTEM_ASSISTANT.ALERT_INFORMATION',
    hideConnections: true,
  },
];
