import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';
import { MatCardModule } from '@angular/material/card';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GALAXY_UPLOADER_SERVICE_TOKEN, GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { UploadService } from './test-management-upload.service';
import { MatDialog } from '@angular/material/dialog';
import { TestUploadModalComponent, TestUploadModalResult } from './test-upload-modal/test-upload-modal.component';
import { MatButtonModule } from '@angular/material/button';
import { AssistantKey, Namespace } from '@vendasta/ai-assistants';
import { TestCaseDisplayComponent } from './test-case-display/test-case-display.component';
import { map, switchMap } from 'rxjs/operators';
import { EMPTY, Observable } from 'rxjs';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatIconModule } from '@angular/material/icon';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { ActivatedRoute } from '@angular/router';
import { TestManagementService } from './test-management.service';
import { TestRunTableComponent } from './test-run-table/test-run-table.component';

export const snackbarDuration = 5000;

@Component({
  selector: 'app-test-management',
  imports: [
    MatButtonModule,
    MatIconModule,
    CommonModule,
    GalaxyPageModule,
    GalaxyStickyFooterModule,
    MatCardModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    GalaxyUploaderModule,
    TestCaseDisplayComponent,
    GalaxyLoadingSpinnerModule,
    GalaxySnackbarModule,
    GalaxyAlertModule,
    TestRunTableComponent,
  ],
  standalone: true,
  providers: [UploadService, { provide: GALAXY_UPLOADER_SERVICE_TOKEN, useClass: UploadService }],
  templateUrl: './test-management.component.html',
  styleUrl: './test-management.component.scss',
})
export class TestManagementComponent implements OnInit {
  @ViewChild('table') table: TestCaseDisplayComponent;
  @ViewChild('testRunTable') testRunTable: TestRunTableComponent;

  private readonly testService = inject(TestManagementService);
  private readonly dialog = inject(MatDialog);
  private readonly snackBar = inject(SnackbarService);
  private activatedRoute = inject(ActivatedRoute);
  testResultsLink = '';
  testRunLoading = false;

  assistantKey: AssistantKey;

  private assistantKey$: Observable<AssistantKey> = this.activatedRoute.paramMap.pipe(
    map((paramMap) => {
      const assistantId = paramMap.get('assistantId');
      const namespaceId = paramMap.get('namespaceId');
      const namespaceType = paramMap.get('namespaceType');
      return new AssistantKey({
        id: assistantId || 'ASSISTANT-system',
        namespace: new Namespace({
          globalNamespace: namespaceType === 'GLOBAL' ? {} : undefined,
          systemNamespace: namespaceType === 'SYSTEM' ? {} : undefined,
          partnerNamespace: namespaceType === 'PARTNER' ? { partnerId: namespaceId } : undefined,
          accountGroupNamespace: namespaceType === 'ACCOUNT_GROUP' ? { accountGroupId: namespaceId } : undefined,
        }),
      });
    }),
  );

  ngOnInit(): void {
    this.assistantKey$.subscribe((assistantKey) => {
      this.assistantKey = assistantKey;
    });
  }

  openUploadDialog() {
    this.assistantKey$.subscribe((assistantKey) => {
      console.log(assistantKey);
    });
    this.dialog
      .open(TestUploadModalComponent, {
        width: '800px',
        disableClose: true,
      })
      .afterClosed()
      .pipe(
        switchMap((result: TestUploadModalResult) => {
          if (result?.cases?.length > 0) {
            const upsertRequest = this.testService.upsertTestCases(this.assistantKey, result.cases);

            if (result.removeExisting) {
              return this.testService.deleteTestCases(this.assistantKey).pipe(switchMap(() => upsertRequest));
            }
            return upsertRequest;
          }
          return EMPTY;
        }),
      )
      .subscribe({
        error: (err) => {
          const message = err.error?.message ? err.error.message : 'An error occurred while uploading the test cases.';
          this.snackBar.openErrorSnack(message, { duration: snackbarDuration });
        },
        next: () => {
          this.snackBar.openSuccessSnack('Tests Saved', { duration: snackbarDuration });
          this.table.dataSource.clearSelection();
        },
      });
  }

  runTests() {
    this.testRunLoading = true;
    this.testService.runTests(this.assistantKey).subscribe({
      error: (err) => {
        const message = err.error?.message ? err.error.message : 'An error occurred while attempting to run tests';
        this.snackBar.openErrorSnack(message, { duration: snackbarDuration });
      },
      next: (response) => {
        this.snackBar.openSuccessSnack('Test run started', { duration: snackbarDuration });
        this.testResultsLink = response?.buildUrl;
        this.testRunLoading = false;
        this.testRunTable.dataSource.clearSelection();
      },
    });
  }

  downloadCSVTemplate(): void {
    const headers = ['name', 'role_1', 'content_1', 'role_2', 'content_2', 'role_3', 'content_3', 'expected'];

    // Sample empty row
    const rows = [
      headers,
      [
        'Delete a Task in Task Manager',
        'user',
        'How do I delete a task in Task Manager?',
        '',
        '',
        '',
        '',
        'Should instruct the user to navigate to the tasks tab. Click on the three dots next to the task. Hit the delete button',
      ],
    ];

    const csvContent = rows.map((row) => row.join(',')).join('\r\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'TestCaseTemplate.csv';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
  }
}
