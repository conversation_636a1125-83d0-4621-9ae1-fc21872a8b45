import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  GalaxyColumnDef,
  GalaxyDataSource,
  GalaxyTableModule,
  PagedListRequestInterface,
  PagedResponseInterface,
} from '@vendasta/galaxy/table';
import { MatTableModule } from '@angular/material/table';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { TestManagementService } from '../test-management.service';
import { AssistantKey, ListTestRunsByAssistantResponse, TestRun } from '@vendasta/ai-assistants';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';

export interface TestStatus {
  name: string;
  icon: string;
  class: string;
}

export const StatusMap = new Map<string, TestStatus>([
  ['success', { name: 'Success', icon: 'check_circle', class: 'success' }],
  ['failure', { name: 'Failure', icon: 'error', class: 'failure' }],
  ['in_progress', { name: 'In progress', icon: 'pending', class: 'running' }],
]);

@Component({
  standalone: true,
  selector: 'app-test-run-table',
  imports: [
    CommonModule,
    MatCardModule,
    MatListModule,
    MatButtonModule,
    MatIconModule,
    GalaxyTableModule,
    MatTableModule,
    MatSortModule,
  ],
  templateUrl: './test-run-table.component.html',
  styleUrl: './test-run-table.component.scss',
})
export class TestRunTableComponent {
  private readonly testService = inject(TestManagementService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  @Input() assistantKey: AssistantKey;
  protected statusMap = StatusMap;

  dataSource: GalaxyDataSource<TestRun, void, MatSort> = {} as GalaxyDataSource<TestRun, void, MatSort>;
  columns: GalaxyColumnDef[] = [
    {
      id: 'created',
      title: 'Created',
      sortable: true,
    },
    {
      id: 'status',
      title: 'Status',
    },
    {
      id: 'passed',
      title: 'Passed',
    },
  ];

  refresh(): void {
    this.dataSource.clearSelection();
  }

  sortData(sort: Sort): void {
    this.dataSource?.setSorting([
      {
        active: sort.active,
        direction: sort.direction,
      } as MatSort,
    ]);
  }

  getPassedCount(testRun: TestRun): string {
    const passed = testRun.testResults.filter((result) => result.status === 'success').length;
    return `${passed} / ${testRun.testResults.length}`;
  }

  ngOnInit(): void {
    this.dataSource = new GalaxyDataSource<TestRun, void, MatSort>(this);
    this.dataSource.setSorting([
      {
        active: 'created',
        direction: 'desc',
      } as MatSort,
    ]);
  }

  get(r: PagedListRequestInterface<void, MatSort>): Observable<PagedResponseInterface<TestRun>> {
    return this.testService.listTestRunsByAssistant(this.assistantKey, r.pagingOptions).pipe(
      map((resp: ListTestRunsByAssistantResponse) => {
        const data = resp?.testRuns || [];
        if (r.sorting?.length > 0) {
          const sort = r.sorting[0] as MatSort;
          data.sort((a, b) => {
            return sort.direction === 'asc'
              ? a.created.getTime() - b.created.getTime()
              : b.created.getTime() - a.created.getTime();
          });
        }
        return {
          data: data,
          pagingMetadata: resp.pagingMetadata,
        };
      }),
    );
  }

  navigateToTestRun(testRun: TestRun): void {
    this.router.navigate([`./${testRun.id}`], { relativeTo: this.route });
  }
}
