<mat-card>
  <mat-card-header>
    <mat-card-title>
      <div class="title">
        Test Run History
        <button color="primary" (click)="refresh()" mat-icon-button><mat-icon>refresh</mat-icon></button>
      </div>
    </mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <glxy-table-container
      class="simple-table"
      [dataSource]="dataSource"
      [columns]="columns"
      [pageSizeOptions]="[10, 20, 30]"
      [pageSize]="10"
      [border]="false"
      [fullWidth]="false"
    >
      <glxy-table-content-header
        showFilters="false"
        showFiltersButton="false"
        showSort="false"
        showSearch="false"
        showColumnArrange="false"
      ></glxy-table-content-header>
      <table mat-table matSort (matSortChange)="sortData($event)">
        <tr mat-header-row *matHeaderRowDef="[]"></tr>

        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <glxy-table-selection />
          </th>
          <td mat-cell *matCellDef="let row">
            <glxy-table-selection [row]="row" />
          </td>
        </ng-container>

        <ng-container matColumnDef="created">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
          <td mat-cell *matCellDef="let element">
            <a (click)="navigateToTestRun(element)">{{ element.created | date: 'medium' }}</a>
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let element">
            @if (statusMap.get(element.status); as status) {
              <div class="status" [ngClass]="status.class">
                <mat-icon>{{ status.icon }}</mat-icon
                >{{ status.name }}
              </div>
            }
          </td>
        </ng-container>

        <ng-container matColumnDef="passed">
          <th mat-header-cell *matHeaderCellDef>Tests Passed</th>
          <td mat-cell *matCellDef="let element">
            {{ getPassedCount(element) }}
          </td>
        </ng-container>

        <tr mat-row *matRowDef="let row; columns: []"></tr>
      </table>
    </glxy-table-container>
  </mat-card-content>
</mat-card>
