@if (testResult) {
  <h2 mat-dialog-title class="title">
    {{ testResult.name }}
    @if (statusMap.get(testResult.status); as status) {
      <div class="status" [ngClass]="status.class">
        <mat-icon>{{ status.icon }}</mat-icon
        >{{ status.name }}
      </div>
    }
  </h2>
  <mat-dialog-content>
    @for (message of testResult.chatHistory; track message; let even = $even) {
      <glxy-chat-message-group [type]="even ? 'sent' : 'received'" [messageFrom]="getRole(message.role)">
        <glxy-chat-message [messageText]="message.content"></glxy-chat-message>
      </glxy-chat-message-group>
    }
    <mat-divider></mat-divider>
    <div class="evaluation">
      <h3>Expectation</h3>
      <p>{{ testResult.expected }}</p>

      <h3>Evaluation</h3>
      <p>{{ testResult.evaluation }}</p>
    </div>
  </mat-dialog-content>
}
