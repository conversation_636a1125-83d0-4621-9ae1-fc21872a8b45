import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable } from 'rxjs';
import { AssistantKey, Namespace, TestResult, TestRun } from '@vendasta/ai-assistants';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import {
  GalaxyColumnDef,
  GalaxyDataSource,
  GalaxyTableModule,
  PagedListRequestInterface,
  PagedResponseInterface,
  PagedResponseMetadata,
} from '@vendasta/galaxy/table';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TestManagementService } from '../test-management.service';
import { StatusMap } from '../test-run-table/test-run-table.component';
import { MatDialog } from '@angular/material/dialog';
import { TestResultsModalComponent } from '../test-results-modal/test-results-modal.component';

@Component({
  selector: 'app-test-run-display',
  standalone: true,
  imports: [
    CommonModule,
    GalaxyPageModule,
    MatCardModule,
    MatListModule,
    MatButtonModule,
    MatIconModule,
    GalaxyTableModule,
    MatTableModule,
    MatSortModule,
  ],
  templateUrl: './test-run-display.component.html',
  styleUrl: './test-run-display.component.scss',
})
export class TestRunDisplayComponent {
  private readonly testService = inject(TestManagementService);
  private activatedRoute = inject(ActivatedRoute);
  statusMap = StatusMap;
  private readonly dialog = inject(MatDialog);

  private testRunKey$: Observable<[AssistantKey, string]> = this.activatedRoute.paramMap.pipe(
    map((paramMap) => {
      const assistantId = paramMap.get('assistantId');
      const namespaceId = paramMap.get('namespaceId');
      const namespaceType = paramMap.get('namespaceType');
      const testRunId = paramMap.get('testRunId');
      return [
        new AssistantKey({
          id: assistantId || 'ASSISTANT-system',
          namespace: new Namespace({
            globalNamespace: namespaceType === 'GLOBAL' ? {} : undefined,
            systemNamespace: namespaceType === 'SYSTEM' ? {} : undefined,
            partnerNamespace: namespaceType === 'PARTNER' ? { partnerId: namespaceId } : undefined,
            accountGroupNamespace: namespaceType === 'ACCOUNT_GROUP' ? { accountGroupId: namespaceId } : undefined,
          }),
        }),
        testRunId,
      ];
    }),
  );

  protected testRun$ = this.testRunKey$.pipe(
    switchMap(([assistantKey, testRunId]) => this.testService.getTestRun(assistantKey, testRunId)),
    shareReplay(1),
  );

  dataSource: GalaxyDataSource<TestResult, void, MatSort> = {} as GalaxyDataSource<TestResult, void, MatSort>;
  columns: GalaxyColumnDef[] = [
    {
      id: 'name',
      title: 'Name',
      sortable: true,
    },
    {
      id: 'status',
      title: 'Status',
    },
    {
      id: 'score',
      title: 'Score',
      sortable: true,
    },
  ];

  sortData(sort: Sort): void {
    this.dataSource?.setSorting([
      {
        active: sort.active,
        direction: sort.direction,
      } as MatSort,
    ]);
  }

  ngOnInit(): void {
    this.dataSource = new GalaxyDataSource<TestResult, void, MatSort>(this);
    this.dataSource.setSorting([
      {
        active: 'name',
        direction: 'asc',
      } as MatSort,
    ]);
  }

  get(r: PagedListRequestInterface<void, MatSort>): Observable<PagedResponseInterface<TestResult>> {
    return this.testRun$.pipe(
      map((resp: TestRun) => {
        let data = resp?.testResults || [];
        const startIndex = +r.pagingOptions.cursor || 0;

        if (r.searchOptions.text) {
          data = data.filter((testResult) => {
            const searchText = r.searchOptions.text.toLowerCase();
            const nameMatch = testResult.name.toLowerCase().includes(searchText);
            const expectationMatch = testResult.expected.toLowerCase().includes(searchText);
            const chatHistoryMatch = testResult.chatHistory.some((msg) =>
              msg.content.toLowerCase().includes(searchText),
            );
            return nameMatch || expectationMatch || chatHistoryMatch;
          });
        }
        if (r.sorting?.length > 0) {
          const sort = r.sorting[0] as MatSort;
          data.sort((a, b) => {
            if (sort.active === 'score') {
              const aScore = a.score || 0;
              const bScore = b.score || 0;
              return sort.direction === 'asc' ? aScore - bScore : bScore - aScore;
            }
            const aValue = a[sort.active];
            const bValue = b[sort.active];
            return sort.direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
          });
        }
        const [paginatedData, nextPage] = paginate(data, r.pagingOptions.pageSize || 10, startIndex);

        return {
          data: paginatedData,
          pagingMetadata: nextPage,
        };
      }),
    );
  }

  openDialog(testResult: TestResult): void {
    this.dialog.open(TestResultsModalComponent, { data: testResult });
  }
}

function paginate(data: TestResult[], pageSize: number, cursor: number): [TestResult[], PagedResponseMetadata] {
  const startIndex = cursor || 0;
  const endIndex = startIndex + pageSize;
  const paginatedData = data.slice(startIndex, endIndex);

  return [
    paginatedData,
    {
      nextCursor: endIndex < data.length ? endIndex.toString() : '',
      hasMore: endIndex < data.length,
      totalResults: data.length,
    },
  ];
}
