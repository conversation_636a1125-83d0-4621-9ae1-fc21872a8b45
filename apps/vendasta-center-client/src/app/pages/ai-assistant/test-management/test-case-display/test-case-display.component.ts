import { Component, inject, Input } from '@angular/core';
import {
  Assistant<PERSON>ey,
  ChatMessage,
  ChatMessageRole,
  ListTestCasesByAssistantResponse,
  TestCase,
} from '@vendasta/ai-assistants';
import { Observable, of } from 'rxjs';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import {
  GalaxyColumnDef,
  GalaxyDataSource,
  GalaxyTableModule,
  PagedListRequestInterface,
  PagedResponseInterface,
} from '@vendasta/galaxy/table';
import { map, switchMap } from 'rxjs/operators';
import { MatTableModule } from '@angular/material/table';
import { snackbarDuration } from '../test-management.component';
import { TestCaseEditModalComponent } from '../test-case-edit-modal.ts/test-case-edit-modal.component';
import { MatDialog } from '@angular/material/dialog';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { TestManagementService } from '../test-management.service';

@Component({
  selector: 'app-test-case-display',
  imports: [
    CommonModule,
    MatCardModule,
    MatListModule,
    MatButtonModule,
    MatIconModule,
    GalaxyTableModule,
    MatTableModule,
    MatSortModule,
  ],
  standalone: true,
  templateUrl: './test-case-display.component.html',
  styleUrl: './test-case-display.component.scss',
})
export class TestCaseDisplayComponent {
  private readonly testService = inject(TestManagementService);
  private readonly dialog = inject(MatDialog);
  private readonly snackbar = inject(SnackbarService);
  private readonly confirmationModal = inject(OpenConfirmationModalService);
  @Input() assistantKey: AssistantKey;

  dataSource: GalaxyDataSource<TestCase, void, MatSort> = {} as GalaxyDataSource<TestCase, void, MatSort>;
  columns: GalaxyColumnDef[] = [
    {
      id: 'name',
      title: 'Name',
      sortable: true,
    },
    {
      id: 'prompt',
      title: 'Prompt',
    },
    {
      id: 'expectation',
      title: 'Expectation',
    },
    {
      id: 'edit',
      title: '',
    },
    {
      id: 'delete',
      title: '',
    },
  ];

  sortData(sort: Sort): void {
    this.dataSource?.setSorting([
      {
        active: sort.active,
        direction: sort.direction,
      } as MatSort,
    ]);
  }

  ngOnInit(): void {
    this.dataSource = new GalaxyDataSource<TestCase, void, MatSort>(this);
    this.dataSource.setSorting([
      {
        active: 'name',
        direction: 'asc',
      } as MatSort,
    ]);
  }

  editTestCase(testCase: TestCase): void {
    if (!testCase) {
      testCase = new TestCase({
        id: '',
        name: '',
        expectation: '',
        chatHistory: [
          new ChatMessage({
            role: ChatMessageRole.CHAT_MESSAGE_ROLE_USER,
            content: '',
          }),
        ],
      });
    }

    this.dialog
      .open(TestCaseEditModalComponent, {
        data: {
          testCase: testCase,
        },
      })
      .afterClosed()
      .pipe(
        switchMap((result: TestCase) => {
          if (result) {
            return this.testService.upsertTestCases(this.assistantKey, [result]);
          }
          return of(null);
        }),
      )
      .subscribe({
        next: (response) => {
          if (response) {
            this.snackbar.openSuccessSnack('Test case saved', { duration: snackbarDuration });
            this.dataSource.clearSelection();
          }
        },
        error: (error) => {
          const message = error.error?.message ? error.error.message : 'An error occurred while saving the test case.';
          this.snackbar.openErrorSnack(message, { duration: snackbarDuration });
        },
      });
  }

  deleteTestCase(testCase: TestCase): void {
    this.confirmationModal
      .openModal({
        title: 'Delete Test Case',
        message: 'Are you sure you want to delete this test case?',
        confirmButtonText: 'Delete',
        type: 'warn',
      })
      .pipe(
        switchMap((result: boolean) => {
          if (result) {
            return this.testService.deleteTestCases(this.assistantKey, [testCase.id]);
          }
          return of(null);
        }),
      )
      .subscribe({
        next: (response) => {
          if (response) {
            this.snackbar.openSuccessSnack('Test case deleted', { duration: snackbarDuration });
            this.dataSource.clearSelection();
          }
        },
        error: (error) => {
          const message = error.error?.message
            ? error.error.message
            : 'An error occurred while deleting the test case.';
          this.snackbar.openErrorSnack(message, { duration: snackbarDuration });
        },
      });
  }

  get(r: PagedListRequestInterface<void, MatSort>): Observable<PagedResponseInterface<TestCase>> {
    return this.testService.listTestCasesByAssistant(this.assistantKey, r.pagingOptions).pipe(
      map((resp: ListTestCasesByAssistantResponse) => {
        let data = resp.testCases || [];
        if (r.searchOptions.text) {
          data = data.filter((testCase) => {
            const searchText = r.searchOptions.text.toLowerCase();
            const nameMatch = testCase.name.toLowerCase().includes(searchText);
            const expectationMatch = testCase.expectation.toLowerCase().includes(searchText);
            const chatHistoryMatch = testCase.chatHistory.some((msg) => msg.content.toLowerCase().includes(searchText));
            return nameMatch || expectationMatch || chatHistoryMatch;
          });
        }
        if (r.sorting?.length > 0) {
          const sort = r.sorting[0] as MatSort;
          data.sort((a, b) => {
            const aValue = a[sort.active];
            const bValue = b[sort.active];
            return sort.direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
          });
        }
        return {
          data: data,
          pagingMetadata: resp.pagingMetadata,
        };
      }),
    );
  }

  protected readonly TestCase = TestCase;
}
