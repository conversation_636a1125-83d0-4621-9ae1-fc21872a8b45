import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  AssistantKey,
  GetTestRunRequest,
  IntegrationTestApiService,
  ListTestCasesByAssistantResponse,
  ListTestRunsByAssistantRequest,
  ListTestRunsByAssistantResponse,
  RunTestsRequest,
  RunTestsResponse,
  TestCase,
  TestRun,
  UpsertTestCasesRequest,
} from '@vendasta/ai-assistants';
import { PagedRequestOptionsInterface } from '@vendasta/galaxy/table';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TestManagementService {
  testService = inject(IntegrationTestApiService);

  upsertTestCases(assistant<PERSON>ey: Assistant<PERSON><PERSON>, cases: TestCase[]): Observable<any> {
    return this.testService.upsertTestCases(
      new UpsertTestCasesRequest({
        assistant: assistant<PERSON><PERSON>,
        testCases: cases,
      }),
    );
  }

  deleteTestCases(assistant<PERSON>ey: Assistant<PERSON><PERSON>, testCaseIDs?: string[]): Observable<any> {
    return this.testService.deleteTestCases({
      assistant: assistant<PERSON><PERSON>,
      testCaseIds: testCaseIDs,
    });
  }

  runTests(assistantKey: AssistantKey): Observable<RunTestsResponse> {
    return this.testService.runTests(
      new RunTestsRequest({
        assistant: assistantKey,
      }),
    );
  }

  listTestCasesByAssistant(
    assistantKey: AssistantKey,
    pagingOptions: PagedRequestOptionsInterface,
  ): Observable<ListTestCasesByAssistantResponse> {
    return this.testService.listTestCasesByAssistant({
      assistant: assistantKey,
      pagingOptions: pagingOptions,
    });
  }

  listTestRunsByAssistant(
    assistantKey: AssistantKey,
    pagingOptions: PagedRequestOptionsInterface,
  ): Observable<ListTestRunsByAssistantResponse> {
    return this.testService.listTestRunsByAssistant(
      new ListTestRunsByAssistantRequest({
        assistant: assistantKey,
        pagingOptions: pagingOptions,
      }),
    );
  }

  getTestRun(assistantKey: AssistantKey, testRunId: string): Observable<TestRun> {
    return this.testService
      .getTestRun(
        new GetTestRunRequest({
          assistant: assistantKey,
          id: testRunId,
        }),
      )
      .pipe(map((response) => response?.testRun));
  }
}
