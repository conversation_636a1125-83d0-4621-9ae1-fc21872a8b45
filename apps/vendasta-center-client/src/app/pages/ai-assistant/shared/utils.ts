import { NamespaceInterface } from '@vendasta/ai-assistants';

export enum NamespaceType {
  ALL = 'ALL',
  GLOBAL = 'GLOBAL',
  SYSTEM = 'SYSTEM',
  ACCOUNT_GROUP = 'ACCOUNT_GROUP',
  PARTNER = 'PARTNER',
}

export const NamespaceUtils = {
  getName(namespace: NamespaceInterface | null | undefined): string {
    if (!namespace) {
      return 'Unknown';
    }

    const namespaceMap = {
      globalNamespace: 'Global',
      systemNamespace: 'System',
      accountGroupsForGroupNamespace: 'Account groups for group',
      accountGroupNamespace: 'Account Group',
      partnerNamespace: 'Partner',
      accountGroupsForPartnerNamespace: 'Account groups for partner',
    };

    // Find the first defined namespace property and return its corresponding name
    const key = Object.keys(namespaceMap).find((key) => namespace[key]);
    return key ? namespaceMap[key] : 'Unknown';
  },

  getId(namespace: NamespaceInterface | null | undefined): string | null {
    if (!namespace) {
      return null;
    }

    if (namespace.accountGroupNamespace) {
      return namespace.accountGroupNamespace.accountGroupId || null;
    }

    if (namespace.partnerNamespace) {
      return namespace.partnerNamespace.partnerId || null;
    }

    return null;
  },

  getType(namespace: NamespaceInterface | null | undefined): NamespaceType {
    if (!namespace) {
      return NamespaceType.ALL;
    }

    if (namespace.globalNamespace) {
      return NamespaceType.GLOBAL;
    }

    if (namespace.systemNamespace) {
      return NamespaceType.SYSTEM;
    }

    if (namespace.accountGroupNamespace) {
      return NamespaceType.ACCOUNT_GROUP;
    }

    if (namespace.partnerNamespace) {
      return NamespaceType.PARTNER;
    }

    return NamespaceType.ALL;
  },
};
