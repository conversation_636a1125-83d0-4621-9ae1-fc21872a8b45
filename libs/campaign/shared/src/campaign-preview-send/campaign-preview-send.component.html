<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <div class="header">
      <glxy-page-title> {{ 'CAMPAIGN_PREVIEW_MODAL.TITLE' | translate }}</glxy-page-title>
      <glxy-page-title-actions>
        <button mat-stroked-button (click)="onCancel()">{{ 'CAMPAIGN_PREVIEW_MODAL.CANCEL' | translate }}</button>
        <button mat-flat-button color="primary" (click)="onSend()" [disabled]="sendingInProgress$ | async">
          @if ((sendingInProgress$ | async) === true) {
            <glxy-loading-spinner size="small"></glxy-loading-spinner>
          } @else {
            {{ 'CAMPAIGN_PREVIEW_MODAL.SEND' | translate }}
          }
        </button>
      </glxy-page-title-actions>
    </div>
  </glxy-page-toolbar>

  @if (!previewSendIsMobile) {
    <div class="preview-wrapper">
      <ng-container *ngTemplateOutlet="campaignPreview"></ng-container>
      <div class="send-options">
        <ng-container *ngTemplateOutlet="sendingOptions"></ng-container>
      </div>
    </div>
  } @else {
    <mat-tab-group mat-stretch-tabs="true" mat-align-tabs="center" preserveContent="true">
      <mat-tab label="{{ 'CAMPAIGN_PREVIEW_MODAL.TABS.SENDING_OPTIONS' | translate }}">
        <ng-template matTabContent>
          <div class="send-options-mobile">
            <ng-container *ngTemplateOutlet="sendingOptions"></ng-container>
          </div>
        </ng-template>
      </mat-tab>
      <mat-tab label="{{ 'CAMPAIGN_PREVIEW_MODAL.TABS.PREVIEW' | translate }}">
        <ng-template matTabContent>
          <ng-container *ngTemplateOutlet="campaignPreview"></ng-container>
        </ng-template>
      </mat-tab>
    </mat-tab-group>
  }

  <!--Preview-->
  <ng-template #campaignPreview>
    <campaign-step-previewer class="campaign-preview"></campaign-step-previewer>
  </ng-template>

  <!--Sending Options-->
  <ng-template #sendingOptions>
    <div class="send-schedule">
      <mat-button-toggle-group size="small" [(ngModel)]="sendOption">
        <mat-button-toggle value="send-now">
          <mat-icon inline="true">send</mat-icon>
          {{ 'CAMPAIGN_PREVIEW_MODAL.SEND_NOW' | translate }}
          <mat-icon matTooltip="{{ 'CAMPAIGN_PREVIEW_MODAL.SEND_NOW_TOOLTIP' | translate }}" inline="true"
            >info</mat-icon
          >
        </mat-button-toggle>
        <mat-button-toggle value="send-later">
          <mat-icon inline="true">schedule_send</mat-icon>
          {{ 'CAMPAIGN_PREVIEW_MODAL.SCHEDULE_SEND' | translate }}
        </mat-button-toggle>
      </mat-button-toggle-group>
    </div>
    <div *ngIf="sendOption === 'send-later'" class="schedule-date-picker">
      <form [formGroup]="formGroup">
        <glxy-form-field class="datepicker">
          <input formControlName="dateFormControl" type="datetime-local" matInput [max]="maxDate" />
          @if (formGroup.get('dateFormControl')?.hasError('maxDateExceeded')) {
            <mat-error>{{ 'CAMPAIGN_PREVIEW_MODAL.ERRORS.MAX_DATE_EXCEEDED_ERROR' | translate }}</mat-error>
          }
          <mat-datepicker></mat-datepicker>
        </glxy-form-field>
      </form>
    </div>
    @if ((disableCampaignSelection$ | async) === false) {
      <campaign-selector (selectedCampaign)="campaignSelected($event)"></campaign-selector>
    }
    @if (!useSelectAll) {
      <glxy-form-field class="recipient-chip-form-field">
        <glxy-label> {{ 'CAMPAIGN_PREVIEW_MODAL.SELECT_RECIPIENTS' | translate }} </glxy-label>
        <mat-chip-grid #recipientChips aria-label="Search contacts">
          <div class="recipient-chip-wrapper">
            <div class="mat-icon-wrapper">
              <mat-icon class="contact-icon">person</mat-icon>
            </div>
            <div class="chip-row-wrapper">
              <mat-chip-row *ngFor="let contact of contacts?.slice(0, 5)" (removed)="removeRecipient(contact)">
                <span class="chip-container"
                  ><mat-icon *ngIf="contact.error" class="warning-icon">warning</mat-icon> {{ contact.name }}</span
                >
                <button matChipRemove [attr.aria-label]="'remove ' + contact">
                  <mat-icon>cancel</mat-icon>
                </button>
              </mat-chip-row>

              <ng-container *ngIf="showMoreChips">
                <mat-chip-row *ngFor="let contact of contacts?.slice(5)" (removed)="removeRecipient(contact)">
                  <span class="chip-container"
                    ><mat-icon *ngIf="contact.error" class="warning-icon">warning</mat-icon> {{ contact.name }}</span
                  >
                  <button matChipRemove [attr.aria-label]="'remove ' + contact">
                    <mat-icon>cancel</mat-icon>
                  </button>
                </mat-chip-row>
              </ng-container>
              <mat-chip *ngIf="contacts?.length > 5" (click)="showMoreChips = !showMoreChips" class="show-more-chip">
                @if (showMoreChips) {
                  {{ 'CAMPAIGN_PREVIEW_MODAL.SHOW_LESS' | translate }}
                } @else {
                  {{ 'CAMPAIGN_PREVIEW_MODAL.SHOW_MORE' | translate: { recipient_count: contacts?.length - 5 } }}
                }
              </mat-chip>
              <div class="select-recipients-text" (click)="openContactModal()">
                {{ 'CAMPAIGN_PREVIEW_MODAL.SEARCH_CONTACTS' | translate }}
                <input [matChipInputFor]="recipientChips" [matChipInputAddOnBlur]="true" disabled />
              </div>
            </div>
          </div>
        </mat-chip-grid>
        <glxy-hint>
          {{
            'CAMPAIGN_PREVIEW_MODAL.ESTIMATED_RECIPIENTS'
              | translate
                : {
                    estimated: estimatedRecipients,
                    total: contacts?.length,
                  }
          }}
          <mat-icon
            matTooltip="{{ 'CAMPAIGN_PREVIEW_MODAL.ESTIMATED_RECIPIENTS_INFO' | translate }}"
            class="info-icon est-recipients-tooltip"
            >info_outline</mat-icon
          >
        </glxy-hint>
      </glxy-form-field>
    } @else {
      <div class="recipient-estimate">
        {{
          'CAMPAIGN_PREVIEW_MODAL.SELECT_ALL_ESTIMATED_RECIPIENTS'
            | translate: { estimated: previewModalSendAll ? totalContacts : totalContacts }
        }}
      </div>
    }

    <glxy-alert
      *ngIf="showErrorMessage"
      type="warning"
      [showAction]="false"
      [showClose]="false"
      [extendedBorder]="true"
      class="error-messages"
    >
      <strong>{{ 'CAMPAIGNS.CONTACTS.MODAL.ERRORS.FAILED_CAMPAIGN_ALERT_TITLE' | translate }}</strong>
      <glxy-alert-extended>
        <ul>
          <li *ngFor="let error of errorMessagesToDisplay" class="error-message">{{ error | translate }}</li>
        </ul>
      </glxy-alert-extended>
    </glxy-alert>

    <glxy-form-field>
      <glxy-label>{{ 'CAMPAIGN_PREVIEW_MODAL.SEND_FROM' | translate }}</glxy-label>
      <mat-select [formControl]="sendFromControl">
        <mat-option *ngFor="let opt of getFilteredSendFromOptions((config.sender$ | async).type)" [value]="opt.value">{{
          opt.label
        }}</mat-option>
      </mat-select>
    </glxy-form-field>

    <div *ngIf="sendFromControl.value === custom">
      <glxy-form-field>
        <glxy-label>{{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.SENDER_NAME' | translate }}</glxy-label>
        <input type="text" [formControl]="senderName" required matInput />
        <glxy-error *ngIf="senderName.touched && !senderName.value">
          {{ getErrorMessage(senderName) }}
        </glxy-error>
        <glxy-hint>{{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.SENDER_NAME_HINT' | translate }}</glxy-hint>
      </glxy-form-field>
      <glxy-form-field appearance="outline" [suffixText]="'@' + (senderInfoDomain$ | async)">
        <glxy-label>{{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.SENDER_ADDRESS' | translate }}</glxy-label>
        <input type="email" [formControl]="email" required matInput />
        <glxy-error *ngIf="email.invalid && email.touched">
          {{ getErrorMessage(email) }}
        </glxy-error>
        <glxy-hint>{{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.SENDER_ADDRESS_HINT' | translate }}</glxy-hint>
      </glxy-form-field>
      <glxy-form-field>
        <glxy-label>{{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.REPLY_ADDRESS' | translate }}</glxy-label>
        <input type="email" [formControl]="replyTo" matInput />
        <glxy-error *ngIf="replyTo.invalid && replyTo.touched">
          {{ getErrorMessage(replyTo) }}
        </glxy-error>
        <glxy-hint>{{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.REPLY_ADDRESS_HINT' | translate }}</glxy-hint>
      </glxy-form-field>
    </div>

    <div *ngIf="sendFromControl.value === assignedSalespersonValue">
      <glxy-alert type="warning" [extendedBorder]="true">
        <strong>{{ 'CAMPAIGN_PREVIEW_MODAL.ASSIGNED_SALESPERSON.TITLE' | translate }}</strong>
        <glxy-alert-extended>
          {{ 'CAMPAIGN_PREVIEW_MODAL.ASSIGNED_SALESPERSON.DESCRIPTION' | translate }}
          <br />
          {{ 'CAMPAIGN_PREVIEW_MODAL.ASSIGNED_SALESPERSON.DESCRIPTION_2' | translate }}
        </glxy-alert-extended>
      </glxy-alert>
    </div>

    <campaign-sender-settings
      *ngIf="sendFromControl.value === default"
      [sender]="config.sender$ | async"
    ></campaign-sender-settings>
  </ng-template>
</glxy-page>
