import { Component } from '@angular/core';
import { GalaxyEmailViewerModule } from '@vendasta/galaxy/email-viewer';
import { CampaignStepSelectorComponent } from './campaign-step-selector/campaign-step-selector.component';
import { CampaignPreviewerService, PreviewStepData } from '../campaign-previewer.service';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';
import { CampaignStepType } from '@vendasta/campaigns';
import { ChatMessageComponent } from '@vendasta/galaxy/chat';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { NonEmailCampaignSteps } from '../non-email-steps';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';

@Component({
  selector: 'campaign-step-previewer',
  templateUrl: 'campaign-step-previewer.component.html',
  styleUrls: ['campaign-step-previewer.component.scss'],
  imports: [
    CommonModule,
    GalaxyEmailViewerModule,
    CampaignStepSelectorComponent,
    ChatMessageComponent,
    GalaxyEmptyStateModule,
    MatIconModule,
    TranslateModule,
    GalaxyLoadingSpinnerModule,
  ],
  providers: [],
})
export class CampaignStepPreviewerComponent {
  stepTypes = CampaignStepType;
  protected readonly snapshotCampaignStepPreviewData = NonEmailCampaignSteps['snapshot-creation'];
  displayData$: Observable<PreviewStepData> = this.previewerService.getSelectedStepData();
  protected readonly displayDataLoading = this.previewerService.stepDataLoading;

  constructor(private previewerService: CampaignPreviewerService) {}
}
