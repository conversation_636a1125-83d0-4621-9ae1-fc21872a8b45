import { Inject, Injectable, signal } from '@angular/core';
import { BehaviorSubject, combineLatest, iif, Observable, of } from 'rxjs';
import {
  CampaignService,
  CampaignStepType,
  GetterCampaignDataInterface,
  SenderInterface,
  SenderType,
} from '@vendasta/campaigns';
import { catchError, filter, map, shareReplay, switchMap, tap } from 'rxjs/operators';
import { CampaignStepInterface, GetterCampaignData } from '@vendasta/campaigns';
import { TemplatesService, TemplateType } from '@vendasta/templates';
import { InboxPreviewHtml } from '@galaxy/email-ui/email-builder/src/inbox-preview';
import {
  AccountGroupApiService,
  LookupRequestInterface,
  ProjectionFilter,
  AccountGroup,
  LookupRequestFiltersInterface,
} from '@vendasta/account-group';
import { CONFIG_TOKEN } from './tokens';
import { CampaignConfig } from '@galaxy/campaign/dependencies';
import { SalespersonService } from '@vendasta/sales';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

export interface DisplayRecipient {
  Name?: string;
  Id?: string;
}

export interface PreviewStepData {
  stepType: CampaignStepType;
  html?: string;
  subject?: string;
}

function calculateDaysSince(campaign: CampaignStepInterface[], stepIndex: number): number {
  const stepsSinceLastEmail = campaign.slice(0, stepIndex + 1);
  const days = stepsSinceLastEmail
    .map((step) => secondsToDays(step.secondsAfterLastEmail || 0))
    .reduce((prev, curr) => prev + curr);
  return days + 1;
}

function secondsToDays(seconds: number): number {
  return seconds / 86400;
}

@Injectable({ providedIn: 'root' })
export class CampaignPreviewerService {
  inboxPreviewHtmlToInject = InboxPreviewHtml;
  sender$: Observable<SenderInterface>;

  campaignSchedule$$: BehaviorSubject<CampaignStepInterface[]> = new BehaviorSubject<CampaignStepInterface[]>([]);
  campaignSchedule$: Observable<CampaignStepInterface[]> = this.campaignSchedule$$.asObservable();

  selectedCampaign$$: BehaviorSubject<GetterCampaignDataInterface> = new BehaviorSubject<GetterCampaignDataInterface>(
    null,
  );
  selectedCampaign$: Observable<GetterCampaignDataInterface> = this.selectedCampaign$$.asObservable();

  selectedStepIndex$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  selectedStepIndex$: Observable<number> = this.selectedStepIndex$$.asObservable();

  selectedRecipient$$: BehaviorSubject<DisplayRecipient> = new BehaviorSubject<DisplayRecipient>(null);
  selectedRecipient$: Observable<DisplayRecipient> = this.selectedRecipient$$.asObservable();

  inboxUsername$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  inboxUsername$: Observable<string> = this.inboxUsername$$.asObservable();

  isSalesperson$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  salespersonId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');

  marketId$$: BehaviorSubject<string> = new BehaviorSubject('');

  public stepDataLoading = signal<boolean>(false);

  constructor(
    private campaignService: CampaignService,
    private readonly templates: TemplatesService,
    private readonly accountGroupService: AccountGroupApiService,
    @Inject(CONFIG_TOKEN) private readonly config: CampaignConfig,
    private readonly salespersonService: SalespersonService,
  ) {
    combineLatest([this.config.sender$, this.config.userId$])
      .pipe(
        takeUntilDestroyed(),
        switchMap(([sender, userID]: [SenderInterface, string]) => {
          if (sender.type === SenderType.SENDER_TYPE_PARTNER) {
            return this.salespersonService.getSalespersonByUserId(sender.id, userID);
          } else {
            return of(null);
          }
        }),
        shareReplay(1),
        catchError(() => {
          return of(null);
        }),
      )
      .subscribe((salesperson) => {
        if (salesperson) {
          this.isSalesperson$$.next(true);
          this.marketId$$.next(salesperson.marketId);
          this.salespersonId$$.next(salesperson.salespersonId);
        } else {
          this.isSalesperson$$.next(false);
          this.salespersonId$$.next('');
        }
      });
  }

  getFullCampaignData(): Observable<GetterCampaignData> {
    return this.selectedCampaign$.pipe(
      filter((campaign) => !!campaign),
      switchMap((campaign) => this.campaignService.get(campaign.campaignId)),
      tap((campaign) => this.campaignSchedule$$.next(campaign.campaignSchedule)),
      shareReplay(1),
    );
  }

  getSelectedStepData(): Observable<PreviewStepData> {
    return combineLatest([
      this.sender$,
      this.selectedStepIndex$,
      this.getFullCampaignData(),
      this.selectedRecipient$,
      this.inboxUsername$,
      this.isSalesperson$$,
      this.salespersonId$$,
      this.marketId$$,
    ]).pipe(
      tap(() => this.stepDataLoading.set(true)),
      switchMap(
        ([sender, index, campaign, recipient, inboxUsername, isSalesperson, salespersonID, marketId]: [
          SenderInterface,
          number,
          GetterCampaignData,
          DisplayRecipient,
          string,
          boolean,
          string,
          string,
        ]) => {
          const { ...step } = campaign.campaignSchedule[index];
          if (!step?.stepType) {
            // Some don't have any stepType specified
            step.stepType = CampaignStepType.CAMPAIGN_STEP_TYPE_UNSPECIFIED;
          }
          if (step.stepType === CampaignStepType.CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION) {
            return of({
              stepType: step.stepType,
            } as PreviewStepData);
          }

          if (step.stepType === CampaignStepType.CAMPAIGN_STEP_TYPE_SMS) {
            // TODO: ideally we would use RenderTemplate here, but it has to be fixed to take the substitutions first.
            return this.templates.get('campaigns', step.templateId).pipe(
              switchMap((resp) => {
                return this.templates.render(
                  resp.content,
                  {
                    account_group_id: sender.id,
                    contact_id: recipient?.Id,
                  },
                  TemplateType.TEMPLATE_TYPE_GOLANG_TEXT,
                  {
                    accountGroupId: 'account_group_id',
                    contactId: 'contact_id',
                  },
                  undefined,
                  false,
                  undefined,
                  false,
                );
              }),
              map((resp) => {
                return {
                  stepType: step.stepType,
                  html: resp,
                };
              }),
            );
          }
          return this.handleEmailStep(
            sender,
            step as CampaignStepInterface,
            campaign,
            recipient,
            inboxUsername,
            isSalesperson,
            salespersonID,
            marketId,
          );
        },
      ),
      tap(() => {
        this.stepDataLoading.set(false);
      }),
    );
  }

  handleEmailStep(
    sender: SenderInterface,
    step: CampaignStepInterface,
    campaign: GetterCampaignData,
    recipient: DisplayRecipient,
    inboxUsername: string,
    isSalesperson: boolean,
    salespersonID: string,
    marketID: string,
  ): Observable<PreviewStepData> {
    const stepData = iif(
      () => sender.type === SenderType.SENDER_TYPE_PARTNER,
      this.getBusiness(sender?.id, isSalesperson, salespersonID, marketID).pipe(
        map((resp) => {
          return {
            recipient_external_type: 'RecipientTypeCampaignRecipient',
            recipient_external_id: recipient?.Id ?? sender?.id,
            partner_id: sender?.id,
            account_group_id: resp.accountGroupId,
          };
        }),
      ),
      of({
        account_group_id: sender?.id,
        recipient_external_type: 'RecipientTypeCampaignRecipient',
        recipient_external_id: recipient?.Id ?? sender?.id,
      }),
    );

    return stepData.pipe(
      switchMap((d) => {
        return this.campaignService.getEmailStepContent(campaign.campaignId, step.campaignStepId, d);
      }),
      map((resp) => {
        const customizedInboxPreview = this.inboxPreviewHtmlToInject
          .replace('{{ username }}', inboxUsername)
          .replace('{{ subjectLine }}', resp.subject ?? '')
          .replace('{{ previewText }}', '');
        return {
          stepType: step.stepType || CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL,
          html: customizedInboxPreview + resp.html,
          subject: resp.subject,
        };
      }),
    );
  }

  private getBusiness(
    partnerId: string,
    isSalesperson: boolean,
    salespersonID: string,
    marketId: string,
  ): Observable<AccountGroup> {
    const projectionFilter = new ProjectionFilter({
      napData: true,
      accountGroupExternalIdentifiers: true,
    });
    const lookupRequest: LookupRequestInterface = {
      projectionFilter: projectionFilter,
      filters: this.buildFilters(isSalesperson, salespersonID, marketId, partnerId),
      pageSize: 1,
      sortOptions: {
        direction: 1,
        field: 1,
      },
    };
    return this.accountGroupService
      .lookup(lookupRequest)
      .pipe(
        map((r) => (r.accountGroups?.length ? r.accountGroups[0] : ({ accountGroupId: 'AG-123' } as AccountGroup))),
      );
  }

  goToNextStep(): void {
    const currStep = this.selectedStepIndex$$.getValue();
    const schedule = this.campaignSchedule$$.getValue();
    if (currStep + 1 < schedule.length) {
      this.selectedStepIndex$$.next(currStep + 1);
    }
  }

  goToPreviousStep(): void {
    const currStep = this.selectedStepIndex$$.getValue();
    if (currStep - 1 > -1) {
      this.selectedStepIndex$$.next(currStep - 1);
    }
  }

  getStepDay(): Observable<number> {
    return combineLatest([this.selectedStepIndex$, this.campaignSchedule$]).pipe(
      filter(([_, schedule]) => !!schedule && schedule.length > 0),
      map(([index, schedule]) => {
        return calculateDaysSince(schedule, index);
      }),
    );
  }

  getStepNumber(): Observable<number> {
    return this.selectedStepIndex$.pipe(map((index) => index + 1));
  }

  getTotalSteps(): Observable<number> {
    return this.campaignSchedule$.pipe(map((schedule) => schedule.length));
  }

  setSender(sender: Observable<SenderInterface>): void {
    this.sender$ = sender;
  }

  setCampaign(campaign: GetterCampaignDataInterface): void {
    this.selectedCampaign$$.next(campaign);
    this.selectedStepIndex$$.next(0);
  }

  setRecipientId(recipient: DisplayRecipient): void {
    this.selectedRecipient$$.next(recipient);
  }

  setPreviewUsername(username: string): void {
    this.inboxUsername$$.next(username);
  }

  buildFilters(
    isSalesperson: boolean,
    salespersonId: string,
    marketId: string,
    partnerId: string,
  ): LookupRequestFiltersInterface {
    if (isSalesperson) {
      return {
        marketIds: [marketId],
        salesPersonId: salespersonId,
        partnerId: partnerId,
        includeDeleted: false,
      } as LookupRequestFiltersInterface;
    }
    return {
      partnerId: partnerId,
      includeDeleted: false,
    } as LookupRequestFiltersInterface;
  }
}
