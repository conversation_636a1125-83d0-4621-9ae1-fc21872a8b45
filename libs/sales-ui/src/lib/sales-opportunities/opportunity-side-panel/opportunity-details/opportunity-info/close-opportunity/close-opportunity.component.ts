import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import { Opportunity, SalesOpportunitiesSdk, SalesOpportunitiesService } from '@vendasta/sales-opportunities';
import { BehaviorSubject, combineLatest, EMPTY, firstValueFrom, Observable } from 'rxjs';
import { shareReplay, switchMap, take } from 'rxjs/operators';
import { ClosedLostReasonStringMapping, CloseState, OpportunityCloseEvent } from '../../../../interface';
import { OrdersUrlFunction } from '../../../../opportunity';
import { OpportunityService } from '../../../../opportunity.service';
import { SALES_UI_OPPORTUNITY_DETAILS_CONFIG_TOKEN, SalesUIOpportunityDetailsConfig } from '../../../../tokens';
import {
  ReopenOpportunityData,
  ReopenOpportunityDialogComponent,
  ReopenOpportunityDialogInputData,
} from '../reopen-opportunity/reopen-opportunity-dialog.component';
import { CloseOpportunityDialogComponent } from './close-opportunity-dialog/close-opportunity-dialog.component';
import { OpportunityOrderService } from './opportunity-order.service';
import { CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';

@Component({
  selector: 'sales-ui-close-opportunity',
  templateUrl: './close-opportunity.component.html',
  styleUrls: ['./close-opportunity.component.scss'],
  standalone: false,
})
export class CloseOpportunityComponent implements OnInit {
  protected opportunity$: Observable<Opportunity>;
  private readonly subscriptions = SubscriptionList.new();
  private readonly opportunityOpen$$ = new BehaviorSubject<boolean>(true);
  private readonly closedWon$$ = new BehaviorSubject<boolean>(true);
  readonly opportunityOpen$: Observable<boolean> = this.opportunityOpen$$.asObservable();
  readonly closedWon$: Observable<boolean> = this.closedWon$$.asObservable();
  protected canCreateOrders$: Observable<boolean>;
  private ordersUrl: OrdersUrlFunction;
  private readonly partnerId$: Observable<string>;
  private readonly marketId$: Observable<string>;

  constructor(
    private readonly dialog: MatDialog,
    private readonly salesOpportunitiesApiService: SalesOpportunitiesService,
    private readonly opportunityUpdates: OpportunityService,
    private readonly snackbarService: SnackbarService,
    private readonly translate: TranslateService,
    private opportunityOrderService: OpportunityOrderService,
    @Inject(SalesOpportunitiesService) private readonly opportunityApi: SalesOpportunitiesSdk,
    @Inject(SALES_UI_OPPORTUNITY_DETAILS_CONFIG_TOKEN) private readonly config: SalesUIOpportunityDetailsConfig,
    @Inject(CrmInjectionToken) private readonly crmConfig: CrmDependencies,
  ) {
    this.opportunity$ = this.opportunityUpdates.currentOpportunity$.pipe(shareReplay(1));
    this.canCreateOrders$ = this.crmConfig.canPartnerAdminCreateOrdersFeatureFlag$;
    this.ordersUrl = this.config.getOrdersUrl;
    this.partnerId$ = this.config.partnerId$;
    this.marketId$ = this.config.marketId$;
  }

  ngOnInit(): void {
    this.subscriptions.add(this.opportunity$, (opp: Opportunity) => {
      this.opportunityOpen$$.next(!opp.isClosed);
      this.closedWon$$.next(opp.pipelineStage === 'closed-won');
    });
  }

  createOrderAction(opp: Opportunity): void {
    this.createOrder(opp);
  }

  createOrder(opp: Opportunity): void {
    if (opp.accountGroupId) {
      combineLatest([this.partnerId$, this.marketId$])
        .pipe(
          take(1),
          switchMap(([partnerId, marketId]: [string, string]) => {
            return this.opportunityOrderService.createOrderAndRedirect(
              partnerId,
              marketId,
              opp,
              (businessId, orderId) => `/order-management/${businessId}/view/${orderId}`,
            );
          }),
        )
        .subscribe();
    } else {
      this.snackbarService.openErrorSnack('SALES_OPPORTUNITIES.ERRORS.GENERIC_ERROR');
    }
  }

  async closeOpportunity(won: boolean): Promise<void> {
    const closeState = won ? CloseState.WON : CloseState.LOST;

    const opportunity = await firstValueFrom(this.opportunity$);

    const dialogRef = this.dialog.open(CloseOpportunityDialogComponent, {
      width: '600px',
      data: { closeState: closeState },
    });

    this.subscriptions.add(
      dialogRef.afterClosed().pipe(
        switchMap((data: OpportunityCloseEvent) => {
          if (!data) {
            return EMPTY;
          }

          const closeState = data.closeState;
          const notes = data?.notes || '';
          if (closeState === CloseState.WON) {
            return this.salesOpportunitiesApiService.closeOpportunityAsWon(
              opportunity.opportunityId,
              opportunity.accountGroupId,
              opportunity.salesPersonId,
              notes,
            );
          }

          if (closeState === CloseState.LOST) {
            const reason = ClosedLostReasonStringMapping[data.reason];
            return this.salesOpportunitiesApiService.closeOpportunityAsLost(
              opportunity.opportunityId,
              opportunity.accountGroupId,
              opportunity.salesPersonId,
              reason,
              notes,
            );
          }

          return EMPTY;
        }),
      ),
      () => {
        this.closedWon$$.next(won);
        this.opportunityOpen$$.next(false);
      },
    );
  }

  async reopenOpportunity(): Promise<void> {
    const opportunity = await firstValueFrom(this.opportunity$);

    const dialogRef = this.dialog.open(ReopenOpportunityDialogComponent, {
      data: <ReopenOpportunityDialogInputData>{
        opportunityName: opportunity.name,
      },
      width: '600px',
    });

    const reopenOpportunityRequest$ = dialogRef.afterClosed().pipe(
      switchMap((data: ReopenOpportunityData) => {
        if (!data) {
          return EMPTY;
        }

        const reason = data.reason;
        return this.salesOpportunitiesApiService.reopenOpportunity(
          opportunity.opportunityId,
          opportunity.accountGroupId,
          opportunity.salesPersonId,
          reason,
        );
      }),
    );

    await firstValueFrom(reopenOpportunityRequest$)
      .then(() => this.opportunityOpen$$.next(true))
      .catch(() => {
        const err = this.translate.instant(
          'FRONTEND.SALES_UI.SALES_OPPORTUNITIES.REOPENING_OPPORTUNITIES.CANNOT_REOPEN_OPPORTUNITY',
        );
        this.snackbarService.openErrorSnack(err);
      });
  }
}
