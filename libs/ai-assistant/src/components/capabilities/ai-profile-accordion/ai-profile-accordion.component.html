<mat-card [formGroup]="assistantForm()">
  <mat-card-header>
    <mat-card-title>
      {{ 'AI_ASSISTANT.SETTINGS.PROFILE_CAPABILITY.TITLE' | translate }}
    </mat-card-title>
  </mat-card-header>
  <mat-accordion displayMode="flat" togglePosition="before" multi>
    @for (
      personalityCapabilityForm of personalityCapabilityForms();
      track personalityCapabilityForm;
      let iPersonalityCapability = $index;
      let isLast = $last
    ) {
      <mat-expansion-panel>
        <mat-expansion-panel-header collapsedHeight="auto" expandedHeight="auto" class="panel-header">
          <div class="content">
            <div class="titles">
              <mat-panel-title>
                {{ 'AI_ASSISTANT.SETTINGS.PROFILE_CAPABILITY.PURPOSE.TITLE' | translate }}
                @if (
                  personalityCapabilityForm.controls.namespace.value?.accountGroupNamespace ||
                  personalityCapabilityForm.controls.namespace.value?.partnerNamespace
                ) {
                  <glxy-badge [color]="'blue'" size="small" class="custom-badge">
                    {{ 'AI_ASSISTANT.SHARED.MODIFIED' | translate }}
                  </glxy-badge>
                }
              </mat-panel-title>
              <mat-panel-description class="description">
                {{ 'AI_ASSISTANT.SETTINGS.PROFILE_CAPABILITY.PURPOSE.HELP_TEXT' | translate }}
              </mat-panel-description>
            </div>
            @if (
              personalityCapabilityForm.controls.namespace.value?.accountGroupNamespace ||
              personalityCapabilityForm.controls.namespace.value?.partnerNamespace
            ) {
              <button
                mat-icon-button
                class="menu-button"
                title="{{ 'AI_ASSISTANT.SETTINGS.ROLLBACK' | translate }}"
                (click)="handleRollbackClicked(personalityCapabilityForm.controls.goalId.getRawValue() || '', $event)"
              >
                <mat-icon>undo</mat-icon>
              </button>
            }
          </div>
        </mat-expansion-panel-header>
        <!-- Personality Capability Prompt(s) -->
        @let promptModuleForms = personalityCapabilityForm.controls.promptModules;
        @if (promptModuleForms.length > 0) {
          @for (promptModuleForm of promptModuleForms.controls; track promptModuleForm; let iPromptModule = $index) {
            <glxy-form-field [formGroup]="promptModuleForm">
              <glxy-label>{{ 'AI_ASSISTANT.GOALS.CONFIGURE_GOAL.PROMPT' | translate }}</glxy-label>
              <textarea
                matInput
                formControlName="instructions"
                cdkTextareaAutosize
                cdkAutosizeMinRows="5"
                cdkAutosizeMaxRows="20"
              ></textarea>
            </glxy-form-field>
          }
        }
      </mat-expansion-panel>
      @if (!isLast) {
        <mat-divider />
      }
    }
    @if (assistantSupportsVoice()) {
      <mat-divider />
      <mat-expansion-panel>
        <mat-expansion-panel-header collapsedHeight="auto" expandedHeight="auto" class="panel-header">
          <div class="content">
            <div class="titles">
              <mat-panel-title>
                {{ 'AI_ASSISTANT.SETTINGS.PROFILE_CAPABILITY.SPEECH.TITLE' | translate }}
              </mat-panel-title>
              <mat-panel-description class="description">
                {{ 'AI_ASSISTANT.SETTINGS.PROFILE_CAPABILITY.SPEECH.HELP_TEXT' | translate }}
              </mat-panel-description>
            </div>
          </div>
        </mat-expansion-panel-header>
        <ai-voice-configuration-form [voiceConfigForm]="assistantForm().controls.voiceConfig" />
      </mat-expansion-panel>
    }
  </mat-accordion>
</mat-card>
