<form [formGroup]="capabilityForm()" [attr.disabled]="capabilityForm().disabled">
  <glxy-form-field [required]="true">
    <glxy-label
      >{{ 'AI_ASSISTANT.GOALS.CONFIGURE_GOAL.NAME' | translate }}
      <mat-icon class="info-icon" [glxyTooltip]="'AI_ASSISTANT.GOALS.CONFIGURE_GOAL.NAME_HELP_TEXT' | translate">
        info_outline
      </mat-icon>
    </glxy-label>
    <input matInput formControlName="name" type="text" />
  </glxy-form-field>
  <glxy-form-field>
    <glxy-label
      >{{ 'AI_ASSISTANT.GOALS.CONFIGURE_GOAL.DESCRIPTION' | translate }}
      <mat-icon class="info-icon" [glxyTooltip]="'AI_ASSISTANT.GOALS.CONFIGURE_GOAL.DESCRIPTION_HELP_TEXT' | translate">
        info_outline
      </mat-icon>
    </glxy-label>
    <textarea matInput formControlName="description" rows="2"></textarea>
  </glxy-form-field>
  <div formArrayName="promptModules">
    @for (promptModule of promptModules(); track promptModule; let iPromptModule = $index) {
      <glxy-form-field [formGroupName]="iPromptModule">
        <glxy-label
          >{{ 'AI_ASSISTANT.GOALS.CONFIGURE_GOAL.PROMPT' | translate }}
          <mat-icon class="info-icon" [glxyTooltip]="'AI_ASSISTANT.GOALS.CONFIGURE_GOAL.PROMPT_HELP_TEXT' | translate">
            info_outline
          </mat-icon>
        </glxy-label>
        <textarea
          matInput
          formControlName="instructions"
          cdkTextareaAutosize
          cdkAutosizeMinRows="5"
          cdkAutosizeMaxRows="20"
        ></textarea>
      </glxy-form-field>
    }
  </div>
</form>
<glxy-form-field class="tools-label">
  <glxy-label>
    {{ 'AI_ASSISTANT.GOALS.CONFIGURE_GOAL.FUNCTIONS' | translate }}
  </glxy-label>
</glxy-form-field>
<ai-tools-list
  [forms]="capabilityForm().controls.tools"
  [showAddButton]="capabilityForm().enabled"
  (toolFormDisplay)="toolFormDisplay.emit($event)"
/>
