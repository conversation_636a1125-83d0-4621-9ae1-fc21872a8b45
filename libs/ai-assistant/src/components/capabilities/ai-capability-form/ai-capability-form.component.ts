import { Component, computed, input, output } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { AiCapabilityForm, AiToolForm } from '../../../core/forms';
import { AiToolsListComponent } from '../../tools/ai-tools-list/ai-tools-list.component';
import { MatIcon } from '@angular/material/icon';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

@Component({
  selector: 'ai-capability-form',
  imports: [
    GalaxyFormFieldModule,
    TranslateModule,
    MatInputModule,
    ReactiveFormsModule,
    AiToolsListComponent,
    MatIcon,
    GalaxyTooltipModule,
  ],
  templateUrl: './ai-capability-form.component.html',
  styleUrl: './ai-capability-form.component.scss',
})
export class AiCapabilityFormComponent {
  readonly capabilityForm = input.required<AiCapabilityForm>();
  readonly toolFormDisplay = output<AiToolForm | undefined>();

  readonly promptModules = computed(() => this.capabilityForm().controls.promptModules.controls);
}
