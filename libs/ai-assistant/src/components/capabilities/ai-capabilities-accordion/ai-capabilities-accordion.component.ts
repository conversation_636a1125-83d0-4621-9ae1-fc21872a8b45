import { Component, inject, input, output, signal, ViewContainerRef } from '@angular/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatBadgeModule } from '@angular/material/badge';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule, MatIconButton } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { MatDividerModule } from '@angular/material/divider';
import { AiCapabilityForm, AiCapabilityFormArray, AiToolForm } from '../../../core/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AiCapabilitySelectComponent } from '../ai-capability-select-dialog/ai-capability-select-dialog.component';
import { GoalInterface, PromptModuleKey } from '@vendasta/ai-assistants';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { MatMenuModule } from '@angular/material/menu';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { AiCapabilityFormLoaderComponent } from '../ai-capability-form-loader/ai-capability-form-loader.component';
import { EnableLeadCaptureDialogComponent } from '../../ai-assistant-configuration/enable-lead-capture-dialog/enable-lead-capture-dialog.component';
import { HIDDEN_GOAL_IDS } from '../../../core/ai-assistant.constants';
import { AiAssistantFormV2Service } from '../../ai-assistant-configuration/ai-assistant-form-v2/ai-assistant-form-v2.service';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'ai-capabilities-accordion',
  imports: [
    MatMenuModule,
    MatBadgeModule,
    MatCardModule,
    MatDividerModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatIconButton,
    MatDialogModule,
    TranslateModule,
    AiCapabilityFormLoaderComponent,
  ],
  templateUrl: './ai-capabilities-accordion.component.html',
  styleUrl: './ai-capabilities-accordion.component.scss',
  host: {
    class: 'ai-capabilities-accordion',
  },
})
export class AiCapabilityAccordionComponent {
  readonly capabilities = input.required<AiCapabilityFormArray>();
  readonly toolFormDisplay = output<{ parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined>();

  readonly dialog = inject(MatDialog);
  private readonly assistantService = inject(AiAssistantService);
  private readonly aiAssistantServiceV2 = inject(AiAssistantFormV2Service);
  private readonly snackbar = inject(SnackbarService);
  private readonly viewContainerRef = inject(ViewContainerRef);
  private readonly confirmationModal = inject(OpenConfirmationModalService);

  protected readonly learnMoreComponents = new Map(
    Object.entries({
      DefaultLeadCapture: EnableLeadCaptureDialogComponent,
    }),
  );

  readonly assistant = toSignal(this.aiAssistantServiceV2.aiAssistant$);
  readonly expandedPanels = signal<number[]>([]);

  handleCapabilityRemoveClicked(event: Event, i: number) {
    event.stopPropagation();
    this.confirmationModal
      .openModal({
        type: 'warn',
        title: 'AI_ASSISTANT.SETTINGS.REMOVE_CAPABILITY_TITLE',
        message: 'AI_ASSISTANT.SETTINGS.REMOVE_CAPABILITY_CONFIRMATION',
        confirmButtonText: 'AI_ASSISTANT.SETTINGS.REMOVE',
      })
      .subscribe((confirmed: boolean) => {
        if (confirmed) {
          this.capabilities().removeAt(i);

          this.expandedPanels.update((panels) =>
            panels.filter((index) => index !== i).map((index) => (index > i ? index - 1 : index)),
          );
        }
      });
  }

  handleLearnMoreClicked(capabilityID: string, event: Event) {
    event.stopPropagation();
    if (this.learnMoreComponents.has(capabilityID)) {
      const learnMoreComponent = this.learnMoreComponents.get(capabilityID);
      if (learnMoreComponent) {
        this.dialog.open(learnMoreComponent);
      }
    }
  }

  async handleCapabilityAddClicked() {
    const dialogRef = this.dialog.open(AiCapabilitySelectComponent, {
      viewContainerRef: this.viewContainerRef,
      width: '900px',
      height: '600px',
      data: {
        assistant: this.assistant(),
        excludedGoalIds: [
          ...this.capabilities()
            .controls.map((capabilityForm) => capabilityForm.controls.goalId.value)
            .filter((id) => id != null),
          ...HIDDEN_GOAL_IDS,
        ],
      },
    });

    dialogRef.afterClosed().subscribe(async (result: { goal?: GoalInterface; newGoal: boolean } | '') => {
      if (result === '' || !result) {
        return;
      }

      const { goal: chosenGoal, newGoal } = result;

      if (chosenGoal) {
        let promptModuleContents: Record<string, string> = {};
        const promptModuleKeys =
          chosenGoal?.promptModules?.map((pm) => new PromptModuleKey({ id: pm.id, namespace: pm.namespace })) || [];
        if (promptModuleKeys.length > 0) {
          try {
            promptModuleContents = await this.assistantService.getMultiPromptModuleVersions(promptModuleKeys);
          } catch (err) {
            this.snackbar.openErrorSnack('AI_ASSISTANT.GOALS.ADD_GOAL.ERROR_LOADING_CAPABILITY');
            return;
          }
        }
        const newCapabilityForm = new AiCapabilityForm({ goal: chosenGoal, configuration: [] }, promptModuleContents);
        this.capabilities().push(newCapabilityForm);
      } else if (newGoal) {
        const newCapabilityForm = new AiCapabilityForm();
        this.capabilities().push(newCapabilityForm);
        this.expandedPanels.update((panels) => [...panels, this.capabilities().length - 1]);
      }
    });
  }

  moveCapability(fromIndex: number, toIndex: number): void {
    const formArray = this.capabilities();

    // Don't move if indices are invalid
    if (toIndex < 0 || toIndex >= formArray.controls.length) {
      return;
    }

    const control = formArray.at(fromIndex);
    formArray.removeAt(fromIndex);
    formArray.insert(toIndex, control);

    // Update expanded panels to maintain expansion state
    const expandedPanels = this.expandedPanels();
    const updatedPanels = expandedPanels.map((panel) =>
      panel === fromIndex
        ? toIndex
        : panel > fromIndex && panel <= toIndex
          ? panel - 1
          : panel < fromIndex && panel >= toIndex
            ? panel + 1
            : panel,
    );

    this.expandedPanels.set(updatedPanels);
  }

  expandPanel(index: number) {
    if (this.expandedPanels().includes(index)) {
      return;
    }

    this.expandedPanels.update((panels) => [...panels, index]);
  }

  collapsePanel(index: number) {
    this.expandedPanels.update((panels) => panels.filter((panelIndex) => panelIndex !== index));
  }
}
