<mat-card>
  <mat-card-header>
    <mat-card-title>
      {{ 'AI_ASSISTANT.SETTINGS.CAPABILITIES' | translate }}
    </mat-card-title>
  </mat-card-header>
  <mat-accordion displayMode="flat" togglePosition="before" multi>
    @for (capability of capabilities().controls; track capability; let i = $index) {
      @if (!capability.isHidden()) {
        <mat-expansion-panel
          [expanded]="expandedPanels().includes(i)"
          (opened)="expandPanel(i)"
          (closed)="collapsePanel(i)"
        >
          <mat-expansion-panel-header collapsedHeight="auto" expandedHeight="auto" class="panel-header">
            <div class="content">
              <div class="titles">
                <mat-panel-title>
                  {{ capability.controls.name.value }}
                </mat-panel-title>
                <mat-panel-description class="description">
                  {{ capability.controls.description.value }}
                  @if (capability.controls.goalId.value && learnMoreComponents.has(capability.controls.goalId.value)) {
                    <a (click)="handleLearnMoreClicked(capability.controls.goalId.value, $event)">{{
                      'AI_ASSISTANT.SETTINGS.LEARN_MORE' | translate
                    }}</a>
                  }
                </mat-panel-description>
              </div>
              <button
                mat-icon-button
                [matMenuTriggerFor]="capabilityMenu"
                class="menu-button"
                (click)="$event.stopPropagation()"
              >
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #capabilityMenu="matMenu">
                <button mat-menu-item [disabled]="i === 0" (click)="moveCapability(i, i - 1)">
                  <span>{{ 'AI_ASSISTANT.SETTINGS.MOVE_UP' | translate }}</span>
                </button>
                <button
                  mat-menu-item
                  [disabled]="i === capabilities().controls.length - 1"
                  (click)="moveCapability(i, i + 1)"
                >
                  <span>{{ 'AI_ASSISTANT.SETTINGS.MOVE_DOWN' | translate }}</span>
                </button>
                <button mat-menu-item (click)="handleCapabilityRemoveClicked($event, i)">
                  <span>{{ 'AI_ASSISTANT.SETTINGS.REMOVE' | translate }}</span>
                </button>
              </mat-menu>
            </div>
          </mat-expansion-panel-header>
          <ai-capability-form-loader
            [capabilityForm]="capability"
            (toolFormDisplay)="
              toolFormDisplay.emit(
                $event !== undefined
                  ? { form: $event.form, parentCapabilityForm: $event.parentCapabilityForm }
                  : undefined
              )
            "
          />
        </mat-expansion-panel>
        <mat-divider />
      }
    }
  </mat-accordion>
  <mat-card-footer class="card-footer">
    <button mat-button (click)="handleCapabilityAddClicked()">
      <mat-icon>add</mat-icon>
      {{ 'AI_ASSISTANT.SETTINGS.ADD_CAPABILITY' | translate }}
    </button>
  </mat-card-footer>
</mat-card>
