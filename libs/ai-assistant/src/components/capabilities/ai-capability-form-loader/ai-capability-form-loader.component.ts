import { Component, computed, effect, input, output, Type, viewChild, ViewContainerRef } from '@angular/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { AiCapabilityForm, AiToolForm, CustomCapabilityConfigurationForm } from '../../../core/forms';
import { AiCapabilityFormComponent } from '../ai-capability-form/ai-capability-form.component';
import {
  BookMeNowCapabilityFormComponent,
  DEFAULT_MEETING_BOOKING_GOAL_ID,
} from '../ai-capability-form/custom-forms/book-me-now-capability-form/book-me-now-capability-form.component';
import {
  DEFAULT_SALES_COACH_GOAL_ID,
  SalesCoachCapabilityFormComponent,
} from '../ai-capability-form/custom-forms/sales-coach-capability-form/sales-coach-capability-form.component';
import {
  RECORD_AND_TRANSCRIBE_GOAL_ID,
  RecordAndTranscribeCapabilityFormComponent,
} from '../ai-capability-form/custom-forms/record-and-transcribe-capability-form/record-and-transcribe-capability-form.component';
import {
  LOG_CRM_ACTIVITY_GOAL_ID,
  LogCrmActivityCapabilityFormComponent,
} from '../ai-capability-form/custom-forms/log-crm-activity-capability-form/log-crm-activity-capability-form.component';

const AiCapabilityCustomForms: { [goalId: string]: Type<CustomCapabilityConfigurationForm> } = {
  [DEFAULT_MEETING_BOOKING_GOAL_ID]: BookMeNowCapabilityFormComponent,
  [DEFAULT_SALES_COACH_GOAL_ID]: SalesCoachCapabilityFormComponent,
  [RECORD_AND_TRANSCRIBE_GOAL_ID]: RecordAndTranscribeCapabilityFormComponent,
  [LOG_CRM_ACTIVITY_GOAL_ID]: LogCrmActivityCapabilityFormComponent,
};

@Component({
  selector: 'ai-capability-form-loader',
  imports: [GalaxyFormFieldModule, TranslateModule, MatInputModule, ReactiveFormsModule, AiCapabilityFormComponent],
  templateUrl: './ai-capability-form-loader.component.html',
})
export class AiCapabilityFormLoaderComponent {
  customForm = computed(() => AiCapabilityCustomForms?.[this.capabilityForm().controls.goalId?.value || '']);
  capabilityForm = input.required<AiCapabilityForm>();

  // This will only emit for the base form, not the custom forms.
  readonly toolFormDisplay = output<{ parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined>();

  formContainer = viewChild('formContainer', { read: ViewContainerRef });

  constructor() {
    effect(() => {
      if (!!this.customForm() && this.formContainer()) {
        const componentRef = this.formContainer()?.createComponent(this.customForm());
        if (componentRef) {
          componentRef.setInput('configurationForm', this.capabilityForm().controls.configuration);
        }
      }
    });
  }
}
