import { Component, inject, OnInit, signal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { AiGoalService } from '../../../core/services/ai-goal.service';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../core/tokens';
import { combineLatest, firstValueFrom, of, switchMap } from 'rxjs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GoalInterface } from '@vendasta/ai-assistants/lib/_internal/interfaces/goal.interface';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { getNamespace } from '../../../core/services/ai-assistant-utils';
import { AssistantType, Namespace } from '@vendasta/ai-assistants';
import { AiAssistant } from '../../../core/interfaces/ai-assistant.interface';

@Component({
  selector: 'ai-capability-select-dialog',
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    GalaxyBadgeModule,
    AiAssistantI18nModule,
    GalaxyLoadingSpinnerModule,
    GalaxyInfiniteScrollTriggerModule,
  ],
  templateUrl: './ai-capability-select-dialog.component.html',
  styleUrls: [
    './ai-capability-select-dialog.component.scss',
    '../../ai-assistant-configuration/ai-assistant-shared.scss',
  ],
})
export class AiCapabilitySelectComponent implements OnInit {
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly aiGoalService = inject(AiGoalService);
  private readonly dialogRef = inject(MatDialogRef<AiCapabilitySelectComponent>);
  private readonly excludedGoalIds: string[] = inject(MAT_DIALOG_DATA).excludedGoalIds || [];
  private readonly assistant: AiAssistant = inject(MAT_DIALOG_DATA).assistant;
  private readonly snackbar = inject(SnackbarService);

  protected readonly isLoading = signal(false);
  protected readonly goals = signal<GoalInterface[]>([]);
  protected readonly nextCursor = signal('');
  protected readonly hasMore = signal(true);

  ngOnInit() {
    this.loadGoals();
  }

  protected async loadGoals(): Promise<void> {
    if (!this.isLoading() && this.hasMore()) {
      this.isLoading.set(true);
      try {
        const nextGoals = await firstValueFrom(
          combineLatest([this.accountGroupId$, this.partnerId$]).pipe(
            switchMap(([accountGroupId, partnerId]) => {
              const namespace = getNamespace(accountGroupId, partnerId);

              if (!namespace) return of(null);

              const namespaces: Namespace[] = [namespace];
              if (this.assistant.assistant.type !== AssistantType.ASSISTANT_TYPE_CUSTOM) {
                const globalNamespace = new Namespace({ globalNamespace: {} });
                namespaces.push(globalNamespace);
              }

              return this.aiGoalService.getGoals(namespaces, this.nextCursor());
            }),
          ),
        );

        const excludedIDs: string[] = this.excludedGoalIds;
        if (this.assistant.assistant.type !== AssistantType.ASSISTANT_TYPE_REPUTATION_MANAGEMENT_REVIEW_AGENT) {
          excludedIDs.push(...['DefaultReviewPersonality', 'DefaultRMWriteReviews', 'AI Review Manager ']);
        }
        if (this.assistant.assistant.type !== AssistantType.ASSISTANT_TYPE_SALES_COACH) {
          excludedIDs.push(...['logCRMActivity', 'recordAndTranscribe', 'DefaultSalesCoach']);
        }
        if (this.assistant.assistant.type !== AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST) {
          excludedIDs.push(...['DefaultVoiceLeadCapture', 'DefaultVoiceAppointmentBooking']);
        }
        this.goals.update((oldGoals) => [
          ...oldGoals,
          ...(nextGoals?.goals || []).filter((goal) => goal.id && !excludedIDs.includes(goal.id)),
        ]);
        this.nextCursor.set(nextGoals?.nextCursor ?? '');
        this.hasMore.set(nextGoals?.hasMore ?? false);
      } catch (error) {
        this.snackbar.openErrorSnack('AI_ASSISTANT.GOALS.ADD_GOAL.ERROR_LOADING_CAPABILITIES');
      } finally {
        this.isLoading.set(false);
      }
    }
  }

  chooseGoal(goal: GoalInterface) {
    this.dialogRef.close({
      goal,
    });
  }

  newGoal() {
    this.dialogRef.close({
      newGoal: true,
    });
  }
}
