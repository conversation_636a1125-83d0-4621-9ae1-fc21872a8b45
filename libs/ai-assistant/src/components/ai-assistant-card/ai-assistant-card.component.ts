import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  DestroyRef,
  ElementRef,
  HostBinding,
  inject,
  input,
  OnInit,
  output,
  signal,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { TranslateService } from '@ngx-translate/core';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { interval, map, of, Subscription, switchMap, takeWhile } from 'rxjs';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { AiAssistant, ConnectionType } from '../../core/interfaces/ai-assistant.interface';
import { MatAnchor, MatButtonModule } from '@angular/material/button';
import { Router, RouterLink } from '@angular/router';
import { AiAssistantService } from '../../core/services/ai-assistant.service';
import { ASSISTANT_ID_VOICE_RECEPTIONIST, DEFAULT_AVATAR_SVG_ICON } from '../../core/ai-assistant.constants';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { SmsDetailsService } from '../../core/services/sms-details.service';
import { ACCOUNT_GROUP_ID_TOKEN } from '../../core/tokens';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'ai-assistant-card',
  imports: [
    CommonModule,
    GalaxyAvatarModule,
    AiAssistantI18nModule,
    MatAnchor,
    RouterLink,
    MatButtonModule,
    GalaxyBadgeModule,
    GalaxyTooltipModule,
    MatIcon,
  ],
  templateUrl: './ai-assistant-card.component.html',
  styleUrls: ['./ai-assistant-card.component.scss'],
})
export class AiAssistantCardComponent implements OnInit {
  @HostBinding('class') class = 'ai-assistant';

  protected readonly translationService = inject(TranslateService);
  protected readonly aiIconService = inject(GalaxyAiIconService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly accountGroupId = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN));
  private readonly router = inject(Router);
  readonly aiAssistant = input.required<AiAssistant>();
  readonly showCTAButton = input(false);
  readonly isDisabled = input(false);
  readonly isComingSoon = input(false);
  readonly ctaClicked = output();
  protected readonly currentUrl = this.router?.url;
  readonly defaultAvatarIcon = DEFAULT_AVATAR_SVG_ICON;
  protected readonly ConnectionType = ConnectionType;

  aiAssistantConfigUrl = toSignal(
    toObservable(this.aiAssistant).pipe(
      switchMap((assistant) => {
        const assistantId = assistant.assistant.id;
        return assistantId ? this.aiAssistantService.buildAssistantConfigurationUrl(assistantId) : of('');
      }),
    ),
    { initialValue: '' },
  );

  private gradientBorderMask = viewChild.required<ElementRef>('gradientBorderMask');

  protected readonly gradientColor = signal<string>('');
  protected readonly gradient = signal<string>('');

  private animationSubscription?: Subscription;

  protected readonly isVoiceReceptionistCard = computed(() => {
    return this.aiAssistant().assistant.id === ASSISTANT_ID_VOICE_RECEPTIONIST;
  });

  // To avoid showing the same icon multiple times in the list of connection icons when multiple connections of the same type exist
  protected readonly uniqueConnections = computed(() => {
    const assistant = this.aiAssistant();
    if (!assistant.connections?.length) {
      return [];
    }

    return assistant.connections
      .filter(
        (connection, index, self) =>
          self.findIndex((c) => c.connection.connectionTypeName === connection.connection.connectionTypeName) === index,
      )
      .map((aiConnection) => ({
        ...aiConnection,
        isGreyscale: !aiConnection.connection.assistantKeys?.length,
        tooltip: aiConnection.connection.assistantKeys?.length
          ? 'AI_ASSISTANT.WORKFORCE.CHANNEL.ACTIVE'
          : 'AI_ASSISTANT.WORKFORCE.CHANNEL.NOT_YET_ACTIVE',
      }))
      .sort((a, b) => Number(a.isGreyscale) - Number(b.isGreyscale));
  });

  protected stopReset() {
    this.animationSubscription?.unsubscribe();
  }

  private getMousePosition(event: MouseEvent): { x: number; y: number } {
    const rect = this.gradientBorderMask().nativeElement.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    return { x, y };
  }

  ngOnInit() {
    if (this.aiAssistant()) {
      this.initializeGradient();
    }
  }

  protected move(event: MouseEvent) {
    const { x, y } = this.getMousePosition(event);
    this.applyGradient(x, y);
  }

  protected leave(event: MouseEvent) {
    const { x: startX, y: startY } = this.getMousePosition(event);

    const duration = 1500;
    const frameRate = 60;
    const intervalMs = 1000 / frameRate;

    const startTime = performance.now();

    this.animationSubscription = interval(intervalMs)
      .pipe(
        map(() => {
          const elapsed = performance.now() - startTime;
          const progress = Math.min(elapsed / duration, 1);
          const easedProgress = this.easeOutCubic(progress);
          return {
            x: startX * (1 - easedProgress),
            y: startY * (1 - easedProgress),
          };
        }),
        takeWhile(({ x, y }) => Math.floor(x) > 0 || Math.floor(y) > 0),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((coords) => {
        this.applyGradient(coords.x, coords.y);
      });
  }

  private easeOutCubic(t: number): number {
    return --t * t * t + 1;
  }

  private applyGradient(x: number, y: number) {
    this.gradient.set(
      `radial-gradient(circle at ${x}px ${y}px, ${this.aiAssistant().decoration?.gradientColor} 40%, transparent 60%)`,
    );
  }

  private initializeGradient() {
    if (this.aiAssistant()?.decoration) {
      this.gradientColor.set(this.aiAssistant().decoration?.gradientColor || '#fff');
      this.applyGradient(0, 0);
    }
  }

  protected readonly showConfigButton = computed(() => {
    return (
      !this.aiAssistant().isDisabled && (!this.aiAssistant().isDefault || this.aiAssistant().showConfigButtonOnDefault)
    );
  });

  protected readonly phoneNumber$ = inject(SmsDetailsService).phoneNumber$;
}
