<div class="ai-assistant-container" (mouseenter)="stopReset()" (mousemove)="move($event)" (mouseleave)="leave($event)">
  <div class="gradient-border-mask" #gradientBorderMask [style.background]="gradient()"></div>
  <div class="content">
    <div class="info-container">
      @if (aiAssistant().assistant.avatarUrl) {
        <glxy-avatar class="assistant-avatar" [src]="aiAssistant().assistant.avatarUrl"></glxy-avatar>
      } @else {
        <glxy-avatar
          class="assistant-avatar"
          [svgIcon]="aiAssistant().decoration?.defaultAvatarIcon ?? defaultAvatarIcon"
          [src]="aiAssistant().decoration?.avatarIconUrl"
          [useGrayScale]="isDisabled()"
        ></glxy-avatar>
      }
      <div class="text-container">
        <p class="title">{{ aiAssistant().assistant.name }}</p>
        @if (aiAssistant().subtitleKey) {
          <p class="subtitle">{{ aiAssistant().subtitleKey | translate }}</p>
        }
      </div>
      @if (isVoiceReceptionistCard()) {
        <glxy-badge [color]="'green'">{{ 'AI_ASSISTANT.SHARED.BETA' | translate }}</glxy-badge>
      }
    </div>
    <p class="description">
      {{ aiAssistant().descriptionKey | translate }}
    </p>
    @let aiConnections = uniqueConnections();
    @if (aiConnections?.length > 0) {
      <div class="connections-container">
        @for (aiConnection of aiConnections; track aiConnection) {
          @if (aiConnection.connection) {
            @if (aiConnection.connection.connectionType === ConnectionType.WhatsApp) {
              <img
                class="availability-icons"
                alt="WA"
                [src]="aiConnection.connection.iconUrl"
                [glxyTooltip]="aiConnection.connection.connectionTypeName + ' ' + (aiConnection.tooltip | translate)"
              />
            } @else if (aiConnection.connection.connectionType !== ConnectionType.System) {
              <mat-icon
                class="availability-icons"
                [class.greyscale]="aiConnection.isGreyscale"
                [glxyTooltip]="aiConnection.connection.connectionTypeName + ' ' + (aiConnection.tooltip | translate)"
                [svgIcon]="aiConnection.connection.connectionType"
              >
              </mat-icon>
            }
          }
        }
      </div>
    }
    <div class="actions">
      @if (showCTAButton()) {
        <button mat-flat-button color="primary" (click)="ctaClicked.emit()">
          {{ aiAssistant().cta.label | translate }}
        </button>
      }
      @if (isDisabled()) {
        <glxy-badge>{{ 'AI_ASSISTANT.SHARED.UPGRADE' | translate }}</glxy-badge>
      } @else if (isComingSoon()) {
        <glxy-badge>{{ 'AI_ASSISTANT.SHARED.COMING_SOON' | translate }}</glxy-badge>
      }
      @if (showConfigButton()) {
        <a mat-stroked-button [routerLink]="[aiAssistantConfigUrl()]">
          {{ 'AI_ASSISTANT.WORKFORCE.CONFIGURE_BUTTON' | translate }}
        </a>
      }
    </div>
  </div>
</div>
