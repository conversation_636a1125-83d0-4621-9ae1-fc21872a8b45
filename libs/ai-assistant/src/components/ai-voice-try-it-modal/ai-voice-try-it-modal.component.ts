import { Component, computed, inject } from '@angular/core';
import { MatD<PERSON>ogContent, MatDialogTitle } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { AiAssistantService } from '../../core/services/ai-assistant.service';
import { ASSISTANT_ID_VOICE_RECEPTIONIST, ConnectionType } from '../../core';
import { catchError, map, of } from 'rxjs';
import { toSignal } from '@angular/core/rxjs-interop';
import { SmsDetailsService } from '../../core/services/sms-details.service';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyPhoneNumberPipe } from '@vendasta/galaxy/pipes/src/phone-number/phone-number.pipe';

@Component({
  selector: 'ai-voice-try-it-modal',
  imports: [
    MatDialogTitle,
    MatDialogContent,
    AiAssistantI18nModule,
    CommonModule,
    GalaxyLoadingSpinnerModule,
    GalaxyAlertModule,
    GalaxyPhoneNumberPipe,
  ],
  templateUrl: './ai-voice-try-it-modal.component.html',
  styleUrl: './ai-voice-try-it-modal.component.scss',
})
export class AiVoiceTryItModalComponent {
  private readonly assistantService = inject(AiAssistantService);
  private readonly smsDetailsService = inject(SmsDetailsService);
  private readonly snackbarService = inject(SnackbarService);

  protected readonly phoneNumber = toSignal(
    this.smsDetailsService.phoneNumber$.pipe(
      catchError((err) => {
        if (err.status !== 404) {
          this.snackbarService.openErrorSnack('Error getting phone number');
        }
        return of('');
      }),
    ),
  );
  protected readonly isVoiceAIAvailable = this.assistantService.$isVoiceAIAvailable;
  // Is the phone number set to direct calls to the voice ai?
  protected readonly isRoutingCallsToVoiceAi = toSignal(
    this.smsDetailsService.isRoutingToVoiceAi$.pipe(
      catchError((err) => {
        if (err.status !== 404) {
          this.snackbarService.openErrorSnack(
            'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.ERRORS.ASSISTANT_CONNECTIONS',
          );
        }
        return of(false);
      }),
    ),
  );
  // Is the voice ai assistant active on the phone channel?
  protected readonly isVoiceAssistantActive = toSignal(
    this.assistantService.activeConnectionsForAssistant(ASSISTANT_ID_VOICE_RECEPTIONIST).pipe(
      map((aiConnections) =>
        aiConnections.some((aiConnection) => aiConnection.connection.connectionType === ConnectionType.Voice),
      ),
      catchError((err) => {
        if (err.status !== 404) {
          this.snackbarService.openErrorSnack(
            'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.ERRORS.PHONE_NUMBER',
          );
        }
        return of(false);
      }),
    ),
  );
  // The final determined status of the voice assistant
  protected readonly assistantStatus = computed(() => {
    if (this.isLoading()) {
      return undefined;
    }

    if (!this.isVoiceAIAvailable()) {
      return 'Unavailable';
    }

    if (!this.isVoiceAssistantActive() && !this.isRoutingCallsToVoiceAi()) {
      return 'Inactive';
    }

    // If there is a mismatch between the assistant being active on the phone channel, and the phone config routing to the AI, then
    // we consider it to be in a provisioning state.
    if (this.isVoiceAssistantActive() !== this.isRoutingCallsToVoiceAi()) {
      return 'Provisioning';
    }

    return 'Active';
  });

  // Whether we're still waiting for the signals to resolve
  protected readonly isLoading = computed(() =>
    [
      this.isVoiceAIAvailable(),
      this.isVoiceAssistantActive(),
      this.isRoutingCallsToVoiceAi(),
      this.phoneNumber(),
    ].includes(undefined),
  );

  protected readonly alertType = computed(() => {
    switch (this.assistantStatus()) {
      case 'Active':
        return 'success';
      case 'Provisioning':
        return 'warning';
      case 'Inactive':
        return 'tip';
      case 'Unavailable':
        return 'tip';
      default:
        return 'tip';
    }
  });

  protected readonly alertTitle = computed(() => {
    return `AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.STATUS.${(this.assistantStatus() || 'Unavailable').toUpperCase()}.TITLE`;
  });

  protected readonly alertMessage = computed(() => {
    return `AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.STATUS.${(this.assistantStatus() || 'Unavailable').toUpperCase()}.MESSAGE`;
  });
}
