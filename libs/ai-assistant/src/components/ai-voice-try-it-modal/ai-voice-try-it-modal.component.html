@if (isLoading() === false) {
  @let status = assistantStatus();
  <h1 mat-dialog-title>
    {{ 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.TRY_IT_TITLE' | translate }}
  </h1>
  <mat-dialog-content>
    @if (status !== 'Active') {
      <glxy-alert showIcon [type]="alertType()" [title]="alertTitle()">
        {{ alertMessage() | translate }}
      </glxy-alert>
    } @else {
      <div
        [innerHTML]="
          'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.TRY_IT_INSTRUCTIONS'
            | translate: { phoneNumber: phoneNumber() | glxyPhoneNumber }
        "
      ></div>
    }
  </mat-dialog-content>
} @else {
  <mat-dialog-content>
    <glxy-loading-spinner />
  </mat-dialog-content>
}
