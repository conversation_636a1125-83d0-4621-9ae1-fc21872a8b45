import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AiAssistantFormV2Service } from './ai-assistant-form-v2.service';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import {
  Assistant,
  AssistantApiService,
  AssistantType,
  Config,
  Namespace,
  UpsertAssistantResponse,
} from '@vendasta/ai-assistants';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ActivatedRoute } from '@angular/router';
import { HttpResponse } from '@angular/common/http';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../../core/tokens';
import { ConversationApiService } from '@vendasta/conversation';
import { AiConnection } from '../../../core/interfaces/ai-assistant.interface';
import { AiKnowledgeService } from '@galaxy/ai-knowledge';
import { AiToolFormRegistryService } from '../../../core/services/ai-tool-form-registry.service';
import { TranslateService } from '@ngx-translate/core';
import { WhatsappDetailsService } from '../../../core/services/whatsapp-details.service';
import { SmsDetailsService } from '../../../core/services/sms-details.service';

const ACCOUNT_GROUP_ID = 'AG-1234';
const PARTNER_ID = 'ABC';

describe('AiAssistantFormV2Service', () => {
  let service: AiAssistantFormV2Service;
  let aiAssistantService: jest.Mocked<AiAssistantService>;
  let assistantApiService: jest.Mocked<AssistantApiService>;
  let conversationApiService: jest.Mocked<ConversationApiService>;
  let snackbarService: jest.Mocked<SnackbarService>;
  let mockAiKnowledgeService: jest.Mocked<AiKnowledgeService>;
  let mockToolFormRegistry: jest.Mocked<AiToolFormRegistryService>;
  let mockWhatsappDetailsService: jest.Mocked<WhatsappDetailsService>;
  let mockSmsDetailsService: jest.Mocked<SmsDetailsService>;

  beforeEach(() => {
    aiAssistantService = {
      getAssistant: jest.fn(),
      hydrateAssistantWithDefaultInfo: jest.fn(),
      listConnectionsForAssistant: jest.fn(),
    } as unknown as jest.Mocked<AiAssistantService>;

    conversationApiService = {
      getMultiWidget: jest.fn(),
    } as unknown as jest.Mocked<ConversationApiService>;

    assistantApiService = {
      upsertAssistant: jest.fn(),
      setAssistantConnections: jest.fn(),
    } as unknown as jest.Mocked<AssistantApiService>;

    snackbarService = {
      openErrorSnack: jest.fn(),
    } as unknown as jest.Mocked<SnackbarService>;

    mockAiKnowledgeService = {
      businessProfileKSId: jest.fn().mockReturnValue('businessProfileKSId'),
      listAllKnowledgeSourcesForApp: jest.fn().mockResolvedValue([]),
    } as unknown as jest.Mocked<AiKnowledgeService>;

    mockToolFormRegistry = {
      getOrRegister: jest.fn(),
      updateRegistry: jest.fn(),
      hasRegisteredForm: jest.fn(),
      cleanupRegistry: jest.fn(),
    } as unknown as jest.Mocked<AiToolFormRegistryService>;

    mockSmsDetailsService = {
      phoneNumber$: of('+***********'),
    } as unknown as jest.Mocked<SmsDetailsService>;

    mockWhatsappDetailsService = {
      whatsappPhoneNumber$: of('**********'),
    } as unknown as jest.Mocked<WhatsappDetailsService>;

    TestBed.configureTestingModule({
      providers: [
        { provide: PARTNER_ID_TOKEN, useValue: of(PARTNER_ID) },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of(ACCOUNT_GROUP_ID) },
        { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of({}) },
        { provide: ConversationApiService, useValue: conversationApiService },
        AiAssistantFormV2Service,
        { provide: AiAssistantService, useValue: aiAssistantService },
        { provide: AssistantApiService, useValue: assistantApiService },
        { provide: SnackbarService, useValue: snackbarService },
        { provide: AiToolFormRegistryService, useValue: mockToolFormRegistry },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              get: (key: string) => (key === 'assistantId' ? 'test-assistant-id' : null),
            }),
          },
        },
        { provide: AiKnowledgeService, useValue: mockAiKnowledgeService },
        { provide: TranslateService, useValue: { instant: jest.fn((key: string) => key) } },
        { provide: SmsDetailsService, useValue: mockSmsDetailsService },
        { provide: WhatsappDetailsService, useValue: mockWhatsappDetailsService },
        { provide: NAMESPACE_CONFIG_TOKEN, useValue: {} },
      ],
    });

    service = TestBed.inject(AiAssistantFormV2Service);
  });

  describe('saveAssistant', () => {
    it('should call upsertAssistant with the correct parameters', (done) => {
      // Arrange
      const mockAssistant = new Assistant({
        id: 'test-assistant-id',
        name: 'Test Assistant',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
        namespace: new Namespace({
          accountGroupNamespace: {
            accountGroupId: ACCOUNT_GROUP_ID,
          },
        }),
        avatarUrl: 'https://example.com/avatar.png',
        config: new Config({
          inboxConfig: {},
          voiceConfig: {},
        }),
        configurableGoals: [],
      });

      const mockResponse = new UpsertAssistantResponse({
        assistant: mockAssistant,
      });

      assistantApiService.upsertAssistant.mockReturnValue(of(mockResponse));

      // Act
      service.upsertAssistant(mockAssistant).subscribe((response) => {
        // Assert
        expect(response).toEqual(mockResponse);
        expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith({
          assistant: mockAssistant,
          options: { applyDefaults: false },
        });
        done();
      });
    });
  });

  describe('updateConnections', () => {
    it('should call setAssistantConnections with the correct payload', (done) => {
      // Arrange
      const assistant = { id: 'test-assistant-id', namespace: 'test-namespace' } as any;
      const connections = [
        { connection: { id: 'conn1', namespace: 'ns1', connectionType: 'type1', isConnectionLocked: false } },
        { connection: { id: 'conn2', namespace: 'ns2', connectionType: 'type2', isConnectionLocked: true } },
      ] as AiConnection[];
      const connectionStates = [
        { connectionId: 'conn1', enabled: true },
        { connectionId: 'conn2', enabled: false },
      ];

      assistantApiService.setAssistantConnections.mockReturnValue(of(new HttpResponse<null>({ status: 200 })));

      // Act
      service.updateConnections(assistant, connections, connectionStates).subscribe(() => {
        // Assert
        expect(assistantApiService.setAssistantConnections).toHaveBeenCalledWith({
          assistantKey: { id: 'test-assistant-id', namespace: 'test-namespace' },
          associationStates: [
            {
              connectionKey: { id: 'conn1', namespace: 'ns1', connectionType: 'type1' },
              isAssociated: true,
            },
          ],
        });
        done();
      });
    });
  });
});
