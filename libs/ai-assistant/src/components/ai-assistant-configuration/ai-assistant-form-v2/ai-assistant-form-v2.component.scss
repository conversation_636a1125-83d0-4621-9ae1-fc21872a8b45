@use 'design-tokens' as *;

.tool-form {
  padding-bottom: $spacing-4;
}

.config-page-wrapper {
  max-height: 100%;
  display: flex;
  overflow: hidden;
}

.two-column-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-column {
  flex: 0 0 320px;
  border-right: 1px solid $border-color;
  display: flex;
  flex-direction: column;
  padding: $spacing-4;
  gap: $spacing-2;
  overflow-y: auto;

  .avatar {
    display: flex;
    align-items: flex-start;
    justify-content: center;
  }
}

.right-column {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  padding: $spacing-4;
  background-color: $primary-background-color;
  align-items: center;
}

@media (max-width: $mobile-breakpoint-max) {
  .two-column-layout {
    flex-direction: column;
    overflow-y: auto;
  }

  .left-column {
    flex: 0 0 auto;
    border-right: none;
  }

  .right-column {
    padding: $spacing-3;
    background-color: transparent;
    overflow-y: visible;
  }
}

.hidden {
  display: none;
}

.form-wrapper {
  max-width: 800px;
  width: 100%;
}

.form-cards {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.info-icon {
  color: $secondary-text-color;
  font-size: $font-preset-3-size;
  height: $font-preset-3-size;
  margin-left: $spacing-1;
}

.beta-badge {
  margin-left: $spacing-2;
}

.hidden {
  display: none;
}

.voice-usage {
  display: block;
  margin-top: $spacing-3;
}
