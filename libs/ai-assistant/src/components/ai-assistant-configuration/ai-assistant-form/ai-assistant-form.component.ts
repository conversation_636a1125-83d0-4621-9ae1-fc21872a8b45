import { CommonModule, Location } from '@angular/common';
import {
  Component,
  computed,
  DestroyRef,
  effect,
  EnvironmentInjector,
  HostListener,
  inject,
  OnDestroy,
  runInInjectionContext,
  signal,
  WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { KnowledgeSource } from '@vendasta/embeddings';
import { AiKnowledgeService, ApplicationKnowledgeComponent } from '@galaxy/ai-knowledge';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyPageNoAccessUnauthorizedModule } from '@vendasta/galaxy/page-no-access-unauthorized';
import {
  catchError,
  combineLatest,
  distinctUntilChanged,
  EMPTY,
  firstValueFrom,
  map,
  Observable,
  of,
  shareReplay,
  startWith,
  switchMap,
  take,
  throwError,
} from 'rxjs';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import {
  AssistantApiService,
  AssistantInterface,
  AssistantType,
  ConfigurableGoal,
  ConfigurableGoalInterface,
  ConnectionInterface,
  KeyValuePairInterface,
  Namespace,
  VendorModel,
} from '@vendasta/ai-assistants';

import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { BookingConfigService } from '../../../core/services/booking-config.service';
import {
  DEFAULT_AVATAR_SVG_ICON,
  knowledgeAppType,
  WEBCHAT_WIDGET_CONNECTION_TYPE,
} from '../../../core/ai-assistant.constants';
import { EnableLeadCaptureDialogComponent } from '../enable-lead-capture-dialog/enable-lead-capture-dialog.component';
import { EnableMeetingBookingDialogComponent } from '../enable-meeting-booking-dialog/enable-meeting-booking-dialog.component';
import { AdditionalInstructionsDialogComponent } from '../../additional-instructions-dialog/additional-instructions-dialog.component';
import { StickyFooterComponent } from '../../sticky-footer/sticky-footer.component';
import { ImageUploadComponent } from '../image-upload/image-upload.component';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import {
  AssistantIdToAssistantType,
  familyFromVoice,
  getNamespace,
  getVoiceFamilies,
  previewUrlFromVoice,
  voicesForFamily,
} from '../../../core/services/ai-assistant-utils';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../../core/tokens';
import { UnsavedChangesGuard } from '../../../core/services/unsaved-changes.guard';
import { ConversationApiService, Widget } from '@vendasta/conversation';
import { AiAssistantVoiceUsageComponent } from '../../ai-assistant-voice-usage/ai-assistant-voice-usage.component';
import { PopoverPositions } from '@vendasta/galaxy/popover';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { CdkAccordionModule } from '@angular/cdk/accordion';
import { MatSliderModule } from '@angular/material/slider';
import { AiVoice, AiVoiceFamily, MeetingType } from '../../../core/interfaces/config.interface';
import { FeatureFlagService } from '@galaxy/partner';
import {
  AiAssistant,
  AiConnection,
  ConnectionType,
  MY_LISTING_CONNECTION_NAME,
} from '../../../core/interfaces/ai-assistant.interface';
import { TranslateService } from '@galaxy/lexicon';
import { GoalsListComponent } from '../goals-list/goals-list.component';
import { AiGoalService } from '../../../core/services/ai-goal.service';
import { AddGoalDialogComponent } from '../add-goal-dialog/add-goal-dialog.component';
import { GoalInterface } from '@vendasta/ai-assistants/lib/_internal/interfaces/goal.interface';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { URLBuilderService } from '../../../core/services/url-builder.service';

const ADDITIONAL_INSTRUCTIONS_LIMIT = 10000;
const FALLBACK_BACK_URL = '../..';
const MEETING_URL = '../../../../events-and-meetings';

// Default global goals
const DEFAULT_LEAD_CAPTURE_GOAL_ID = 'DefaultLeadCapture';
const DEFAULT_MEETING_BOOKING_GOAL_ID = 'BookAppointmentsWithBookMeNow';
const ADDITIONAL_INSTRUCTIONS_GOAL_ID = 'AdditionalInstructions';
const DEEPGRAM_SPEAK_FEATURE_FLAG = 'deepgram_speak';

// Goal configuration keys
const GOAL_CONFIG_KEY_CALENDAR_ID = 'calendarId';
const GOAL_CONFIG_KEY_MEETING_TYPE_ID = 'meetingTypeId';

@Component({
  selector: 'ai-assistant-form',
  imports: [
    CommonModule,
    MatInputModule,
    MatCardModule,
    MatOptionModule,
    MatSelectModule,
    MatSliderModule,
    CdkAccordionModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    GalaxyFormFieldModule,
    GalaxyLoadingSpinnerModule,
    GalaxyPageNoAccessUnauthorizedModule,
    AiAssistantI18nModule,
    MatCheckboxModule,
    RouterModule,
    MatIconModule,
    MatButtonToggleModule,
    MatDialogModule,
    MatDividerModule,
    ApplicationKnowledgeComponent,
    GalaxyBadgeModule,
    ImageUploadComponent,
    GalaxyTooltipModule,
    StickyFooterComponent,
    GalaxyPageModule,
    GalaxyAlertModule,
    GalaxyEmptyStateModule,
    GalaxyAvatarModule,
    AiAssistantVoiceUsageComponent,
    GoalsListComponent,
  ],
  templateUrl: './ai-assistant-form.component.html',
  styleUrls: ['./ai-assistant-form.component.scss', '../ai-assistant-shared.scss'],
})
export class AiAssistantFormComponent implements OnDestroy {
  private destroyRef = inject(DestroyRef);
  private confirmationModal = inject(OpenConfirmationModalService);
  PopoverPositions = PopoverPositions;

  static connectionsFormGroupFactory(assistant?: AssistantInterface, aiConnections?: AiConnection[]): FormGroup {
    const formArray =
      aiConnections?.map(
        (aiConnection) =>
          new FormControl({
            value: aiConnection.connection.assistantKeys?.some((assistantKey) => assistantKey.id === assistant?.id),
            disabled: aiConnection.connection.isConnectionLocked || aiConnection.isDefault || false,
          }),
      ) || [];

    return new FormGroup({
      connections: new FormArray(formArray),
    });
  }

  static assistantFormGroupFactory(
    assistantService: AiAssistantService,
    assistant?: AssistantInterface,
    additionalInstructions?: string,
  ) {
    const leadCaptureEnabled =
      assistant?.configurableGoals?.some((goal) => goal.goal?.id === DEFAULT_LEAD_CAPTURE_GOAL_ID) || false;

    let meetingBookingEnabled = false;
    let calendarId = '';
    let meetingTypeId = '';
    if (assistantService.$isAIMeetingBookingEnabled()) {
      const bookingGoal = assistant?.configurableGoals?.find(
        (goal) => goal.goal?.id === DEFAULT_MEETING_BOOKING_GOAL_ID,
      );

      meetingBookingEnabled = Boolean(bookingGoal);
      calendarId =
        bookingGoal?.configuration?.find((config) => config.key === GOAL_CONFIG_KEY_CALENDAR_ID)?.value || '';
      meetingTypeId =
        bookingGoal?.configuration?.find((config) => config.key === GOAL_CONFIG_KEY_MEETING_TYPE_ID)?.value || '';
    }

    const additionalPromptValidators = assistantService.$isNewCustomAIGoalsEnabled()
      ? []
      : [Validators.maxLength(ADDITIONAL_INSTRUCTIONS_LIMIT)];
    return new FormGroup({
      name: new FormControl(assistant?.name || ''),
      assistantAvatarUrl: new FormControl(assistant?.avatarUrl || ''),
      enableLeadCapture: new FormControl(leadCaptureEnabled || false),
      enableMeetingBooking: new FormControl(meetingBookingEnabled || false),
      calendarAndMeetingType: new FormControl({
        calendarId: calendarId || '',
        meetingTypeId: meetingTypeId || '',
      }),
      additionalPrompt: new FormControl(additionalInstructions || '', additionalPromptValidators),
      assistantId: new FormControl(assistant?.id || ''),
      namespace: new FormControl(assistant?.namespace),
    });
  }

  static voiceConfigFormGroupFactory(assistant?: AssistantInterface) {
    // There is a translation here between the options we present to the user, and which models/config-options those become.
    let voice = assistant?.config?.voiceConfig?.modelConfig?.openaiRealtimeConfig?.voice || 'alloy';
    let family = familyFromVoice(voice);
    const vendorModel = assistant?.config?.voiceConfig?.vendorModel ?? VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME;

    if (vendorModel === VendorModel.VENDOR_MODEL_DEEPGRAM) {
      voice = assistant?.config?.voiceConfig?.modelConfig?.deepgramConfig?.voice ?? 'aura-asteria-en';
      family = familyFromVoice(voice);
    }

    return new FormGroup({
      family: new FormControl(family?.name ?? 'OpenAI GPT-4o Realtime (Sep. 17 Preview)'),
      voice: new FormControl(voice),
      advanced: new FormGroup({
        turnDetectionThreshold: new FormControl(
          assistant?.config?.voiceConfig?.modelConfig?.openaiRealtimeConfig?.turnDetection?.threshold || 0.75,
        ),
        turnDetectionPrefixPadding: new FormControl(
          assistant?.config?.voiceConfig?.modelConfig?.openaiRealtimeConfig?.turnDetection?.prefixPadding || 300,
        ),
        turnDetectionSilenceDuration: new FormControl(
          assistant?.config?.voiceConfig?.modelConfig?.openaiRealtimeConfig?.turnDetection?.silenceDuration || 500,
        ),
      }),
    });
  }

  static knowledgeFormGroupFactory(assistantId?: string) {
    const kID = inject(AiAssistantService).buildAppId(assistantId);
    return new FormGroup({
      knowledgeSources: new FormControl<KnowledgeSource[]>([]),
      // following is used only to store web chat state
      knowledgeAppId: new FormControl<string>(kID),
    });
  }

  private readonly urlBuilder = inject(URLBuilderService);
  private readonly assistantAPI = inject(AssistantApiService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly knowledgeService = inject(AiKnowledgeService);
  private readonly assistantService = inject(AiAssistantService);
  private readonly goalService = inject(AiGoalService);
  private readonly featureFlagService = inject(FeatureFlagService);
  private readonly bookingConfigService = inject(BookingConfigService);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly unsavedGuard = inject(UnsavedChangesGuard);
  private readonly dialog = inject(MatDialog);
  private readonly injector = inject(EnvironmentInjector);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN).pipe(distinctUntilChanged());
  private readonly accountGroupId = toSignal(this.accountGroupId$);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN).pipe(distinctUntilChanged());
  private readonly partnerId = toSignal(this.partnerId$);
  private readonly conversationApiService = inject(ConversationApiService);
  private readonly translateService = inject(TranslateService);
  private readonly pageService = inject(PageService);
  private readonly aiIconService = inject(GalaxyAiIconService);
  private readonly namespaceConfiguration = inject(NAMESPACE_CONFIG_TOKEN);
  protected voiceFamilies: AiVoiceFamily[] = [];
  protected readonly vendorModels = VendorModel;
  protected availableVoices = signal<AiVoice[]>([]);
  private readonly defaultWorkforce$ = inject(AI_DEFAULT_WORKFORCE_TOKEN);
  protected readonly voiceFamily = signal<AiVoiceFamily | null>(null);
  protected previewAudio = signal(new Audio(''));
  protected currentlyPlayingPreview = signal(false);
  readonly defaultAvatarIcon = DEFAULT_AVATAR_SVG_ICON;
  // combine the accountGroupId$ and partnerId$ into a single observable that only emits when one of them changes
  private readonly partnerIdAndAccountGroupId$ = combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
    distinctUntilChanged((a, b) => a[0] === b[0] && a[1] === b[1]),
  );

  protected readonly $isSaving = signal(false);
  protected readonly error = signal<null | string>(null);

  protected readonly additionalInstructionsLimit = ADDITIONAL_INSTRUCTIONS_LIMIT;

  private readonly $hasUnsavedChanges = signal(false);
  private readonly $initialSources = signal<KnowledgeSource[] | null>(null);
  protected readonly $canSave = computed(() => !this.$isLoading() && this.$hasUnsavedChanges());

  protected assistantForm = AiAssistantFormComponent.assistantFormGroupFactory(this.assistantService);
  protected knowledgeForm = AiAssistantFormComponent.knowledgeFormGroupFactory();
  protected connectionForm = AiAssistantFormComponent.connectionsFormGroupFactory();
  protected voiceConfigForm = AiAssistantFormComponent.voiceConfigFormGroupFactory();

  public readonly showCustomGoals = computed(() => {
    return this.assistantService.$isNewCustomAIGoalsEnabled();
  });

  private readonly assistantId$ = this.route.paramMap.pipe(
    map((params) => params.get('assistantId') || ''),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  protected readonly assistantId = toSignal(this.assistantId$, { initialValue: null });
  protected readonly isCreateMode = computed(() => this.assistantId() === '');

  readonly aiAssistant$: Observable<AiAssistant | null> = combineLatest([
    this.assistantId$,
    this.partnerIdAndAccountGroupId$,
    this.defaultWorkforce$,
  ]).pipe(
    switchMap(([assistantId, partnerIdAndAccountGroupId, defaultWorkforce]): Observable<AiAssistant | null> => {
      const partnerId = partnerIdAndAccountGroupId[0];
      const accountGroupId = partnerIdAndAccountGroupId[1];

      // If we're in create mode, return null
      if (!assistantId) {
        const namespace = getNamespace(accountGroupId, partnerId);
        if (!namespace) {
          throw of(null);
        }
        return of({
          assistant: {
            id: '',
            name: 'Custom Assistant',
            type: AssistantType.ASSISTANT_TYPE_CUSTOM,
            avatarUrl: '',
            namespace: new Namespace(namespace),
          },
          options: {
            applyDefaults: false,
          },
        });
      }

      return this.assistantService.getAssistant(partnerId, accountGroupId, assistantId).pipe(
        map((assistant) => {
          return this.assistantService.hydrateAssistantWithDefaultInfo(assistant, defaultWorkforce, accountGroupId);
        }),
        catchError((err) => {
          // create the assistant if it does not exist
          if (err.status === 404) {
            return this.assistantService.getDefaultAssistant(partnerId, accountGroupId, assistantId).pipe(
              map((assistant) => assistant),
              catchError((err) => {
                this.error.set(err.message);
                return EMPTY;
              }),
            );
          } else {
            this.error.set(err.message);
            return EMPTY;
          }
        }),
      );
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );
  readonly aiAssistant = toSignal<AiAssistant | null>(this.aiAssistant$, {
    initialValue: null,
  });

  private readonly newGoalId$ = this.route.queryParams.pipe(
    map((params) => params['addGoal']),
    distinctUntilChanged(),
  );
  private readonly newGoal$ = combineLatest([
    this.newGoalId$,
    this.accountGroupId$,
    this.partnerId$,
    this.aiAssistant$,
  ]).pipe(
    switchMap(([goalId, accountGroupId, partnerId, aiAssistant]) => {
      if (!goalId || aiAssistant?.assistant?.configurableGoals?.some((goal) => goal.goal?.id === goalId)) {
        return of(null);
      }
      return this.goalService.getGoal(accountGroupId, partnerId, goalId);
    }),
  );

  private readonly newGoal = toSignal(this.newGoal$);
  protected readonly newGoalToSave = signal(false);

  readonly assistant = computed(() => this.aiAssistant()?.assistant);
  readonly isCustomAssistant = computed(
    () => this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_CUSTOM,
  );

  readonly customGoals: WritableSignal<GoalInterface[]> = signal([]);
  readonly globalAndManagedGoals: WritableSignal<GoalInterface[]> = signal([]);

  readonly additionalInstructions$ = this.aiAssistant$.pipe(
    switchMap((aiAssistant: AiAssistant | null) => {
      const additionalInstructionsPromptModule = aiAssistant?.assistant?.configurableGoals?.find(
        (goal) => goal.goal?.id === this.buildAdditionalInstructionsGoalID(aiAssistant?.assistant?.id || ''),
      )?.goal?.promptModules?.[0];
      if (additionalInstructionsPromptModule) {
        return this.assistantService
          .getPromptModuleVersion(
            additionalInstructionsPromptModule.id ?? '',
            additionalInstructionsPromptModule.namespace ?? {},
            additionalInstructionsPromptModule.deployedVersion ?? '',
          )
          .pipe(map((instructions) => instructions || ''));
      }
      return of('');
    }),
  );

  readonly $additionalInstructions = toSignal(this.additionalInstructions$, {
    initialValue: '',
  });

  readonly connections$ = combineLatest([this.namespaceConfiguration, this.assistantId$]).pipe(
    switchMap(([namespaceConfig, assistantId]) =>
      this.assistantService.listConnectionsForAssistant(namespaceConfig.namespace, assistantId),
    ),
    catchError(() => {
      this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.CHANNELS.FETCH_ERROR');
      return of([] as AiConnection[]);
    }),
  );
  readonly $connections = toSignal(this.connections$);

  protected readonly $isVoiceAssistant = computed(() => {
    return this.assistant() && this.assistant()?.type === AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST;
  });

  protected readonly showConnections = computed(() => {
    return !this.isCustomAssistant() && !this.isCreateMode() && !this.aiAssistant()?.hideConnections;
  });

  protected readonly $showPrimaryGoalCheckbox = computed(() => {
    return (
      this.assistant() &&
      ![AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST, AssistantType.ASSISTANT_TYPE_CUSTOM, undefined].includes(
        this.assistant()?.type,
      )
    );
  });

  protected readonly $isMeetingBookingEnabled = this.assistantService.$isAIMeetingBookingEnabled;

  protected readonly $showBetaBadge = computed(() => {
    return (
      this.assistant() &&
      (this.assistant()?.type === AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST ||
        this.assistant()?.type === AssistantType.ASSISTANT_TYPE_CUSTOM)
    );
  });

  protected readonly widgetConnections$ = this.connections$.pipe(
    map((aiConnections) =>
      aiConnections
        .filter((aiConnection) => aiConnection.connection.connectionType === WEBCHAT_WIDGET_CONNECTION_TYPE)
        .map((aiConnection) => aiConnection.connection.id),
    ),
    switchMap((webchatWidgetConnectionIds) => {
      const widgetIds = webchatWidgetConnectionIds.filter((id): id is string => !id || !id.includes('default'));
      if (widgetIds?.length > 0) {
        return this.conversationApiService.getMultiWidget({ widgetIds: widgetIds }).pipe(
          map((response) => response.widgets),
          catchError(() => {
            this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.CHANNELS.FETCH_WIDGET_NAME_WARNING');
            return of([]);
          }),
        );
      }
      return of([]);
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  private readonly fetching$ = combineLatest([this.connections$, this.widgetConnections$, this.aiAssistant$]).pipe(
    map(() => false),
    startWith(true),
  );
  private readonly $fetching = toSignal(this.fetching$, {
    requireSync: true,
  });
  protected readonly $isLoading = computed(() => this.$fetching() || this.$isSaving());

  protected readonly calendarsWithTypes$ = this.bookingConfigService.getCalendarsWithTypes();

  constructor(private location: Location) {
    // Make sure the default AI icon is registered
    this.aiIconService.dummyInit();

    // leverage the unsavedGuard to prevent leaving current route if there are unsaved changes
    effect(() => {
      this.unsavedGuard.notifyStateChanged(this.$hasUnsavedChanges() || this.newGoalToSave());
    });

    effect(() => {
      const assistant = this.assistant();
      const additionalInstructions = this.$additionalInstructions();
      const aiConnections = this.$connections();
      const isCreateMode = this.isCreateMode();

      // Initialize forms if we have an assistant or we're in create mode
      if (assistant || isCreateMode) {
        if (assistant) {
          this.knowledgeForm.get('knowledgeAppId')?.setValue(this.assistantService.buildAppId(assistant.id));
        }

        // Initialize assistantForm with either the existing assistant or the custom template
        const assistantToUse = assistant || {
          name: '',
          type: AssistantType.ASSISTANT_TYPE_CUSTOM,
          avatarUrl: '',
          namespace: getNamespace(this.accountGroupId(), this.partnerId()),
        };

        this.assistantForm = runInInjectionContext<
          ReturnType<typeof AiAssistantFormComponent.assistantFormGroupFactory>
        >(this.injector, () =>
          AiAssistantFormComponent.assistantFormGroupFactory(
            this.assistantService,
            assistantToUse,
            additionalInstructions,
          ),
        );

        // Initialize connectionForm if we have connections or we're in create mode
        if (aiConnections || isCreateMode) {
          this.connectionForm = runInInjectionContext<
            ReturnType<typeof AiAssistantFormComponent.connectionsFormGroupFactory>
          >(this.injector, () => AiAssistantFormComponent.connectionsFormGroupFactory(assistantToUse, aiConnections));
        }

        // Initialize voiceConfigForm if we have an assistant or we're in create mode
        this.voiceConfigForm = runInInjectionContext<
          ReturnType<typeof AiAssistantFormComponent.voiceConfigFormGroupFactory>
        >(this.injector, () => AiAssistantFormComponent.voiceConfigFormGroupFactory(assistantToUse));

        // Set up voice family and voices
        this.voiceFamily.set(
          this.voiceFamilies.find((f) => f.name === this.voiceConfigForm.controls.family.value) ?? null,
        );

        this.previewAudio.set(new Audio(previewUrlFromVoice(this.voiceConfigForm.controls.voice.value ?? '') ?? ''));

        this.availableVoices.set(voicesForFamily(this.voiceConfigForm.controls.family.value ?? ''));

        // Subscribe to form changes
        this.subscribeToFormChanges();
      }
    });

    this.partnerId$
      .pipe(
        switchMap((partnerId) =>
          this.featureFlagService.batchGetStatus(partnerId, '', [DEEPGRAM_SPEAK_FEATURE_FLAG]).pipe(
            map((resp) => resp[DEEPGRAM_SPEAK_FEATURE_FLAG]),
            catchError(() => of(false)),
          ),
        ),
        take(1),
      )
      .subscribe((deepgramEnabled) => {
        this.voiceFamilies = getVoiceFamilies(deepgramEnabled);
      });

    effect(() => {
      const configurableGoals = this.assistant()?.configurableGoals || [];
      const customGoals: GoalInterface[] = [];
      const globalAndManagedGoals: GoalInterface[] = [];

      for (const configurableGoal of configurableGoals) {
        const goal = configurableGoal?.goal;
        if (goal && !goal.namespace?.globalNamespace && !goal.managed) {
          customGoals.push(goal);
        } else if (goal) {
          globalAndManagedGoals.push(goal);
        }
      }

      const newGoal = this.newGoal();
      if (newGoal) {
        customGoals.push(newGoal);
        this.newGoalToSave.set(true);
      }

      this.customGoals.set(customGoals);
      this.globalAndManagedGoals.set(globalAndManagedGoals);
    });
  }

  private subscribeToFormChanges(): void {
    this.assistantForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.markChanges();
      this.updateBookingValidator();
    });

    this.connectionForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.markChanges();
    });

    this.knowledgeForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.markChanges();
    });

    this.voiceConfigForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.markChanges();
    });

    this.voiceConfigForm.controls.family.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((familyName) => {
        if (!familyName) {
          this.availableVoices.set([]);
          return;
        }

        this.availableVoices.set(voicesForFamily(familyName));
        this.voiceFamily.set(this.voiceFamilies.find((f) => f.name === familyName) ?? null);

        if (this.availableVoices().length > 0) {
          this.voiceConfigForm.controls.voice.setValue(this.availableVoices()[0].value);
        }
      });

    this.voiceConfigForm.controls.voice.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((voice) => {
      this.stopPlayingPreview();
      this.previewAudio.set(new Audio(previewUrlFromVoice(voice ?? '') ?? ''));
    });
  }

  protected togglePreview() {
    if (this.currentlyPlayingPreview()) {
      this.stopPlayingPreview();
      return;
    }

    this.currentlyPlayingPreview.set(true);

    const handleEnded = () => {
      this.currentlyPlayingPreview.set(false);
      this.previewAudio().removeEventListener('ended', handleEnded);
    };

    this.previewAudio()
      ?.play()
      .then(() => {
        this.audioEndedHandler = handleEnded;
        this.previewAudio().addEventListener('ended', handleEnded);
      });
  }

  private stopPlayingPreview() {
    this.previewAudio().pause();
    this.previewAudio().currentTime = 0;
    this.currentlyPlayingPreview.set(false);
    this.cleanupAudioEventListeners();
  }

  private audioEndedHandler: ((this: HTMLAudioElement, ev: Event) => void) | null = null;

  private cleanupAudioEventListeners() {
    if (this.audioEndedHandler) {
      this.previewAudio().removeEventListener('ended', this.audioEndedHandler);
      this.audioEndedHandler = null;
    }
  }

  private knowledgeSourcesHaveChanged(): boolean {
    const currentSources = this.knowledgeForm.get('knowledgeSources')?.value;
    if (this.$initialSources()?.length !== currentSources?.length) {
      return true;
    }
    for (const source of this.$initialSources() || []) {
      if (!(currentSources || []).some((s) => s.id === source.id)) {
        return true;
      }
    }
    return false;
  }

  protected getConnectionName(connection: ConnectionInterface, widgets: Widget[]): string {
    if (
      connection.namespace?.accountGroupNamespace &&
      connection.connectionType === ConnectionType.WebchatWidget &&
      connection.name !== MY_LISTING_CONNECTION_NAME &&
      !connection.assistantKeys?.length
    ) {
      return this.translateService.instant('AI_ASSISTANT.SETTINGS.CONNECTIONS.WEBCHAT_CREATING');
    }

    const widget = widgets?.find((widget) => widget.widgetId === connection.id);
    return widget ? widget.name || '' : connection.name || '';
  }

  protected updateSelectedSources(sources: KnowledgeSource[] | null): void {
    if (this.$initialSources() === null && sources !== null) {
      this.$initialSources.set(JSON.parse(JSON.stringify(sources)));
    }
    if (sources === null) {
      // reset initial sources
      this.$initialSources.set(null);
    }
    this.knowledgeForm.get('knowledgeSources')?.setValue(sources);
  }

  protected async editGoal(goalId: string): Promise<void> {
    const goalConfigurationUrl = await firstValueFrom(this.urlBuilder.goalConfigurationUrl(goalId));
    await this.router.navigate([goalConfigurationUrl], { relativeTo: this.route });
  }

  protected removeGoal(goalId: string): void {
    this.$hasUnsavedChanges.set(true);
    this.assistantForm.markAsDirty();
    const goals = this.customGoals().filter((goal) => goal.id !== goalId);
    this.customGoals.set(goals);
  }

  async openGoalsModal(): Promise<void> {
    const result = await firstValueFrom(
      this.dialog
        .open(AddGoalDialogComponent, {
          width: '900px',
          height: '600px',
          data: {
            excludedGoalIds: this.customGoals().map((goal) => goal?.id),
          },
        })
        .afterClosed(),
    );

    if (result.goal) {
      this.$hasUnsavedChanges.set(true);
      this.assistantForm.markAsDirty();
      const goals = this.customGoals();
      this.customGoals.set([...goals, result.goal]);
    } else if (result.newGoal) {
      await this.router.navigate([await firstValueFrom(this.urlBuilder.newGoalUrl())], {
        queryParams: {
          addToAssistant: this.assistant()?.id,
        },
      });
    }
  }

  @HostListener('window: beforeunload', ['$event'])
  onBeforeUnload(event: BeforeUnloadEvent): void {
    if (this.$hasUnsavedChanges()) {
      event.preventDefault();
      event.stopPropagation();
      event.returnValue = false;
    }
  }

  protected async submit(): Promise<void> {
    this.assistantForm.markAllAsTouched();

    if (
      this.assistantForm.invalid ||
      this.knowledgeForm.invalid ||
      (!this.assistantForm.dirty &&
        !this.connectionForm.dirty &&
        !this.knowledgeSourcesHaveChanged() &&
        !this.voiceConfigForm.dirty &&
        !this.newGoalToSave())
    ) {
      return;
    }

    this.$isSaving.set(true);
    try {
      // Step 1: Create or get the assistant
      let assistant = this.toAssistant();
      if (this.isCreateMode()) {
        const createResponse = await firstValueFrom(
          this.assistantAPI.upsertAssistant({
            assistant: assistant,
            options: {
              applyDefaults: true,
            },
          }),
        );
        assistant = createResponse.assistant;
      }

      const ps: Promise<unknown>[] = [];
      ps.push(this.updateAssistant(assistant));
      if (this.connectionForm.dirty) {
        ps.push(this.updateConnections());
      }
      await Promise.all(ps);

      this.markAsSaved();
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATED');

      // Step 4: Navigate if we created a new assistant
      if (this.isCreateMode()) {
        // markAsSaved updates this async, so to ensure we can navigate after save without being blocked by the unsaved changes modal we need to update this manually
        this.unsavedGuard.notifyStateChanged(false);
        const url = await firstValueFrom(this.assistantService.buildAssistantConfigurationUrl(assistant.id || ''));
        await this.router.navigateByUrl(url, { replaceUrl: true });
      }
    } catch (e) {
      console.error(e);
      this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATE_ERROR');
    } finally {
      this.newGoalToSave.set(false);
      this.$isSaving.set(false);
    }
  }

  private markChanges(): void {
    const hasChanges =
      this.assistantForm.dirty ||
      this.knowledgeForm.dirty ||
      this.knowledgeSourcesHaveChanged() ||
      this.connectionForm.dirty ||
      this.voiceConfigForm.dirty;
    this.$hasUnsavedChanges.set(hasChanges);
  }

  private markAsSaved() {
    this.assistantForm.markAsPristine();
    this.knowledgeForm.markAsPristine();
    this.connectionForm.markAsPristine();
    this.voiceConfigForm.markAsPristine();
    this.$initialSources.set(JSON.parse(JSON.stringify(this.knowledgeForm.get('knowledgeSources')?.value)));
    this.$hasUnsavedChanges.set(false);
  }

  private updateBookingValidator(): void {
    const enableMeetingBooking = this.assistantForm.get('enableMeetingBooking')?.value;
    const calendarAndMeetingType = this.assistantForm.get('calendarAndMeetingType');
    if (enableMeetingBooking) {
      calendarAndMeetingType?.setValidators([
        (control) => (!control?.value?.calendarId || !control?.value?.meetingTypeId ? { bothRequired: true } : null),
      ]);
    } else {
      calendarAndMeetingType?.clearValidators();
    }
    calendarAndMeetingType?.updateValueAndValidity({ emitEvent: false });
  }

  private async updateAssistant(assistant: AssistantInterface): Promise<void> {
    if (!assistant.id) {
      throw new Error('Assistant ID is required');
    }

    try {
      // Update goals first
      await this.updateGoals(assistant);

      // Then update knowledge
      const configurationUrl = await firstValueFrom(
        this.assistantService.buildAiKnowledgeAppConfigurationUrl(assistant.id),
      );
      const knowledge = this.toKnowledgeSource();
      await this.knowledgeService.upsertKnowledgeForApp(
        this.assistantService.buildAppId(assistant.id),
        assistant.name || '',
        configurationUrl,
        knowledgeAppType,
        knowledge,
      );

      // Finally update the assistant itself
      await firstValueFrom(
        this.assistantAPI
          .upsertAssistant({
            assistant: assistant,
          })
          .pipe(catchError((err) => throwError(() => err))),
      );
    } catch (err) {
      throw new Error('Failed to update assistant', { cause: err });
    }
  }

  private async updateGoals(assistant: AssistantInterface): Promise<void> {
    // Initialize configurableGoals if it is undefined
    if (!assistant.configurableGoals) {
      assistant.configurableGoals = [];
    }

    const globalNamespace = new Namespace({
      globalNamespace: {},
    });

    switch (assistant.type) {
      case AssistantType.ASSISTANT_TYPE_INBOX:
        if (this.assistantForm.get('enableLeadCapture')?.value) {
          this.updateGoal(DEFAULT_LEAD_CAPTURE_GOAL_ID, globalNamespace, assistant.configurableGoals);
        } else {
          assistant.configurableGoals = assistant.configurableGoals?.filter(
            (goal) => goal.goal?.id !== DEFAULT_LEAD_CAPTURE_GOAL_ID,
          );
        }

        if (this.assistantForm.get('enableMeetingBooking')?.value) {
          const goalConfig: KeyValuePairInterface[] = [
            {
              key: GOAL_CONFIG_KEY_CALENDAR_ID,
              value: this.assistantForm.get('calendarAndMeetingType')?.value?.calendarId || '',
            },
            {
              key: GOAL_CONFIG_KEY_MEETING_TYPE_ID,
              value: this.assistantForm.get('calendarAndMeetingType')?.value?.meetingTypeId || '',
            },
          ];
          this.updateGoal(DEFAULT_MEETING_BOOKING_GOAL_ID, globalNamespace, assistant.configurableGoals, goalConfig);
        } else {
          assistant.configurableGoals = assistant.configurableGoals?.filter(
            (goal) => goal.goal?.id !== DEFAULT_MEETING_BOOKING_GOAL_ID,
          );
        }
        break;
      case AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST:
        // TODO: Create configurable settings for voice goals (i.e: Lead Capture and Appointment Booking)
        break;
    }

    try {
      await this.addAdditionalInstructionsGoal(assistant);
    } catch (err) {
      throw new Error('Failed to add additional instructions', { cause: err });
    }
  }

  private updateGoal(
    defaultGoalId: string,
    namespace: Namespace,
    existingGoals: ConfigurableGoalInterface[],
    goalConfig?: KeyValuePairInterface[],
  ): void {
    const goalIndex = existingGoals.findIndex((goal) => goal?.goal?.id === defaultGoalId);
    if (goalIndex === -1) {
      existingGoals.push(
        new ConfigurableGoal({
          goal: {
            id: defaultGoalId,
            namespace: namespace,
          },
          configuration: goalConfig,
        }),
      );
    } else {
      existingGoals[goalIndex].configuration = goalConfig;
    }
  }

  private async addAdditionalInstructionsGoal(assistant: AssistantInterface): Promise<void> {
    const hasAdditionalInstructionsGoal = assistant.configurableGoals?.some(
      (goal) => goal.goal?.id === this.buildAdditionalInstructionsGoalID(assistant.id),
    );
    const additionalInstructionsFormValue = this.assistantForm.get('additionalPrompt')?.value;
    if (additionalInstructionsFormValue) {
      try {
        const goalKey = await this.assistantService.upsertAdditionalInstructions(
          assistant.id,
          assistant.namespace,
          additionalInstructionsFormValue,
        );

        if (!hasAdditionalInstructionsGoal) {
          assistant.configurableGoals?.push(
            new ConfigurableGoal({
              goal: goalKey,
            }),
          );
        }
      } catch (err) {
        throw new Error('Failed to upsert additional instructions goal', { cause: err });
      }
    } else if (hasAdditionalInstructionsGoal) {
      assistant.configurableGoals = assistant.configurableGoals?.filter(
        (goal) => goal.goal?.id !== this.buildAdditionalInstructionsGoalID(assistant.id),
      );
    }
  }

  private buildAdditionalInstructionsGoalID(assistantId?: string): string {
    if (assistantId === undefined) {
      console.warn('Assistant ID is undefined');
      return '';
    }
    return `${ADDITIONAL_INSTRUCTIONS_GOAL_ID}-${assistantId}`;
  }

  private async updateConnections(): Promise<void> {
    await firstValueFrom(
      this.assistantAPI.setAssistantConnections({
        assistantKey: {
          id: this.assistant()?.id,
          namespace: this.assistant()?.namespace,
        },
        associationStates: this.$connections()
          ?.map((aiConnection, index) =>
            !aiConnection?.connection.isConnectionLocked
              ? {
                  connectionKey: {
                    id: aiConnection.connection.id,
                    namespace: aiConnection.connection.namespace,
                    connectionType: aiConnection.connection.connectionType,
                  },
                  isAssociated: this.connectionForm.getRawValue().connections[index],
                }
              : null,
          )
          .filter((connection) => connection !== null),
      }),
    );
  }

  get selectedAiVoice(): AiVoice | undefined {
    const value = this.voiceConfigForm.controls.voice.value;
    return this.availableVoices().find((voice) => voice.value === value);
  }

  private toAssistant(): AssistantInterface {
    const customGoals = this.customGoals().map((goal) => new ConfigurableGoal({ goal: goal }));
    const globalAndManagedGoals = this.globalAndManagedGoals().map((goal) => new ConfigurableGoal({ goal: goal }));

    return {
      name: this.assistantForm.get('name')?.value || '',
      id: this.assistantForm.get('assistantId')?.value || '',
      namespace: this.assistantForm.get('namespace')?.value || ({} as Namespace),
      type: AssistantIdToAssistantType(this.assistantForm.get('assistantId')?.value || ''),
      config: {
        inboxConfig: {
          leadCaptureEnabled: this.assistantForm.get('enableLeadCapture')?.value || false,
          additionalInstructions: this.assistantForm.get('additionalPrompt')?.value || '',
        },
        voiceConfig: this.$isVoiceAssistant()
          ? {
              vendorModel: this.voiceFamily()?.provider,
              modelConfig: this.buildVoiceModelConfig(),
            }
          : undefined,
      },
      avatarUrl: this.assistantForm.get('assistantAvatarUrl')?.value || '',
      configurableGoals: [...globalAndManagedGoals, ...customGoals],
    };
  }

  private buildVoiceModelConfig() {
    const family = this.voiceConfigForm.get('family')?.value;
    const voice = this.voiceConfigForm.get('voice')?.value || '';
    const voiceFamily = this.voiceFamilies.find((f) => f.name === family);

    if (voiceFamily?.provider === VendorModel.VENDOR_MODEL_DEEPGRAM) {
      return {
        deepgramConfig: {
          voice: voice,
        },
      };
    }

    return {
      openaiRealtimeConfig: {
        voice: voice,
        turnDetection: {
          threshold: this.voiceConfigForm.get('advanced')?.get('turnDetectionThreshold')?.value ?? 0.75,
          prefixPadding: this.voiceConfigForm.get('advanced')?.get('turnDetectionPrefixPadding')?.value ?? 300,
          silenceDuration: this.voiceConfigForm.get('advanced')?.get('turnDetectionSilenceDuration')?.value ?? 500,
        },
      },
    };
  }

  private toKnowledgeSource(): KnowledgeSource[] {
    return this.knowledgeForm.get('knowledgeSources')?.value || [];
  }

  openEnableLeadCaptureDialog(event: Event): void {
    event.preventDefault();
    this.dialog.open(EnableLeadCaptureDialogComponent, {
      width: '550px',
    });
  }

  openEnableMeetingBookingDialog(event: Event): void {
    event.preventDefault();
    this.dialog.open(EnableMeetingBookingDialogComponent, {
      width: '550px',
    });
  }

  openAdditionalInstructionsDialog(): void {
    this.dialog.open(AdditionalInstructionsDialogComponent, {
      width: '100vh',
    });
  }

  onImageChanged(imageUrl: string): void {
    this.assistantForm.get('assistantAvatarUrl')?.markAsDirty();
    this.assistantForm.get('assistantAvatarUrl')?.setValue(imageUrl);
  }

  protected triggerAction(aiConnection: AiConnection): void {
    if (aiConnection) {
      this.buildConnectionActionCallback(aiConnection)?.();
    }
  }

  protected buildConnectionActionCallback(aiConnection: AiConnection): (() => void) | undefined {
    if (!aiConnection?.cta || !aiConnection?.cta.action?.showButton) {
      return undefined;
    }

    const action = aiConnection?.cta?.action;

    if (action?.url) {
      if (action.newTab && !action.relativeRoute) {
        return () => window.open(action.url, '_blank');
      }

      return () => this.router.navigate([action.url], action.relativeRoute ? { relativeTo: this.route } : undefined);
    }

    if (action?.pathCommands) {
      return () => this.router.navigate(action.pathCommands);
    }

    if (action?.callback) {
      return action.callback;
    }
    return undefined;
  }

  protected backUrl(): string {
    return FALLBACK_BACK_URL;
  }

  protected back(): void {
    this.pageService.navigateToPrevious(FALLBACK_BACK_URL, this.route);
  }

  protected goToMeetings(): void {
    this.markAsSaved();
    this.router.navigate([MEETING_URL], { relativeTo: this.route });
  }

  protected compareBookingOptions(a: MeetingType, b: MeetingType): boolean {
    return a.calendarId === b.calendarId && a.meetingTypeId === b.meetingTypeId;
  }

  protected async openDeleteAssistantConfirmationModal() {
    if (!this.isCustomAssistant()) {
      // Safety net to prevent deletion of non-custom assistants - this is not supported
      return;
    }
    const assistant = this.assistant();
    if (!assistant) {
      return;
    }
    const deleteConfirmed = await firstValueFrom(
      this.confirmationModal.openModal({
        type: 'warn',
        title: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.TITLE',
        message: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.MESSAGE',
        confirmButtonText: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.CONFIRM',
        cancelButtonText: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.CANCEL',
        cancelOnEscapeKeyOrBackgroundClick: true,
      }),
    );
    if (!deleteConfirmed) {
      return;
    }

    try {
      await firstValueFrom(
        this.assistantAPI.deleteAssistant({
          id: assistant.id,
          namespace: assistant.namespace,
        }),
      );
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_DELETED');
      this.back();
    } catch (e) {
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_DELETE_ERROR');
    }
  }

  ngOnDestroy(): void {
    this.stopPlayingPreview();
  }
}
