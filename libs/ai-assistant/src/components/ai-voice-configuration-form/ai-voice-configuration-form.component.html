@let form = voiceConfigForm();
<form [formGroup]="form">
  <glxy-form-field>
    <glxy-label>
      {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.FAMILY' | translate }}
    </glxy-label>
    <mat-select formControlName="voiceFamily">
      <ng-template #voiceFamilyDisplay let-voiceFamily>
        {{ voiceFamily?.name }}
        @if (voiceFamily?.recommended) {
          <glxy-badge color="green" size="small">{{
            'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.RECOMMENDED' | translate
          }}</glxy-badge>
        }
      </ng-template>

      <mat-select-trigger>
        <ng-container *ngTemplateOutlet="voiceFamilyDisplay; context: { $implicit: voiceFamily() }"></ng-container>
      </mat-select-trigger>

      @for (family of voiceFamilies(); track family) {
        <mat-option [value]="family">
          <ng-container *ngTemplateOutlet="voiceFamilyDisplay; context: { $implicit: family }"></ng-container>
        </mat-option>
      }
    </mat-select>
    @let family = voiceFamily();
    @if (family) {
      <glxy-hint>
        {{ family.description_key | translate }}
      </glxy-hint>
    }
  </glxy-form-field>

  @let modelConfigForm = modelConfig();
  @if (modelConfigForm) {
    <div class="voice-select-container">
      <div class="voice-select-label">
        {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.VOICE' | translate }}
      </div>
      <div class="voice-select-and-preview">
        @let voiceForm = modelConfigForm?.controls?.voice;
        @if (voiceForm) {
          <glxy-form-field [bottomSpacing]="'none'" class="select-form">
            <mat-select [formControl]="voiceForm">
              <ng-template #voiceDisplay let-voice>
                {{ voice.name }} ({{ voice.region | translate }}, {{ voice.gender | translate }})
                @if (voice.recommended) {
                  <glxy-badge color="green" size="small">{{
                    'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.RECOMMENDED' | translate
                  }}</glxy-badge>
                }
              </ng-template>

              <mat-select-trigger>
                <ng-container *ngTemplateOutlet="voiceDisplay; context: { $implicit: aiVoice() }"></ng-container>
              </mat-select-trigger>

              @for (voice of voiceFamily()?.voices; track voice) {
                <mat-option [value]="voice.value">
                  <ng-container *ngTemplateOutlet="voiceDisplay; context: { $implicit: voice }"></ng-container>
                </mat-option>
              }
            </mat-select>
          </glxy-form-field>
        }
        <button mat-icon-button class="preview-player" (click)="togglePreview()" [disabled]="!voice()">
          @if (currentlyPlayingPreview()) {
            <mat-icon>stop</mat-icon>
          } @else {
            <mat-icon>play_arrow</mat-icon>
          }
        </button>
      </div>
    </div>

    @let turnDetectionForm = modelConfigForm?.controls?.turnDetection;
    @if (turnDetectionForm) {
      <cdk-accordion [formGroup]="turnDetectionForm">
        <cdk-accordion-item #accordionItem="cdkAccordionItem">
          <a (click)="accordionItem.toggle()" class="advanced-voice-options-toggle">
            <mat-icon class="accordian-expansion-icon">
              {{ accordionItem.expanded ? 'expand_less' : 'expand_more' }}
            </mat-icon>
            {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.ADVANCED' | translate }}
          </a>
          @if (accordionItem.expanded) {
            <div class="advanced-voice-options-content">
              <glxy-form-field>
                <glxy-label>
                  {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.THRESHOLD.LABEL' | translate }}
                </glxy-label>
                <mat-slider min="0" max="1" step="0.01" discrete>
                  <input matSliderThumb formControlName="threshold" />
                </mat-slider>
                <glxy-hint>
                  {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.THRESHOLD.DESCRIPTION' | translate }}
                </glxy-hint>
              </glxy-form-field>
              <glxy-form-field>
                <glxy-label>
                  {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.PREFIX_PADDING.LABEL' | translate }}
                </glxy-label>
                <mat-slider min="0" max="1000" step="1" discrete>
                  <input matSliderThumb formControlName="prefixPadding" />
                </mat-slider>
                <glxy-hint>
                  {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.PREFIX_PADDING.DESCRIPTION' | translate }}
                </glxy-hint>
              </glxy-form-field>
              <glxy-form-field>
                <glxy-label>
                  {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.SILENCE_DURATION.LABEL' | translate }}
                </glxy-label>
                <mat-slider min="0" max="1000" step="1" discrete>
                  <input matSliderThumb formControlName="silenceDuration" />
                </mat-slider>
                <glxy-hint>
                  {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.SILENCE_DURATION.DESCRIPTION' | translate }}
                </glxy-hint>
              </glxy-form-field>
            </div>
          }
        </cdk-accordion-item>
      </cdk-accordion>
    }
  }
</form>
