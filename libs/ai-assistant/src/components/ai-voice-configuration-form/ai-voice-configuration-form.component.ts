import { Component, computed, effect, inject, input, OnDestroy, signal, untracked } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { CdkAccordionModule } from '@angular/cdk/accordion';
import { MatSliderModule } from '@angular/material/slider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { CommonModule } from '@angular/common';
import {
  AiVoiceConfigurationForm,
  DeepgramConfigForm,
  ModelConfigForm,
  OpenAIRealtimeConfigForm,
} from '../../core/forms';
import { VendorModel } from '@vendasta/ai-assistants';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { getVoiceFamilies } from '../../core/services/ai-assistant-utils';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { filter, of, startWith, switchMap } from 'rxjs';
import { AiAssistantService } from '../../core/services/ai-assistant.service';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';

@Component({
  selector: 'ai-voice-configuration-form',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatSliderModule,
    CdkAccordionModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    AiAssistantI18nModule,
    GalaxyBadgeModule,
  ],
  templateUrl: './ai-voice-configuration-form.component.html',
  styleUrl: './ai-voice-configuration-form.component.scss',
})
export class AiVoiceConfigurationFormComponent implements OnDestroy {
  readonly voiceConfigForm = input.required<AiVoiceConfigurationForm>();

  VendorModel = VendorModel;
  private readonly aiAssistantService = inject(AiAssistantService);

  protected readonly voiceFamilies = computed(() => getVoiceFamilies(this.aiAssistantService.$isDeepgramEnabled()));

  private readonly voiceFamily$ = toObservable(this.voiceConfigForm).pipe(
    filter((form) => !!form?.controls?.voiceFamily),
    switchMap((form) => {
      const voiceFamilyControl = form?.controls?.voiceFamily;
      return voiceFamilyControl?.valueChanges.pipe(startWith(voiceFamilyControl?.value)) ?? of(null);
    }),
  );
  protected readonly voiceFamily = toSignal(this.voiceFamily$);

  protected readonly aiVoice = computed(() => {
    return this.voiceFamily()?.voices.find((voice) => voice.value === this.voice());
  });

  protected readonly vendorModel = computed(() => this.voiceFamily()?.provider);

  protected readonly modelConfig = computed(() => {
    return this.getModelConfig(this.voiceConfigForm()?.controls?.modelConfig, this.vendorModel() ?? null);
  });

  private readonly voice$ = toObservable(this.modelConfig).pipe(
    filter((modelConfig) => !!modelConfig?.controls?.voice),
    switchMap((modelConfig) => {
      const voiceControl = modelConfig?.controls?.voice;
      return voiceControl?.valueChanges.pipe(startWith(voiceControl?.value)) ?? of(null);
    }),
  );
  protected readonly voice = toSignal(this.voice$);

  protected readonly currentlyPlayingPreview = signal(false);
  private audioElement: HTMLAudioElement | null = null;

  private readonly previewAudio = computed(() => {
    const selectedVoice = this.voiceFamily()?.voices.find((voice) => voice.value === this.voice());
    return new Audio(selectedVoice?.previewAudioUrl || '');
  });

  constructor() {
    effect(() => {
      // When voice changes, stop any playing audio
      this.voice();
      untracked(() => {
        if (this.currentlyPlayingPreview()) {
          this.stopAudio();
        }
      });
    });

    effect(() => {
      // When the voice family changes
      const voiceFamily = this.voiceFamily();

      // update the voice value to a voice in that family if it isn't already
      const voiceControl = this.getModelConfig(
        this.voiceConfigForm()?.controls?.modelConfig,
        voiceFamily?.provider ?? null,
      )?.controls?.voice;

      if (!voiceFamily?.voices.some((aiVoice) => aiVoice.value === voiceControl?.value)) {
        voiceControl?.setValue(voiceFamily?.voices[0].value || '');
        voiceControl?.markAsDirty();
      }

      // and update the vendor model
      const vendorModelControl = this.voiceConfigForm()?.controls?.vendorModel;
      if (vendorModelControl) {
        vendorModelControl.setValue(voiceFamily?.provider ?? null);
        vendorModelControl.markAsDirty();
      }
    });
  }

  private getModelConfig(
    modelConfigForm: ModelConfigForm | undefined,
    vendorModel: VendorModel | null,
  ): OpenAIRealtimeConfigForm | DeepgramConfigForm | null {
    switch (vendorModel) {
      case VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME:
        return modelConfigForm?.controls?.openAIRealtimeConfig ?? null;
      case VendorModel.VENDOR_MODEL_DEEPGRAM:
        return modelConfigForm?.controls?.deepgramConfig ?? null;
      default:
        return null;
    }
  }

  protected togglePreview(): void {
    if (this.currentlyPlayingPreview()) {
      this.stopAudio();
    } else {
      this.playAudio();
    }
  }

  private playAudio(): void {
    this.stopAudio(); // Stop any existing audio first
    this.audioElement = this.previewAudio();

    if (this.audioElement) {
      this.audioElement.onended = () => this.currentlyPlayingPreview.set(false);
      this.audioElement
        .play()
        .then(() => this.currentlyPlayingPreview.set(true))
        .catch((error) => console.error('Error playing audio:', error));
    }
  }

  private stopAudio(): void {
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.currentTime = 0;
      this.audioElement.onended = null;
      this.audioElement = null;
    }
    this.currentlyPlayingPreview.set(false);
  }

  ngOnDestroy(): void {
    this.stopAudio();
  }
}
