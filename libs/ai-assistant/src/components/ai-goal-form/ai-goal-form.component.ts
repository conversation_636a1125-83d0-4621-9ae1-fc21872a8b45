import {
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  signal,
  ViewContainerRef,
  WritableSignal,
} from '@angular/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { ActivatedRoute, Router } from '@angular/router';
import { AiGoalService } from '../../core/services/ai-goal.service';
import { ACCOUNT_GROUP_ID_TOKEN, NAMESPACE_CONFIG_TOKEN, PARTNER_ID_TOKEN } from '../../core/tokens';
import { combineLatest, distinctUntilChanged, firstValueFrom, map, Observable, of, switchMap } from 'rxjs';
import { CommonModule } from '@angular/common';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FunctionInterface, Goal, GoalInterface, GoalType } from '@vendasta/ai-assistants';
import { MatSelectModule } from '@angular/material/select';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { MatChipsModule } from '@angular/material/chips';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { MatCardModule } from '@angular/material/card';
import { StickyFooterComponent } from '../sticky-footer/sticky-footer.component';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { MatInputModule } from '@angular/material/input';
import { getNamespace } from '../../core/services/ai-assistant-utils';
import { MatDialog } from '@angular/material/dialog';
import { AddFunctionDialogComponent } from './add-function-dialog/add-function-dialog.component';
import { AiFunctionsListComponent } from './ai-functions-list/ai-functions-list.component';
import { AiAssistantService } from '../../core/services/ai-assistant.service';
import { UnsavedChangesGuard } from '../../core/services/unsaved-changes.guard';
import { URLBuilderService } from '../../core/services/url-builder.service';

const FALLBACK_BACK_URL = '../../../assistants';

@Component({
  selector: 'ai-ai-goal-form',
  imports: [
    CommonModule,
    GalaxyPageModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatSelectModule,
    GalaxyBadgeModule,
    MatChipsModule,
    GalaxyAvatarModule,
    MatCardModule,
    StickyFooterComponent,
    AiAssistantI18nModule,
    FormsModule,
    MatInputModule,
    AiFunctionsListComponent,
  ],
  templateUrl: './ai-goal-form.component.html',
  styleUrl: './ai-goal-form.component.scss',
})
export class AiGoalFormComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly goalService = inject(AiGoalService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly pageService = inject(PageService);
  private readonly router = inject(Router);
  private readonly dialog = inject(MatDialog);
  private readonly assistantService = inject(AiAssistantService);
  private readonly unsavedChangesGuard = inject(UnsavedChangesGuard);
  private readonly namespaceConfig$ = inject(NAMESPACE_CONFIG_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly urlBuilder = inject(URLBuilderService);

  private readonly goalId$ = this.route.paramMap.pipe(
    map((params) => params.get('goalId')),
    distinctUntilChanged((prev, curr) => prev === curr && curr !== null),
  );

  protected readonly goal$ = combineLatest([this.goalId$, this.partnerId$, this.accountGroupId$]).pipe(
    switchMap(([goalId, partnerId, accountGroupId]) => {
      if (!goalId) {
        return of(new Goal());
      }
      return this.goalService.getGoal(accountGroupId, partnerId, goalId);
    }),
  );

  protected readonly promptModule$ = this.goal$.pipe(
    switchMap((goal) => {
      if (!goal.promptModules || goal.promptModules.length === 0) {
        return of(null);
      }
      return this.goalService.getPromptModule(goal.promptModules[0]);
    }),
  );

  protected readonly form = toSignal(
    combineLatest([this.goal$, this.promptModule$]).pipe(
      map(([goal, promptModule]) => this.initForm(goal, promptModule?.promptVersion?.content || '')),
    ),
  );

  protected readonly isLoading = computed(() => !this.form());
  protected readonly isSubmitting = signal(false);
  protected readonly hasUnsavedChanges = signal(false);
  protected readonly canSave = computed(() => !this.isLoading() && !this.isSubmitting() && this.hasUnsavedChanges());
  protected functions: WritableSignal<FunctionInterface[]> = signal([]);
  private viewContainerRef = inject(ViewContainerRef);

  constructor(private destroyRef: DestroyRef) {
    effect(() => {
      const form = this.form();
      if (form) {
        form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
          this.hasUnsavedChanges.set(true);
        });
        this.functions.set(form.get('functions')?.value);
      }
    });

    effect(() => {
      this.unsavedChangesGuard.notifyStateChanged(this.hasUnsavedChanges());
    });
  }

  private initForm(goal: GoalInterface, promptContent: string): FormGroup {
    return new FormGroup({
      name: new FormControl(goal?.name, [Validators.required]),
      description: new FormControl(goal?.description),
      prompt: new FormControl(promptContent || ''),
      functions: new FormControl(goal.functions || []),
    });
  }

  getGoalFromForm(formValue: any): Observable<GoalInterface> {
    return this.goal$.pipe(
      map((goal) => {
        const newGoal = { ...goal };
        newGoal.name = formValue.name || '';
        newGoal.description = formValue.description || '';
        newGoal.functions = formValue.functions || [];
        return newGoal;
      }),
    );
  }

  async openFunctionsModal(): Promise<void> {
    const res = await firstValueFrom(
      this.dialog
        .open(AddFunctionDialogComponent, {
          width: '900px',
          height: '600px',
          data: {
            excludedFunctionIds: this.functions().map((func) => func.id),
          },
          viewContainerRef: this.viewContainerRef,
        })
        .afterClosed(),
    );

    if (res.functions) {
      this.hasUnsavedChanges.set(true);
      this.form()?.markAsDirty();
      const functions = this.functions();
      this.functions.set([...functions, res.functions]);
      this.form()?.get('functions')?.setValue(this.functions());
    } else if (res.newFunction) {
      await this.router.navigate([await firstValueFrom(this.urlBuilder.buildNewFunctionUrl())]);
    }
  }

  removeFunction(funcId: string) {
    this.hasUnsavedChanges.set(true);
    this.form()?.markAsDirty();
    const funcs = this.functions().filter((func) => func.id !== funcId);
    this.functions.set(funcs);
    this.form()?.get('functions')?.setValue(this.functions());
  }

  async submit(): Promise<void> {
    if (!this.form()?.valid) {
      this.snackbarService.openErrorSnack('AI_ASSISTANT.GOALS.CONFIGURE_GOAL.FORM_INVALID');
      return;
    }

    this.isSubmitting.set(true);

    try {
      const [goal, promptModule, accountGroupId, partnerId] = await Promise.all([
        firstValueFrom(this.getGoalFromForm(this.form()?.value)),
        firstValueFrom(this.promptModule$),
        firstValueFrom(this.accountGroupId$),
        firstValueFrom(this.partnerId$),
      ]);

      const namespace = getNamespace(accountGroupId, partnerId);

      let promptModuleId = promptModule?.promptModule?.id;
      const promptContent = this.form()?.value.prompt;

      if (!promptModuleId) {
        promptModuleId = await firstValueFrom(
          this.goalService.createPromptModule(accountGroupId, partnerId, promptContent, goal.name, goal.description),
        );
        goal.promptModules = [
          {
            id: promptModuleId,
            namespace: namespace,
          },
        ];
      } else {
        // Update the prompt module with the same name and description as the goal for consistency
        // (prompt modules are hidden from the user, all they should see is that each goal has a prompt)
        await firstValueFrom(
          this.goalService.updatePromptModule(
            accountGroupId,
            partnerId,
            promptModuleId,
            goal.name || '',
            goal.description || '',
          ),
        );
        // And update to a new version with the new prompt module content
        await firstValueFrom(
          this.goalService.createPromptModuleVersion(accountGroupId, partnerId, promptModuleId, promptContent),
        );
      }

      if (!goal.id) {
        goal.namespace = namespace;
        goal.type = GoalType.GOAL_TYPE_CUSTOM;
        goal.id = await firstValueFrom(this.goalService.createGoal(goal));
        this.unsavedChangesGuard.notifyStateChanged(false);

        const assistantId = this.route.snapshot.queryParams['addToAssistant'];
        if (assistantId) {
          const configUrl = await firstValueFrom(this.assistantService.buildAssistantConfigurationUrl(assistantId));
          await this.router.navigate([configUrl], {
            queryParams: {
              addGoal: goal.id,
            },
          });
        } else {
          const goalUrl = await firstValueFrom(this.urlBuilder.goalConfigurationUrl(goal.id));
          await this.router.navigate([goalUrl], {
            replaceUrl: true,
          });
        }
      } else {
        await firstValueFrom(this.goalService.updateGoal(goal));
        this.unsavedChangesGuard.notifyStateChanged(false);
      }

      this.snackbarService.openSuccessSnack('AI_ASSISTANT.GOALS.CONFIGURE_GOAL.SAVE_SUCCESS');
    } catch (error) {
      this.snackbarService.openErrorSnack('AI_ASSISTANT.GOALS.CONFIGURE_GOAL.SAVE_ERROR');
    }
    this.isSubmitting.set(false);
  }

  protected cancel(): void {
    const previousUrl = this.pageService.getPreviousPageUrl(FALLBACK_BACK_URL);
    if (previousUrl == FALLBACK_BACK_URL) {
      this.router.navigate([FALLBACK_BACK_URL], { relativeTo: this.route });
    } else {
      this.pageService.pop();
      this.router.navigate([previousUrl]);
    }
  }

  async editFunction(functionId: string) {
    const functionUrl = await firstValueFrom(this.urlBuilder.editFunctionUrl(functionId));
    await this.router.navigate([functionUrl]);
  }
}
