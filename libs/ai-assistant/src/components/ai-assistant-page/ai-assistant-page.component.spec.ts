import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By, DomSanitizer } from '@angular/platform-browser';
import { AiAssistantPageComponent } from './ai-assistant-page.component';
import { TranslateService } from '@ngx-translate/core';
import { firstValueFrom, of } from 'rxjs';
import { LexiconModule } from '@galaxy/lexicon';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { DebugElement, Provider, signal } from '@angular/core';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_CONNECTIONS_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  MARKET_ID_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../core/tokens';
import { AiAssistant, AiConnection, ConnectionType } from '../../core/interfaces/ai-assistant.interface';
import { AiAssistantService } from '../../core/services/ai-assistant.service';
import { AssistantInterface, AssistantType, Connection } from '@vendasta/ai-assistants';
import { ActivatedRoute } from '@angular/router';
import { SmsDetailsService } from '../../core/services/sms-details.service';
import { ASSISTANT_ID_CHAT_RECEPTIONIST, ASSISTANT_ID_VOICE_RECEPTIONIST } from '../../core/ai-assistant.constants';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { MatIconRegistry } from '@angular/material/icon';
import { URLBuilderService } from '../../core/services/url-builder.service';

describe('AiAssistantPageComponent', () => {
  let component: AiAssistantPageComponent;
  let fixture: ComponentFixture<AiAssistantPageComponent>;

  async function initializeComponent(customProviders: Provider[] = []) {
    await TestBed.configureTestingModule({
      imports: [AiAssistantI18nModule, HttpClientTestingModule, LexiconModule.forRoot(), AiAssistantPageComponent],
      providers: [
        { provide: AiAssistantService, useValue: mockAiAssistantService },
        { provide: TranslateService, useValue: translationServiceMock },
        { provide: PARTNER_ID_TOKEN, useValue: of('partnerId') },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('accountGroupId') },
        { provide: MARKET_ID_TOKEN, useValue: of('marketId') },
        { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of(defaultWorkforce) },
        { provide: NAMESPACE_CONFIG_TOKEN, useValue: of({}) },
        { provide: AI_DEFAULT_CONNECTIONS_TOKEN, useValue: of([]) },
        { provide: ActivatedRoute, useValue: {} },
        { provide: SmsDetailsService },
        { provide: URLBuilderService },
        ...customProviders,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AiAssistantPageComponent);
    component = fixture.componentInstance;
    const iconRegistry = TestBed.inject(MatIconRegistry);
    const sanitizer = TestBed.inject(DomSanitizer);

    // Register the test icon
    iconRegistry.addSvgIcon('default-icon', sanitizer.bypassSecurityTrustResourceUrl('assets/icons/default-icon.svg'));
    fixture.detectChanges();
  }

  const mockAssistant: AiAssistant = {
    assistant: {
      id: ASSISTANT_ID_CHAT_RECEPTIONIST,
      name: 'Test Assistant',
      type: AssistantType.ASSISTANT_TYPE_INBOX,
      avatarUrl: 'https://example.com/avatar.png',
    },
    decoration: {
      gradientColor: '#000000',
    },
  };

  const defaultWorkforce: AiAssistant[] = [
    {
      assistant: {
        id: ASSISTANT_ID_CHAT_RECEPTIONIST,
        name: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CHAT_RECEPTIONIST.TITLE',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
      },
      descriptionKey: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CHAT_RECEPTIONIST.DESCRIPTION',
      decoration: {
        defaultAvatarIcon: 'default-icon',
        gradientColor: '#22C0CA',
      },
    },
    {
      assistant: {
        id: ASSISTANT_ID_VOICE_RECEPTIONIST,
        name: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.TITLE',
        type: AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST,
      },
      descriptionKey: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.DESCRIPTION',
      decoration: {
        defaultAvatarIcon: 'default-icon',
        gradientColor: '#A16EEF',
      },
      isDefault: true,
    },
  ];

  const translationServiceMock = {
    instant: (key: string) => key,
  } as TranslateService;

  const defaultInboxConnections = [
    {
      connection: new Connection({
        id: '1',
        name: 'Webchat',
        connectionTypeName: 'Webchat',
        iconUrl: 'https://vstatic-prod.apigateway.co/business-center-client/assets/webchat-icon.svg',
        connectionType: ConnectionType.WebchatWidget,
        supportedAssistantTypes: [AssistantType.ASSISTANT_TYPE_INBOX],
      }),
    } as AiConnection,
  ];

  const mockSmsDetailsService = {
    phoneNumber$: of('1234567890'),
  };

  const mockAiAssistantService = {
    // @ts-expect-error: private property on class
    translateService: translationServiceMock,
    $isCustomAssistantsEnabled: signal(true),
    $isVoiceAIAvailable: signal(true),
    $isPartnerVoiceReceptionistEnabled: signal(true),
    $defaultWorkforce: signal(defaultWorkforce),
    $isVoiceAiEnabled: signal(true),
    defaultWorkforce$: of(defaultWorkforce),
    listAssistants: jest.fn().mockReturnValue(of([mockAssistant.assistant])),
    hydrateAssistantWithDefaultInfo: jest
      .fn()
      .mockImplementation(AiAssistantService.prototype.hydrateAssistantWithDefaultInfo),
    cloneAssistant: jest.fn().mockImplementation(AiAssistantService.prototype.cloneAssistant),
    buildAssistantConfigurationUrl: jest.fn().mockReturnValue('/ai/assistants/test-id/edit'),
    listConnections: jest.fn().mockReturnValue(of(defaultInboxConnections)),
    buildCreateAssistantUrl: jest.fn().mockResolvedValue('/ai/assistants/create'),
  } satisfies Partial<AiAssistantService>;

  describe('initialization', () => {
    it('should create', async () => {
      await initializeComponent();
      await fixture.whenStable();
      expect(component).toBeTruthy();
    });
  });

  describe('UI initialization', () => {
    beforeEach(async () => {
      await initializeComponent([
        {
          provide: AiAssistantService,
          useValue: {
            ...mockAiAssistantService,
            hydrateAssistantFromInfo: jest.fn().mockReturnValue(mockAssistant),
          },
        },
      ]);
    });

    it('should render the glxy-page-toolbar title', async () => {
      await fixture.whenStable();
      const titleElement: DebugElement = fixture.debugElement.query(By.css('glxy-page-title'));
      expect(titleElement).toBeTruthy();
      expect(titleElement.nativeElement.textContent).toContain('AI Workforce');
    });

    it('should render ai-assistant-card components', async () => {
      await fixture.whenStable();
      const aiAssistantCards: DebugElement[] = fixture.debugElement.queryAll(By.css('ai-assistant-card'));
      expect(aiAssistantCards.length).toBe(2);
    });

    it('should render ai-assistant-card components with default data', async () => {
      mockAiAssistantService.defaultWorkforce$ = of([
        {
          assistant: {
            id: 'default-content-writer',
            name: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CONTENT_WRITER.TITLE',
            type: AssistantType.ASSISTANT_TYPE_SOCIAL_MARKETING,
            avatarUrl: 'https://example.com/avatar.png',
          },
        },
      ]);

      fixture = TestBed.createComponent(AiAssistantPageComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      await fixture.whenStable();
      const aiAssistantCards: DebugElement[] = fixture.debugElement.queryAll(By.css('ai-assistant-card'));
      expect(aiAssistantCards.length).toBe(2);
    });
  });

  //TODO: MEGA-1715: Implement tests following the AC described in the story
  //   describe('listNamespaceConnections', () => {
  //     it('should group connections by assistant ID', () => {
  //       const mockConnectionsMultiple = [
  //         {
  //           id: 'connection-1',
  //           assistantKeys: [{ id: 'assistant-1' }],
  //         },
  //         {
  //           id: 'connection-2',
  //           assistantKeys: [{ id: 'assistant-1' }],
  //         },
  //         {
  //           id: 'connection-3',
  //           assistantKeys: [{ id: 'assistant-2' }],
  //         },
  //       ] as Connection[];

  //       mockAiAssistantService.listConnections.mockReturnValue(of(mockConnectionsMultiple));

  //       component['listNamespaceConnections']().subscribe((result) => {
  //         expect(result.size).toBe(2);
  //         expect(result.get('assistant-1')?.length).toBe(2);
  //         expect(result.get('assistant-2')?.length).toBe(1);
  //       });
  //     });

  //     it('should handle empty connections list', () => {
  //       mockAiAssistantService.listConnections.mockReturnValue(of([]));

  //       component['listNamespaceConnections']().subscribe((result) => {
  //         expect(result.size).toBe(0);
  //       });
  //     });
  //   });

  describe('getAiWorkforce', () => {
    const mockAssistant: AssistantInterface = {
      id: ASSISTANT_ID_CHAT_RECEPTIONIST,
      type: AssistantType.ASSISTANT_TYPE_INBOX,
      name: 'James',
      avatarUrl: 'https://example.com/avatar.png',
    };

    const mockDefaultAssistant: AiAssistant = {
      assistant: {
        id: ASSISTANT_ID_CHAT_RECEPTIONIST,
        name: 'Chat Receptionist',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
      },
      connections: defaultInboxConnections,
    };

    it('should combine "real" and default assistants without duplicates', async () => {
      await initializeComponent([
        {
          provide: AI_DEFAULT_WORKFORCE_TOKEN,
          useValue: of([mockDefaultAssistant]),
        },
      ]);
      mockAiAssistantService.listAssistants.mockReturnValue(of([mockAssistant]));

      const result = await firstValueFrom(component['getAiWorkforce']());
      expect(result.length).toBe(1);
      const expectedAssistant = {
        ...mockDefaultAssistant,
        assistant: {
          ...mockDefaultAssistant.assistant,
          ...mockAssistant,
        },
        connections: defaultInboxConnections,
        isDefault: false,
        subtitleKey: 'Chat Receptionist',
      };
      expect(result[0]).toEqual(expectedAssistant);
    });

    it('should add default assistants when no custom assistant exists for that type', async () => {
      await initializeComponent([
        {
          provide: AI_DEFAULT_WORKFORCE_TOKEN,
          useValue: of([mockDefaultAssistant]),
        },
      ]);
      const mockDifferentTypeAssistant: AiAssistant = {
        assistant: {
          id: 'default-social-marketing',
          type: AssistantType.ASSISTANT_TYPE_UNSPECIFIED,
          name: 'Custom Support Assistant',
        },
        connections: [],
      };

      mockAiAssistantService.listAssistants.mockReturnValue(of([mockDifferentTypeAssistant.assistant]));

      const result = await firstValueFrom(component['getAiWorkforce']());
      expect(result.length).toBe(2);
      expect(result).toContainEqual(mockDifferentTypeAssistant);
      expect(result).toContainEqual({
        ...mockDefaultAssistant,
        connections: defaultInboxConnections,
      });
    });

    it('should handle empty custom assistants list', async () => {
      await initializeComponent([
        {
          provide: AI_DEFAULT_WORKFORCE_TOKEN,
          useValue: of([mockDefaultAssistant]),
        },
      ]);
      mockAiAssistantService.listAssistants.mockReturnValue(of([]));

      const result = await firstValueFrom(component['getAiWorkforce']());
      expect(result.length).toBe(1);
      expect(result[0]).toEqual(mockDefaultAssistant);
    });

    it('should add tryIt callback to voice receptionist assistant, without writing back to default workforce', async () => {
      await initializeComponent([
        {
          provide: AI_DEFAULT_WORKFORCE_TOKEN,
          useValue: of(defaultWorkforce),
        },
        {
          provide: SmsDetailsService,
          useValue: mockSmsDetailsService,
        },
      ]);
      mockAiAssistantService.listAssistants.mockReturnValue(of([]));
      const result = await firstValueFrom(component['getAiWorkforce']());
      expect(result.length).toBe(2);
      expect(
        result.find((assistant) => assistant.assistant.id === ASSISTANT_ID_VOICE_RECEPTIONIST)?.cta?.action,
      ).toBeDefined();
      expect(
        defaultWorkforce.find((assistant) => assistant.assistant.id === ASSISTANT_ID_VOICE_RECEPTIONIST)?.cta?.action,
      ).not.toBeDefined();
    });
  });
});
