<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>{{ 'AI_ASSISTANT.WORKFORCE.TITLE' | translate }}</glxy-page-title>
    @if (isCustomAssistantsEnabled$ | async) {
      <glxy-page-actions>
        <button mat-flat-button color="primary" [routerLink]="[$assistantUrl() | async]">
          {{ 'AI_ASSISTANT.WORKFORCE.CREATE_ASSISTANT' | translate }}
        </button>
      </glxy-page-actions>
    }
  </glxy-page-toolbar>
  <glxy-page-wrapper widthPreset="wide">
    @if ($fetching()) {
      <div class="spinner-container">
        <glxy-loading-spinner [size]="'large'"></glxy-loading-spinner>
      </div>
    } @else {
      <div class="row">
        @for (aiAssistant of aiWorkforce(); track aiAssistant) {
          <ai-assistant-card
            class="assistant-card col-xs-12 col-sm-6 col-lg-4"
            [aiAssistant]="aiAssistant"
            [showCTAButton]="!!aiAssistant.cta?.action?.showButton"
            (ctaClicked)="triggerAction(aiAssistant)"
            [isDisabled]="!!aiAssistant?.isDisabled"
            [isComingSoon]="!!aiAssistant?.isComingSoon"
          >
          </ai-assistant-card>
        }
      </div>
    }
  </glxy-page-wrapper>
</glxy-page>
