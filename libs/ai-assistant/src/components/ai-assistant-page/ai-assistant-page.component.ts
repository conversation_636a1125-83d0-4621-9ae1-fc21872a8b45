import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TranslateService } from '@ngx-translate/core';
import { AiAssistant, AiAssistantService, ASSISTANT_ID_VOICE_RECEPTIONIST, ConnectionType } from '../../core';
import { AiAssistantCardComponent } from '../ai-assistant-card/ai-assistant-card.component';
import { catchError, combineLatest, map, merge, Observable, of, switchMap } from 'rxjs';
import { convertAssistantTypeToDefaultID } from '../../core/services/ai-assistant-utils';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { AssistantType } from '@vendasta/ai-assistants';
import { SmsDetailsService } from '../../core/services/sms-details.service';
import { MatDialog } from '@angular/material/dialog';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../core/tokens';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { isNotNullOrUndefined } from '@vendasta/rx-utils';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { AiVoiceTryItModalComponent } from '../ai-voice-try-it-modal/ai-voice-try-it-modal.component';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MatButton } from '@angular/material/button';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';

@Component({
  selector: 'ai-assistant-page',
  imports: [
    CommonModule,
    GalaxyPageModule,
    AiAssistantCardComponent,
    AiAssistantI18nModule,
    GalaxyLoadingSpinnerModule,
    MatButton,
    RouterLink,
  ],
  templateUrl: './ai-assistant-page.component.html',
  styleUrl: './ai-assistant-page.component.scss',
})
export class AiAssistantPageComponent {
  protected readonly aiAssistantService = inject(AiAssistantService);
  protected readonly translateService = inject(TranslateService);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly defaultWorkforce$ = inject(AI_DEFAULT_WORKFORCE_TOKEN);
  private readonly namespaceConfig$ = inject(NAMESPACE_CONFIG_TOKEN);
  private matDialog = inject(MatDialog);
  private readonly router = inject(Router);
  smsDetailsService = inject(SmsDetailsService);
  private readonly phoneNumber$ = this.smsDetailsService.phoneNumber$;
  protected readonly isCustomAssistantsEnabled$ = toObservable(this.aiAssistantService.$isCustomAssistantsEnabled);
  private readonly isVoiceAIAvailable$ = toObservable(this.aiAssistantService.$isVoiceAIAvailable);
  private readonly route = inject(ActivatedRoute);
  private readonly aiIconService = inject(GalaxyAiIconService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly aiWorkforce$ = this.getAiWorkforce();
  private readonly fetching$ = merge(
    combineLatest([this.partnerId$, this.accountGroupId$]).pipe(map(() => true)),
    this.aiWorkforce$.pipe(map(() => false)),
  );

  protected readonly phoneNumber = toSignal(
    this.smsDetailsService.phoneNumber$.pipe(
      catchError((err) => {
        if (err.status !== 404) {
          this.snackbarService.openErrorSnack('Error getting phone number');
        }
        return of('');
      }),
    ),
  );

  protected readonly $fetching = toSignal(this.fetching$, {
    requireSync: true,
  });

  protected readonly aiWorkforce = toSignal(this.aiWorkforce$);

  protected readonly $assistantUrl = toSignal(of(this.aiAssistantService.buildCreateAssistantUrl()));

  constructor() {
    this.aiIconService.dummyInit();
  }

  private getAiWorkforce(): Observable<AiAssistant[]> {
    return combineLatest([
      this.accountGroupId$,
      this.isVoiceAIAvailable$,
      this.defaultWorkforce$,
      this.namespaceConfig$,
    ]).pipe(
      switchMap(([accountGroupId, isVoiceAIAvailable, defaultWorkforce, namespaceConfig]) => {
        return this.aiAssistantService.listAssistants(namespaceConfig.namespace).pipe(
          map((assistantRes) => {
            let assistants =
              assistantRes
                ?.map((assistant) =>
                  this.aiAssistantService.hydrateAssistantWithDefaultInfo(assistant, defaultWorkforce, accountGroupId),
                )
                ?.filter(isNotNullOrUndefined) ?? [];

            // Add default assistants if they are not already in the list of assistants allocated for this account
            defaultWorkforce.forEach((defaultAssistant) => {
              if (
                !assistants.find((assistant) => {
                  return (
                    convertAssistantTypeToDefaultID(
                      assistant.assistant.type ?? AssistantType.ASSISTANT_TYPE_UNSPECIFIED,
                    ) === defaultAssistant.assistant.id
                  );
                })
              ) {
                // Ensure we're not updating the default assistant by cloning it before
                const defaultAssistantCopy = this.aiAssistantService.cloneAssistant(defaultAssistant);

                defaultAssistantCopy.assistant.name = this.translateService.instant(
                  defaultAssistantCopy.assistant.name ?? '',
                );
                assistants.push(defaultAssistantCopy);
              }
            });
            if (!isVoiceAIAvailable) {
              assistants = assistants.filter((assistant) => {
                return assistant.assistant.id !== ASSISTANT_ID_VOICE_RECEPTIONIST;
              });
            }
            return assistants;
          }),
          switchMap((assistants) => {
            const connections$ = this.aiAssistantService.listConnections(namespaceConfig.namespace, true);
            return connections$.pipe(
              map((aiConnections) => {
                return assistants.map((assistant) => {
                  assistant.connections = aiConnections.filter((aiConnection) => {
                    return (
                      assistant.assistant.type !== undefined &&
                      aiConnection.connection.supportedAssistantTypes?.includes(assistant.assistant.type)
                    );
                  });
                  return assistant;
                });
              }),
            );
          }),
          map((assistants) =>
            assistants.map((assistant) => {
              if (!assistant.cta && assistant.computeCTA) {
                assistant.cta = assistant.computeCTA(assistant);
              }

              return assistant;
            }),
          ),
          map((assistants) => this.addVoiceReceptionistCTA(assistants)),
          catchError((e) => {
            console.error('error getting AI workforce', e);
            return of([]);
          }),
        );
      }),
    );
  }

  private addVoiceReceptionistCTA(assistants: AiAssistant[]): AiAssistant[] {
    return assistants.map((assistant) => {
      if (assistant.assistant.id === ASSISTANT_ID_VOICE_RECEPTIONIST && !assistant.cta && !assistant.isDisabled) {
        const clonedAssistant = this.aiAssistantService.cloneAssistant(assistant);
        if (this.phoneNumber()) {
          clonedAssistant.cta = {
            label: 'AI_ASSISTANT.WORKFORCE.TRY_IT',
            action: {
              callback: this.openVoiceReceptionistTryItModal.bind(this),
              showButton: true,
            },
          };
        } else if (
          !this.phoneNumber() &&
          clonedAssistant.connections?.some(
            (connection) => connection.connection.connectionType === ConnectionType.Voice,
          )
        ) {
          clonedAssistant.cta = {
            label: 'INBOX.SETTINGS.SMS_MESSAGES.CARD.CLAIM_PHONE_NUMBER',
            action: {
              pathCommands: ['/inbox/settings/'],
              showButton: true,
            },
          };
        }
        return clonedAssistant;
      }
      return assistant;
    });
  }

  protected async openVoiceReceptionistTryItModal(): Promise<void> {
    this.matDialog.open(AiVoiceTryItModalComponent);
  }

  protected triggerAction(aiAssistant: AiAssistant): void {
    if (aiAssistant) {
      this.buildAssistantActionCallback(aiAssistant)?.();
    }
  }

  protected buildAssistantActionCallback(aiAssistant: AiAssistant): (() => void) | undefined {
    if (!aiAssistant?.cta || !aiAssistant?.cta.action?.showButton) {
      return undefined;
    }

    const action = aiAssistant?.cta?.action;
    if (action?.url) {
      if (action.newTab && !action.relativeRoute) {
        return () => window.open(action.url, '_blank');
      }
      return () => this.router.navigate([action.url], action.relativeRoute ? { relativeTo: this.route } : undefined);
    }

    if (action?.pathCommands) {
      return () => this.router.navigate(action.pathCommands);
    }

    if (action?.callback) {
      return action.callback;
    }
    return undefined;
  }
}
