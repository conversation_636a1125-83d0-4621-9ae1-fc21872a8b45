import {
  Component,
  computed,
  DestroyRef,
  effect,
  forwardRef,
  inject,
  input,
  OnInit,
  output,
  signal,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatOption } from '@angular/material/autocomplete';
import { MatSelect } from '@angular/material/select';
import { CommonModule } from '@angular/common';
import { CdkAccordion, CdkAccordionItem } from '@angular/cdk/accordion';
import { MatCard, MatCardContent } from '@angular/material/card';
import { FunctionParameterParameterLocation } from '@vendasta/ai-assistants';
import { AiToolParameterForm } from '../../../core/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged, tap } from 'rxjs';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

export enum ParameterType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  INTEGER = 'integer',
  OBJECT = 'object',
  ARRAY = 'array',
}

@Component({
  selector: 'ai-tool-parameter-form',
  imports: [
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatIcon,
    MatIconButton,
    MatInput,
    MatOption,
    MatSelect,
    CommonModule,
    CdkAccordion,
    CdkAccordionItem,
    MatButton,
    MatCard,
    MatCardContent,
    TranslateModule,
    GalaxyTooltipModule,
  ],
  templateUrl: './ai-tool-parameter-form.component.html',
  styleUrl: './ai-tool-parameter-form.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AiToolParameterFormComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => AiToolParameterFormComponent),
      multi: true,
    },
  ],
})
export class AiToolParameterFormComponent implements OnInit {
  ParameterType = ParameterType;
  ParameterLocation = FunctionParameterParameterLocation;

  parameterLocationOptions = [
    { value: FunctionParameterParameterLocation.LOCATION_BODY, label: 'Request Body Parameter' },
    { value: FunctionParameterParameterLocation.LOCATION_QUERY_PARAM, label: 'Query Parameter' },
  ];

  private readonly destroyRef = inject(DestroyRef);

  readonly form = input.required<AiToolParameterForm>();
  readonly parentForm = input<AiToolParameterForm | undefined>();
  readonly remove = output();

  readonly parameterTypeOptions = computed(() => {
    const allParameterTypes = [
      { value: ParameterType.STRING, label: 'String' },
      { value: ParameterType.NUMBER, label: 'Number' },
      { value: ParameterType.BOOLEAN, label: 'Boolean' },
      { value: ParameterType.INTEGER, label: 'Integer' },
      { value: ParameterType.OBJECT, label: 'Object' },
      { value: ParameterType.ARRAY, label: 'Array' },
    ];

    // We do not allow object type in query params
    const removeObjectType = this.locationValue() === FunctionParameterParameterLocation.LOCATION_QUERY_PARAM;
    // We do not allow array types in query params
    const removeArrayType = this.locationValue() === FunctionParameterParameterLocation.LOCATION_QUERY_PARAM;

    return allParameterTypes.filter((type) =>
      type.value === ParameterType.OBJECT
        ? !removeObjectType
        : type.value === ParameterType.ARRAY
          ? !removeArrayType
          : true,
    );
  });

  // We need to react intelligently to a number of form controls - so we map their `valueChanges` observables to signals
  private locationValue = signal<FunctionParameterParameterLocation | null>(null);
  private parentLocationValue = signal<FunctionParameterParameterLocation | null | undefined>(undefined);
  private typeValue = signal<string | null>(null);

  constructor() {
    // Location should not be editable when type is object or array
    effect(() => {
      if (this.typeValue() === ParameterType.OBJECT || this.typeValue() === ParameterType.ARRAY) {
        this.form().controls.location.disable();
      } else if (this.form().enabled) {
        this.form().controls.location.enable();
      }

      if (this.typeValue() === ParameterType.ARRAY && this.form().controls.nestedParameters.length === 0) {
        this.form().controls.nestedParameters.push(this.generateChildParameterForm());
      }
    });
  }

  ngOnInit() {
    this.locationValue.set(this.form().controls.location.value);
    this.form()
      .controls.location.valueChanges.pipe(
        distinctUntilChanged(),
        tap((value) => this.locationValue.set(value)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.parentLocationValue.set(this.parentForm()?.controls.location.value);
    this.parentForm()
      ?.controls.location.valueChanges.pipe(
        distinctUntilChanged(),
        tap((value) => this.parentLocationValue.set(value)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.typeValue.set(this.form().controls.type.value);
    this.form()
      .controls.type.valueChanges.pipe(
        distinctUntilChanged(),
        tap((value) => this.typeValue.set(value)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  handleParamAdd() {
    this.form()?.controls.nestedParameters?.push(this.generateChildParameterForm());
  }

  handleParamRemove(index: number) {
    this.form()?.controls.nestedParameters?.removeAt(index);
  }

  generateChildParameterForm() {
    const form = new AiToolParameterForm({ location: this.form().controls.location.value ?? undefined });
    if (this.form().disabled) {
      form.disable();
    }

    return form;
  }
}
