import { Component, inject, input, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AiToolForm, AiToolFormArray } from '../../../core/forms';
import { MatDialog } from '@angular/material/dialog';
import {
  AiToolSelectDialogComponent,
  AiToolSelectDialogData,
  AiToolSelectDialogResult,
} from '../ai-tool-select-dialog/ai-tool-select-dialog.component';
import { v4 as uuidv4 } from 'uuid';
import { AiToolFormRegistryService } from '../../../core/services/ai-tool-form-registry.service';

@Component({
  selector: 'ai-tools-list',
  imports: [MatListModule, TranslateModule, MatButtonModule, MatIconModule],
  templateUrl: './ai-tools-list.component.html',
  styleUrl: './ai-tools-list.component.scss',
})
export class AiToolsListComponent {
  readonly forms = input.required<AiToolFormArray>();
  readonly showAddButton = input<boolean>(true);
  readonly toolFormDisplay = output<AiToolForm | undefined>();

  private readonly toolFormRegistry = inject(AiToolFormRegistryService);
  readonly addDialog = inject(MatDialog);

  handleToolRemoved(event: Event, idx: number) {
    event.stopPropagation();
    this.forms().removeAt(idx);
  }

  handleToolAdd(event: Event) {
    event.stopPropagation();

    const dialogRef = this.addDialog.open<
      AiToolSelectDialogComponent,
      AiToolSelectDialogData,
      AiToolSelectDialogResult
    >(AiToolSelectDialogComponent, {
      width: '900px',
      height: '600px',
      data: {
        excludedFunctionIds: this.forms()
          .controls.map((form) => form.controls.id.value)
          .filter((id) => id != null),
      },
    });

    dialogRef.afterClosed().subscribe((result: AiToolSelectDialogResult) => {
      if (!result) {
        return;
      }

      const { selectedFunction, newFunction } = result;

      if (selectedFunction) {
        // Use registry for existing tools with an ID
        const toolId = selectedFunction.id;
        if (toolId) {
          // Get or create a form instance from the registry
          const newToolForm = this.toolFormRegistry.getOrRegister(toolId, () => {
            return new AiToolForm({ func: selectedFunction });
          });
          this.forms().push(newToolForm);
        } else {
          // Fallback for tools without IDs (shouldn't happen for existing tools)
          const newToolForm = new AiToolForm({ func: selectedFunction });
          this.forms().push(newToolForm);
        }
      } else if (newFunction) {
        // For new tools, create with a localId
        const newToolForm = new AiToolForm({ metadata: { isNew: true, localId: uuidv4() } });
        this.toolFormDisplay.emit(newToolForm);
      }
    });
  }
}
