import { Component, inject, input } from '@angular/core';
import { AiToolForm } from '../../../core/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { AiToolHeaderFormArrayComponent } from '../ai-tool-header-form-array/ai-tool-header-form-array.component';
import { AiToolParameterFormArrayComponent } from '../ai-tool-parameter-form-array/ai-tool-parameter-form-array.component';
import {
  CurlImportArrayItem,
  CurlImportDialogComponent,
  CurlImportObject,
  CurlImportResult,
} from '../../ai-function-form/curl-import-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import {
  FunctionInterface,
  FunctionParameterInterface,
  FunctionParameterParameterLocation,
} from '@vendasta/ai-assistants';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Clipboard } from '@angular/cdk/clipboard';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

@Component({
  selector: 'ai-tool-form',
  imports: [
    AiToolHeaderFormArrayComponent,
    AiToolParameterFormArrayComponent,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    TranslateModule,
    MatButtonModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatIconModule,
    GalaxyTooltipModule,
  ],
  templateUrl: './ai-tool-form.component.html',
  styleUrl: './ai-tool-form.component.scss',
})
export class AiToolFormComponent {
  private readonly dialog = inject(MatDialog);
  private readonly snackbarService = inject(SnackbarService);
  private readonly clipboard = inject(Clipboard);

  readonly form = input.required<AiToolForm>();

  protected readonly httpMethods = Array.from<HttpMethod>(['GET', 'POST', 'PUT', 'PATCH', 'DELETE']);

  protected readonly commonHeaderOptions = [
    { label: 'Content-Type: JSON', header: { key: 'Content-Type', value: 'application/json' } },
    { label: 'Accept: JSON', header: { key: 'Accept', value: 'application/json' } },
    { label: 'Authorization', header: { key: 'Authorization', value: 'YOUR_TOKEN_HERE' } },
  ];

  openCurlImportDialog(): void {
    const dialogRef = this.dialog.open(CurlImportDialogComponent, {
      width: '600px',
      disableClose: false,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe((result: CurlImportResult) => {
      if (result) {
        const func = this.convertCurlImportToFunctionInterface(result);
        this.form().merge(new AiToolForm({ func: func }));
      }
    });
  }

  /**
   * Converts a CurlImportResult to a FunctionInterface
   */
  convertCurlImportToFunctionInterface(curlImport: CurlImportResult): FunctionInterface {
    // Prepare function parameters from body and query params
    const functionParameters: FunctionParameterInterface[] = [
      ...curlImport.bodyParams.map((param) =>
        this.convertCurlImportObjectToFunctionParameter(param, FunctionParameterParameterLocation.LOCATION_BODY),
      ),

      ...curlImport.queryParams.map(
        (param): FunctionParameterInterface => ({
          name: param.name,
          type: 'string',
          location: FunctionParameterParameterLocation.LOCATION_QUERY_PARAM,
        }),
      ),
    ];

    return {
      id: this.form().value.id || '',
      description: this.form().value.description || '',
      url: curlImport.url,
      methodType: curlImport.methodType,
      functionParameters,
      headers: curlImport.headers.map((header) => ({
        key: header.key,
        value: header.value,
      })),
    };
  }

  /**
   * Converts a CurlImportObject to a FunctionParameterInterface
   */
  convertCurlImportObjectToFunctionParameter(
    curlObject: CurlImportObject,
    location?: FunctionParameterParameterLocation,
  ): FunctionParameterInterface {
    const result: FunctionParameterInterface = {
      name: curlObject.name,
      type: curlObject.type,
      location,
    };

    if (curlObject.properties && curlObject.properties.length > 0) {
      result.properties = curlObject.properties.map((prop) =>
        this.convertCurlImportObjectToFunctionParameter(prop, location),
      );
    }

    if (curlObject.item) {
      result.items = this.convertCurlImportArrayItemToFunctionParameter(curlObject.item);
    }

    return result;
  }

  /**
   * Converts a CurlImportArrayItem to a FunctionParameterInterface
   */
  convertCurlImportArrayItemToFunctionParameter(arrayItem: CurlImportArrayItem): FunctionParameterInterface {
    const result: FunctionParameterInterface = {
      name: arrayItem.name,
      description: arrayItem.description,
      type: arrayItem.type,
      location: arrayItem.location,
    };

    if (arrayItem.properties && arrayItem.properties.length > 0) {
      result.properties = arrayItem.properties.map((prop) =>
        this.convertCurlImportObjectToFunctionParameter(prop, arrayItem.location),
      );
    }

    if (arrayItem.item) {
      result.items = this.convertCurlImportArrayItemToFunctionParameter(arrayItem.item);
    }

    return result;
  }

  /**
   * Creates a curl command from the tool and copies it to the clipboard
   */
  copyCurlCommand(): void {
    const form = this.form().value;
    let curlCommand = `curl -X ${form.method} `;

    if (form.headers && form.headers.length > 0) {
      form.headers.forEach((header: any) => {
        if (header.key && header.value) {
          curlCommand += `-H '${header.key}: ${header.value}' `;
        }
      });
    }

    const requestBodyArgs: any = {};
    const urlQueryArgs: any = {};

    const getDefaultValue = (param: any): any => {
      switch (param.type) {
        case 'integer':
          return 0;
        case 'number':
          return 0;
        case 'boolean':
          return false;
        case 'array':
          if (param.item) {
            return [getDefaultValue(param.item)];
          }
          return [];
        case 'object':
          if (param.nestedParameters && Array.isArray(param.nestedParameters)) {
            const obj: any = {};
            param.nestedParameters.forEach((prop: any) => {
              obj[prop.name] = getDefaultValue(prop);
            });
            return obj;
          }
          return {};
        case 'string':
        default:
          return '';
      }
    };

    if (form.parameters && form.parameters.length > 0) {
      form.parameters.forEach((param: any) => {
        if (param.location === FunctionParameterParameterLocation.LOCATION_QUERY_PARAM) {
          urlQueryArgs[param.name] = '';
        } else {
          requestBodyArgs[param.name] = getDefaultValue(param);
        }
      });
    }

    if (Object.keys(requestBodyArgs).length > 0) {
      curlCommand += `-d '${JSON.stringify(requestBodyArgs)}' `;
    }

    let url = form.url || '';
    if (Object.keys(urlQueryArgs).length > 0) {
      const searchParams = new URLSearchParams(urlQueryArgs).toString();
      url += `?${searchParams}`;
    }
    curlCommand += `'${url}'`;

    this.clipboard.copy(curlCommand);
    this.snackbarService.openSuccessSnack('Copied cURL command to clipboard');
  }
}
