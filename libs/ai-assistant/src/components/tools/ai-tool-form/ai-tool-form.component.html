<form [formGroup]="form()">
  <glxy-form-row>
    <glxy-form-field bottomSpacing="small">
      <glxy-label
        >{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.NAME' | translate }}
        <mat-icon class="info-icon" [glxyTooltip]="'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.NAME_HELP_TEXT' | translate">
          info_outline
        </mat-icon>
      </glxy-label>
      <input type="text" matInput formControlName="id" />
      @if (form().controls.id.errors) {
        <glxy-error>{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.NAME_PATTERN_ERROR' | translate }}</glxy-error>
      }
    </glxy-form-field>
  </glxy-form-row>
  <glxy-form-field>
    <glxy-label
      >{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.DESCRIPTION' | translate }}
      <mat-icon
        class="info-icon"
        [glxyTooltip]="'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.DESCRIPTION_HELP_TEXT' | translate"
      >
        info_outline
      </mat-icon>
    </glxy-label>
    <input type="text" matInput formControlName="description" />
  </glxy-form-field>
  <div class="button-container">
    <button mat-stroked-button (click)="openCurlImportDialog()">
      <mat-icon class="import-icon">code</mat-icon>
      {{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.GENERATE_FROM_CURL' | translate }}
    </button>
    <button mat-stroked-button (click)="copyCurlCommand()">
      <mat-icon class="copy-icon">content_copy</mat-icon>
      {{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.EXPORT_TO_CURL' | translate }}
    </button>
  </div>
  @if (!form().controls.generatesAnswer.value) {
    <glxy-form-row>
      <glxy-form-field class="col-xs-12 col-sm-3" bottomSpacing="small">
        <glxy-label
          >{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.METHOD' | translate }}
          <mat-icon
            class="info-icon"
            [glxyTooltip]="'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.METHOD_HELP_TEXT' | translate"
          >
            info_outline
          </mat-icon>
        </glxy-label>
        <mat-select formControlName="method">
          @for (option of httpMethods; track option) {
            <mat-option [value]="option">{{ option }}</mat-option>
          }
        </mat-select>
      </glxy-form-field>
      <glxy-form-field class="col-xs-12 col-sm-9" bottomSpacing="small">
        <glxy-label
          >{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.URL' | translate }}
          <mat-icon
            class="info-icon"
            [glxyTooltip]="'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.URL_HELP_TEXT' | translate"
          >
            info_outline
          </mat-icon>
        </glxy-label>
        <input
          type="text"
          matInput
          formControlName="url"
          placeholder="{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.URL_PLACEHOLDER' | translate }}"
        />
        @let urlErrors = form().controls.url.errors;
        @if (urlErrors && urlErrors['invalidHttpUrl']) {
          <glxy-error>{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.URL_ERROR' | translate }}</glxy-error>
        } @else {
          <glxy-hint>{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.URL_HINT' | translate }}</glxy-hint>
        }
      </glxy-form-field>
    </glxy-form-row>

    <div class="parameters-header">
      <h4>
        {{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.HEADERS' | translate }}
        <mat-icon
          class="info-icon"
          [glxyTooltip]="'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.HEADERS_HELP_TEXT' | translate"
        >
          info_outline
        </mat-icon>
      </h4>
    </div>
    <ai-tool-header-form-array [forms]="form().controls.headers" />
  }
  <div class="parameters-header">
    <h4>{{ 'AI_ASSISTANT.FUNCTIONS.CREATE_FUNCTION.PARAMETERS' | translate }}</h4>
  </div>
  <ai-tool-parameter-form-array [forms]="form().controls.parameters" />
</form>
