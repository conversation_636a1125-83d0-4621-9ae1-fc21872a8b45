import { CommonModule } from '@angular/common';
import { Component, computed, effect, ElementRef, inject, input } from '@angular/core';

import { MatButtonModule } from '@angular/material/button';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { Router, RouterLink } from '@angular/router';
import { AiAssistantService } from '../../core/services/ai-assistant.service';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { Assistant } from '@vendasta/ai-assistants';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ACCOUNT_GROUP_ID_TOKEN } from '../../core/tokens';
import { of, switchMap } from 'rxjs';

@Component({
  selector: 'ai-assistant-reference-card',
  imports: [CommonModule, GalaxyAvatarModule, RouterLink, MatButtonModule, AiAssistantI18nModule],
  templateUrl: './ai-assistant-reference-card.component.html',
  styleUrl: './ai-assistant-reference-card.component.scss',
})
export class AiAssistantReferenceCardComponent {
  readonly assistant = input.required<Assistant>();
  readonly accountGroupId = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN));
  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly router = inject(Router);
  protected readonly currentUrl = this.router.url;
  defaultWorkforce = toSignal(this.aiAssistantService.defaultWorkforce$, { initialValue: [] });

  aiAssistant = computed(() =>
    this.aiAssistantService.hydrateAssistantWithDefaultInfo(
      this.assistant(),
      this.defaultWorkforce(),
      this.accountGroupId() ?? '',
    ),
  );
  gradientColor = computed(() => this.aiAssistant()?.decoration?.gradientColor);

  readonly aiAssistantConfigUrl = toSignal(
    toObservable(this.assistant).pipe(
      switchMap((assistant) => {
        const id = assistant?.id;
        return id ? this.aiAssistantService.buildAssistantConfigurationUrl(id) : of('');
      }),
    ),
    { initialValue: '' },
  );

  constructor(private elementRef: ElementRef) {
    const element = this.elementRef.nativeElement;
    effect(() => {
      element.style.setProperty('--gradient-start-color', this.gradientColor());
    });
  }
}
