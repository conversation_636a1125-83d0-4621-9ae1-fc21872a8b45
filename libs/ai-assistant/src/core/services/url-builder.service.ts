import { inject, Injectable } from '@angular/core';
import { NAMESPACE_CONFIG_TOKEN } from '../tokens';
import { distinctUntilChanged, map, Observable } from 'rxjs';
@Injectable()
export class URLBuilderService {
  private readonly namespaceConfig$ = inject(NAMESPACE_CONFIG_TOKEN);

  private root$: Observable<string> = this.namespaceConfig$.pipe(
    map((config) => config?.root ?? ''),
    distinctUntilChanged(),
  );

  goalConfigurationUrl(goalId: string): Observable<string> {
    return this.root$.pipe(map((root) => `${root}/goals/${goalId}/edit`));
  }

  newGoalUrl(): Observable<string> {
    return this.root$.pipe(map((root) => `${root}/goals/create`));
  }

  editFunctionUrl(functionId: string): Observable<string> {
    return this.root$.pipe(map((root) => `${root}/functions/${functionId}/edit`));
  }

  assistantConfigurationUrl(assistantId: string, v2 = false): Observable<string> {
    return this.root$.pipe(map((root) => `${root}/assistants/${assistantId}/edit${v2 ? '-v2' : ''}`));
  }

  createAssistantUrl(): Observable<string> {
    return this.root$.pipe(map((root) => `${root}/assistants/create`));
  }

  aiKnowledgeAppConfigurationUrl(assistantId: string): Observable<string> {
    return this.root$.pipe(map((root) => `${root}/assistants/${assistantId}/edit`));
  }

  buildNewFunctionUrl(): Observable<string> {
    return this.root$.pipe(map((root) => `${root}/functions/create`));
  }
}
