import { inject, Injectable } from '@angular/core';
import {
  FunctionApiService,
  GoalApiService,
  GoalInterface,
  Namespace,
  PromptModuleApiService,
  PromptModuleInterface,
  PromptVersion,
} from '@vendasta/ai-assistants';
import { getNamespace } from './ai-assistant-utils';
import { map, Observable } from 'rxjs';
import { HttpResponse } from '@angular/common/http';
import { PromptModuleKeyInterface } from '@vendasta/ai-assistants/lib/_internal/interfaces/prompt.interface';

@Injectable({
  providedIn: 'root',
})
export class AiGoalService {
  private readonly goalApiService = inject(GoalApiService);
  private readonly promptModuleApiService = inject(PromptModuleApiService);
  private readonly functionApiService = inject(FunctionApiService);

  getGoal(accountGroupId: string, partnerId: string, goalId: string): Observable<GoalInterface> {
    return this.goalApiService
      .get({
        id: goalId,
        namespace: getNamespace(accountGroupId, partnerId),
      })
      .pipe(map((response) => response?.goal));
  }

  getGoals(
    namespaces: Namespace[],
    cursor?: string,
  ): Observable<{
    goals: GoalInterface[];
    nextCursor: string;
    hasMore: boolean;
  }> {
    return this.goalApiService
      .list({
        filters: {
          namespaces: namespaces,
        },
        pagingOptions: {
          cursor,
          pageSize: 25,
        },
      })
      .pipe(
        map((response) => ({
          goals: response?.goals,
          nextCursor: response?.metadata?.nextCursor,
          hasMore: response?.metadata?.hasMore,
        })),
      );
  }

  getPromptModule(promptModuleId: PromptModuleKeyInterface): Observable<{
    promptModule: PromptModuleInterface;
    promptVersion: PromptVersion;
  }> {
    return this.promptModuleApiService
      .getDeployedVersion({
        id: promptModuleId.id,
        namespace: promptModuleId.namespace,
      })
      .pipe(
        map((response) => ({
          promptModule: response?.promptModule,
          promptVersion: response?.deployedPromptModuleVersion,
        })),
      );
  }

  createPromptModule(
    accountGroupId: string,
    partnerId: string,
    content: string,
    name?: string,
    description?: string,
  ): Observable<string> {
    return this.promptModuleApiService
      .create({
        namespace: getNamespace(accountGroupId, partnerId),
        name: name,
        description: description,
        content: content,
      })
      .pipe(map((response) => response?.id));
  }

  updatePromptModule(
    accountGroupId: string,
    partnerId: string,
    promptModuleId: string,
    name: string,
    description: string,
  ): Observable<HttpResponse<null>> {
    return this.promptModuleApiService.update({
      id: promptModuleId,
      namespace: getNamespace(accountGroupId, partnerId),
      name,
      description,
    });
  }

  createPromptModuleVersion(
    accountGroupId: string,
    partnerId: string,
    promptModuleId: string,
    content: string,
  ): Observable<HttpResponse<null>> {
    return this.promptModuleApiService.createVersion({
      id: promptModuleId,
      namespace: getNamespace(accountGroupId, partnerId),
      content: content,
      options: {
        shouldDeploy: true,
      },
    });
  }

  updateGoal(goal: GoalInterface): Observable<HttpResponse<null>> {
    return this.goalApiService.update({ goal });
  }

  createGoal(goal: GoalInterface): Observable<string> {
    return this.goalApiService.create({ goal }).pipe(map((response) => response?.id));
  }
}
