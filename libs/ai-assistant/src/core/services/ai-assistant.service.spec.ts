import { AiAssistantService } from './ai-assistant.service';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_CONNECTIONS_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  MARKET_ID_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
  VOICE_AI_AVAILABLE_TOKEN,
} from '../tokens';
import { of } from 'rxjs';
import {
  Assistant,
  AssistantApiService,
  AssistantType,
  Connection,
  ConnectionApiService,
  Namespace,
} from '@vendasta/ai-assistants';
import { TranslateService } from '@ngx-translate/core';
import { AiAssistant } from '../interfaces/ai-assistant.interface';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { ASSISTANT_ID_CHAT_RECEPTIONIST } from '../../core/ai-assistant.constants';
import { Router } from '@angular/router';
import { getNamespace } from './ai-assistant-utils';
import { URLBuilderService } from './url-builder.service';

describe('AiAssistantService', () => {
  let spectator: SpectatorService<AiAssistantService>;

  const mockAssistant = {
    id: ASSISTANT_ID_CHAT_RECEPTIONIST,
    name: 'Test Assistant',
    type: AssistantType.ASSISTANT_TYPE_INBOX,
  } as Assistant;

  const assistantApiMock = {
    listAssistant: jest.fn().mockReturnValue(of({ assistants: [mockAssistant] })),
    getAssistant: jest.fn().mockReturnValue(of({ assistant: mockAssistant })),
    listAllAssistantsAssociatedToConnection: jest.fn().mockReturnValue(of({ assistants: [mockAssistant] })),
  };

  const connectionApiMock = {
    getConnection: jest.fn().mockReturnValue(of({ connection: {} as Connection })),
    listConnections: jest.fn().mockReturnValue(of({ connections: [] })),
  };

  const routerMock = {
    navigate: jest.fn(),
  };

  const translateServiceMock = {
    instant: jest.fn().mockReturnValue('Translated Name'),
  };

  const createService = createServiceFactory({
    service: AiAssistantService,
    providers: [
      { provide: PARTNER_ID_TOKEN, useValue: of('ABC') },
      { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-123') },
      { provide: MARKET_ID_TOKEN, useValue: of('market-id') },
      { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of([]) },
      { provide: AI_DEFAULT_CONNECTIONS_TOKEN, useValue: of([]) },
      { provide: VOICE_AI_AVAILABLE_TOKEN, useValue: of(true) },
      { provide: Router, useValue: routerMock },
      { provide: AssistantApiService, useValue: assistantApiMock },
      { provide: ConnectionApiService, useValue: connectionApiMock },
      { provide: TranslateService, useValue: translateServiceMock },
      { provide: HttpClient },
      { provide: HttpHandler },
      { provide: NAMESPACE_CONFIG_TOKEN, useValue: of([]) },
      { provide: URLBuilderService },
    ],
  });

  describe('ListAssistants', () => {
    it('should return assistants list', (done) => {
      spectator = createService();
      spectator.service
        .listAssistants(new Namespace({ accountGroupNamespace: { accountGroupId: 'AG-123' } }))
        .subscribe((assistants) => {
          expect(assistants).toEqual([mockAssistant]);
          expect(assistantApiMock.listAssistant).toHaveBeenCalled();
          done();
        });
    });

    it('should throw error when no namespace is available', () => {
      spectator = createService();
      expect(() => spectator.service.listAssistants('', '')).toThrow('No account group or partner ID found');
    });
  });

  describe('getAssistantWithDefaultInfo', () => {
    const customDefaultWorkforce = [
      {
        assistant: {
          id: ASSISTANT_ID_CHAT_RECEPTIONIST,
          name: 'Default Name',
          type: AssistantType.ASSISTANT_TYPE_INBOX,
        },
      },
    ] as AiAssistant[];

    const createServiceWithDefaults = createServiceFactory({
      service: AiAssistantService,
      providers: [
        { provide: PARTNER_ID_TOKEN, useValue: of('ABC') },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-123') },
        { provide: MARKET_ID_TOKEN, useValue: of('market-id') },
        { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of(customDefaultWorkforce) },
        { provide: AI_DEFAULT_CONNECTIONS_TOKEN, useValue: of([]) },
        { provide: VOICE_AI_AVAILABLE_TOKEN, useValue: of(true) },
        { provide: Router, useValue: routerMock },
        { provide: AssistantApiService, useValue: assistantApiMock },
        { provide: ConnectionApiService, useValue: connectionApiMock },
        { provide: TranslateService, useValue: translateServiceMock },
        { provide: HttpClient },
        { provide: HttpHandler },
      ],
    });

    beforeEach(() => {
      spectator = createServiceWithDefaults();
    });

    it('should return empty assistant when input is null', () => {
      const result = spectator.service.hydrateAssistantWithDefaultInfo(
        null as unknown as Assistant,
        customDefaultWorkforce,
        '',
      );
      expect(result).toEqual(null);
    });

    it('should merge assistant with default info', () => {
      const result = spectator.service.hydrateAssistantWithDefaultInfo(mockAssistant, customDefaultWorkforce, '');
      expect(result).not.toBeNull();
      expect(result!.assistant).toEqual(mockAssistant);
      expect(result!.subtitleKey).toBe('Default Name');
    });
  });
});

describe('getNamespace', () => {
  it('should return Namespace with accountGroupId when accountGroupId is provided', () => {
    const accountGroupId = 'AG-123';
    const result = getNamespace(accountGroupId, undefined);
    expect(result).toEqual(new Namespace({ accountGroupNamespace: { accountGroupId } }));
  });

  it('should return Namespace with partnerId when partnerId is provided', () => {
    const partnerId = 'P-456';
    const result = getNamespace(undefined, partnerId);
    expect(result).toEqual(new Namespace({ partnerNamespace: { partnerId } }));
  });

  it('should return undefined when neither accountGroupId nor partnerId is provided', () => {
    const result = getNamespace(undefined, undefined);
    expect(result).toBeUndefined();
  });
  it('should return System namespace when neither Vendasta partner id is provided', () => {
    const partnerId = 'VA';
    const result = getNamespace(undefined, partnerId);
    expect(result).toEqual(new Namespace({ systemNamespace: {} }));
  });
});
