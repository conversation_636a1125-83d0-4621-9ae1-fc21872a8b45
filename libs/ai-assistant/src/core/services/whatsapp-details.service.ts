import { inject, Injectable } from '@angular/core';
import { catchError, combineLatest, map, of, shareReplay, switchMap } from 'rxjs';
import { FacebookApiService } from '@vendasta/facebook';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../tokens';

@Injectable()
export class WhatsappDetailsService {
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly facebookAPI = inject(FacebookApiService);

  public readonly phoneNumber$ = combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
    switchMap(([partnerId, accountGroupId]) => {
      if (!partnerId || !accountGroupId) {
        return of(null);
      }
      return this.facebookAPI.getWhatsAppConnection({
        organizationId: accountGroupId || partnerId,
        includeStatus: true,
      });
    }),
    catchError((err) => {
      if (err.status !== 404) {
        console.error('Error fetching WhatsApp connection:', err);
      }
      return of(null);
    }),
    map((response) => {
      return response?.status?.displayPhoneNumber;
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );
}
