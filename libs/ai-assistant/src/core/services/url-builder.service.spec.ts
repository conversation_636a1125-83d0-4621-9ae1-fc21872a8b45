import { URLBuilderService } from './url-builder.service';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator';
import { NAMESPACE_CONFIG_TOKEN } from '../tokens';
import { BehaviorSubject, firstValueFrom, Observable } from 'rxjs';

describe('URLBuilderService', () => {
  let spectator: SpectatorService<URLBuilderService>;
  const namespaceConfig$ = new BehaviorSubject<{ root: string } | null>({ root: 'https://example.com' });

  const createService = createServiceFactory({
    service: URLBuilderService,
    providers: [{ provide: NAMESPACE_CONFIG_TOKEN, useValue: namespaceConfig$ }],
  });

  beforeEach(() => {
    spectator = createService();
  });

  describe('URL builders', () => {
    type GoalConfigParams = [goalId: string];
    type NoParams = [];
    type FunctionParams = [functionId: string];
    type AssistantConfigParams = [assistantId: string, v2?: boolean];
    type KnowledgeAppParams = [assistantId: string];

    type MethodParams = {
      goalConfigurationUrl: GoalConfigParams;
      newGoalUrl: NoParams;
      editFunctionUrl: FunctionParams;
      assistantConfigurationUrl: AssistantConfigParams;
      createAssistantUrl: NoParams;
      aiKnowledgeAppConfigurationUrl: KnowledgeAppParams;
    };

    interface UrlTestCase<M extends keyof MethodParams> {
      method: M;
      params: MethodParams[M];
      expectedUrl: string;
    }

    function createTestCase<M extends keyof MethodParams>(
      method: M,
      params: MethodParams[M],
      expectedUrl: string,
    ): UrlTestCase<M> {
      return { method, params, expectedUrl };
    }

    const testCases = [
      createTestCase('goalConfigurationUrl', ['goal-123'], 'https://example.com/goals/goal-123/edit'),
      createTestCase('newGoalUrl', [], 'https://example.com/goals/create'),
      createTestCase('editFunctionUrl', ['function-123'], 'https://example.com/functions/function-123/edit'),
      createTestCase(
        'assistantConfigurationUrl',
        ['assistant-123', false],
        'https://example.com/assistants/assistant-123/edit',
      ),
      createTestCase(
        'assistantConfigurationUrl',
        ['assistant-123', true],
        'https://example.com/assistants/assistant-123/edit-v2',
      ),
      createTestCase('createAssistantUrl', [], 'https://example.com/assistants/create'),
      createTestCase(
        'aiKnowledgeAppConfigurationUrl',
        ['assistant-123'],
        'https://example.com/assistants/assistant-123/edit',
      ),
    ];

    testCases.forEach((testCase) => {
      it(`should build correct URL for ${String(testCase.method)}`, async () => {
        namespaceConfig$.next({ root: 'https://example.com' });
        const result = await firstValueFrom(applyMethodWithParams(spectator.service, testCase.method, testCase.params));
        expect(result).toBe(testCase.expectedUrl);
      });
    });

    testCases.forEach((testCase) => {
      it(`should return URL without root prefix when root is not configured for ${String(testCase.method)}`, async () => {
        namespaceConfig$.next(null);
        const result = await firstValueFrom(applyMethodWithParams(spectator.service, testCase.method, testCase.params));
        expect(result).not.toContain('https://example.com');
      });
    });

    function applyMethodWithParams<M extends keyof MethodParams>(
      service: URLBuilderService,
      method: M,
      params: MethodParams[M],
    ): Observable<string> {
      switch (method) {
        case 'goalConfigurationUrl':
          return service.goalConfigurationUrl(...(params as GoalConfigParams));
        case 'newGoalUrl':
          return service.newGoalUrl();
        case 'editFunctionUrl':
          return service.editFunctionUrl(...(params as FunctionParams));
        case 'assistantConfigurationUrl':
          return service.assistantConfigurationUrl(...(params as AssistantConfigParams));
        case 'createAssistantUrl':
          return service.createAssistantUrl();
        case 'aiKnowledgeAppConfigurationUrl':
          return service.aiKnowledgeAppConfigurationUrl(...(params as KnowledgeAppParams));
        default:
          throw new Error(`Unknown method: ${method}`);
      }
    }
  });
});
