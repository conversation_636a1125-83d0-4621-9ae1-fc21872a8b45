import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  ConfigurableGoalInterface,
  GoalInterface,
  GoalType,
  KeyValuePair,
  NamespaceInterface,
} from '@vendasta/ai-assistants';
import { AiPromptModuleFormArray } from './ai-prompt-module.form';
import { AiToolFormArray } from './ai-tools.form';
import { FormArrayWithDirtyTracking } from './forms';
import { AiToolFormRegistryService } from '../services/ai-tool-form-registry.service';
import { HIDDEN_GOAL_IDS } from '../ai-assistant.constants';
import { Directive, input } from '@angular/core';

export type CapabilityConfigurationFormControl = FormControl<KeyValuePair[] | null>;

// Capabilities
export type AiCapabilityFormControls = {
  goalId: FormControl<string | null>;
  namespace: FormControl<NamespaceInterface | null>;
  name: FormControl<string | null>;
  description: FormControl<string | null>;
  type: FormControl<GoalType | null>;
  promptModules: AiPromptModuleFormArray;
  tools: AiToolFormArray;
  configuration: CapabilityConfigurationFormControl;
};

export class AiCapabilityForm extends FormGroup<AiCapabilityFormControls> {
  constructor(
    goal?: ConfigurableGoalInterface,
    promptModuleContents?: Record<string, string>,
    toolFormRegistry?: AiToolFormRegistryService,
  ) {
    const capability = goal?.goal ?? null;
    const keyValuePairs = goal?.configuration
      ?.map((v): KeyValuePair => {
        if (v instanceof KeyValuePair) {
          return v;
        }
        return new KeyValuePair(v);
      })
      ?.filter((v) => Boolean(v.key && v.value));

    super({
      goalId: new FormControl({ value: capability?.id ?? null, disabled: true }),
      namespace: new FormControl({ value: capability?.namespace ?? null, disabled: true }),
      name: new FormControl(capability?.name ?? null, Validators.required),
      description: new FormControl(capability?.description ?? null),
      type: new FormControl({ value: capability?.type ?? GoalType.GOAL_TYPE_CUSTOM, disabled: true }),
      promptModules: new AiPromptModuleFormArray({
        promptModules: capability?.promptModules || [],
        contents: promptModuleContents || {},
      }),
      tools: new AiToolFormArray(capability?.functions || [], toolFormRegistry),
      configuration: new FormControl<KeyValuePair[] | null>(keyValuePairs ?? null),
    });

    if (
      (capability?.managed || capability?.namespace?.globalNamespace) &&
      capability?.type !== GoalType.GOAL_TYPE_PERSONALITY
    ) {
      this.disable();
    }
    this.controls.configuration.enable();
  }

  toConfigurableGoal(fallbackNamespace: NamespaceInterface | undefined): ConfigurableGoalInterface {
    return {
      goal: {
        id: this.controls.goalId.getRawValue() ?? undefined,
        name: this.controls.name.getRawValue() ?? undefined,
        namespace: this.controls.namespace.getRawValue() ?? fallbackNamespace,
        description: this.controls.description.getRawValue() ?? undefined,
        type: this.controls.type.getRawValue() ?? undefined,
        functions: this.controls.tools.toFunctions(fallbackNamespace),
      },
      configuration: this.controls.configuration.getRawValue() ?? undefined,
    };
  }

  toGoal(fallbackNamespace: NamespaceInterface): GoalInterface {
    return {
      id: this.controls.goalId.getRawValue() ?? undefined,
      name: this.controls.name.getRawValue() ?? undefined,
      namespace: this.controls.namespace.getRawValue() ?? fallbackNamespace,
      description: this.controls.description.getRawValue() ?? undefined,
      type: this.controls.type.getRawValue() ?? undefined,
      functions: this.controls.tools.toFunctionKeys(fallbackNamespace),
      promptModules: this.controls.promptModules.getPromptModuleKeys(fallbackNamespace),
    };
  }

  updatedPromptModules(): Array<{ id: string | undefined; content: string | undefined }> {
    return this.controls.promptModules.updatedPromptModules();
  }

  isHidden(): boolean {
    return HIDDEN_GOAL_IDS.includes(this.controls.goalId.getRawValue() ?? '') || this.controls.type.getRawValue() === GoalType.GOAL_TYPE_PERSONALITY;
  }
}

export class AiCapabilityFormArray extends FormArrayWithDirtyTracking<AiCapabilityForm> {
  constructor(
    capabilities: ConfigurableGoalInterface[],
    promptModuleContents: Record<string, string>,
    toolFormRegistry?: AiToolFormRegistryService,
  ) {
    super(capabilities.map((capability) => new AiCapabilityForm(capability, promptModuleContents, toolFormRegistry)));
  }

  toConfigurableGoals(fallbackNamespace: NamespaceInterface | undefined): ConfigurableGoalInterface[] {
    return this.controls.map((capabilityForm) => capabilityForm.toConfigurableGoal(fallbackNamespace));
  }

  updatedPromptModules(): Array<{ id: string | undefined; content: string | undefined }> {
    return this.controls.flatMap((capabilityForm) => capabilityForm.updatedPromptModules());
  }
}

@Directive()
export abstract class CustomCapabilityConfigurationForm {
  configurationForm = input.required<CapabilityConfigurationFormControl>();
}
