import { FormControl, FormGroup } from '@angular/forms';
import { NamespaceInterface, PromptModuleInterface } from '@vendasta/ai-assistants';
import { FormArrayWithDirtyTracking } from './forms';

// Prompt Module
export type AiPromptModuleControls = {
  id: FormControl<string | null>;
  namespace: FormControl<NamespaceInterface | null>;
  instructions: FormControl<string | null>;
};

export class AiPromptModuleForm extends FormGroup<AiPromptModuleControls> {
  private originalContents: string | undefined;

  constructor(opts?: { promptModule?: PromptModuleInterface; contents?: string }) {
    const { promptModule, contents } = opts || {};

    super({
      id: new FormControl({ value: promptModule?.id ?? null, disabled: true }),
      namespace: new FormControl({ value: promptModule?.namespace ?? null, disabled: true }),
      instructions: new FormControl(contents ?? null),
    });
    this.originalContents = contents;

    if (
      (promptModule?.managed || promptModule?.namespace?.globalNamespace) &&
      !['DefaultPersonality', 'DefaultVoicePersonality'].includes(promptModule?.id || '')
    ) {
      this.disable();
    }
  }

  override get dirty() {
    const currentValue = this.controls.instructions.getRawValue();
    // Treat falsy values the same (yes we could do != instead, but this way it's harder for someone to come along and change it by accident)
    return (currentValue || null) !== (this.originalContents || null);
  }

  override markAsPristine(): void {
    super.markAsPristine();
    const value = this.controls.instructions.getRawValue();
    this.originalContents = value === null ? undefined : value;
  }
}

export class AiPromptModuleFormArray extends FormArrayWithDirtyTracking<AiPromptModuleForm> {
  constructor(opts: { promptModules: PromptModuleInterface[]; contents: Record<string, string> }) {
    const { promptModules, contents } = opts;

    const controls = promptModules.map((pm) => {
      const content = pm.id ? contents[pm.id] : undefined;
      return new AiPromptModuleForm({ promptModule: pm, contents: content });
    });

    if (controls.length === 0) {
      controls.push(new AiPromptModuleForm());
    }
    super(controls);
  }

  updatedPromptModules(): Array<{ id: string | undefined; content: string | undefined }> {
    return this.controls
      .filter((pm) => pm.dirty)
      .map((pm) => {
        const id = pm.controls.id.getRawValue() ?? undefined;
        const content = pm.controls.instructions.getRawValue() ?? undefined;
        return { id, content };
      });
  }

  getPromptModuleKeys(fallbackNamespace: NamespaceInterface): PromptModuleInterface[] {
    return this.controls
      .map((pm) => {
        return {
          id: pm.controls.id.getRawValue() ?? undefined,
          namespace: pm.controls.namespace.getRawValue() ?? fallbackNamespace,
        };
      })
      .filter((pm) => !!pm.id);
  }
}
