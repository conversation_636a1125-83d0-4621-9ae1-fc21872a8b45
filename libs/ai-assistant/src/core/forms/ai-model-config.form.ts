import { FormControl, FormGroup } from '@angular/forms';
import {
  ConfigVoiceConfigInterface,
  DeepgramConfigInterface,
  ModelConfigInterface,
  OpenAIRealtimeConfigInterface,
  VendorModel,
} from '@vendasta/ai-assistants';
import { AiVoiceFamily } from '../interfaces/config.interface';
import { familyFromVoice, voiceFamilies } from '../services/ai-assistant-utils';

export type DeepgramConfigFormControls = {
  voice: FormControl<string>;
};

export class DeepgramConfigForm extends FormGroup<DeepgramConfigFormControls> {
  constructor(deepgramConfig?: DeepgramConfigInterface) {
    super({
      voice: new FormControl(deepgramConfig?.voice ?? 'aura-asteria-en', { nonNullable: true }),
    });
  }
}

export type OpenAIRealtimeConfigFormControls = {
  voice: FormControl<string>;
  turnDetection: FormGroup<{
    threshold: FormControl<number | null>;
    prefixPadding: FormControl<number | null>;
    silenceDuration: FormControl<number | null>;
  }>;
};

export class OpenAIRealtimeConfigForm extends FormGroup<OpenAIRealtimeConfigFormControls> {
  constructor(openAIRealtimeConfig?: OpenAIRealtimeConfigInterface) {
    super({
      voice: new FormControl(openAIRealtimeConfig?.voice ?? 'alloy', { nonNullable: true }),
      turnDetection: new FormGroup({
        threshold: new FormControl(openAIRealtimeConfig?.turnDetection?.threshold ?? 0.75),
        prefixPadding: new FormControl(openAIRealtimeConfig?.turnDetection?.prefixPadding ?? 300),
        silenceDuration: new FormControl(openAIRealtimeConfig?.turnDetection?.silenceDuration ?? 500),
      }),
    });
  }
}

export type ModelConfigFormControls = {
  openAIRealtimeConfig: OpenAIRealtimeConfigForm;
  deepgramConfig: DeepgramConfigForm;
};

export class ModelConfigForm extends FormGroup<ModelConfigFormControls> {
  constructor(modelConfig?: ModelConfigInterface) {
    super({
      openAIRealtimeConfig: new OpenAIRealtimeConfigForm(modelConfig?.openaiRealtimeConfig),
      deepgramConfig: new DeepgramConfigForm(modelConfig?.deepgramConfig),
    });
  }
}

export type AiVoiceConfigFormControls = {
  vendorModel: FormControl<VendorModel | null>;
  voiceFamily: FormControl<AiVoiceFamily | null>;
  modelConfig: ModelConfigForm;
};

export class AiVoiceConfigurationForm extends FormGroup<AiVoiceConfigFormControls> {
  constructor(voiceConfig?: ConfigVoiceConfigInterface) {
    let voice = 'alloy';
    switch (voiceConfig?.vendorModel) {
      case VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME:
        voice = voiceConfig?.modelConfig?.openaiRealtimeConfig?.voice ?? '';
        break;
      case VendorModel.VENDOR_MODEL_DEEPGRAM:
        voice = voiceConfig?.modelConfig?.deepgramConfig?.voice ?? '';
        break;
    }
    const voiceFamily = familyFromVoice(voice);

    super({
      vendorModel: new FormControl(voiceConfig?.vendorModel ?? VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME, {
        nonNullable: true,
      }),
      voiceFamily: new FormControl(voiceFamily ?? voiceFamilies[0], { nonNullable: true }),
      modelConfig: new ModelConfigForm(voiceConfig?.modelConfig),
    });
  }

  toAssistantVoiceConfig(): ConfigVoiceConfigInterface {
    return {
      vendorModel: this.controls?.vendorModel.value ?? undefined,
      modelConfig: this.buildVoiceModelConfig(),
    };
  }

  private buildVoiceModelConfig(): ModelConfigInterface {
    const family = this.controls?.voiceFamily?.value;
    const deepgramConfig = this.controls?.modelConfig?.controls?.deepgramConfig?.getRawValue();
    const openaiConfig = this.controls?.modelConfig?.controls?.openAIRealtimeConfig?.getRawValue();

    if (family?.provider === VendorModel.VENDOR_MODEL_DEEPGRAM) {
      return {
        deepgramConfig: {
          voice: deepgramConfig?.voice,
        },
      };
    }

    return {
      openaiRealtimeConfig: {
        voice: openaiConfig?.voice,
        turnDetection: {
          threshold: openaiConfig?.turnDetection.threshold ?? 0.75,
          prefixPadding: openaiConfig?.turnDetection?.prefixPadding ?? 300,
          silenceDuration: openaiConfig?.turnDetection?.silenceDuration ?? 500,
        },
      },
    };
  }
}
