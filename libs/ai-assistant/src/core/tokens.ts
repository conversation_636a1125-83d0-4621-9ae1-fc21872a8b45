import { InjectionToken } from '@angular/core';
import { Observable } from 'rxjs';
import { AiAssistantConfig } from './interfaces/config.interface';
import { AiAssistant, AiConnection, NamespaceConfig } from './interfaces/ai-assistant.interface';

export const AI_ASSISTANT_CONFIG_TOKEN = new InjectionToken<AiAssistantConfig>(
  '[AiAssistant]: token for configuration',
);

export const ACCOUNT_GROUP_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[AiAssistant]: token for accountGroupId',
);

export const MARKET_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[AiAssistant]: token for marketId',
);

export const PARTNER_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[AiAssistant]: token for partnerId',
);

export const AI_DEFAULT_WORKFORCE_TOKEN: InjectionToken<Observable<AiAssistant[]>> = new InjectionToken(
  '[AiAssistant]: token for ai default workforce',
);

export const AI_DEFAULT_CONNECTIONS_TOKEN: InjectionToken<Observable<AiConnection[]>> = new InjectionToken(
  '[AiAssistant]: token for ai default connections',
);

export const CURRENT_USER_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[AiAssistant]: token for currentUserId',
);

export const VOICE_AI_AVAILABLE_TOKEN: InjectionToken<Observable<boolean>> = new InjectionToken(
  '[AiAssistant]: token for voice ai available',
);

export const NAMESPACE_CONFIG_TOKEN: InjectionToken<Observable<NamespaceConfig>> = new InjectionToken(
  '[AiAssistant]: token for namespace configuration',
);
