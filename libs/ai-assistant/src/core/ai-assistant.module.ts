import { In<PERSON><PERSON><PERSON>, ModuleWithProviders, NgModule } from '@angular/core';
import { AiAssistantService } from './services/ai-assistant.service';
import { AiAssistantConfig } from './interfaces/config.interface';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_ASSISTANT_CONFIG_TOKEN,
  AI_DEFAULT_CONNECTIONS_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  CURRENT_USER_ID_TOKEN,
  MARKET_ID_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
  VOICE_AI_AVAILABLE_TOKEN,
} from './tokens';
import { isObservable, Observable, of } from 'rxjs';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import {
  auroraAvatar,
  ChatReceptionistAvatar,
  ContentWriterAvatar,
  googleMeetIcon,
  ReviewManagerAvatar,
  SalesCoachAvatar,
  SEOAnalystAvatar,
  smsIcon,
  voiceIcon,
  VoiceReceptionistAvatar,
  webchatIcon,
  whatsappIcon,
} from '../assets/avatars/avatar-svg-string';
import { SmsDetailsService } from './services/sms-details.service';
import { BookingConfigService } from './services/booking-config.service';
import { AiAssistant, AiConnection, NamespaceConfig } from './interfaces/ai-assistant.interface';
import { DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON } from './ai-assistant.constants';
import { WhatsappDetailsService } from './services/whatsapp-details.service';
import { URLBuilderService } from './services/url-builder.service';

@NgModule()
export class AiAssistantModule {
  static forRoot(c: { config: InjectionToken<AiAssistantConfig> }): ModuleWithProviders<AiAssistantModule> {
    return {
      ngModule: AiAssistantModule,
      providers: [
        AiAssistantService,
        URLBuilderService,
        BookingConfigService,
        SmsDetailsService,
        WhatsappDetailsService,
        { provide: AI_ASSISTANT_CONFIG_TOKEN, useExisting: c.config },
        { provide: ACCOUNT_GROUP_ID_TOKEN, deps: [AI_ASSISTANT_CONFIG_TOKEN], useFactory: accountGroupIdFactory },
        { provide: MARKET_ID_TOKEN, deps: [AI_ASSISTANT_CONFIG_TOKEN], useFactory: marketIdFactory },
        { provide: PARTNER_ID_TOKEN, deps: [AI_ASSISTANT_CONFIG_TOKEN], useFactory: partnerIdFactory },
        {
          provide: AI_DEFAULT_WORKFORCE_TOKEN,
          deps: [AI_ASSISTANT_CONFIG_TOKEN],
          useFactory: aiDefaultWorkforceFactory,
        },
        {
          provide: NAMESPACE_CONFIG_TOKEN,
          deps: [AI_ASSISTANT_CONFIG_TOKEN],
          useFactory: aiNamespaceConfig,
        },
        {
          provide: AI_DEFAULT_CONNECTIONS_TOKEN,
          deps: [AI_ASSISTANT_CONFIG_TOKEN],
          useFactory: aiDefaultConnectionsFactory,
        },
        {
          provide: CURRENT_USER_ID_TOKEN,
          deps: [AI_ASSISTANT_CONFIG_TOKEN],
          useFactory: currentUserIdFactory,
        },
        {
          provide: VOICE_AI_AVAILABLE_TOKEN,
          deps: [AI_ASSISTANT_CONFIG_TOKEN],
          useFactory: voiceAIAvailableFactory,
        },
      ],
    };
  }

  constructor(iconRegistry: MatIconRegistry, sanitizer: DomSanitizer) {
    iconRegistry.addSvgIconLiteral(
      DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON,
      sanitizer.bypassSecurityTrustHtml(ChatReceptionistAvatar),
    );
    iconRegistry.addSvgIconLiteral('ai-voice-receptionist', sanitizer.bypassSecurityTrustHtml(VoiceReceptionistAvatar));
    iconRegistry.addSvgIconLiteral('ai-content-writer', sanitizer.bypassSecurityTrustHtml(ContentWriterAvatar));
    iconRegistry.addSvgIconLiteral('ai-review-manager', sanitizer.bypassSecurityTrustHtml(ReviewManagerAvatar));
    iconRegistry.addSvgIconLiteral('ai-sales-coach', sanitizer.bypassSecurityTrustHtml(SalesCoachAvatar));
    iconRegistry.addSvgIconLiteral('ai-seo-analyst', sanitizer.bypassSecurityTrustHtml(SEOAnalystAvatar));
    iconRegistry.addSvgIconLiteral('WebchatWidget', sanitizer.bypassSecurityTrustHtml(webchatIcon));
    iconRegistry.addSvgIconLiteral('SMS', sanitizer.bypassSecurityTrustHtml(smsIcon));
    iconRegistry.addSvgIconLiteral('Voice', sanitizer.bypassSecurityTrustHtml(voiceIcon));
    iconRegistry.addSvgIconLiteral('Whatsapp', sanitizer.bypassSecurityTrustHtml(whatsappIcon));
    iconRegistry.addSvgIconLiteral('aurora', sanitizer.bypassSecurityTrustHtml(auroraAvatar));
    iconRegistry.addSvgIconLiteral('GoogleMeet', sanitizer.bypassSecurityTrustHtml(googleMeetIcon));
  }
}

function accountGroupIdFactory(config: AiAssistantConfig): Observable<string | undefined | null> {
  return isObservable(config.accountGroupId$) ? config.accountGroupId$ : of(config.accountGroupId$);
}

function marketIdFactory(config: AiAssistantConfig): Observable<string | undefined | null> {
  return isObservable(config.marketId$) ? config.marketId$ : of(config.marketId$);
}

function partnerIdFactory(config: AiAssistantConfig): Observable<string | undefined | null> {
  return isObservable(config.partnerId$) ? config.partnerId$ : of(config.partnerId$);
}

function aiDefaultWorkforceFactory(config: AiAssistantConfig): Observable<AiAssistant[] | undefined | null> {
  return isObservable(config.defaultAIWorkforce$) ? config.defaultAIWorkforce$ : of(config.defaultAIWorkforce$);
}

function aiNamespaceConfig(config: AiAssistantConfig): Observable<NamespaceConfig | undefined | null> {
  return isObservable(config.namespaceConfig$) ? config.namespaceConfig$ : of(config.namespaceConfig$);
}

function aiDefaultConnectionsFactory(config: AiAssistantConfig): Observable<AiConnection[] | undefined | null> {
  return isObservable(config.defaultConnections$) ? config.defaultConnections$ : of(config.defaultConnections$);
}

function currentUserIdFactory(config: AiAssistantConfig): Observable<string | undefined | null> {
  return isObservable(config.currentUserId$) ? config.currentUserId$ : of(config.currentUserId$);
}

function voiceAIAvailableFactory(config: AiAssistantConfig): Observable<boolean | undefined | null> {
  return isObservable(config.voiceAIAvailable$) ? config.voiceAIAvailable$ : of(config.voiceAIAvailable$);
}
