import { animate, state, style, transition, trigger } from '@angular/animations';

export const LoadingBehavior = trigger('loadingBehavior', [
  state(
    'loading',
    style({
      visibility: 'hidden',
      opacity: 0,
    }),
  ),
  state(
    'finished',
    style({
      visibility: 'visible',
      opacity: 1,
    }),
  ),
  transition('loading => finished', [animate('200ms')]),
  transition('finished => loading', [animate('200ms')]),
]);

export const FadeIn = trigger('fadeIn', [
  transition(':enter', [style({ opacity: 0 }), animate('200ms', style({ opacity: 1 }))]),
  transition(':leave', [animate('200ms', style({ opacity: 0 }))]),
]);

export const AutoFill = trigger('autoFill', [
  state(
    'highlight',
    style({
      backgroundColor: 'rgba(88, 179, 243, 0.2)',
    }),
  ),
  state(
    'default',
    style({
      backgroundColor: 'unset',
    }),
  ),
  transition('default => highlight', [animate('1s')]),
  transition('highlight => default', [animate('1s')]),
]);

export const ExpandCollapse = [
  trigger('expandCollapse', [
    state(
      'collapsed',
      style({
        height: 'auto',
        minHeight: 'auto',
        maxHeight: '{{maxHeight}}px',
        overflow: 'hidden',
      }),
      { params: { maxHeight: 100 } },
    ),
    state(
      'expanded',
      style({
        height: '*',
        overflow: 'visible',
      }),
    ),
    transition('expanded <=> collapsed', animate('100ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
  ]),
];

export const OverlayFade = trigger('overlayFade', [
  state('visible', style({ opacity: 1 })),
  state('hidden', style({ opacity: 0 })),
  transition('visible <=> hidden', animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
]);
