import { Directive, HostBinding, Input } from '@angular/core';

@Directive({
  selector: '[crmExpandCollapse]',
})
export class ExpandCollapseDirective {
  @Input('crmExpandCollapse') expanded = false;
  @Input() maxHeight = 100;

  @HostBinding('@expandCollapse')
  get animationState() {
    return {
      value: this.expanded ? 'expanded' : 'collapsed',
      params: { maxHeight: this.maxHeight },
    };
  }
}
