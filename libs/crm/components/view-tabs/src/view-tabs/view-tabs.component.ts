import { Component, computed, inject, input, output, signal } from '@angular/core';
import { MatTabLink, MatTabNav, MatTabNavPanel } from '@angular/material/tabs';
import { Tab, TabGroup } from '../interfaces';
import { MatDialog } from '@angular/material/dialog';
import { DialogResponse, EditTabsModalComponent } from '../edit-tabs-modal/edit-tabs-modal.component';
import { firstValueFrom } from 'rxjs';
import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { NgClass } from '@angular/common';

@Component({
  selector: 'crm-view-tabs',
  templateUrl: './view-tabs.component.html',
  styleUrls: ['./view-tabs.component.scss'],
  imports: [MatTabNav, MatTabNavPanel, MatTabLink, CdkDropList, CdkDrag, NgClass],
})
export class ViewTabsComponent<T extends Tab> {
  tabTemplate = input.required<T>();
  editTabLabel = input.required<string>();
  editTabHint = input<string>();
  addTabText = input<string>();
  nameRequiredError = input<string>();
  scrollable = input<boolean>(true);

  tabGroups = input<TabGroup<T>[]>([]);
  tabsUpdated = output<TabGroup<T>[]>();

  savedOrder = input<string[]>([]);
  orderUpdated = output<string[]>();

  selectedTabChanged = output<T>();

  protected readonly visibleTabs = computed(() => {
    const tabs = this.tabGroups()
      .map((group) => group.tabs)
      .flat()
      .filter((tab) => !tab.hidden);
    const savedOrder = this.savedOrder();
    return this.orderTabs(tabs, savedOrder);
  });
  protected readonly selectedIndex = signal(-1);

  private readonly dialog = inject(MatDialog);

  protected loadTab(index: number) {
    const tabs = this.visibleTabs();
    const tabToLoad = tabs[index];
    if (tabToLoad) {
      this.selectedIndex.set(index);
      this.selectedTabChanged.emit(tabToLoad);
    }
  }

  protected async modifyTabs() {
    const dialog = this.dialog.open(EditTabsModalComponent, {
      data: {
        tabGroups: this.tabGroups(),
        tabTemplate: this.tabTemplate(),
        title: this.editTabLabel(),
        subtitle: this.editTabHint() ?? '',
        nameRequiredError: this.nameRequiredError() ?? '',
        addTabText: this.addTabText() ?? '',
      },
      minWidth: '360px',
    });
    const response: DialogResponse<T> = await firstValueFrom(dialog.afterClosed());
    if (response?.updatedTabs) {
      this.tabsUpdated.emit(response.updatedTabs);
    }
  }

  protected drop($event: CdkDragDrop<T>) {
    const tabs = this.visibleTabs();
    moveItemInArray(tabs, $event.previousIndex, $event.currentIndex);
    const newOrder: string[] = tabs.map((tab) => tab.id ?? '');
    this.orderUpdated.emit(newOrder);
    //select tab that was dragged, similar to how Google Chrome tabs work
    this.loadTab($event.currentIndex);
  }

  private orderTabs(tabs: T[], order: string[]): T[] {
    const orderedTabs = new Array<T>(tabs.length);
    const unorderedTabs: T[] = [];
    tabs.forEach((tab) => {
      const position = order.findIndex((t) => t === tab.id);
      if (position !== -1) {
        orderedTabs[position] = tab;
      } else {
        unorderedTabs.push(tab);
      }
    });
    return [...orderedTabs.filter((t) => t), ...unorderedTabs];
  }
}
