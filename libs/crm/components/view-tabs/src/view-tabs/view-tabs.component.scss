@use 'design-tokens' as *;

.view-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.view-contents {
  overflow: auto;
  height: 100%;
  position: relative;
}

.open-modal-tab {
  color: $link-color;
}

.drag-tabs {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: $primary-background-color;
  width: 100%;
}

.drag-tabs.cdk-drop-list-dragging {
  pointer-events: none;
}

.drag-tabs-preview.cdk-drag-animating {
  transition: all 250ms cubic-bezier(0, 0, 0.2, 1);
}

.mat-tab-link.drag-tabs-preview {
  outline: dashed 1px $border-color;
  outline-offset: $spacing-1;
}

.drag-tabs .cdk-drag-placeholder {
  opacity: 0.5;
}
