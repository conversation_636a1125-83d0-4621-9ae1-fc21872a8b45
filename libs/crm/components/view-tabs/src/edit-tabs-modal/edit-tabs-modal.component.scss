@use 'design-tokens' as *;

.tab-group {
  display: flex;
  flex-direction: column;
  padding-bottom: $spacing-2;
}

.dialog-title {
  @include text-preset-3;
  color: $primary-text-color;
  padding-bottom: $spacing-1;
}

.dialog-subtitle {
  @include text-preset-4;
  color: $secondary-text-color;
  padding-left: $spacing-1;
}

.tab-group-name {
  @include text-preset-4;
  color: $secondary-text-color;
  margin-bottom: $spacing-2;
}

.tab-group-tabs {
  padding-left: $spacing-4;
  padding-bottom: $spacing-2;
}

.tab-form {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  color: $primary-text-color;
  padding-bottom: $spacing-1;
}

.tab-form-name-and-toggle {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  flex-grow: 1;
  @include text-preset-4;
}

.tab-form-name {
  flex-grow: 1;
}

.add-button-container {
  display: flex;
}

.add-button {
  align-self: flex-start;
}
