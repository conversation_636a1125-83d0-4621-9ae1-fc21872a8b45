import { Component, Inject, signal } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import { Tab, TabGroup } from '../interfaces';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { v4 as uuidv4 } from 'uuid';

export interface DialogData<T extends Tab> {
  tabGroups: TabGroup<T>[];
  tabTemplate: T;
  title?: string;
  subtitle?: string;
  nameRequiredError?: string;
  addTabText?: string;
}
export interface DialogResponse<T extends Tab> {
  updatedTabs?: TabGroup<T>[];
}

@Component({
  selector: 'crm-edit-tabs-modal',
  templateUrl: './edit-tabs-modal.component.html',
  styleUrls: ['./edit-tabs-modal.component.scss'],
  imports: [
    MatDialogContent,
    GalaxyFormFieldModule,
    MatCheckbox,
    MatButton,
    MatIconButton,
    MatIcon,
    MatDialogActions,
    TranslateModule,
    FormsModule,
    MatDialogTitle,
  ],
})
export class EditTabsModalComponent<T extends Tab> {
  protected readonly groupedTabs: TabGroup<T>[];
  private readonly tabTemplate: T;
  protected readonly title: string;
  protected readonly subTitle: string;
  protected readonly nameRequiredError: string;
  protected readonly addTabText: string;
  protected readonly tabsAreMissingNames = signal(false);

  constructor(
    public readonly dialogRef: MatDialogRef<EditTabsModalComponent<T>, DialogResponse<T>>,
    @Inject(MAT_DIALOG_DATA) public readonly data: DialogData<T>,
  ) {
    this.groupedTabs = JSON.parse(JSON.stringify(data.tabGroups)); //deep copy
    this.tabTemplate = data.tabTemplate;
    this.title = data?.title ?? '';
    this.subTitle = data?.subtitle ?? '';
    this.nameRequiredError = data?.nameRequiredError ?? '';
    this.addTabText = data?.addTabText ?? '';
  }

  protected addTabToGroup(groupIdx: number) {
    const newTab: T = JSON.parse(JSON.stringify(this.tabTemplate)); //deep copy
    newTab.id = uuidv4();
    this.groupedTabs[groupIdx].tabs.push(newTab);
  }

  protected cancel() {
    this.dialogRef.close({});
  }

  protected saveChanges() {
    const tabsWithoutNames = this.groupedTabs
      .map((group) => group.tabs)
      .flat()
      .filter((tab) => !tab?.name);
    if (tabsWithoutNames.length > 0 && this.nameRequiredError) {
      this.tabsAreMissingNames.set(true);
    } else {
      this.dialogRef.close({
        updatedTabs: this.groupedTabs,
      });
    }
  }

  protected deleteTab(groupIdx: number, tabIdx: number) {
    this.groupedTabs[groupIdx].tabs.splice(tabIdx, 1);
  }
}
