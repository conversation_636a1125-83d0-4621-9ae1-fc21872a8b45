import {
  GalaxyFilterChipDependencies,
  GalaxyFilterDefinitionInterface,
  GalaxyFilterInputOverride,
  GalaxyFilterInterface,
} from '@vendasta/galaxy/filter/chips';
import {
  CrmObjectInterface,
  FieldType,
  FieldValueInterface as SdkFieldValueInterface,
  GetMultiCrmObjectResponse,
} from '@vendasta/crm';
import { combineLatest, distinctUntilChanged, filter, Observable, of } from 'rxjs';
import { AbstractControl, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { CustomCellComponent, GalaxyColumnDef, Row } from '@vendasta/galaxy/table';
import { Directive, inject, InjectionToken, input, Type, ViewContainerRef } from '@angular/core';
import { Properties } from 'posthog-js';
import { CardDataType } from '@vendasta/galaxy/contact-info-card';
import { CrmFieldOptionsService } from './shared-services/crm-services/crm-field-options.service';
import { CrmFiltersService } from './shared-services/crm-services/crm-filters.service';
import { CRMTrackingService } from './shared-services/page-analytics.service';
import { FieldValidation } from './shared-components/model-driven-form/field-validation';
import { User } from '@vendasta/iamv2';
import { ConversationChannel } from '@vendasta/conversation';
import { FormFieldAssociation, PresetFilters, SavedFilters } from './shared-components';
import {
  ActivityTableCustomizationService,
  CrmObjectDisplayService,
  Pipeline,
  TableCustomizationService,
} from './shared-services';
import { NavigationEnd, NavigationSkipped, Router, Scroll } from '@angular/router';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { ObjectNames } from './shared-services/crm-object-display.service';

export { FieldType } from '@vendasta/crm';

export interface ObjectRouteInterface {
  ROOT: string;
  SUBROUTES: {
    PROFILE: string;
  };
}

export interface ProfileCard {
  component: any;
}

export interface ProfileHeaderComponentInterface {
  crmObjectType: ObjectType | undefined;
  crmObject: CrmObjectInterface | undefined;
}

export interface ProfileHeader {
  component: Type<ProfileHeaderComponentInterface>;
  position?: 'top' | 'bottom';
}

export interface ProfileTitle {
  component: any;
}

export interface CRMSelectAllOptions {
  filters?: GalaxyFilterInterface[];
  search?: string;
  useSelectAll?: boolean;
  totalObjects?: number;
}

export interface UserWithDisplayName extends User {
  displayName: string;
}

export type OnProfilePageInitFunc = (object: CrmObjectInterface) => void;
export type MultiRowActionFunc = (rows: Row[]) => void;
export type SelectAllActionFunc = (rows: Row[], selectOptions?: CRMSelectAllOptions) => void;
export type SingleRowActionFunc = (row: Row) => void;
export type ObjectType = 'Contact' | 'Company' | 'Activity' | 'Opportunity' | 'CustomObject';
export const ObjectTypes: ObjectType[] = ['Contact', 'Company', 'Activity', 'Opportunity', 'CustomObject'];
export type ObjectDependencyKey = 'contact' | 'company' | 'opportunity' | 'customobject';
// Extra object types that can be associated with an activity
export type ObjectAssociationType = 'Opportunity' | 'User';
export type RowVisibilityFunc = (row: Row, type: ObjectType) => boolean;
export type GetDisplayNameFunc = (crmObject: CrmObjectInterface) => string;

export interface MultiRowAction {
  label: string;
  callback: MultiRowActionFunc;
}

export interface SelectAllAction {
  label: string;
  callback: SelectAllActionFunc;
  selectAllVisible?: boolean;
}

export interface SingleRowAction {
  label: string;
  callback: SingleRowActionFunc;
  visible?: RowVisibilityFunc;
}

export interface CRMRowObject {
  namespace: string;
  objectId: string;
  objectType: ObjectType;
}

export interface TableCustomCell {
  fieldIds: string[];
  customCellComponent: Type<CustomCellComponent>;
  columnDefinition: GalaxyColumnDef;
}

export interface CRMFilterOverride extends GalaxyFilterInputOverride {
  title: string;
}

export interface ActionButton {
  component: any;
}

export type RequiresField<T, K extends keyof T> = T & { [P in K]-?: T[P] };
export type FieldValueInterface = RequiresField<SdkFieldValueInterface, 'fieldId'>;

@Directive()
export abstract class FormInputComponent {
  readOnly = false;
  inlineEdit$: Observable<boolean> = of(false);
  objectType: ObjectType = 'Contact';
  isEditing = false;
  objectId = input('');

  abstract set control(control: UntypedFormControl);
  abstract get control(): UntypedFormControl;

  abstract set formGroup(group: UntypedFormGroup);

  abstract set fieldId(fieldId: string);

  abstract set label(label: string);

  abstract get hasModification(): boolean;

  abstract get value(): FieldValueInterface;
}

export interface FormCustomInput {
  fieldId: string;
  component: Type<FormInputComponent>;
}

export interface FormDefaultInputValue {
  fieldId: string;
  value: any;
}

export interface SimplifiedUser {
  userId: string;
  firstName: string;
  lastName: string;
  displayName: string;
  profileImageUrl?: string;
}

export interface UserService {
  searchUsers?(searchTerm?: string): Observable<SimplifiedUser[]>;

  getMultiUsers(userIds: string[]): Observable<SimplifiedUser[]>;
}

export interface CrmAccessService {
  canViewAllCampaigns$?: Observable<boolean>;
  canModifyAutomations$?: Observable<boolean>;
  hasPartnerPermissions$?: Observable<boolean>;
  hasSalespersonPermissions$?: Observable<boolean>;
  canAccessSales$?: Observable<boolean>;
  canAccessTaskManager$?: Observable<boolean>;
  canAccessAutomations$?: Observable<boolean>;
  canAccessInbox$?: Observable<boolean>;

  canAccessProductPage$?(partnerId: string, marketId: string): Observable<boolean>;

  canAccessCrmObjectType$?(objectType: ObjectType): Observable<boolean>;
}

export const CRM_ACCESS_SERVICE_TOKEN = new InjectionToken<CrmAccessService>(
  'Service for the crm for user access checks',
);

export interface SimplifiedOpportunity {
  opportunityId: string;
  name: string;
}

export interface SalesOpportunityService {
  searchOpportunities(searchTerm: string): Observable<SimplifiedOpportunity[]>;

  getMultiOpportunity(opportunityIds: string[]): Observable<SimplifiedOpportunity[]>;

  setAccountGroupId(accountGroupId: string): void;
}

export interface InitialFilters {
  fieldId: string;
  filterId: string;
}

export interface CreateAssociationFields {
  objectTypes: ObjectType[];
  required?: boolean;
}

export interface PipelineService {
  getPipeline$(pipelineId: string): Observable<Pipeline>;

  getMultiPipeline$(pipelineIds: string[]): Observable<Pipeline[]>;
}

export interface CampaignService {
  navigateToSendCampaign(
    ids: string[],
    filters?: GalaxyFilterInterface[],
    search?: string,
    useSelectAll?: boolean,
    totalContacts?: number,
  ): void;
}

// Dependencies related to specific object types
export interface CrmObjectDependencies {
  multiRowTableActions$?: Observable<MultiRowAction[]>;
  singleRowTableActions$?: Observable<SingleRowAction[]>;
  selectAllTableActions$?: Observable<SelectAllAction[]>;
  onProfilePageInit?: OnProfilePageInitFunc;
  profileCards$?: Observable<ProfileCard[]>;
  profileHeader$?: Observable<ProfileHeader[]>;
  profileTitle$?: Observable<ProfileTitle[]>;
  actionButtons$?: Observable<ActionButton[]>;
  baseColumnIds?: string[];
  filterInputOverrides$?: Observable<CRMFilterOverride[]>;
  initialFilters$?: Observable<InitialFilters[]>;
  tableCustomCells?: TableCustomCell[];
  formCustomInputs?: FormCustomInput[];
  formDefaultOnCreateInputValues$?: Observable<FormDefaultInputValue[]>;
  showRightHandProfilePanel$?: Observable<boolean>; // default to show right hand profile panel
  presetFilters$?: Observable<PresetFilters>;
  syncIds$?: Observable<string[]>;
  accountId$?: (crmObject: Observable<GetMultiCrmObjectResponse>) => Observable<string>;
  showCampaigns$?: Observable<boolean>;
  createAssociationFields?: CreateAssociationFields;
  additionalBaseFormFieldIds$?: Observable<string[]>;
  objectSubtype$?: Observable<string>;
  singularObjectName$?: Observable<string>;
  pluralObjectName$?: Observable<string>;
  defaultPageUrl$?: Observable<string>;
  services?: {
    tableCustomizationService?: TableCustomizationService;
    campaignService?: CampaignService;
  };
  getDisplayName?: GetDisplayNameFunc;
  profilePageDefaultViews?: SavedFilters;
}

export interface CrmActivityDependencies {
  filterInputOverrides$?: Observable<CRMFilterOverride[]>;
  initialFilters$?: Observable<InitialFilters[]>;
}

export interface CrmListsDependencies {
  canStartManualAutomation$?: Observable<boolean>;
}

export interface CrmMultiLocationDependencies {
  multiLocationContext$: Observable<MultiLocationContext>;
  routePrefix$: Observable<string>;
  currentLocationName$: Observable<string>;
  isMultiLocation$: Observable<boolean>;
}

export interface MultiLocationContext {
  groupId: string;
  accountGroupsIds: string[];
}

export interface CrmDependencies {
  appID?: string;
  // the prefix for the route, e.g. /crm. necessary to avoid using relative routing with CRM lib subrouter
  routePrefix$: Observable<string>;

  // A namespace is an instance of the CRM
  // e.g. VUNI for a partner managing their contacts in Partner Center,
  // or AG-12345 for an SMB managing their contacts in Business Center.
  // (This distinction gets weirder in the future when we start merging these Centers,
  // but just roll with it for now.)
  namespace$: Observable<string>;

  // When the CRM is used in Business Center, the namespace is the account group id.
  // This will give you the parent namespace, which is the partner id.
  parentNamespace$: Observable<string>;

  // featureFlags$
  viewProfileFlag$: Observable<boolean>;
  hasCrmAssociationModalFeatureFlag$: Observable<boolean>;
  customObjectFeatureFlag?: string;
  hasTimelineActivitiesFeatureFlag$: Observable<boolean>;
  hasRestrictedFieldValuesAdminFeatureFlag$?: Observable<boolean>;
  canPartnerAdminCreateOrdersFeatureFlag$?: Observable<boolean>;

  // the unified user id of the current user
  currentUserId$: Observable<string>;

  locale$?: Observable<string>;

  // dependencies by object type
  activity?: CrmActivityDependencies;
  company?: CrmObjectDependencies;
  contact?: CrmObjectDependencies;
  task?: CrmObjectDependencies;
  opportunity?: CrmObjectDependencies;
  lists?: CrmListsDependencies;
  customobject?: CrmObjectDependencies;

  showBackToAccountsButton?: boolean;

  // dependencies for namespace specific shared-services
  services?: {
    userService?: UserService;
    opportunityService?: SalesOpportunityService;
    accessService?: CrmAccessService;
    pipelineService?: PipelineService;
  };

  hasInboxAccess$: Observable<boolean>;
  canShowInboxForContact$: Observable<boolean>;
  openAccountGroupConversation?: (accountGroupId: string) => void | Promise<void>;
  openContactConversation?: (
    namespace: string,
    parentNamespace: string,
    contactId: string,
    channel: ConversationChannel,
  ) => void | Promise<void>;

  // Helps determine whether a partner has access to a feature
  hasAccessToFeature$?: (feature: string) => Observable<boolean>;
  openRestrictedDialog?: (featureId: string) => void;
  showLeadProspector$?: Observable<boolean>;

  // Client specific actions to shared components
  customActions?: {
    // navigates to the fulfillment projects page for a given company
    navigateToFulfillmentProjects?: (row: CRMRowObject) => void;
    openOpportunitiesDetails?: (opportunityId: string, accountGroupId: string) => void;
  };

  isMeetingIconNeeded$?: Observable<boolean>;
  meetingAction?: (
    contactId: string,
    currentUserId: Observable<string>,
    namespace: Observable<string>,
    parentNamespace: Observable<string>,
  ) => void;
  automationsSideMenuComponent?: Type<any>;

  activityFeedDefaultViews?: SavedFilters;
}

export const CrmInjectionToken = new InjectionToken<CrmDependencies>('dependencies for the crm');

export const CrmObjectInjectionToken = new InjectionToken<CrmObjectDependencies>(
  'dependencies for the crm object type',
);

export const CrmMultilocationInjectionToken = new InjectionToken<CrmMultiLocationDependencies>(
  'dependencies for the multi-location context',
);

export function crmObjectDependenciesTokenGenerator(
  crm: CrmDependencies,
  objectTableCustomizationService: TableCustomizationService,
  objectUrlRoot: string,
  crmObjectDependencies?: CrmObjectDependencies,
): CrmObjectDependencies {
  if (!crmObjectDependencies) {
    return {} as CrmObjectDependencies;
  }
  const crmObjectDisplayService = inject(CrmObjectDisplayService);
  const router = inject(Router);
  const objectSubtype$: Observable<string> = router.events.pipe(
    map((event) => {
      if (event instanceof Scroll) {
        return event.routerEvent;
      }
      return event;
    }),
    filter((event) => event instanceof NavigationEnd || event instanceof NavigationSkipped),
    map((event: NavigationEnd | NavigationSkipped) => {
      const url = event.url;
      const customObjectTypeRegex = /(CustomObjectTypeID-[-0-9a-zA-Z]*)/;
      const capture = customObjectTypeRegex.exec(url);
      return capture && capture.length > 1 ? capture[1] : '';
    }),
    distinctUntilChanged(),
    shareReplay(1),
  );
  if (!crmObjectDependencies.objectSubtype$) {
    crmObjectDependencies.objectSubtype$ = objectSubtype$;
  }

  const names$: Observable<ObjectNames> = crmObjectDependencies.objectSubtype$.pipe(
    switchMap((objectSubtype: string | undefined) =>
      crmObjectDisplayService.getObjectNames(crm.namespace$, objectSubtype),
    ),
    shareReplay(1),
  );
  crmObjectDependencies.singularObjectName$ = names$.pipe(map((names: ObjectNames) => names.singularObjectName));
  crmObjectDependencies.pluralObjectName$ = names$.pipe(map((names: ObjectNames) => names.pluralObjectName));

  crmObjectDependencies.defaultPageUrl$ = combineLatest([crm.routePrefix$, objectSubtype$]).pipe(
    map(([routePrefix, objectSubtype]) => {
      if (objectSubtype) {
        return `${routePrefix}/${objectUrlRoot}/${objectSubtype}`;
      }
      return `${routePrefix}/${objectUrlRoot}`;
    }),
    shareReplay(1),
  );

  crmObjectDependencies.services = {
    tableCustomizationService: objectTableCustomizationService,
  };

  return crmObjectDependencies as CrmObjectDependencies;
}

export interface CompanyProfileCardData {
  company_id: string;
  account_group_id?: string;
  group_id?: string;
  name?: string;
}

export const COMPANY_PROFILE_CARD_DATA_TOKEN = new InjectionToken<CompanyProfileCardData>('company-profile-card-data');

export interface PageAnalyticsDependencies {
  trackEvent(action: string, properties?: Properties): void;
}

export const PageAnalyticsInjectionToken = new InjectionToken<PageAnalyticsDependencies>(
  'dependencies for page analytics',
);
export type ActivityType = 'Note' | 'Email' | 'Call' | 'Meeting' | 'Task' | 'Communication';
export type CommunicationSubType = 'SMS' | 'LinkedIn' | 'Inbox';
export type RecordChangeType = 'CREATED' | 'IMPORTANT_FIELD_UPDATED';

export interface CardData extends CardDataType {
  tags?: string[];
  hideAvatar?: boolean;
  accountGroupCompanyName?: string;
  accountGroupCompanyId?: string;
  associationId?: string;
}

export function tableFiltersInjectionTokenGenerator(
  objectType: ObjectType,
  activityType?: ActivityType,
  useUrlFilters = true,
): () => GalaxyFilterChipDependencies {
  if (!objectType) {
    console.error('objectType is required for tableFiltersInjectionTokenGenerator');
    return () => ({}) as GalaxyFilterChipDependencies;
  }

  return () => {
    const crmFieldOptionsService = inject(CrmFieldOptionsService);
    const crmFiltersService = inject(CrmFiltersService);
    const crmConfigDependencies = inject(CrmInjectionToken);
    const crmObjectDependencies = inject(CrmObjectInjectionToken, { optional: true });
    const deps: GalaxyFilterChipDependencies = {
      listObjectFilters(searchTerm: string): Observable<GalaxyFilterDefinitionInterface[]> {
        return crmFiltersService.listObjectFilters$(
          searchTerm,
          objectType,
          activityType,
          crmObjectDependencies?.objectSubtype$,
        );
      },
      getFieldOptions(fieldId: string, search: string): Observable<string[]> {
        return crmFieldOptionsService.getFieldOptions(
          fieldId,
          search,
          objectType,
          crmObjectDependencies?.objectSubtype$,
        );
      },
      getFilterInputOverrides(): Observable<GalaxyFilterInputOverride[]> {
        if (crmObjectDependencies) {
          return crmObjectDependencies.filterInputOverrides$ ?? of([] as GalaxyFilterInputOverride[]);
        }
        switch (objectType) {
          case 'Contact':
            return crmConfigDependencies.contact?.filterInputOverrides$ ?? of([] as GalaxyFilterInputOverride[]);
          case 'Company':
            return crmConfigDependencies.company?.filterInputOverrides$ ?? of([] as GalaxyFilterInputOverride[]);
          case 'Activity':
            switch (activityType) {
              case 'Task':
                return crmConfigDependencies.task?.filterInputOverrides$ ?? of([] as GalaxyFilterInputOverride[]);
              default:
                return crmConfigDependencies.activity?.filterInputOverrides$ ?? of([] as GalaxyFilterInputOverride[]);
            }
          case 'Opportunity':
            return crmConfigDependencies.opportunity?.filterInputOverrides$ ?? of([] as GalaxyFilterInputOverride[]);
          default:
            return of([] as GalaxyFilterInputOverride[]);
        }
      },
      getInitialAppliedFilters(): Promise<GalaxyFilterInterface[]> {
        return crmFiltersService.getInitialAppliedFilters$(
          objectType,
          crmObjectDependencies?.objectSubtype$,
          activityType,
          useUrlFilters,
        );
      },
      setInitialAppliedFilters(filters: GalaxyFilterInterface[]): void {
        return crmFiltersService.setInitialFilters(filters);
      },
    };
    return deps;
  };
}

export function pageAnalyticsInjectionTokenGenerator(
  objectType: ObjectType,
  objectSubtype = '',
): () => PageAnalyticsDependencies {
  return () => {
    const crmTrackingService = inject(CRMTrackingService);
    const deps: PageAnalyticsDependencies = {
      trackEvent(action: string, properties?: Properties): void {
        if (objectSubtype) {
          crmTrackingService.trackEvent(`${objectType}-${objectSubtype}`, action, properties);
          return;
        }
        crmTrackingService.trackEvent(objectType, action, properties);
      },
    };
    return deps;
  };
}

export interface DropdownOptionInterface {
  label?: string;
  value?: string;
}

export interface FieldSchemaInterface {
  fieldId: string;
  fieldType: FieldType;
  fieldName: string;
  fieldDescription: string;
  dropdownOptions: DropdownOptionInterface[];
  currencyCode: string;
  value: any;
  readOnly: boolean;
  hasDefaultValue?: boolean;
}

export interface AssociationFormField {
  type: ObjectType;
  associations: FormFieldAssociation[];
  associationsRequired?: boolean;
  subtype?: string;
}

export interface FieldInterface {
  fieldId: string;
  name: string;
  value: any;
  description: string;

  // Gets the field value for the backend
  fieldValue(): FieldValueInterface;

  // Checks if the field received a update
  hasModification(): boolean;
}

export interface FieldGroupInterface {
  description?: string;
  fieldIds: string[];
}

export const ActivityTaskStatus = {
  Open: 'Open',
  Completed: 'Completed',
};

export interface FieldOptionsDependencies {
  getFieldOptions(fieldId: string, search: string): Observable<string[]>;
}

export const FieldOptionsInjectionToken = new InjectionToken<FieldOptionsDependencies>(
  'dependencies for getting field options in the model-driven form',
);

export enum LoadingStatus {
  LOADING = 1,
  OK = 2,
  EMPTY = 3,
  ERROR = 4,
}

export enum SavingStatus {
  NOT_SAVING = 1,
  SAVING = 2,
  OK = 3,
  ERROR = 4,
}

export enum NullValue {
  NULL_VALUE = 0,
}

export interface RenderableFieldInterface extends FieldInterface {
  formControl: AbstractControl;
  validation: FieldValidation;
  objectType: ObjectType;
  readOnly: boolean;

  // Chooses the correct component to display information
  render(viewContainerRef: ViewContainerRef): void;

  setValidation(validation: FieldValidation): void;
}

export interface RenderableFieldGroupInterface {
  description?: string;
  fields: RenderableFieldInterface[];
  editableFieldCount: number;
}

export interface FieldConfigInterface {
  fieldSchemas?: FieldSchemaInterface[];
  fieldGroups?: FieldGroupInterface[];
}

export function modelDrivenFormFieldOptionsInjectionTokenGenerator(
  objectType: ObjectType,
): () => FieldOptionsDependencies {
  return () => {
    const crmFieldOptionsService = inject(CrmFieldOptionsService);
    const crmObjectDependencies = inject(CrmObjectInjectionToken, { optional: true });
    const deps: FieldOptionsDependencies = {
      getFieldOptions(fieldId: string, search: string): Observable<string[]> {
        return crmFieldOptionsService.getFieldOptions(
          fieldId,
          search,
          objectType,
          crmObjectDependencies?.objectSubtype$,
        );
      },
    };
    return deps;
  };
}

export interface ProfileCardData {
  id: string;
  name: string;
  objectType: ObjectType;
}

export interface ProfilePageData {
  accountGroupId?: string;
  crmObject?: CrmObjectInterface;
  onChangeCallback?: (crmObject: CrmObjectInterface) => void;
}

export const PROFILE_PAGE_DATA_TOKEN = new InjectionToken<ProfilePageData>(
  'CRM object data loaded in the profile page',
);

export const PrimaryAccountGroupIDForCrmObject = new InjectionToken<Observable<string>>(
  'primary account group ID for crm object in a given context',
);

export const TaskTableCustomizationServiceToken = new InjectionToken<ActivityTableCustomizationService>(
  'Injection token for the task table customization service to be provided in the dynamic CRM module',
);

export type ListObjectView = 'board' | 'table' | 'unknown';
