import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivityType, ObjectType } from '../../tokens-and-interfaces';

/*
  this reference tracks which translation keys are object-specific
  while we could just assume a convention of `objectType.KEY` for all object-specific keys
  this book-keeping provides some safety around the completeness of the translation files
*/
const objectSpecificTranslationKeys: string[] = [
  'TITLE',
  'OBJECT_TYPE',
  'ALL_OBJECTS',
  'MY_OBJECTS',
  'ADD_OBJECT_BUTTON',
  'PROSPECT_OBJECT_BUTTON',
  'RECORD_CHANGE_ACTIVITY.CREATED',
  'RECORD_CHANGE_ACTIVITY.CREATED_VIA',
  'RECORD_CHANGE_ACTIVITY.DETAILS_UPDATED',
  'RECORD_CHANGE_ACTIVITY.IMPORTANT_FIELD_UPDATED',
  'RECORD_CHANGE_ACTIVITY.IMPORTANT_FIELD_UPDATED_SALESPERSON',
  'RECORD_CHANGE_ACTIVITY.IMPORTANT_FIELD_UPDATED_SALESPERSON_NOT_ASSIGNED',
  'RECORD_CHANGE_ACTIVITY.IMPORTANT_FIELD_UPDATED_SALESPERSON_UNKNOWN',
  'LIST_OBJECTS_TABLE.EMPTY_TABLE.IMAGE_ALT',
  'LIST_OBJECTS_TABLE.EMPTY_TABLE.HEADER',
  'LIST_OBJECTS_TABLE.EMPTY_TABLE.HINT',
  'LIST_OBJECTS_TABLE.ACTIONS.EDIT.TITLE',
  'LIST_OBJECTS_TABLE.ACTIONS.DELETE.ACTION',
  'LIST_OBJECTS_TABLE.ACTIONS.DELETE.TITLE',
  'LIST_OBJECTS_TABLE.ACTIONS.DELETE.CANCEL',
  'LIST_OBJECTS_TABLE.ACTIONS.DELETE.CONFIRM',
  'LIST_OBJECTS_TABLE.ACTIONS.DELETE.CONFIRMATION_MESSAGE',
  'LIST_OBJECTS_TABLE.ACTIONS.DELETE.SUCCESS',
  'LIST_OBJECTS_TABLE.ACTIONS.DELETE.ERROR',
  `LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.ACTION`,
  `LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.TITLE`,
  `LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.CANCEL`,
  `LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.CONFIRM`,
  `LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.CONFIRMATION_MESSAGE`,
  `LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.SUCCESS`,
  `LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.ERROR`,
  'LIST_OBJECTS_TABLE.SELECT_ALL.SELECT_ALL_OBJECTS',
  'LIST_OBJECTS_TABLE.SELECT_ALL.TOTAL_SELECTED',
  'LIST_OBJECTS_TABLE.SELECT_ALL.CLEAR_SELECTION',
  'LIST_OBJECTS_TABLE.PIPELINE.EMPTY_STATE.TITLE',
  'LIST_OBJECTS_TABLE.PIPELINE.EMPTY_STATE.MESSAGE',
  'LIST_OBJECTS_TABLE.PIPELINE.EMPTY_STATE.SETUP_PIPELINE',
  'CREATE_PAGE.TITLE',
  'CREATE_PAGE.SAVE.ASSOCIATIONS_REQUIRED',
  'EDIT_PAGE.TITLE',
  'EDIT_PAGE.SAVE.SUCCESS',
  'CREATE_PAGE.SAVE.SUCCESS',
  'CREATE_PAGE.SAVE.ERROR',
  'CREATE_PAGE.LOAD.ERROR',
  'TABLE_FILTERS.HEADER',
  'STANDARD_FIELD_RULES.UNIQUE_FIELD_VALUE.ENFORCED',
  'STANDARD_FIELD_RULES.UNIQUE_FIELD_VALUE.WARNING',
  'STANDARD_FIELD_RULES.E164_FORMAT.WARNING',
  'PROFILE_PAGE.EDIT',
  'PROFILE_PAGE.TITLE',
  'PROFILE_PAGE.NPS',
  'PROFILE_PAGE.LEAD_SCORE',
  'PROFILE_PAGE.ABOUT_TAB',
  'PROFILE_PAGE.ACTIVITIES_TIMELINE_TAB',
  'PROFILE_PAGE.ASSOCIATIONS_TAB',
  'PROFILE_PAGE.PREVIOUS_PAGE_TITLE',
  'VIEW_OBJECT',
  'SELECT_MODAL.ONE_SELECTED',
  'SELECT_MODAL.MORE_SELECTED',
  'SELECT_MODAL.NO_SELECTED',
  'SELECT_MODAL.CREATE_NEW',
  'ASSOCIATION_PANEL.TITLE',
  'ASSOCIATION_PANEL.ADD_ASSOCIATION',
  'ASSOCIATION_PANEL.TITLE_ASSOCIATE',
  'ASSOCIATION_PANEL.CREATE_BUTTON',
  'ASSOCIATION_FORM.TITLE',
  'ASSOCIATION_FORM.PLACEHOLDER',
  'ASSOCIATION_PANEL.SET_PRIMARY_MODAL.RADIO_BUTTON_TEXT',
  'ASSOCIATION_PANEL.SET_PRIMARY_MODAL.RADIO_BUTTON_DESCRIPTION',
  'SELECT_FIELD',
  'ASSOCIATION_PANEL.CONFIRM_EXISTING_PRIMARY_MODAL.TITLE',
  'ASSOCIATION_PANEL.CONFIRM_EXISTING_PRIMARY_MODAL.CONFIRMATION_MESSAGE',
  'ASSOCIATION_PANEL.CHANGE_PRIMARY_DIALOG.TITLE',
  'ASSOCIATION_PANEL.CHANGE_PRIMARY_DIALOG.MESSAGE',
];

@Injectable()
export class TranslateByObjectTypeService {
  constructor(private readonly translateService: TranslateService) {}

  private getTranslationKeyByObjectType(objectType: ObjectType | ActivityType, partialTranslationKey: string): string {
    const formattedObjectType = objectType === 'CustomObject' ? 'CUSTOM_OBJECT' : objectType.toUpperCase();
    if (!objectSpecificTranslationKeys.includes(partialTranslationKey)) {
      console.warn(`Translation key ${partialTranslationKey} is not supported for object type ${objectType}`);
      return `PARTIAL.${partialTranslationKey}`;
    }
    return `${formattedObjectType}.${partialTranslationKey}`;
  }

  getTranslationByObjectType(
    objectType: ObjectType | ActivityType,
    partialTranslationKey: string,
    // eslint-disable-next-line @typescript-eslint/ban-types
    interpolateParams?: Object,
  ): string {
    const translationKey = this.getTranslationKeyByObjectType(objectType, partialTranslationKey);
    const translation = this.translateService.instant(translationKey, interpolateParams);
    if (translationKey === translation) {
      console.warn(`Missing translation for object specific key ${translationKey}`);
    }
    return translation;
  }

  getTranslatedObjectType(objectType: ObjectType | ActivityType): string {
    const formattedObjectType = objectType === 'CustomObject' ? 'CUSTOM_OBJECT' : objectType.toUpperCase();
    const translatedObject = this.translateService.instant(`OBJECT_TYPES.${formattedObjectType}`);
    return translatedObject || this.translateService.instant(`OBJECT_TYPES.OBJECT`);
  }
}
