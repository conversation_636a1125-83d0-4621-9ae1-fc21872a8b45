import { Inject, Injectable, Optional } from '@angular/core';
import { BehaviorSubject, combineLatest, firstValueFrom, Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { CrmObjectService } from './crm-object-service/crm-object.service';
import { GalaxyFilterDefinitionInterface, GalaxyFilterInterface } from '@vendasta/galaxy/filter/chips';
import { ContactTableCustomizationService } from '../table-customization/contact-table-customization.service';
import { CompanyTableCustomizationService } from '../table-customization/company-table-customization.service';
import { ActivityCustomizationService } from '../table-customization/activity-customization.service';
import {
  CrmInjectionToken,
  ActivityType,
  CrmDependencies,
  ObjectType,
  TaskTableCustomizationServiceToken,
} from '../../tokens-and-interfaces';
import { FilterType } from '@vendasta/crm';
import { convertToGalaxyFilterOperators, convertToGalaxyFilterType } from '../../utils';
import { ActivityTableCustomizationService } from '../../shared-services/table-customization/_abstract-table-customization.service';
import { OpportunityTableCustomizationService, CustomObjectTableCustomizationService } from '../table-customization';

// right now filters are sorted manually in the backend, the page size needs to cover the amount of system + standard + extensions + custom filters (up to 100)
const FILTER_PAGE_SIZE = 200;

function deduplicateFilters(filters: GalaxyFilterInterface[]): GalaxyFilterInterface[] {
  return filters.filter((filter, index) => {
    return index === filters.findIndex((f) => filter.filterId === f.filterId);
  });
}

@Injectable()
export class CrmFiltersService {
  private initialFilters$$ = new BehaviorSubject<GalaxyFilterInterface[]>([]);
  initialFilters$ = this.initialFilters$$.asObservable();

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly crmObjectService: CrmObjectService,
    private readonly contactTableCustomization: ContactTableCustomizationService,
    private readonly companyTableCustomization: CompanyTableCustomizationService,
    private readonly activityCustomizationService: ActivityCustomizationService,
    private readonly opportunityTableCustomization: OpportunityTableCustomizationService,
    private readonly customObjectTableCustomization: CustomObjectTableCustomizationService,
    @Optional()
    @Inject(TaskTableCustomizationServiceToken)
    private readonly taskTableCustomization?: ActivityTableCustomizationService,
  ) {}

  private customizeFilters(
    filters: GalaxyFilterDefinitionInterface[],
    objectType: ObjectType,
    activityType?: ActivityType,
  ): Observable<GalaxyFilterDefinitionInterface[]> {
    switch (objectType) {
      case 'Contact':
        return this.contactTableCustomization.customizeFilters(filters);
      case 'Company':
        return this.companyTableCustomization.customizeFilters(filters);
      case 'Activity':
        switch (activityType) {
          case 'Task':
            return this.taskTableCustomization?.customizeFilters(filters) ?? of(filters);
          default:
            return of(filters);
        }
      case 'Opportunity':
        return this.opportunityTableCustomization.customizeFilters(filters);
      default:
        return of(filters);
    }
  }

  listObjectFilters$(
    searchTerm: string,
    objectType: ObjectType,
    activityType?: ActivityType,
    objectSubtype$?: Observable<string>,
    withNamespace?: string,
  ): Observable<GalaxyFilterDefinitionInterface[]> {
    return combineLatest([this.config.namespace$, objectSubtype$ ?? of('')]).pipe(
      switchMap(([namespace, objectSubtype]) => {
        return this.crmObjectService.listObjectsFilters(
          objectType,
          {
            namespace: namespace,
            paging: {
              pageSize: FILTER_PAGE_SIZE,
            },
            searchTerm: searchTerm,
            crmObjectSubtype: objectSubtype,
          },
          withNamespace,
        );
      }),
      map((resp) => {
        if (!resp.filters) {
          return [] as GalaxyFilterDefinitionInterface[];
        }
        return resp.filters.map((filterDef) => ({
          fieldId: filterDef.fieldId,
          type: convertToGalaxyFilterType(filterDef.typeV2 || FilterType.FILTER_TYPE_INVALID),
          hasFilterOptions: filterDef.hasFilterOptions,
          supportedOperators: convertToGalaxyFilterOperators(filterDef.supportedOperatorsV2 || []),
          fieldName: filterDef.fieldName,
        }));
      }),
      switchMap((filters) => this.customizeFilters(filters, objectType, activityType)),
    );
  }

  async getInitialAppliedFilters$(
    objectType: ObjectType,
    objectSubtype$?: Observable<string>,
    activityType?: ActivityType,
    useUrlFilters?: boolean,
    withNamespace?: string,
  ): Promise<GalaxyFilterInterface[]> {
    if (objectType === 'Activity' && activityType === 'Task') {
      const urlFilters = firstValueFrom(this.initialFilters$);
      return urlFilters.then((filters) => {
        if (filters?.length > 0 && useUrlFilters) return filters;
        if (this.taskTableCustomization?.getInitialFilters()) {
          return this.taskTableCustomization.getInitialFilters();
        }
        console.error('No getInitialFilters method found in taskTableCustomization service');
        return [];
      });
    }

    if (objectType === 'Activity') {
      const urlFilters = await firstValueFrom(this.initialFilters$);
      const initialActivityFilters = await this.activityCustomizationService.getInitialFilters(
        this.listObjectFilters$('', objectType, undefined, undefined, withNamespace),
      );
      if (urlFilters?.length > 0 && useUrlFilters) {
        return deduplicateFilters([...urlFilters, ...initialActivityFilters]);
      }
      return initialActivityFilters;
    }
    if (objectType === 'Contact') {
      const urlFilters = await firstValueFrom(this.initialFilters$);
      const initialContactFilters = await this.contactTableCustomization.getInitialFilters(
        this.listObjectFilters$('', objectType, undefined, undefined, withNamespace),
      );
      if (urlFilters?.length > 0 && useUrlFilters) {
        return deduplicateFilters([...urlFilters, ...initialContactFilters]);
      }
      return initialContactFilters;
    }
    if (objectType === 'Company') {
      const urlFilters = await firstValueFrom(this.initialFilters$);
      const initialCompanyFilters = await this.companyTableCustomization.getInitialFilters(
        this.listObjectFilters$('', objectType, undefined, undefined, withNamespace),
      );
      if (urlFilters?.length > 0 && useUrlFilters) {
        return deduplicateFilters([...urlFilters, ...initialCompanyFilters]);
      }
      return initialCompanyFilters;
    }
    if (objectType === 'Opportunity') {
      const urlFilters = await firstValueFrom(this.initialFilters$);
      const initialOpportunityFilters = await this.opportunityTableCustomization.getInitialFilters(
        this.listObjectFilters$('', objectType, undefined, undefined, withNamespace),
      );
      if (urlFilters?.length > 0 && useUrlFilters) {
        return deduplicateFilters([...urlFilters, ...initialOpportunityFilters]);
      }
      return initialOpportunityFilters;
    }
    if (objectType === 'CustomObject') {
      const urlFilters = await firstValueFrom(this.initialFilters$);
      const initialCustomObjectFilters = await this.customObjectTableCustomization.getInitialFilters(
        this.listObjectFilters$('', objectType, undefined, objectSubtype$, withNamespace),
      );
      if (urlFilters?.length > 0 && useUrlFilters) {
        return deduplicateFilters([...urlFilters, ...initialCustomObjectFilters]);
      }
      return initialCustomObjectFilters;
    }
    return firstValueFrom(this.initialFilters$);
  }

  setInitialFilters(filters: GalaxyFilterInterface[]) {
    this.initialFilters$$.next(filters);
  }
}
