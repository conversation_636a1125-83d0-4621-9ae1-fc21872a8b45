@let readonly = isReadOnlyMode();
@let hasViewTabsFeatureFlag = hasViewTabsFeatureFlag$ | async;
@if (!readonly) {
  <crm-sidepanel></crm-sidepanel>
}
<!-- if we don't wait until the columns are fully ready, mat-table is unable to render the headers all at once -->
<!-- sometimes it will make the headers blink, sometimes the rows blink -->
@if (hasViewTabsFeatureFlag && view() === 'list' && !readonly) {
  <crm-view-tabs
    [tabGroups]="viewTabs$ | async"
    [tabTemplate]="newViewTemplate()"
    [editTabLabel]="'VIEW_TABS.EDIT_TABS' | translate"
    [addTabText]="'VIEW_TABS.ADD_TAB' | translate"
    [editTabHint]="'VIEW_TABS.HINT' | translate"
    [nameRequiredError]="'VIEW_TABS.ERRORS.NAME_REQUIRED' | translate"
    [savedOrder]="tabOrder$$ | async"
    (selectedTabChanged)="changeViewFilters($event)"
    (tabsUpdated)="saveTabChanges($event)"
    (orderUpdated)="saveTabOrder($event)"
  >
    <ng-container *ngTemplateOutlet="objectsTable"></ng-container>
  </crm-view-tabs>
} @else {
  <ng-container *ngTemplateOutlet="objectsTable"></ng-container>
}
<ng-template #objectsTable>
  @if (columns$ | async) {
    <glxy-table-container
      class="object-table"
      @fadeIn
      [columns]="columns$ | async"
      [groupConfigs]="groupConfigs$ | async"
      [id]="tableId"
      [dataSource]="isBoardView() ? undefined : dataSource"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [showSelection]="showMultiRowActions$ | async"
      (selectionChanged)="selectionChanged($event)"
      (columnsChanged)="columnsChanged($event)"
      (searchTermChanged)="searchTermChanged($event)"
      [fullWidth]="true"
      [showFooter]="!isBoardView()"
    >
      <glxy-table-content-header
        [showColumnArrange]="!isBoardView() && !readonly"
        [showSort]="false"
        [showActions]="showMultiRowActions$ | async"
        [showFilters]="!readonly"
        [showFiltersOpen]="showFiltersOpen()"
        (showFiltersOpenChange)="handleFilterOpenChange($event)"
        [showFiltersApplied]="(showFiltersApplied$ | async) ?? !readonly"
        [saveSearchToQueryParams]="!readonly"
        [columnArrangeType]="'advanced'"
        [showSelectedAllCount]="useSelectAll$ | async"
        [showDisplayToggle]="(isOnBoardFeatureFlag$ | async) === true && !readonly"
        [display]="view()"
        (displayChanges)="changeDisplay($event)"
      >
        @if (!readonly) {
          <!-- this component cannot be in the DOM in readonly mode or else the filters changed is triggered and erases forced filters -->
          <glxy-filter-chips
            [addFiltersHeader]="
              ('TABLE_FILTERS.HEADER' | translateForCrmObject: objectTypeForTranslation | async) ?? ''
            "
            (filtersChanged)="updateFilters($event)"
            #filterChipRef
            filters-area
          ></glxy-filter-chips>
        }
        @if (hasSelectAllActions$ | async) {
          <div class="selectAllButtons" optional-select-all>
            <div class="select-all-objects">
              @if (useSelectAll$ | async) {
                <div class="selection-count">
                  {{
                    'LIST_OBJECTS_TABLE.SELECT_ALL.TOTAL_SELECTED'
                      | translateForCrmObject: objectType : { totalSelected: dataSource.state.totalDataMembers }
                      | async
                  }}
                </div>
                <a (click)="handleSelectAll()">
                  {{ 'LIST_OBJECTS_TABLE.SELECT_ALL.CLEAR_SELECTION' | translateForCrmObject: objectType | async }}
                </a>
              } @else {
                <a (click)="handleSelectAll()">
                  {{
                    'LIST_OBJECTS_TABLE.SELECT_ALL.SELECT_ALL_OBJECTS'
                      | translateForCrmObject: objectType : { totalObjects: dataSource.state.totalDataMembers }
                      | async
                  }}
                </a>
              }
            </div>
          </div>
        }
        @if (useSelectAll$ | async) {
          <button
            mat-menu-item
            *ngFor="let action of selectAllActions$ | async"
            (click)="
              action.callback(selectedRows, {
                filters: filters(),
                search: this.dataSource.state.searchTerm,
                useSelectAll: this.dataSource.state.selectAll,
                totalObjects: this.dataSource.state.totalDataMembers,
              })
            "
          >
            <span>{{ action.label | translate }}</span>
          </button>
        } @else {
          <button *ngFor="let action of multiRowActions$ | async" mat-menu-item (click)="action.callback(selectedRows)">
            {{ action.label | translate }}
          </button>
        }
        @if (!readonly) {
          <div after-search class="list-objects-after-search">
            @if (isBoardView()) {
              <crm-pipeline-selector
                [selectedPipelineId]="this.initialPipeline()"
                [objectType]="objectType"
                (selectionChanged)="pipelineChanged($event)"
              ></crm-pipeline-selector>
            }
            @if (!hasViewTabsFeatureFlag) {
              <crm-filter-view
                class="filter-view-button"
                [filters]="filters()"
                (filtersChange)="selectViewFilters($event)"
                [keys]="[viewTabKey()]"
                [presetFilters]="presetFilters()"
              ></crm-filter-view>
            }
          </div>
        }
      </glxy-table-content-header>
      @switch (view()) {
        @case ('grid') {
          @if (showBoardEmptyState()) {
            <glxy-empty-state class="empty-table-container" data-testid="crm-objects-table-empty-state">
              <glxy-empty-state-hero>
                <img
                  class="empty-state-image"
                  src="https://storage.googleapis.com/galaxy-libs-public-images/crm/opportunities-empty-state.svg"
                  [alt]="
                    'LIST_OBJECTS_TABLE.EMPTY_TABLE.IMAGE_ALT' | translateForCrmObject: objectTypeForTranslation | async
                  "
                />
              </glxy-empty-state-hero>
              <glxy-empty-state-title>
                {{
                  'LIST_OBJECTS_TABLE.PIPELINE.EMPTY_STATE.TITLE'
                    | translateForCrmObject: objectTypeForTranslation
                    | async
                }}
              </glxy-empty-state-title>
              <p>
                {{
                  'LIST_OBJECTS_TABLE.PIPELINE.EMPTY_STATE.MESSAGE'
                    | translateForCrmObject: objectTypeForTranslation
                    | async
                }}
              </p>
              <glxy-empty-state-actions>
                <button mat-flat-button color="primary" (click)="openAndSetupPipelineSettings()">
                  {{
                    'LIST_OBJECTS_TABLE.PIPELINE.EMPTY_STATE.SETUP_PIPELINE'
                      | translateForCrmObject: objectTypeForTranslation
                      | async
                  }}
                </button>
              </glxy-empty-state-actions>
            </glxy-empty-state>
          } @else {
            <crm-board
              [columns]="boardColumnLayout()"
              [datasource]="boardSource"
              minColumnWidth="300"
              carousel="auto"
              itemUpdateStrategy="cache"
              (itemUpdateFailed)="boardItemUpdateFailed()"
              (itemClicked)="boardItemClicked($event)"
            >
              <!-- Board column header -->
              <ng-template crm-board-column-header let-column let-metadata="metadata">
                <crm-list-objects-board-column-header
                  [column]="column"
                  [metadata]="metadata"
                  [summary]="boardValueSummary$ | async"
                ></crm-list-objects-board-column-header>
              </ng-template>
              <!-- Board card content -->
              <ng-template crm-board-card-content let-row>
                <crm-list-objects-board-card-content
                  [row]="row"
                  [objectType]="objectType"
                ></crm-list-objects-board-card-content>
              </ng-template>
            </crm-board>
          }
        }
        @default {
          <!-- [hidden] is used here to hide the table columns until the table has finished loading data for the first time -->

          <table
            mat-table
            [@loadingBehavior]="(dataSource.loading$ | async) ? 'loading' : 'finished'"
            [hidden]="initialLoadingCompleted$ | async"
          >
            <tr mat-header-row *matHeaderRowDef="[]"></tr>
            <ng-container matColumnDef="select">
              <th mat-header-cell *matHeaderCellDef>
                <glxy-table-selection></glxy-table-selection>
              </th>
              <td mat-cell *matCellDef="let row">
                <glxy-table-selection [row]="row"></glxy-table-selection>
              </td>
            </ng-container>
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef></th>
              <td mat-cell *matCellDef="let row">
                <div>
                  <button
                    [disabled]="readonly"
                    mat-icon-button
                    [matMenuTriggerFor]="menu"
                    data-testid="actions-button"
                    [attr.data-action-group]="
                      'clicked-crm-' + objectTypeForTranslation.toLowerCase() + '-row-action-menu'
                    "
                    [attr.data-action]="
                      'clicked-crm-' + objectTypeForTranslation.toLowerCase() + '-row-' + row.id + '-action-menu'
                    "
                  >
                    <mat-icon class="actions-menu-button">more_vert</mat-icon>
                  </button>
                  <mat-menu #menu="matMenu">
                    <ng-container
                      *ngFor="let action of singleRowActions$ | async | filterVisibleActions: { row, objectType }"
                    >
                      <button mat-menu-item (click)="action.callback(row)">
                        <span>
                          {{ action.label | translate }}
                        </span>
                      </button>
                    </ng-container>
                    <button
                      mat-menu-item
                      (click)="editObject(row)"
                      data-testid="edit-crm-object-button"
                      [attr.data-action-group]="'clicked-edit-crm-' + objectTypeForTranslation.toLowerCase()"
                      [attr.data-action]="'clicked-edit-crm-' + objectTypeForTranslation.toLowerCase() + '-' + row.id"
                    >
                      <span>
                        {{
                          'LIST_OBJECTS_TABLE.ACTIONS.EDIT.TITLE'
                            | translateForCrmObject: objectTypeForTranslation
                            | async
                        }}
                      </span>
                    </button>
                    <button
                      mat-menu-item
                      (click)="deleteObject(row)"
                      data-testid="delete-crm-object-button"
                      [attr.data-action-group]="'clicked-delete-crm-' + objectTypeForTranslation.toLowerCase()"
                      [attr.data-action]="'clicked-delete-crm-' + objectTypeForTranslation.toLowerCase() + '-' + row.id"
                    >
                      <span>
                        {{
                          'LIST_OBJECTS_TABLE.ACTIONS.DELETE.ACTION'
                            | translateForCrmObject: objectTypeForTranslation
                            | async
                        }}
                      </span>
                    </button>
                  </mat-menu>
                </div>
              </td>
            </ng-container>
            <tr
              mat-row
              *matRowDef="let row; columns: []"
              [attr.data-testid]="'crm-object-row-' + row.id"
              [ngStyle]="rowStyle?.(row) || {}"
            ></tr>
          </table>
        }
      }
      @if (showEmptyState$ | async) {
        <glxy-empty-state class="empty-table-container" data-testid="crm-objects-table-empty-state">
          <glxy-empty-state-hero>
            <img
              class="empty-state-image"
              src="https://storage.googleapis.com/galaxy-libs-public-images/crm/contacts-empty-state.svg"
              [alt]="
                'LIST_OBJECTS_TABLE.EMPTY_TABLE.IMAGE_ALT' | translateForCrmObject: objectTypeForTranslation | async
              "
            />
          </glxy-empty-state-hero>
          <glxy-empty-state-title>
            {{ 'LIST_OBJECTS_TABLE.EMPTY_TABLE.HEADER' | translateForCrmObject: objectTypeForTranslation | async }}
          </glxy-empty-state-title>
          <p>
            {{ 'LIST_OBJECTS_TABLE.EMPTY_TABLE.HINT' | translateForCrmObject: objectTypeForTranslation | async }}
          </p>
        </glxy-empty-state>
      }
    </glxy-table-container>
  }
</ng-template>
<ng-container #automationsSideMenuContainer></ng-container>
