import { computed, inject, Injectable, Signal, signal, WritableSignal } from '@angular/core';
import { ActivityInterface, CRMCustomObjectTypeApiService, CrmObject, CRMOpportunityApiService } from '@vendasta/crm';
import { combineLatest, map, Observable, of, startWith, switchMap } from 'rxjs';
import { CrmInjectionToken, PrimaryAccountGroupIDForCrmObject, SimplifiedUser } from '../../../tokens-and-interfaces';
import { ObjectTypePrefix, PAGE_ROUTES } from '../../../constants';
import { CrmObjectDisplayService, CrmObjectService } from '../../../shared-services';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { GetMultiCustomObjectTypeRequestInterface } from '@vendasta/crm/lib/_internal/interfaces/custom-objects-type.interface';
import { GetMultiCustomObjectTypeResponse } from '@vendasta/crm/lib/_internal/objects';
import { AvatarGroupItem } from '@vendasta/galaxy/avatar';

export interface ActivityAssociationDisplayInfo {
  name?: string;
  linkUrl?: string;
  objectType?: string;
  objectSubtype?: string;
  avatarUrl?: string;
}

export interface ActivityAssociationDisplayInfoGroup {
  objectGroupName: string;
  activityAssociationDisplayInfo: ActivityAssociationDisplayInfo[];
  objectSubtype: string;
}

@Injectable()
export class ActivityAssociationService {
  private _activity: WritableSignal<ActivityInterface> = signal({});
  set activity(activity: ActivityInterface) {
    this._activity.set(activity);
  }
  get activity(): ActivityInterface {
    return this._activity();
  }

  companies: Signal<ActivityAssociationDisplayInfo[]>;
  contacts: Signal<ActivityAssociationDisplayInfo[]>;
  customObjects: Signal<ActivityAssociationDisplayInfo[]>;
  customObjectGroups: Signal<ActivityAssociationDisplayInfoGroup[]>;
  opportunities: Signal<ActivityAssociationDisplayInfo[]>;
  teamMembers: Signal<ActivityAssociationDisplayInfo[]>;
  teamMembersAvatars = computed<AvatarGroupItem[]>(() => {
    return this.teamMembers()
      .filter((a) => !!a.name)
      .map((a) => {
        return {
          src: a.avatarUrl,
          name: a.name,
        };
      });
  });
  primaryAssociation: Signal<ActivityAssociationDisplayInfo | null>;
  numberOfAssociations: Signal<number | null>;

  routePrefix: Signal<string>;

  protected readonly config = inject(CrmInjectionToken);
  protected readonly primaryAccountGroupId$ = inject(PrimaryAccountGroupIDForCrmObject);
  protected readonly opportunityService = this.config.services?.opportunityService;
  protected readonly userService = this.config.services?.userService;
  private readonly opportunityApiService = inject(CRMOpportunityApiService);

  constructor(
    private readonly objectService: CrmObjectService,
    private readonly customObjectTypeService: CRMCustomObjectTypeApiService,
    private readonly objectDisplayService: CrmObjectDisplayService,
  ) {
    this.routePrefix = toSignal(this.config.routePrefix$);

    const companies$ = toObservable(this._activity).pipe(
      switchMap((activity) => {
        const ids =
          activity?.associations
            ?.filter((a) => a.crmObjectType == 'Company' && a.crmObjectId)
            .map((a) => {
              return a.crmObjectId;
            }) || [];
        if (!!ids && ids.length > 0) {
          return this.objectService.getMultiObject('Company', ids);
        } else {
          return of(null);
        }
      }),
      map((response) => (response?.crmObjects || []).filter((crmObject) => crmObject?.crmObjectId)),
      map((crmObjects) => {
        return crmObjects.map((object) => {
          const displayName = this.objectDisplayService.getObjectDisplayName('Company', object);
          return this.newActivityAssociationDisplayInfo(
            displayName,
            `${PAGE_ROUTES.COMPANY.ROOT}/${PAGE_ROUTES.COMPANY.SUBROUTES.PROFILE}`.replace(
              ':crmObjectId',
              object.crmObjectId || '',
            ),
            'OBJECT_TYPES.COMPANY',
          );
        });
      }),
    );

    this.companies = toSignal(companies$, { initialValue: [] });

    const contacts$ = toObservable(this._activity).pipe(
      switchMap((activity) => {
        const ids: string[] =
          activity?.associations
            ?.filter((a) => a.crmObjectType == 'Contact' && a.crmObjectId)
            .map((a) => {
              return a.crmObjectId;
            }) || [];
        if (ids.length > 0) {
          return this.objectService.getMultiObject('Contact', ids);
        } else {
          return of(null);
        }
      }),
      map((response) => (response?.crmObjects || []).filter((crmObject) => crmObject?.crmObjectId)),
      map((crmObjects) => {
        return crmObjects.map((object) => {
          const displayName = this.objectDisplayService.getObjectDisplayName('Contact', object);
          return this.newActivityAssociationDisplayInfo(
            displayName,
            `${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.PROFILE}`.replace(
              ':crmObjectId',
              object.crmObjectId || '',
            ),
            'OBJECT_TYPES.CONTACT',
          );
        });
      }),
    );

    this.contacts = toSignal(contacts$, { initialValue: [] });

    const customObjects$ = toObservable(this._activity).pipe(
      switchMap((activity) => {
        const ids: string[] =
          activity?.associations
            ?.filter((a) => a.crmObjectType == 'CustomObject' && a.crmObjectId)
            .map((a) => {
              return a.crmObjectId;
            }) || [];
        if (ids.length > 0) {
          return this.objectService.getMultiObject('CustomObject', ids);
        } else {
          return of(null);
        }
      }),
      map((response) => (response?.crmObjects || []).filter((crmObject) => crmObject?.crmObjectId)),
      map((crmObjects) => {
        return crmObjects.map((object) => {
          const displayName = this.objectDisplayService.getObjectDisplayName('CustomObject', object);
          return this.newActivityAssociationDisplayInfo(
            displayName,
            `${PAGE_ROUTES.CUSTOM_OBJECT.ROOT}/${PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.PROFILE}`
              .replace(':customObjectTypeID', object.crmObjectSubtype ?? '')
              .replace(':crmObjectId', object.crmObjectId || ''),
            'OBJECT_TYPES.CUSTOM_OBJECT',
            object.crmObjectSubtype ?? '',
          );
        });
      }),
    );

    this.customObjects = toSignal(customObjects$, { initialValue: [] });

    const customObjectGroups$ = combineLatest([toObservable(this.customObjects), this.config.namespace$]).pipe(
      switchMap(([customObjects, namespace]) => {
        const customObjectIds = Array.from(new Set(customObjects.map((o) => o.objectSubtype)));
        if (customObjectIds.length === 0) {
          return of([{} as GetMultiCustomObjectTypeResponse, customObjects] as [
            GetMultiCustomObjectTypeResponse,
            ActivityAssociationDisplayInfo[],
          ]);
        }
        return this.customObjectTypeService
          .getMultiCustomObjectType({
            namespace: namespace,
            customObjectTypeIds: customObjectIds,
          } as GetMultiCustomObjectTypeRequestInterface)
          .pipe(
            map(
              (response) =>
                [response, customObjects] as [GetMultiCustomObjectTypeResponse, ActivityAssociationDisplayInfo[]],
            ),
          );
      }),
      map(([response, customObjects]: [GetMultiCustomObjectTypeResponse, ActivityAssociationDisplayInfo[]]) => {
        const customObjectGroups: ActivityAssociationDisplayInfoGroup[] = [];
        if (!response.customObjectTypes) {
          return customObjectGroups;
        }
        for (const customObjectType of response.customObjectTypes) {
          customObjectGroups.push({
            objectSubtype: customObjectType.customObjectTypeId,
            objectGroupName: customObjectType.pluralObjectName,
            activityAssociationDisplayInfo: [],
          } as ActivityAssociationDisplayInfoGroup);
        }
        for (const customObject of customObjects) {
          const group = customObjectGroups.find((group) => group.objectSubtype === customObject.objectSubtype);
          if (group) {
            group.activityAssociationDisplayInfo.push(customObject);
          }
        }
        return customObjectGroups;
      }),
    );

    this.customObjectGroups = toSignal(customObjectGroups$, { initialValue: [] });

    const opportunityIds$ = toObservable(this._activity).pipe(
      map(
        (activity) =>
          activity?.associations
            ?.filter((a) => a.crmObjectType == 'Opportunity' && a.crmObjectId)
            .map((a) => a.crmObjectId ?? '') || [],
      ),
    );
    const legacyOpportuynities$ = combineLatest([
      this.primaryAccountGroupId$.pipe(startWith('')),
      opportunityIds$,
    ]).pipe(
      switchMap(([accountGroupId, opportunityIds]) => this.fetchLegacyOpportunities(accountGroupId, opportunityIds)),
    );
    const crmOpportunities$ = opportunityIds$.pipe(
      switchMap((opportunityIds) => this.fetchCrmOpportunities(opportunityIds)),
    );

    const opportunities$: Observable<ActivityAssociationDisplayInfo[]> = combineLatest([
      crmOpportunities$,
      legacyOpportuynities$,
    ]).pipe(map(([opportunities, legacyOpportuynities]) => opportunities.concat(legacyOpportuynities)));

    const primaryAssociation$ = combineLatest([contacts$, companies$]).pipe(
      map(([contacts, companies]) => {
        return contacts.length > 0 ? contacts[0] : companies.length > 0 ? companies[0] : null;
      }),
    );
    this.primaryAssociation = toSignal(primaryAssociation$, { initialValue: null });

    this.opportunities = toSignal(opportunities$, { initialValue: [] });
    const userIds$: Observable<ActivityAssociationDisplayInfo[]> = toObservable(this._activity).pipe(
      switchMap((activity: ActivityInterface) => {
        const ownerId = activity?.ownerId || '';
        const ids =
          activity?.associations
            ?.filter((a) => a.crmObjectType == 'User')
            .filter((a) => a.crmObjectId !== ownerId)
            .map((a) => {
              return a.crmObjectId ?? '';
            }) || [];
        const idsWithOwnerFirst = [ownerId, ...ids].filter((id) => !!id);
        if (idsWithOwnerFirst && idsWithOwnerFirst.length > 0) {
          return this.userService?.getMultiUsers(idsWithOwnerFirst);
        }
        return of([] as SimplifiedUser[]);
      }),
      map((users: SimplifiedUser[]) => (users || []).filter((user) => user?.userId)),
      map((users) => {
        if (!users) {
          return [];
        }
        return users?.map((o) => {
          return this.newActivityAssociationDisplayInfo(o?.displayName ?? '-', '', '', '', o?.profileImageUrl || '');
        });
      }),
    );

    this.teamMembers = toSignal(userIds$, { initialValue: [] });

    const associationCount$ = combineLatest([companies$, contacts$, customObjects$, opportunities$]).pipe(
      map((associations) => associations.reduce((acc, curr) => acc + curr.length, 0)),
    );

    this.numberOfAssociations = toSignal(associationCount$, { initialValue: null });
  }

  private newActivityAssociationDisplayInfo(
    name: string,
    link: string,
    objectType: string,
    objectSubtype?: string,
    avatarUrl?: string,
  ): ActivityAssociationDisplayInfo {
    return {
      name: name,
      linkUrl: link,
      objectType: objectType,
      objectSubtype: objectSubtype,
      avatarUrl: avatarUrl,
    };
  }

  private fetchLegacyOpportunities(
    accountGroupId: string,
    opportunityIds: string[],
  ): Observable<ActivityAssociationDisplayInfo[]> {
    const legacyIds = opportunityIds.filter((id) => !id.startsWith(ObjectTypePrefix.Opportunity));
    if (accountGroupId && legacyIds.length > 0 && this.opportunityService) {
      this.opportunityService.setAccountGroupId(accountGroupId);
      return this.opportunityService.getMultiOpportunity(legacyIds).pipe(
        map((response) => {
          if (!response) {
            return [];
          }
          return response.map((o) =>
            this.newActivityAssociationDisplayInfo(o?.name ?? '-', '', 'OBJECT_TYPES.OPPORTUNITY'),
          );
        }),
      );
    }
    return of([]);
  }

  private fetchCrmOpportunities(opportunityIds: string[]): Observable<ActivityAssociationDisplayInfo[]> {
    const ids = opportunityIds.filter((id) => id.startsWith(ObjectTypePrefix.Opportunity));
    if (ids.length > 0) {
      return this.config.namespace$.pipe(
        switchMap((namespace) => this.opportunityApiService.getMultiOpportunity({ namespace, crmObjectIds: ids })),
        map((response) => response?.crmObjects?.map((o) => this.getOpportunityInfo(o)) ?? []),
      );
    }
    return of([]);
  }

  private getOpportunityInfo(crmObject: CrmObject): ActivityAssociationDisplayInfo {
    const name = this.objectDisplayService.getObjectDisplayName('Opportunity', crmObject);
    const link = `${PAGE_ROUTES.OPPORTUNITY.ROOT}/${PAGE_ROUTES.OPPORTUNITY.SUBROUTES.PROFILE}`.replace(
      ':crmObjectId',
      crmObject.crmObjectId,
    );
    return this.newActivityAssociationDisplayInfo(name, link, 'OBJECT_TYPES.OPPORTUNITY');
  }
}
