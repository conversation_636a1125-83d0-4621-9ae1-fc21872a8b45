import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  ElementRef,
  inject,
  input,
  Input,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule, DOCUMENT } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { ActivityInterface } from '@vendasta/crm';
import { ActivityAssociationService } from '../../../../activities/activity-association.service';
import { AVATAR_WIDTH, ObjectTypePrefix } from '../../../../../../constants';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';
import { TranslateModule } from '@ngx-translate/core';
import { MatMenu, Mat<PERSON>enuItem, MatMenuTrigger } from '@angular/material/menu';
import { GalaxyFilterInterface, GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';
import { SystemFieldIds } from '../../../../../../shared-services/crm-services/field.service';
import { encodeFilters } from '@vendasta/galaxy/filter/chips/src/utils';
import { Clipboard } from '@angular/cdk/clipboard';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { CrmActivityTemplateOptionsDirective } from '../../../../activities/activity-template-options.directive';
import { User } from '@vendasta/iamv2';
import { CrmActivityLoggerComponent } from '../../../../activities/activity-logger.component';
import { CrmTimelineAssociatedObjectPipe } from '../../../../timeline/timeline-associated-object.pipe';
import { ActivityTypes, CrmObjectIdentifier } from '../../../../activities';
import { CrmActivityEffectiveDatePipe } from '../../../../../../shared-pipes';
import { ActivitiesTimelineService } from '../../../../timeline/timeline.service';
import { CrmActivityAssociationPopoverComponent } from './association-popover.component';
import { ExpandCollapse, OverlayFade } from '@galaxy/crm/components/animations';
import { ExpandCollapseDirective } from '@galaxy/crm/components/animations/src/expand-collapse.directive';

interface ActivityCardProps {
  effectiveDate?: Date;
  ownerUser?: User;
  isLoggedActivity?: boolean;
  crmObjectSubtype?: string;
}

@Component({
  selector: 'crm-activity-card',
  templateUrl: './activity-card.component.html',
  styleUrls: ['./activity-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    GalaxyAvatarModule,
    CrmActivityAssociationPopoverComponent,
    TranslateModule,
    MatMenu,
    MatMenuTrigger,
    MatMenuItem,
    CrmActivityTemplateOptionsDirective,
    CrmActivityLoggerComponent,
    CrmTimelineAssociatedObjectPipe,
    CrmActivityEffectiveDatePipe,
    ExpandCollapseDirective,
  ],
  providers: [ActivityAssociationService],
  animations: [OverlayFade, ExpandCollapse],
})
export class ActivityCardComponent implements AfterViewInit {
  activity = input<ActivityInterface & ActivityCardProps>();
  @Input() showTitle = true;
  @Input() icon = '';
  @Input() showAssociationPopover = true;
  @Input() maxCollapsedHeight = 100;
  @Input() crmObjectIdentifier?: CrmObjectIdentifier;
  @Input() showPrimaryAssociation!: boolean;
  @Input() subtitleTemplate?: TemplateRef<unknown>;
  @Input() disableCollapse = false;
  @ViewChild('previewText') previewTextElement?: ElementRef;

  private readonly document = inject(DOCUMENT);
  private readonly clipboard = inject(Clipboard);
  private readonly snackbarService = inject(SnackbarService);
  protected readonly AVATAR_WIDTH = AVATAR_WIDTH;
  protected readonly dateFormat = DateFormat.mediumDateShortTime;
  private readonly activitiesTimelineService = inject(ActivitiesTimelineService);
  private readonly cdr = inject(ChangeDetectorRef);

  editMode = signal<boolean>(false);
  /**
   * Controls if content is expandable. When false, the expand button is not shown
   * but spacing is maintained through CSS to keep consistent alignment
   */
  hasExpandableContent = false;
  state = signal<'collapsed' | 'expanded'>('collapsed');

  protected readonly hideCrmAssociations = computed(() => {
    const objId = this.crmObjectIdentifier;
    return objId?.objectType === 'Opportunity' && !objId?.crmObjectId.startsWith(ObjectTypePrefix.Opportunity);
  });

  protected readonly shouldShowAssociation = computed(() => {
    const activity = this.activity();
    if (!activity?.crmObjectSubtype || !this.showAssociationPopover) {
      return false;
    }
    return activity.crmObjectSubtype !== ActivityTypes.RecordChange;
  });
  overlayState = computed(() => (this.state() === 'collapsed' ? 'visible' : 'hidden'));

  ngAfterViewInit() {
    if (!this.disableCollapse) {
      this.checkExpandableContent();
    }
    this.cdr.markForCheck();
  }

  private checkExpandableContent() {
    if (this.previewTextElement) {
      const element = this.previewTextElement.nativeElement;
      this.hasExpandableContent = element.scrollHeight > this.maxCollapsedHeight;
    }
  }

  public toggle(): void {
    this.state.set(this.state() === 'expanded' ? 'collapsed' : 'expanded');
  }

  public onTitleClick(event: MouseEvent): void {
    if (this.disableCollapse || !this.hasExpandableContent) return;

    // Check if the click was not on a clickable element except for the expansion icon itself
    if (!(event.target as HTMLElement).closest('button')) {
      this.toggle();
    }
  }

  private constructCopyLink(): string {
    const documentUrl = this.document.URL.split('?')[0];
    const activity = this.activity();
    if (!activity?.crmObjectId) return documentUrl;

    const glxyFilter: GalaxyFilterInterface = {
      fieldId: SystemFieldIds.ActivityID,
      operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
      values: [
        {
          string: this.activity()?.crmObjectId,
        },
      ],
    };
    const encodedFilter = encodeFilters([glxyFilter]);
    return `${documentUrl}?filter=${encodedFilter}`;
  }

  copyLinkToActivity(): void {
    this.clipboard.copy(this.constructCopyLink());
    this.snackbarService.openSuccessSnack('COMMON.SNACKBAR_MESSAGE.COPY_SUCCESS');
  }

  activityChanged(activity: ActivityInterface): void {
    const updated = this.activitiesTimelineService.updateActivity(activity);
    if (this.activity()?.crmObjectId && updated) {
      this.editMode.set(false);
    }
  }
}
