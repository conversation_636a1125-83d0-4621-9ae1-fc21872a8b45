<mat-card class="bottom-card">
  <div class="card-container">
    <mat-card-header class="icon-container">
      <div matCardAvatar>
        <glxy-avatar
          [width]="AVATAR_WIDTH"
          [icon]="icon"
          shape="square"
          backgroundColor="var(--card-background-color)"
          textColor="var(--tertiary-text-color)"
          borderColor="var(--border-color)"
        ></glxy-avatar>
      </div>
    </mat-card-header>
    <mat-card-content class="content-container">
      <mat-card appearance="outlined" class="inner-card content-padding">
        @if (activity(); as currentActivity) {
          <div class="card-header">
            @if (showTitle) {
              <div class="activity-title-group" (click)="onTitleClick($event)">
                <mat-card-title class="mat-card-title title-with-icon">
                  <div class="title-container">
                    @if (hasExpandableContent && !disableCollapse) {
                      <mat-card-actions>
                        <a>
                          <mat-icon class="expand-icon-card" [class.expanded]="state() === 'expanded'">
                            keyboard_arrow_right
                          </mat-icon>
                        </a>
                      </mat-card-actions>
                    }
                    <!-- Activity title slot -->
                    <ng-content select="[activity-title]"></ng-content>
                  </div>
                </mat-card-title>
              </div>
            }
            <div class="effective-date-and-options">
              <span class="activity-date">
                <ng-container
                  *ngTemplateOutlet="
                    subtitleTemplate || defaultSubtitleTemplate;
                    context: { activity: currentActivity }
                  "
                >
                </ng-container>
              </span>

              @if (!editMode()) {
                <div class="activity-options-menu" crmActivityTemplateOptions>
                  <mat-menu #activityOptionsMenu="matMenu">
                    @if (currentActivity.isLoggedActivity) {
                      <button mat-menu-item (click)="editMode.set(true)">
                        {{ 'ACTIVITY.MENU.EDIT' | translate }}
                      </button>
                    }
                    <button mat-menu-item (click)="copyLinkToActivity()">
                      {{ 'ACTIVITY.MENU.COPY_LINK' | translate }}
                    </button>
                  </mat-menu>
                  <button mat-icon-button [matMenuTriggerFor]="activityOptionsMenu">
                    <mat-icon>more_vert</mat-icon>
                  </button>
                </div>
              }
            </div>
          </div>
        }

        <!-- Activity subtitle slot -->
        <div class="activity-subtitle">
          <ng-content select="[activity-subtitle]"></ng-content>
        </div>

        <div class="main-content">
          @if (editMode()) {
            @if (activity(); as currentActivity) {
              @if (currentActivity.isLoggedActivity) {
                @if (currentActivity | crmTimelineAssociatedObject: crmObjectIdentifier; as objectIdentifier) {
                  <crm-activity-logger
                    [crmObjectId]="objectIdentifier.crmObjectId"
                    [objectType]="objectIdentifier.objectType"
                    [crmActivityId]="currentActivity.crmObjectId!"
                    [expandAndSelectType]="currentActivity.crmObjectSubtype"
                    [hideCrmObjectAssociations]="hideCrmAssociations()"
                    [activityOwner]="currentActivity.ownerUser"
                    (cancel)="editMode.set(false)"
                    (activityUpdated)="activityChanged($event)"
                  ></crm-activity-logger>
                }
              }
            }
          } @else {
            <div class="preview-content preview-text-container">
              <!-- Activity preview slot -->
              <div
                #previewText
                class="preview-text"
                [crmExpandCollapse]="disableCollapse || state() === 'expanded'"
                [maxHeight]="maxCollapsedHeight"
              >
                <ng-content select="[activity-preview]"></ng-content>

                @if (hasExpandableContent && !disableCollapse) {
                  <div class="preview-text-overlay" [@overlayFade]="overlayState()"></div>
                }
              </div>

              <!-- Activity details slot -->
              <div class="activity-details">
                <ng-content select="[activity-details]"></ng-content>
              </div>

              @if (hasExpandableContent && !disableCollapse) {
                <div class="toggle-collapsed-button" (click)="toggle()">
                  {{
                    (state() === 'collapsed' ? 'COLLAPSIBLE_SECTION.VIEW_MORE' : 'COLLAPSIBLE_SECTION.VIEW_LESS')
                      | translate
                  }}
                </div>
              }

              @if (shouldShowAssociation()) {
                @if (activity(); as currentActivity) {
                  <crm-association-popover [activity]="currentActivity" [hideDivider]="true"></crm-association-popover>
                }
              }
            </div>
          }
        </div>

        @if (showTitle) {
          <mat-card-content>
            <!-- Activity expandable content slot -->
            <div class="expandable-content">
              <ng-content select="[activity-content]"></ng-content>
            </div>
          </mat-card-content>
        }
      </mat-card>
    </mat-card-content>
  </div>
</mat-card>

<ng-template #defaultSubtitleTemplate let-activity="activity">
  <div>{{ activity | crmActivityEffectiveDate | date: dateFormat }}</div>
</ng-template>
