@use 'design-tokens' as dt;

$avatar-width: 36px;
$BREAKPOINT_WIDTH_MOBILE: 768px;

:host {
  display: block;
  margin-left: calc((dt.$spacing-3 * 2 + $avatar-width) * -1);

  ::ng-deep {
    mat-card.bottom-card {
      background: none;
      border-color: transparent;
    }

    mat-card.inner-card {
      background: dt.$card-background-color;
      border-color: dt.$border-color;
    }

    mat-card-header.mat-mdc-card-header + mat-card-content {
      margin-top: 0;
    }

    mat-card-header.mat-mdc-card-header {
      border-bottom: none;
      margin-top: dt.$negative-2;
    }

    .mat-mdc-card-avatar {
      width: unset;
      height: dt.$spacing-5;
    }

    .content-padding .mat-mdc-card-content:last-child {
      padding-bottom: 0;
    }

    .mat-mdc-card-content:last-child {
      padding-bottom: dt.$spacing-1;
    }
  }
}

.card-container {
  display: flex;
}

.content-container {
  margin-left: dt.$negative-3;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: dt.$spacing-3 dt.$spacing-3 0 dt.$spacing-3;
  width: 100%;
  gap: dt.$spacing-2;
  flex-wrap: nowrap;

  .mat-card-title {
    min-width: 0;
  }
}

.main-content {
  padding: 0 dt.$spacing-5 dt.$spacing-3 dt.$spacing-3;
  flex: 1;
  width: 100%;
  min-width: 0;
  margin-right: auto;
}

.preview-content {
  color: dt.$primary-text-color;
  font-size: dt.$font-preset-4-size;
  margin-left: dt.$spacing-3 - dt.$spacing-1;
  margin-right: auto;
  cursor: default;

  .preview-text-container {
    position: relative;
    overflow: hidden;
  }

  .preview-text {
    position: relative;
    overflow: hidden;
    word-break: break-word;
  }

  .preview-text-overlay {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 32px;
    pointer-events: none;
    background: linear-gradient(to bottom, transparent 50%, dt.$card-background-color 100%);
    transform-origin: bottom;
    will-change: transform, opacity;
    opacity: 1;
    transform: scaleY(3);
    z-index: 0;
  }

  .preview-wrapper {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.collapsed {
      clip-path: inset(0 0 0 0);
    }

    &:not(.collapsed) {
      clip-path: inset(0 0 0 0);
    }
  }
}

.activity-subtitle {
  padding-left: dt.$spacing-3 + (dt.$spacing-3 - dt.$spacing-1);
  padding-right: dt.$spacing-5;
}

.mat-mdc-card-subtitle:empty,
.activity-subtitle:empty,
.activity-details:empty {
  display: none;
}

.activity-details {
  padding: dt.$spacing-3;
  padding-top: 0;
  color: dt.$secondary-text-color;
  font-size: dt.$font-preset-5-size;

  .row {
    display: flex;
    flex-wrap: wrap;
    gap: dt.$spacing-3;
  }

  .activity-data-label {
    color: dt.$tertiary-text-color;
    margin-bottom: dt.$spacing-1;
  }

  .activity-data-value {
    color: dt.$primary-text-color;
  }
}

crm-association-popover {
  display: block;
  padding-top: dt.$spacing-2;
}

.title-with-icon {
  display: flex;
  padding: dt.$spacing-2 0;
  min-width: 0;
  align-items: center;

  .title-container {
    display: flex;
    min-width: 0;
    margin-left: dt.$spacing-3 - dt.$spacing-1;
    align-items: center;

    a {
      display: flex;
      align-items: center;
    }
  }

  .mat-mdc-card-actions {
    border-top: none;
    margin: 0;
    padding: 0;
    min-height: 0;
    align-items: center;
    margin-left: dt.$negative-2 - dt.$spacing-1;
  }
}

.expandable-content {
  overflow: hidden;
}

.content-no-expand {
  padding-top: dt.$spacing-3;
}

.mat-mdc-card-subtitle {
  font-weight: bold;
  padding: 0 dt.$font-preset-5-size;
  color: dt.$secondary-text-color;
  margin-top: dt.$spacing-3 - dt.$spacing-1;
}

.mat-card-title {
  font-size: dt.$font-preset-4-size;
  margin: 0;
  padding: 0;
  min-width: 0;
}

.expand-icon-card {
  line-height: 1;
  width: calc(dt.$font-preset-2-size + 2px);
  height: calc(dt.$font-preset-2-size + 2px);
  font-size: calc(dt.$font-preset-2-size + 2px);
  color: dt.$primary-color;
  font-weight: bold;
  margin-right: dt.$spacing-1;
  margin-left: dt.$negative-2 - dt.$spacing-1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  transform-origin: center;
  cursor: pointer;

  &:not(.expanded) {
    &:hover {
      transform: scale(1.1);
    }
    &:active {
      transform: scale(0.95);
    }
  }

  &.expanded {
    transform: rotate(90deg);
  }
}

.activity-title-group {
  display: flex;
  flex-basis: 100%;
  align-items: center;
  justify-content: space-between;

  &:has(.expand-icon-card) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.effective-date-and-options {
  display: flex;
  flex-shrink: 0;
  margin-left: dt.$spacing-2;
  align-items: center;
  .activity-date {
    color: dt.$tertiary-text-color;
    font-size: dt.$font-preset-5-size;
    text-align: right;

    @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
      display: none;
    }
  }
}

.mobile-activity-date {
  margin-top: dt.$spacing-2;
  color: dt.$tertiary-text-color;
  font-size: dt.$font-preset-5-size;
  text-align: right;

  @media (min-width: $BREAKPOINT_WIDTH_MOBILE) {
    display: none;
  }
}

.activity-options-menu {
  display: flex;
  align-items: center;
  height: dt.$spacing-4;

  mat-icon {
    color: dt.$glxy-grey-500;
    font-size: dt.$spacing-4;
  }

  button {
    &:hover {
      mat-icon {
        color: dt.$glxy-grey-700;
      }
    }
  }
}

.icon-container {
  @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
    display: none;
  }
}

.toggle-collapsed-button {
  font-size: dt.$font-preset-5-size;
  color: dt.$glxy-blue-700;
  cursor: pointer;
  margin-top: dt.$spacing-1;
}
