@if (cardInfo(); as cardInfo) {
  <crm-activity-card [activity]="activity()" [icon]="icon()" [showAssociationPopover]="showAssociationPopover()">
    <span activity-title>
      @if (headerText(); as headerText) {
        <strong>{{ headerText }}</strong>
        @if (showPrimaryAssociation()) {
          @if (primaryAssociation(); as primaryAssociation) {
            {{ 'OBJECT_TYPES.FOR_LABEL' | translate }}
            <a [routerLink]="primaryAssociationLink()">{{ primaryAssociation.name }}</a>
          }
        }
      } @else {
        {{ 'ACTIVITY.UNKNOWN_TITLE' | translate }}
      }
    </span>

    <div activity-subtitle>
      @if (cardInfo.url) {
        @if (subtitleForListActivity(); as subtitle) {
          <span class="list-activity-subtitle">{{ subtitle }} </span>
        }
        <span class="link">
          <a [href]="cardInfo.url" target="_blank">{{ cardInfo.urlName || cardInfo.url }}</a>
        </span>
      }
    </div>

    <div activity-preview>
      @if (cardInfo.body) {
        <span class="note-body" [innerHtml]="cardInfo.body | sanitizeHtml"></span>
      }
    </div>
  </crm-activity-card>
}
