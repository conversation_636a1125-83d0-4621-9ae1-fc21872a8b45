@if (cardInfo(); as info) {
  <crm-activity-card [activity]="info.activity" [icon]="isCampaign() ? EmailIcons.campaign : EmailIcons.default">
    <!-- Activity title -->
    <span activity-title>
      @if (titleText(); as title) {
        <strong>{{ title.typeWithVerb | translate }}</strong>
        @if (title.fromText && title.fromName) {
          {{ title.fromText | translate }} {{ title.fromName }}
        }
        @if (title?.status) {
          <span class="status-text"
            ><strong>{{ title?.status | lowercase }}</strong></span
          >
          @if (title.statusReason) {
            <mat-icon
              [glxyTooltip]="title.statusReason"
              [tooltipPositions]="[PopoverPositions['Right'], PopoverPositions['Bottom'], PopoverPositions['Top']]"
              class="warning-icon"
            >
              warning
            </mat-icon>
          }
        }
        @if (title.preposition) {
          {{ title.preposition | translate }}
        }
        @if (showPrimaryAssociation()) {
          @if (primaryAssociation(); as primaryAssociation) {
            <a [routerLink]="routePrefix() + '/' + primaryAssociation.linkUrl">{{ primaryAssociation.name }}</a>
          }
        }
      }
    </span>

    <!-- Email content -->
    <div activity-preview>
      @if (isCampaign()) {
        <span class="campaign-display">
          <span class="bolded">{{ info.emailSubject }}</span>
          {{ 'CONTACT.ACTIVITIES.CREATED_FROM' | translate }}
        </span>
        @if (!isDeletedCampaign()) {
          <span class="campaign-display bolded">
            <a href="{{ campaignLink() }}" target="_blank">{{ campaignName() }}</a>
          </span>
        } @else {
          <span class="campaign-display">
            {{ campaignName() }}
          </span>
        }
      } @else if (isYeswareTrackedEmail() || isYeswareCampaignEmail()) {
        <span class="campaign-display">
          <span class="bolded">{{ info.emailSubject }}</span>
          @if (info.campaignName) {
            {{ 'CONTACT.ACTIVITIES.CREATED_FROM' | translate }}
            {{ info.campaignName }}
          }
        </span>
      } @else {
        @if (info.emailSubject) {
          <div class="email-subject">{{ info.emailSubject }}</div>
        }

        @if (info.emailBody && !isYeswareCampaignEmail()) {
          <div class="email-body" [innerHTML]="info.emailBody"></div>
        }
      }
    </div>
  </crm-activity-card>

  <!-- Related Activities -->
  @if (info.relatedActivities?.length) {
    <div class="show-activities" (click)="showRelatedActivities.set(!showRelatedActivities())">
      @if (showRelatedActivities()) {
        <mat-icon>unfold_less</mat-icon>
        {{ 'ACTIVITY.HIDE_RELATED_ACTIVITIES' | translate }}
      } @else {
        <mat-icon>unfold_more</mat-icon>
        {{ 'ACTIVITY.SHOW_RELATED_ACTIVITIES' | translate }}
      }
    </div>
    @if (showRelatedActivities()) {
      <div class="related-activities">
        @for (activity of info.relatedActivities; track activity; let last = $last) {
          <div class="activity-item" [ngClass]="{ 'last-activity': last }">
            <div class="activity-content">
              @if (activity.emailStatus) {
                <div class="activity-status">
                  {{ activity.emailStatus }}
                  @if (activity.emailStatusReason) {
                    <mat-icon
                      [glxyTooltip]="activity.emailStatusReason"
                      [tooltipPositions]="[
                        PopoverPositions['Right'],
                        PopoverPositions['Bottom'],
                        PopoverPositions['Top'],
                      ]"
                      class="warning-icon"
                    >
                      warning
                    </mat-icon>
                  }
                </div>
              }
              @if (activity.effectiveDate) {
                <div class="activity-date">
                  {{ activity.effectiveDate | date: dateFormat }}
                </div>
              }
            </div>
          </div>
        }
      </div>
    }
  }
}
