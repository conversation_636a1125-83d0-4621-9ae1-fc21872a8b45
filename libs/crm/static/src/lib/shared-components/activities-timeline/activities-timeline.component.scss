@use 'design-tokens' as dt;
@use 'utilities' as ut;

$BREAKPOINT_WIDTH_MOBILE: 768px;

.activity-timeline-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  @include ut.tablet {
    overflow-y: auto;
  }
}

.activity-timeline-container-with-views {
  display: flex;
  flex-direction: column;
  position: relative;
}

.logger {
  padding: dt.$spacing-4;
  margin: 0;
}

.summary {
  padding: 0 dt.$spacing-4;
}

.activity-list {
  display: flex;
  height: inherit;
  overflow-y: hidden;
  overflow-x: auto;
  justify-content: center;
}

.timeline {
  flex: 1;
}

crm-activity-logger {
  max-width: 900px;
  margin: 0 auto dt.$spacing-3;
}

.search-and-filter-container {
  max-width: 900px;
  width: 100%;
  margin: dt.$spacing-3 auto;
  padding: 0 dt.$spacing-4;
}

.search-and-filter-container-with-views {
  position: sticky;
  top: calc(var(--mat-toolbar-standard-height, dt.$glxy-page-toolbar-min-height) - dt.$spacing-3);
  z-index: 1;
  background-color: dt.$primary-background-color;
  width: 100%;
}

.search-and-filter-row {
  display: flex;
  flex-wrap: nowrap;
  @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
    flex-direction: column;
  }
}

.search-and-filter-row-with-views {
  display: flex;
  justify-content: space-between;
  gap: dt.$spacing-2;
  width: 100%;
  padding: dt.$spacing-3 dt.$spacing-4;
  @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
    flex-direction: column;
  }
}

.filters-toggle {
  height: 100%;
  @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
    min-height: 40px;
  }

  &.on {
    background-color: dt.$secondary-background-color;
  }
}

.campaign-toggle-button {
  align-self: center;
  @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
    align-self: flex-start;
  }
}

.filters-toggle--icon {
  overflow: visible;
}

.filters-toggle--indicator {
  position: absolute;
  right: -2px;
  top: 2px;
  width: 8px;
  height: 8px;
  background-color: dt.$primary-color;
  border-radius: 50%;
  pointer-events: none;
}

.filter-bar {
  width: 100%;
  overflow: hidden;
}

.filter-bar--inner-space {
  // spacing is on a separate element to facilitate height animation of the filter-bar
  padding: dt.$spacing-2;
}

.filter-search-group {
  // prevent line-break between filter button and search
  display: flex;
  gap: dt.$spacing-2;
  width: 60%;
  container-name: filter-search-group;
  container-type: inline-size;
  @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
    width: 100%;
    align-items: center;
  }
}

@container filter-search-group (max-width: 325px) {
  .filters-toggle--label {
    display: none;
  }
  .filters-toggle--icon {
    margin-left: 6px; // center the icon when the label is hidden
  }
}

.searchbar {
  min-width: 100px;
  flex-grow: 1;
  margin-bottom: 0;
  @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
    min-width: 0;
    flex-shrink: 1;
    max-width: 100%;
  }
}

.filter-view-button {
  display: flex;
  justify-content: flex-end;
  min-height: 36px;
  width: 40%;
  padding-left: dt.$spacing-2;
  @media (max-width: $BREAKPOINT_WIDTH_MOBILE) {
    padding-top: dt.$spacing-2;
    width: 100%;
    justify-content: flex-start;
  }
}

:host ::ng-deep glxy-form-field.darker-border .input-wrapper {
  border: 1px solid #9e9e9e99;
}
