@let objectType = activityObjectType();
@let hasViewTabsFeatureFlag = hasViewTabsFeatureFlag$ | async;
<div
  #scrollableContainerRef
  [ngClass]="{
    'activity-timeline-container-with-views': hasViewTabsFeatureFlag,
    'activity-timeline-container': true,
  }"
>
  @if (objectType) {
    <crm-activity-logger
      [crmObjectId]="crmObjectId()"
      [objectType]="objectType"
      (activityCreated)="addActivity($event)"
      class="logger"
    ></crm-activity-logger>
  }

  @if (enableAISummary() && !hasTimelineActivitiesFeatureFlag()) {
    <crm-ai-summary [crmObjectId]="crmObjectId()" [crmObjectType]="objectType!" class="summary"></crm-ai-summary>
  }
  @if (hasViewTabsFeatureFlag) {
    <crm-view-tabs
      [tabGroups]="viewTabs$ | async"
      [tabTemplate]="newViewTemplate()"
      [editTabLabel]="'VIEW_TABS.EDIT_TABS' | translate"
      [editTabHint]="'VIEW_TABS.HINT' | translate"
      [nameRequiredError]="'VIEW_TABS.ERRORS.NAME_REQUIRED' | translate"
      [addTabText]="'VIEW_TABS.ADD_TAB' | translate"
      [savedOrder]="tabOrder$$ | async"
      (selectedTabChanged)="changeViewFilters($event.filters)"
      (tabsUpdated)="saveTabChanges($event)"
      (orderUpdated)="saveTabOrder($event)"
      [scrollable]="false"
    >
      <div class="search-and-filter-container-with-views">
        <div class="search-and-filter-row-with-views">
          <div class="filter-search-group">
            <button
              mat-stroked-button
              class="filters-toggle"
              data-action="clicked-filters-toggle"
              [ngClass]="{ on: showFiltersOpen }"
              (click)="toggleFilterBar()"
            >
              <mat-icon class="filters-toggle--icon">
                filter_list
                @if (filtersApplied()) {
                  <span class="filters-toggle--indicator"></span>
                }
              </mat-icon>
              <span class="filters-toggle--label">{{ 'GALAXY.TABLE.FILTERS.TOGGLE_BUTTON' | translate }}</span>
            </button>

            <glxy-form-field
              class="searchbar"
              suffixIcon="search"
              bottomSpacing="none"
              [ngClass]="{ 'darker-border': hasTimelineActivitiesFeatureFlag() }"
            >
              <input type="text" [placeholder]="'ACTIONS.SEARCH' | translate" matInput [formControl]="searchControl" />
            </glxy-form-field>
          </div>
          <mat-slide-toggle
            class="campaign-toggle-button"
            [checked]="excludeCampaignsOverride()"
            (change)="toggleExcludeCampaignsOverride()"
          >
            {{ 'VIEWS.PRESET_FILTERS.EXCLUDE_CAMPAIGNS' | translate }}
          </mat-slide-toggle>
        </div>
        <div class="filter-bar" [@openClose]="showFiltersOpen ? 'open' : 'closed'">
          <div class="filter-bar--inner-space">
            <glxy-filter-chips
              #filterChipRef
              [addFiltersHeader]="'ACTIVITY.TITLE' | translate"
              (filtersChanged)="updateFilters($event, false)"
              filters-area
            ></glxy-filter-chips>
          </div>
        </div>
      </div>
      <div class="activity-list">
        <crm-timeline
          #crmTimelineRef
          class="timeline"
          [crmObjectId]="crmObjectId()"
          [objectType]="objectType!"
          (activitiesChanged)="handleActivitiesChanged($event)"
          [showCopyLink]="true"
        ></crm-timeline>
      </div>
      @if (showLoadMore) {
        <glxy-infinite-scroll-trigger
          [visiblilityMargin]="10"
          (isVisible)="loadMoreActivities()"
        ></glxy-infinite-scroll-trigger>
      }
    </crm-view-tabs>
  } @else {
    <div class="search-and-filter-container">
      <div class="search-and-filter-row">
        <div class="filter-search-group">
          <button
            mat-stroked-button
            class="filters-toggle"
            data-action="clicked-filters-toggle"
            [ngClass]="{ on: showFiltersOpen }"
            (click)="toggleFilterBar()"
          >
            <mat-icon class="filters-toggle--icon">
              filter_list
              @if (filtersApplied()) {
                <span class="filters-toggle--indicator"></span>
              }
            </mat-icon>
            <span class="filters-toggle--label">{{ 'GALAXY.TABLE.FILTERS.TOGGLE_BUTTON' | translate }}</span>
          </button>

          <glxy-form-field
            class="searchbar"
            suffixIcon="search"
            bottomSpacing="none"
            [ngClass]="{ 'darker-border': hasTimelineActivitiesFeatureFlag() }"
          >
            <input type="text" [placeholder]="'ACTIONS.SEARCH' | translate" matInput [formControl]="searchControl" />
          </glxy-form-field>
        </div>
        <crm-filter-view
          class="filter-view-button"
          [filters]="filters()"
          (filtersChange)="updateFilters($event, true)"
          [keys]="keys"
          [presetFilters]="presetFilters()"
        ></crm-filter-view>
      </div>
      <div class="filter-bar" [@openClose]="showFiltersOpen ? 'open' : 'closed'">
        <div class="filter-bar--inner-space">
          <glxy-filter-chips
            #filterChipRef
            [addFiltersHeader]="'ACTIVITY.TITLE' | translate"
            (filtersChanged)="updateFilters($event, false)"
            filters-area
          ></glxy-filter-chips>
        </div>
      </div>
    </div>
    <crm-timeline
      #crmTimelineRef
      class="timeline"
      [crmObjectId]="crmObjectId()"
      [objectType]="objectType!"
      (activitiesChanged)="handleActivitiesChanged($event)"
      [showCopyLink]="true"
    >
    </crm-timeline>
    <!-- this must be predicated on the first load already being done so more items will load if the page does not initially overflow -->
    @if (showLoadMore) {
      <glxy-infinite-scroll-trigger
        [visiblilityMargin]="60"
        (isVisible)="loadMoreActivities()"
      ></glxy-infinite-scroll-trigger>
    }
  }
</div>
