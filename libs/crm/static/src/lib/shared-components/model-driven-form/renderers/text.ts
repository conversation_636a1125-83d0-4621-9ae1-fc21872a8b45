import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  Inject,
  ViewContainerRef,
  ViewEncapsulation,
  ComponentRef,
  ChangeDetectorRef,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { provideAnimations } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  of,
  skipWhile,
  startWith,
  switchMap,
} from 'rxjs';
import {
  ObjectType,
  RenderableFieldInterface,
  FieldOptionsInjectionToken,
  FieldOptionsDependencies,
} from '../../../tokens-and-interfaces';
import { SEARCH_DEBOUNCE_MS } from '../../../constants';
import { FieldValidation } from '../field-validation';
import { InlineEditService } from '../inline-edit.service';
import { CrmFieldWarningDirective } from './model-driven-form-error/field-warning.directive';
import { FieldWarningService } from './model-driven-form-error/field-warning.service';
import { CrmModelDrivenFormErrorComponent } from './model-driven-form-error/model-driven-form-error.component';
import { stopChromeAutofill } from './stop-chrome-autofill';
import { FieldValueInterface } from '../../../tokens-and-interfaces';
import {
  CrmFieldService,
  StandardExternalIds,
  SystemFieldIds,
} from '../../../shared-services/crm-services/field.service';
import { SyncIconComponent } from './accounts/sync-icon/sync-icon.component';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { AutoFill } from '@galaxy/crm/components/animations';
import { UpdaterService } from './updater.service';
import { filter, map, tap } from 'rxjs/operators';

export interface AutocompleteOption {
  value: string;
  type: 'match' | 'divider' | 'non-match';
  disabled?: boolean;
}

export class TextFieldHandler implements RenderableFieldInterface {
  private component: ComponentRef<TextRendererComponent> | null = null;
  formControl: UntypedFormControl;

  constructor(
    public objectType: ObjectType,
    public fieldId: string,
    public name: string,
    public label: string,
    public value: string,
    public description: string,
    public readOnly = false,
    public validation: FieldValidation = new FieldValidation(),
  ) {
    this.formControl = new UntypedFormControl(this.value);
  }

  setValidation(validation: FieldValidation): void {
    this.validation = validation;
    this.formControl.setValidators(validation.getValidators());
    this.formControl.setAsyncValidators(validation.getAsyncValidators());
  }

  hasModification(): boolean {
    return this.formControl?.dirty;
  }

  fieldValue(): FieldValueInterface {
    return {
      fieldId: this.fieldId,
      stringValue: this.formControl.value,
    };
  }

  render(viewContainerRef: ViewContainerRef): void {
    viewContainerRef.clear();
    const component = viewContainerRef.createComponent<TextRendererComponent>(TextRendererComponent);
    component.instance.objectType = this.objectType;
    component.instance.fieldId = this.fieldId;
    component.instance.control = this.formControl;
    component.instance.label = this.label;
    component.instance.validation = this.validation;
    component.instance.readOnly = this.readOnly;
    this.component = component;
  }
}

const EXCLUDED_FROM_AUTOCOMPLETE = [
  StandardExternalIds.FirstName,
  StandardExternalIds.LastName,
  SystemFieldIds.ContactExternalID,
  SystemFieldIds.CompanyExternalID,
];

@Component({
  template: `
    <div class="crm-read-only-container" *ngIf="readOnly; else formField">
      <div class="crm-field-label">
        {{ label | translate }}
        <crm-sync-icon [fieldId]="fieldId"></crm-sync-icon>
      </div>
      <div class="crm-field-value">
        <!-- ng-container necessary since Angular interpolation will add empty space -->
        <ng-container *ngIf="control.value">
          {{ control.value }}
        </ng-container>
      </div>
    </div>
    <ng-template #formField>
      <glxy-form-field
        [crmFieldWarning]="validation"
        [warningControl]="control"
        [attr.data-testid]="'field-with-id-' + fieldId"
      >
        <glxy-label *ngIf="label">
          {{ label | translate }}
          <crm-sync-icon [fieldId]="fieldId"></crm-sync-icon>
        </glxy-label>
        <input
          matInput
          [formControl]="control"
          [matAutocomplete]="auto"
          [placeholder]="'MODEL_DRIVEN_FORM.ADD_FIELD_VALUE' | translate: { fieldName: label?.toLowerCase() }"
          #textInput="matInput"
          maxlength="4000"
          id="{{ disableAutoComplete }}"
          name="{{ disableAutoComplete }}"
          autocomplete="{{ disableAutoComplete }}"
          [@autoFill]="autoFillState"
          (keydown.enter)="$event.preventDefault()"
          (focus)="initializeField()"
          (blur)="saveField()"
        />
        @if (control.value) {
          <button matSuffix mat-icon-button class="crm-field-clear-button" aria-label="Clear" (click)="clear()">
            <mat-icon>close</mat-icon>
          </button>
        }
        <mat-autocomplete #auto="matAutocomplete">
          <mat-option
            *ngFor="let option of filteredOptions$ | async"
            [value]="option.type === 'divider' ? '' : option.value"
            [disabled]="option.disabled"
            [class.option-divider]="option.type === 'divider'"
            [class.option-non-match]="option.type === 'non-match'"
            (mousedown)="option.type === 'divider' ? $event.preventDefault() : null"
          >
            <span *ngIf="option.type === 'divider'" class="divider-text">
              {{ 'MODEL_DRIVEN_FORM.OTHER_OPTIONS' | translate }}
            </span>
            <span *ngIf="option.type !== 'divider'">{{ option.value }}</span>
          </mat-option>
        </mat-autocomplete>
        <glxy-error *ngIf="control.value?.length === 4000">
          {{ 'MODEL_DRIVEN_FORM.STRING_LIMIT_HINT' | translate: { characters: control.value?.length || 0 } }}
        </glxy-error>
        <crm-model-driven-form-error
          [objectType]="objectType"
          [fieldId]="fieldId"
          [label]="label"
          [control]="control"
          [validation]="validation"
        ></crm-model-driven-form-error>
      </glxy-form-field>
    </ng-template>
  `,
  imports: [
    CommonModule,
    MatInputModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    CrmModelDrivenFormErrorComponent,
    CrmFieldWarningDirective,
    MatAutocompleteModule,
    SyncIconComponent,
    MatIcon,
    MatIconButton,
  ],
  providers: [FieldWarningService, provideAnimations()],
  styleUrls: ['./renderers.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [AutoFill],
})
export class TextRendererComponent {
  fieldId!: string;
  objectType!: ObjectType;
  label!: string;
  readOnly!: boolean;
  validation!: FieldValidation;

  filteredOptions$: Observable<AutocompleteOption[]> = of([]);
  initialized$ = new BehaviorSubject<boolean>(false);
  #control!: UntypedFormControl;
  set control(formControl: UntypedFormControl) {
    this.#control = formControl;
    const controlChanges$ = this.#control.valueChanges.pipe(
      startWith(this.#control.value),
      debounceTime(SEARCH_DEBOUNCE_MS),
    );
    const initialized$ = this.initialized$.pipe(distinctUntilChanged());
    this.filteredOptions$ = combineLatest([controlChanges$, initialized$]).pipe(
      takeUntilDestroyed(this.destroyRef),
      skipWhile(([, initialized]: [string, boolean]) => !initialized),
      switchMap(([value]: [string, boolean]) => {
        // turn off for first name, last name, hardcoded for the time being
        for (const id of EXCLUDED_FROM_AUTOCOMPLETE) {
          if (this.fieldId === this.fieldService.getFieldId(id)) {
            return of([]);
          }
        }
        return this.fieldOptionsDependencies.getFieldOptions(this.fieldId, value).pipe(
          switchMap((matchingOptions: string[]) => {
            // If no search value, return all options as matches
            if (!value || value.trim() === '') {
              return of(matchingOptions.map((option) => ({ value: option, type: 'match' as const })));
            }

            // If we have 5 or more matching options, only show matches
            if (matchingOptions.length >= 5) {
              return of(matchingOptions.map((option) => ({ value: option, type: 'match' as const })));
            }

            // Get all options to find non-matching ones
            return this.fieldOptionsDependencies.getFieldOptions(this.fieldId, '').pipe(
              map((allOptions: string[]) => {
                const nonMatchingOptions = allOptions.filter((option) => !matchingOptions.includes(option));

                const result: AutocompleteOption[] = [
                  ...matchingOptions.map((option) => ({ value: option, type: 'match' as const })),
                ];

                if (nonMatchingOptions.length > 0) {
                  // Add divider if we have both matching and non-matching options
                  if (matchingOptions.length > 0) {
                    result.push({ value: '---', type: 'divider', disabled: true });
                  }

                  result.push(...nonMatchingOptions.map((option) => ({ value: option, type: 'non-match' as const })));
                }
                return result;
              }),
            );
          }),
        );
      }),
    );
  }

  get control(): UntypedFormControl {
    return this.#control;
  }

  disableAutoComplete: string = stopChromeAutofill();
  autoFillState = 'default';
  private isInlineEdit = toSignal(this.inlineEditService.isInlineEdit$);

  constructor(
    @Inject(FieldOptionsInjectionToken) private readonly fieldOptionsDependencies: FieldOptionsDependencies,
    readonly destroyRef: DestroyRef,
    private readonly inlineEditService: InlineEditService,
    private readonly fieldService: CrmFieldService,
    private changeDetRef: ChangeDetectorRef,
    private updaterService: UpdaterService,
  ) {
    this.updaterService.clearedField$
      .pipe(
        takeUntilDestroyed(),
        filter((f: FieldValueInterface) => f.fieldId === this.fieldId),
      )
      .subscribe((_) => this.clear());

    this.updaterService.updatedField
      .pipe(
        takeUntilDestroyed(),
        tap((data) => {
          if (data.stringValue && data.fieldId && data.fieldId === this.fieldId) {
            this.autoFillState = 'highlight';
            setTimeout(() => {
              this.autoFillState = 'default';
              this.changeDetRef.detectChanges();
            }, 1200);
            this.control.setValue(data.stringValue);
            this.control.markAsDirty();
            this.changeDetRef.detectChanges();
          }
        }),
      )
      .subscribe();
  }

  clear(): void {
    this.control.reset();
    this.control.markAsDirty();
    if (this.isInlineEdit()) {
      this.saveField();
    }
  }

  initializeField(): void {
    this.initialized$.next(true);
  }

  saveField(): void {
    this.inlineEditService.saveInlineEdit(this.objectType, [
      {
        fieldId: this.fieldId,
        stringValue: this.control.value,
      },
    ]);
  }
}
