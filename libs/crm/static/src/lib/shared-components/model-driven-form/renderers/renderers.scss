@use 'design-tokens' as *;

.stencil-shimmer {
  height: $font-preset-3-size;
}

.crm-read-only-container {
  overflow-wrap: anywhere;
}

.crm-field-label {
  display: block;
  @include text-preset-5;
  color: $secondary-font-color;
}

.crm-field-value {
  display: block;
  min-height: 1.4em;
  margin-bottom: $spacing-3;

  &:empty:before {
    content: '—';
    color: $tertiary-font-color;
  }
}

.crm-field-clear-button {
  color: $icon-color;
}

.glxy-form-field.crm-field-warning {
  .prefix-icon {
    color: $warn-icon-color;
  }
}

.company-options .autocomplete-suggestion,
.company-options .mat-mdc-option,
.company-options .mat-option:hover,
.company-options .mat-option.mat-selected,
.company-options .mat-option.mat-selected:hover,
.company-options .mat-option.mat-active,
.company-options .mat-option.mat-active:hover {
  padding: $spacing-2 $spacing-3 $spacing-3 $spacing-1;
  line-height: $spacing-3;
  border-bottom: 1px solid $glxy-grey-300;
  background-image: url('../../../../assets/images/grey-pin.png');
  background-repeat: no-repeat;
  background-position: $spacing-2 30%;
  background-size: 12px 21px;
  white-space: nowrap;
  font-size: $font-preset-4-size;
  color: $glxy-grey-50;
}

.company-options .mat-mdc-option:hover {
  background-image: url('../../../../assets/images/pin-selected.png');
  background-color: $row-hover-bg-color;
  background-repeat: no-repeat;
  background-position: $spacing-2 30%;
  background-size: 12px 21px;
}

.company-options .mat-mdc-option.mat-active,
.company-options .mat-option.mat-active:hover,
.company-options .mat-option.mat-selected,
.company-options .mat-option.mat-selected:hover {
  background-color: $row-hover-bg-color;
  background-image: url('../../../../assets/images/pin-selected.png');
}

.autocomplete-suggestion span.main-text {
  color: $primary-font-color;
  padding-left: $spacing-4;
}

.autocomplete-suggestion span.secondary-text {
  font-size: $font-preset-5-size;
  padding-left: $spacing-4;
  color: $glxy-grey-500;
}

// Autocomplete divider and non-matching option styles
.mat-mdc-option.option-divider {
  pointer-events: none;
  cursor: default;
  background-color: $glxy-grey-100;
  border-top: 1px solid $glxy-grey-300;
  border-bottom: 1px solid $glxy-grey-300;

  .divider-text {
    font-size: $font-preset-5-size;
    color: $secondary-font-color;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.mat-mdc-option.option-non-match {
  color: $tertiary-font-color;
  opacity: 0.8;

  &:hover {
    opacity: 1;
    color: $primary-font-color;
  }
}
