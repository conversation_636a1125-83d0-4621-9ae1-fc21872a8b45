import { dasherize } from '@angular-devkit/core/src/utils/strings';
import { Component, HostBinding, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'crm-vertical-nav-card',
  template: `
    <ng-container *ngIf="active">
      <div class="vertical-nav-card-content">
        <ng-content></ng-content>
      </div>
    </ng-container>
  `,
  imports: [CommonModule],
})
export class VerticalNavCardComponent implements OnInit {
  @Input() label = '';
  @Input() active = false;
  @Input() tabId = '';
  @HostBinding('class') class = 'vertical-nav-card';

  public id = '';

  ngOnInit(): void {
    if (this.tabId) {
      this.id = this.tabId;
    } else {
      this.id = dasherize(this.label);
    }
  }
}
