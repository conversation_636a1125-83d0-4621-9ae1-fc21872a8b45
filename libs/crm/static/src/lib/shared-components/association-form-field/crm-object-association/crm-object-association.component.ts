import { CommonModule } from '@angular/common';
import { Component, inject, input, model } from '@angular/core';
import { FormFieldAssociation } from '../interface';
import { CrmAssociationFormFieldComponent } from '../association-form-field.component';
import { CrmInjectionToken, CrmObjectInjectionToken, ObjectType } from '../../../tokens-and-interfaces';
import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  from,
  map,
  Observable,
  switchMap,
} from 'rxjs';
import { ListCrmObjectsRequestInterface } from '@vendasta/crm';
import { CrmFiltersService, CrmObjectDisplayService, CrmObjectService } from '../../../shared-services';
import { TranslateByObjectTypePipe } from '../../../i18n';

@Component({
  selector: 'crm-object-association-form-field',
  templateUrl: './crm-object-association.component.html',
  imports: [CommonModule, CrmAssociationFormFieldComponent, TranslateByObjectTypePipe],
})
export class CrmObjectAssociationFormFieldComponent {
  objectType = input.required<ObjectType>();
  associationsList = model<FormFieldAssociation[]>([]);
  private readonly customizationService = inject(CrmObjectDisplayService);
  private readonly searchTerm$$ = new BehaviorSubject<string>('');
  private readonly searchTerm$ = this.searchTerm$$.asObservable();
  private readonly config = inject(CrmInjectionToken);
  private readonly crmObjectService = inject(CrmObjectService);
  private readonly crmFiltersService = inject(CrmFiltersService);
  protected readonly options$ = this.setupSearchTerm();
  private readonly crmObjectDependencies = inject(CrmObjectInjectionToken, { optional: true });
  protected updateSearchTerm(searchTerm?: unknown): void {
    if (typeof searchTerm === 'string') {
      this.searchTerm$$.next(searchTerm.trim());
    } else {
      this.searchTerm$$.next('');
    }
  }
  protected handleAdd(association: FormFieldAssociation): void {
    this.associationsList.update((prevAssociations) => {
      if (!prevAssociations.find((a) => a.crmObjectId === association.crmObjectId)) {
        return [...prevAssociations, association];
      }
      return prevAssociations;
    });
  }

  protected handleRemove(association: FormFieldAssociation): void {
    this.associationsList.update((prevAssociations) => {
      return prevAssociations.filter((a) => a.crmObjectId !== association.crmObjectId);
    });
  }

  async buildListCrmObjectsRequest(namespace: string, searchTerm: string): Promise<ListCrmObjectsRequestInterface> {
    const initialFilters = await this.crmFiltersService.getInitialAppliedFilters$(
      this.objectType(),
      this.crmObjectDependencies?.objectSubtype$,
    );
    return {
      namespace,
      search: { searchTerm },
      pagingOptions: { cursor: '', pageSize: 10 },
      filtersV2: this.crmObjectService.convertFilters(initialFilters),
    } as ListCrmObjectsRequestInterface;
  }

  protected setupSearchTerm(): Observable<FormFieldAssociation[]> {
    return combineLatest([this.searchTerm$, this.config.namespace$]).pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(([searchTerm, namespace]) => from(this.buildListCrmObjectsRequest(namespace, searchTerm))),
      switchMap((r: ListCrmObjectsRequestInterface) => this.crmObjectService.listObjects(this.objectType(), r)),
      map(
        (filteredOptions) =>
          filteredOptions?.crmObjects?.map(
            (option) =>
              ({
                name: this.crmObjectDependencies?.getDisplayName
                  ? this.crmObjectDependencies?.getDisplayName(option)
                  : this.customizationService.getObjectDisplayName(this.objectType(), option),
                associationInfo: this.customizationService.getAssociationInfo(this.objectType(), option),
                crmObjectId: option.crmObjectId,
                crmObjectType: this.objectType(),
              }) as FormFieldAssociation,
          ) || [],
      ),
    );
  }
}
