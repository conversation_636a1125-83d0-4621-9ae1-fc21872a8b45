import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, model, Output, input, computed } from '@angular/core';
import { FormFieldAssociation } from '../interface';
import { CrmAssociationFormFieldComponent } from '../association-form-field.component';
import {
  AssociationFormField,
  CrmInjectionToken,
  ObjectType,
  SimplifiedOpportunity,
} from '../../../tokens-and-interfaces';
import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  from,
  map,
  Observable,
  of,
  switchMap,
} from 'rxjs';
import { ListCrmObjectsRequestInterface, ListCrmObjectsResponseInterface } from '@vendasta/crm';
import { CrmFiltersService, CrmObjectDisplayService, CrmObjectService } from '../../../shared-services';
import { TranslateModule } from '@ngx-translate/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'crm-multi-object-association-form-field',
  templateUrl: './crm-multi-object-association.component.html',
  imports: [CommonModule, CrmAssociationFormFieldComponent, TranslateModule],
})
export class CrmMultiObjectAssociationFormFieldComponent {
  @Input({ required: true }) set accountGroupId(value: string | null) {
    this.opportunityService?.setAccountGroupId(value ?? '');
  }

  isMobile = input<boolean>(false);

  protected objectType = model<ObjectType>('Contact');
  private readonly objectType$ = toObservable(this.objectType);
  protected objectSubtype = model<string | undefined>();
  private readonly objectSubtype$ = toObservable(this.objectSubtype);

  availableObjectTypes = input<ObjectType[]>();
  associationFormFields = model<AssociationFormField[]>([]);
  filteredAssociationFormFields = computed(() => {
    const availableObjectTypes = this.availableObjectTypes();
    return this.associationFormFields().filter((field) => {
      if (!availableObjectTypes) {
        return true;
      }
      return availableObjectTypes.includes(field.type);
    });
  });

  @Output() associationFormFieldsOutput = new EventEmitter<AssociationFormField[]>();

  private readonly customizationService = inject(CrmObjectDisplayService);
  private readonly searchTerm$$ = new BehaviorSubject<string>('');
  private readonly searchTerm$ = this.searchTerm$$.asObservable();
  private readonly config = inject(CrmInjectionToken);
  private readonly crmObjectService = inject(CrmObjectService);
  private readonly crmFiltersService = inject(CrmFiltersService);
  protected readonly opportunityService = this.config.services?.opportunityService;
  protected readonly options$ = this.setupSearchTerm();

  protected updateSearchTerm(searchTerm?: unknown): void {
    if (typeof searchTerm === 'string') {
      this.searchTerm$$.next(searchTerm.trim());
    } else {
      this.searchTerm$$.next('');
    }
  }

  protected handleRemove(association: FormFieldAssociation): void {
    this.associationFormFields.update((associationFormFields) => {
      let found = false;
      const updatedAssociationFormFields = associationFormFields.map((field) => {
        if (field.type === association.crmObjectType) {
          found = true;
          const updatedAssociations = field.associations.filter((a) => a.crmObjectId !== association.crmObjectId);
          return { ...field, associations: updatedAssociations };
        }
        return field;
      });
      if (!found) {
        console.warn(`Association form field for ${association.crmObjectType} not found`);
      }
      this.associationFormFieldsOutput.emit([...updatedAssociationFormFields]);
      return updatedAssociationFormFields;
    });
  }

  protected handleUpdate(associationFormFields: AssociationFormField[]): void {
    this.associationFormFields.update((fields) => {
      return fields.map((field) => {
        const newAssociations =
          associationFormFields.find((f) => f.type === field.type)?.associations || field.associations;
        return { ...field, associations: newAssociations };
      });
    });
    this.associationFormFieldsOutput.emit(this.associationFormFields());
  }

  protected updateObjectType([objectType, objectSubtype]: [ObjectType, string?]): void {
    this.objectType.set(objectType);
    this.objectSubtype.set(objectSubtype);
  }

  private async buildListCrmObjectsRequest(
    namespace: string,
    searchTerm: string,
    objectSubtype?: string,
  ): Promise<ListCrmObjectsRequestInterface> {
    const objectSubtype$ = of(objectSubtype || '');
    const initialFilters = await this.crmFiltersService.getInitialAppliedFilters$(this.objectType(), objectSubtype$);
    return {
      namespace,
      search: { searchTerm },
      pagingOptions: { cursor: '', pageSize: 10 },
      filtersV2: this.crmObjectService.convertFilters(initialFilters),
      crmObjectSubtype: objectSubtype,
    } as ListCrmObjectsRequestInterface;
  }

  protected setupSearchTerm(): Observable<FormFieldAssociation[]> {
    return combineLatest([this.searchTerm$, this.config.namespace$, this.objectType$, this.objectSubtype$]).pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(([searchTerm, namespace, objectType, objectSubtype]) => {
        return from(this.buildListCrmObjectsRequest(namespace, searchTerm, objectSubtype)).pipe(
          map(
            (r) =>
              [r, objectType, objectSubtype, searchTerm] as [
                ListCrmObjectsRequestInterface,
                ObjectType,
                string | undefined,
                string,
              ],
          ),
        );
      }),
      switchMap(
        ([r, objectType, objectSubtype, searchTerm]: [
          ListCrmObjectsRequestInterface,
          ObjectType,
          string | undefined,
          string,
        ]) => {
          if (objectType === 'Opportunity') {
            if (this.opportunityService) {
              return this.opportunityService.searchOpportunities(searchTerm).pipe(
                map((opportunities) => {
                  return [this.mapOpportunityAssociations(opportunities), objectType, objectSubtype] as [
                    FormFieldAssociation[],
                    ObjectType,
                    string | undefined,
                  ];
                }),
                catchError((error) => {
                  console.error('Error in searchOpportunities:', error);
                  return of([[], objectType, objectSubtype] as [
                    FormFieldAssociation[],
                    ObjectType,
                    string | undefined,
                  ]);
                }),
              );
            } else {
              return of([[], objectType, objectSubtype] as [FormFieldAssociation[], ObjectType, string | undefined]);
            }
          }
          return this.crmObjectService.listObjects(objectType, r).pipe(
            map((filteredOptions) => {
              return [
                this.mapFilteredOptions(filteredOptions, objectType, objectSubtype),
                objectType,
                objectSubtype,
              ] as [FormFieldAssociation[], ObjectType, string | undefined];
            }),
            catchError((error) => {
              console.error('Error in listObjects:', error);
              return of([[], objectType, objectSubtype] as [FormFieldAssociation[], ObjectType, string | undefined]);
            }),
          );
        },
      ),
      // Don't emit results if input types have changed, or the loading state will mess up
      filter(([_, objectType, objectSubtype]) => {
        return objectType === this.objectType() && objectSubtype === this.objectSubtype();
      }),
      map(([filteredOptions]) => filteredOptions),
    );
  }

  private mapFilteredOptions(
    filteredOptions: ListCrmObjectsResponseInterface,
    objectType: ObjectType,
    objectSubtype?: string,
  ): FormFieldAssociation[] {
    return (
      filteredOptions?.crmObjects?.map(
        (option) =>
          ({
            name: this.customizationService.getObjectDisplayName(objectType, option),
            associationInfo: this.customizationService.getAssociationInfo(objectType, option),
            crmObjectId: option.crmObjectId,
            crmObjectType: objectType,
            crmObjectSubtype: objectSubtype,
          }) as FormFieldAssociation,
      ) || []
    );
  }

  private mapOpportunityAssociations(opportunities: SimplifiedOpportunity[]): FormFieldAssociation[] {
    return (
      opportunities?.map(
        (option) =>
          ({
            name: option.name,
            crmObjectId: option.opportunityId,
            crmObjectType: this.objectType(),
          }) as FormFieldAssociation,
      ) || []
    );
  }
}
