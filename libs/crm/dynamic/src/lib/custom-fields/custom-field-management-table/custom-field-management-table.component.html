@if (loading() === false) {
  <glxy-table-container
    @fadeIn
    [dataSource]="dataSource"
    [columns]="columns"
    [pageSizeOptions]="[5, 10, 25, 50, 100]"
    [pageSize]="25"
    [border]="true"
    [fullWidth]="true"
    [showFooter]="true"
  >
    <glxy-table-content-header
      [showSearch]="true"
      [showFilters]="false"
      [showColumnArrange]="false"
      [showFiltersOpen]="false"
      [showSort]="false"
      [showExport]="false"
      [showActions]="false"
    ></glxy-table-content-header>
    <table mat-table [@loadingBehavior]="(dataSource.loading$ | async) ? 'loading' : 'finished'">
      <tr mat-header-row *matHeaderRowDef="[]"></tr>
      <ng-container matColumnDef="select">
        <th mat-header-cell *matHeaderCellDef>
          <glxy-table-selection></glxy-table-selection>
        </th>
        <td mat-cell *matCellDef="let row">
          <glxy-table-selection [row]="row"></glxy-table-selection>
        </td>
      </ng-container>
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let row">
          <button mat-icon-button [matMenuTriggerFor]="menu">
            <mat-icon>more_vert</mat-icon>
            <mat-menu #menu="matMenu">
              <div matTooltip="Edit">
                <button mat-menu-item (click)="openEdit(row)">
                  <span>{{ 'CUSTOM_FIELDS.EDIT' | translate }}</span>
                </button>
              </div>
              <div matTooltip="Delete">
                <button mat-menu-item (click)="openDelete(row)" [disabled]="isSystemFieldMap()[row.id]">
                  <span>{{ 'CUSTOM_FIELDS.DELETE' | translate }}</span>
                </button>
              </div>
            </mat-menu>
          </button>
        </td>
      </ng-container>
      <tr mat-row *matRowDef="let row; columns: []"></tr>
    </table>
  </glxy-table-container>
} @else {
  <div class="spinner-container">
    <glxy-loading-spinner></glxy-loading-spinner>
  </div>
}
