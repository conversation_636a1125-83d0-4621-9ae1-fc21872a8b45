import { Component, EventEmitter, Inject, Input, signal, WritableSignal, Output, computed } from '@angular/core';
import { CrmDependencies, CrmInjectionToken, ObjectType } from '@galaxy/crm/static';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

import { MatIconModule } from '@angular/material/icon';

import {
  CellData,
  GalaxyColumnDef,
  GalaxyDataSource,
  GalaxyTableModule,
  PagedListRequestInterface,
  PagedResponseInterface,
  Row,
} from '@vendasta/galaxy/table';
import { map, Observable, of, switchMap, tap, catchError, combineLatest, shareReplay } from 'rxjs';
import {
  CRMFieldSchemaApiService,
  FieldSchemaInterface,
  FieldType,
  ListFieldSchemaResponseInterface,
} from '@vendasta/crm';
import {
  MatCell,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderRow,
  MatRow,
  MatTable,
  MatTableModule,
} from '@angular/material/table';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatMenu, MatMenuItem, MatMenuModule } from '@angular/material/menu';
import { ReactiveFormsModule } from '@angular/forms';

import { CustomFieldsManagementTableChangesService } from '../custom-fields-management-table-changes.service';
import { LoadingBehavior, FadeIn } from '@galaxy/crm/components/animations';
import { isSystemField } from '../utils';

export interface DeleteData {
  namespace: string;
  fieldId: string;
  objectType: ObjectType;
}

@Component({
  selector: 'crm-list-custom-fields',
  templateUrl: './custom-field-management-table.component.html',
  imports: [
    CommonModule,
    MatMenuModule,
    MatTableModule,
    MatButtonModule,
    TranslateModule,
    GalaxyButtonLoadingIndicatorModule,
    GalaxyFormFieldModule,
    MatIconModule,
    GalaxyTableModule,
    MatHeaderRow,
    MatTable,
    MatRow,
    GalaxyLoadingSpinnerModule,
    GalaxyEmptyStateModule,
    MatCell,
    MatColumnDef,
    MatHeaderCell,
    MatMenu,
    MatMenuItem,
    ReactiveFormsModule,
  ],
  animations: [LoadingBehavior, FadeIn],
})
export class ListCustomFieldManagementTableComponent {
  @Output() editField = new EventEmitter<Row>();
  @Output() deleteField = new EventEmitter<DeleteData>();

  @Input({ required: true }) set objType(objType: string) {
    this.objectType = objType as ObjectType;
  }

  get objType() {
    return this.objectType;
  }
  objectType: ObjectType = this.objType as ObjectType;
  private allFields: WritableSignal<FieldSchemaInterface[]> = signal([]);
  isSystemFieldMap = computed(() => {
    return this.allFields().reduce(
      (acc, field) => {
        acc[field.fieldId] = isSystemField(field?.namespace);
        return acc;
      },
      {} as Record<string, boolean>,
    );
  });
  dataSource: GalaxyDataSource<Row>;
  loading: WritableSignal<boolean> = signal(false);

  columns: GalaxyColumnDef[] = [
    {
      id: 'name',
      title: 'Name',
    },
    {
      id: 'external_id',
      title: 'External ID',
      hidden: true,
    },
    {
      id: 'description',
      title: 'Description',
    },
    {
      id: 'field_type',
      title: 'Field type',
    },
    {
      id: 'created',
      title: 'Created',
    },
    {
      id: 'namespace',
      title: 'Namespace',
    },
    {
      id: 'actions',
      title: '',
    },
  ];

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly crmFieldSchemaApiService: CRMFieldSchemaApiService,
    private readonly tableChangesService: CustomFieldsManagementTableChangesService,
  ) {
    const paginatedAPI = {
      get: (req: PagedListRequestInterface<void>): Observable<PagedResponseInterface<Row>> => {
        this.tableChangesService.resetChanges(this.objectType);
        return combineLatest([this.tableChangesService.changes$, this.listFieldSchema(req)]).pipe(
          map(([{ deletedFieldIds, createdFields, updatedFields }, resp]) => {
            resp.fieldSchemas = this.applyCreatedFieldChanges(createdFields.get(this.objectType), resp.fieldSchemas);
            resp.fieldSchemas = this.applyUpdatedFieldChanges(updatedFields.get(this.objectType), resp.fieldSchemas);
            resp.fieldSchemas = this.applyDeletedFieldChanges(deletedFieldIds.get(this.objectType), resp.fieldSchemas);
            return resp;
          }),
          tap((resp) => {
            this.allFields.set(resp.fieldSchemas);
            this.loading.set(false);
          }),
          map((resp) => {
            const rows: Row[] = resp?.fieldSchemas.map((option) => {
              return {
                id: option.fieldId,
                data: this.rowData(option),
              } as Row;
            });
            return {
              data: rows,
              pagingMetadata: {
                nextCursor: resp?.pagingMetadata?.nextCursor || '',
                hasMore: resp?.pagingMetadata?.hasMore || false,
                totalResults: resp?.pagingMetadata?.totalResults || Infinity,
              },
            } as PagedResponseInterface<Row>;
          }),
        );
      },
    };

    this.dataSource = new GalaxyDataSource(paginatedAPI);
  }

  private applyUpdatedFieldChanges(
    updatedFields: FieldSchemaInterface[] = [],
    existingResponseSchemas: FieldSchemaInterface[] = [],
  ): FieldSchemaInterface[] {
    return existingResponseSchemas.map((field) => {
      const updatedField = updatedFields.find((updatedField) => updatedField.fieldId === field.fieldId);
      if (updatedField) {
        return { ...field, ...updatedField };
      } else {
        return field;
      }
    });
  }

  private applyDeletedFieldChanges(
    deletedFields: string[] = [],
    existingResponseSchemas: FieldSchemaInterface[] = [],
  ): FieldSchemaInterface[] {
    return existingResponseSchemas.filter((field) => !deletedFields.includes(field.fieldId));
  }

  private applyCreatedFieldChanges(
    createdFields: FieldSchemaInterface[] = [],
    existingResponseSchemas: FieldSchemaInterface[] = [],
  ): FieldSchemaInterface[] {
    for (const field of createdFields) {
      if (existingResponseSchemas.findIndex((f) => f.fieldId === field.fieldId) === -1) {
        existingResponseSchemas.unshift(field);
      }
    }
    return existingResponseSchemas;
  }

  private listFieldSchema(req: PagedListRequestInterface<void>): Observable<ListFieldSchemaResponseInterface> {
    return this.config.namespace$.pipe(
      switchMap((namespace) => {
        return this.crmFieldSchemaApiService.listFieldSchema({
          namespace: namespace,
          crmObjectType: this.objectType,
          pagingOptions: {
            cursor: req?.pagingOptions?.cursor,
            pageSize: req?.pagingOptions?.pageSize,
          },
          searchTerm: req.searchOptions?.text,
        });
      }),
      catchError(() => {
        return of({
          crmObjects: [],
          pagingMetadata: {
            nextCursor: '',
            hasMore: false,
          },
        }) as Observable<ListFieldSchemaResponseInterface>;
      }),
      tap(() => {
        this.loading.set(false);
      }),
      shareReplay(1),
    );
  }

  openEdit(row: Row): void {
    this.editField.emit(row);
  }

  openDelete(row: Row): void {
    this.deleteField.emit({
      namespace: row.data['namespace'].value,
      fieldId: row.id,
      objectType: this.objectType,
    });
  }

  rowAction(option: FieldSchemaInterface): void {
    const row: Row = {
      id: option.fieldId,
      data: {
        name: {
          cellType: 'text',
          value: option.fieldName,
        },
        description: {
          cellType: 'text',
          value: option.fieldDescription,
        },
        field_type: {
          cellType: 'text',
          value: option.fieldType ? this.customFieldTypeToReadableText(option.fieldType) : '',
        },
        created: {
          cellType: 'date',
          value: option.created,
        },
        namespace: {
          cellType: 'text',
          value: option.namespace,
        },
        external_id: {
          cellType: 'text',
          value: option.externalId,
        },
      },
    };
    this.editField.emit(row);
  }

  private rowData(option: FieldSchemaInterface): { [columnId: string]: CellData } {
    return {
      name: {
        cellType: 'text',
        value: option.fieldName,
        action: () => this.rowAction(option),
      },
      description: {
        cellType: 'text',
        value: option.fieldDescription,
      },
      field_type: {
        cellType: 'text',
        value: option.fieldType ? this.customFieldTypeToReadableText(option.fieldType) : '',
      },
      created: {
        cellType: 'date',
        value: option.created,
      },
      namespace: {
        cellType: 'text',
        value: option.namespace,
      },
      external_id: {
        cellType: 'text',
        value: option.externalId,
      },
    };
  }

  customFieldTypeToReadableText(fieldType: FieldType): string {
    switch (fieldType) {
      case FieldType.FIELD_TYPE_DATE:
        return 'Date';
      case FieldType.FIELD_TYPE_INTEGER:
        return 'Integer';
      case FieldType.FIELD_TYPE_STRING:
        return 'String';
      case FieldType.FIELD_TYPE_STRING_LIST:
        return 'String List';
      case FieldType.FIELD_TYPE_GEOPOINT:
        return 'Geopoint';
      case FieldType.FIELD_TYPE_FLOAT:
        return 'Float';
      case FieldType.FIELD_TYPE_BOOLEAN:
        return 'Boolean';
      case FieldType.FIELD_TYPE_TAG:
        return 'Tag';
      case FieldType.FIELD_TYPE_PHONE:
        return 'Phone number';
      case FieldType.FIELD_TYPE_EMAIL:
        return 'Email';
      case FieldType.FIELD_TYPE_DATETIME:
        return 'Date time';
      default:
        return '';
    }
  }
}
