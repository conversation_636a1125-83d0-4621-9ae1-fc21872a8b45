<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button
        [attr.data-action]="'clicked-back-from-crm-field-management'"
        [useHistory]="true"
      ></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>{{ 'OBJECT_TYPES.CUSTOM_FIELDS' | translate }}</glxy-page-title>
  </glxy-page-toolbar>
  <crm-drawer #drawer>
    <crm-vertical-nav-tab [useQueryParams]="false" class="side-nav" (tabClicked)="tabClicked($event)">
      @for (objectType of supportedCrmObjectTypes(); track objectType) {
        <crm-vertical-nav-card [label]="'TITLE' | translateForCrmObject: objectType | async" [tabId]="objectType">
          <div class="actions-container">
            <a class="action" mat-raised-button color="primary" (click)="createDrawer()">
              {{ 'ACTIONS.CREATE' | translate }}
            </a>
          </div>
          <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" animationDuration="0ms">
            <mat-tab label="Fields">
              <crm-list-custom-fields
                [objType]="objectType"
                (editField)="editDrawer($event)"
                (deleteField)="confirmDelete($event)"
              ></crm-list-custom-fields>
            </mat-tab>
          </mat-tab-group>
        </crm-vertical-nav-card>
      }
      @for (nav of extraNavs(); track nav) {
        <crm-vertical-nav-card [label]="nav.label">
          @if ((nav.label | dasherize) === currentTabClicked()) {
            <ng-container *ngComponentOutlet="nav.component; inputs: nav.inputs" />
          }
        </crm-vertical-nav-card>
      }
    </crm-vertical-nav-tab>
  </crm-drawer>
</glxy-page>

<ng-template #upsert>
  <div class="drawer-container">
    @if (isSystemField()) {
      <div class="alert">
        <glxy-alert type="tip">
          @if (canEditSystemFieldRestrictedValues()) {
            {{ 'CUSTOM_FIELDS.SYSTEM_FIELDS_RESTRICTED_VALUES_MESSAGE' | translate }}
          } @else {
            {{ 'CUSTOM_FIELDS.SYSTEM_FIELDS_MESSAGE' | translate }}
          }
        </glxy-alert>
      </div>
    }
    <form [formGroup]="formGroup" novalidate #form="ngForm" class="drawer">
      <glxy-form-row>
        <glxy-form-field bottomSpacing="small" [showLabel]="true" [required]="true">
          <glxy-label>{{ 'CUSTOM_FIELDS.OBJECT' | translate }}</glxy-label>
          <mat-select #defaultInput formControlName="objectType" [placeholder]="'CUSTOM_FIELDS.OBJECT' | translate">
            @for (objectType of supportedCrmObjectTypes(); track objectType) {
              <mat-option value="{{ objectType }}">{{
                'OBJECT_TYPE' | translateForCrmObject: objectType | async
              }}</mat-option>
            }
          </mat-select>
        </glxy-form-field>
      </glxy-form-row>
      <glxy-form-row>
        <glxy-form-field bottomSpacing="small" [showLabel]="true" [required]="true">
          <glxy-label>{{ 'CUSTOM_FIELDS.FIELD_NAME' | translate }}</glxy-label>
          <input formControlName="fieldName" matInput placeholder="{{ 'CUSTOM_FIELDS.FIELD_NAME' | translate }}" />
          @if (formGroup.get('fieldName')?.touched && formGroup.get('fieldName')?.hasError('required')) {
            <glxy-error>
              {{ 'CUSTOM_FIELDS.ERROR.FIELD_NAME_REQUIRED' | translate }}
            </glxy-error>
          }
        </glxy-form-field>
      </glxy-form-row>
      <crm-external-id
        [fieldId]="editObjectId()"
        [externalId]="formGroup.value?.externalId ?? ''"
        [name]="formGroup.value?.fieldName ?? ''"
        [objectType]="objectType()"
        [createNewField]="action() === 'create'"
        [disabled]="isEditingDisabled()"
        (externalIdChange)="externalIdChanged($event)"
        (externalIdErrors)="externalIdErrorsChanged($event)"
      ></crm-external-id>
      <glxy-form-row>
        <glxy-form-field bottomSpacing="small" [showLabel]="true">
          <glxy-label>{{ 'CUSTOM_FIELDS.FIELD_DESCRIPTION' | translate }}</glxy-label>
          <textarea
            formControlName="fieldDescription"
            matInput
            placeholder="{{ 'CUSTOM_FIELDS.FIELD_DESCRIPTION' | translate }}"
          ></textarea>
        </glxy-form-field>
      </glxy-form-row>

      <glxy-form-field bottomSpacing="small" [showLabel]="true" [required]="true">
        <glxy-label>{{ 'CUSTOM_FIELDS.FIELD_TYPE' | translate }}</glxy-label>
        <mat-select formControlName="fieldType" placeholder="{{ 'CUSTOM_FIELDS.FIELD_TYPE' | translate }}">
          @for (fieldType of availableFieldTypes(); track fieldType.fieldType) {
            @if (fieldType.label && fieldType.fieldType) {
              <mat-option [value]="fieldType.fieldType">
                {{ fieldType.label | translate }}
                @if (fieldType.example) {
                  ({{ fieldType.example | translate }})
                }
              </mat-option>
            }
          }
        </mat-select>
        @if (showFieldTypeError()) {
          <glxy-error>
            {{ 'CUSTOM_FIELDS.ERROR.FIELD_TYPE_REQUIRED' | translate }}
          </glxy-error>
        }
      </glxy-form-field>

      <!-- Restricted Values Section -->
      @if (showRestrictedValues()) {
        <glxy-form-row>
          <glxy-form-field bottomSpacing="small" [showLabel]="true">
            <glxy-label>{{ 'CUSTOM_FIELDS.RESTRICTED_VALUES.LABEL' | translate }}</glxy-label>
            <div class="restrict-description">
              {{ 'CUSTOM_FIELDS.RESTRICT_TO_SPECIFIC_VALUES_DESCRIPTION' | translate }}
            </div>
            <crm-restricted-values formControlName="restrictedValues" />
          </glxy-form-field>
        </glxy-form-row>
      }
    </form>
    @if (action() === 'create') {
      <div class="drawer-actions">
        <button mat-flat-button color="primary" type="button" (click)="createField()">
          {{ 'ACTIONS.CREATE' | translate }}
        </button>
      </div>
    }
    @if (action() === 'edit' && !isSystemField()) {
      <div class="drawer-actions">
        <button mat-flat-button color="primary" type="button" (click)="editField()">
          {{ 'ACTIONS.SAVE' | translate }}
        </button>
      </div>
    }
    @if (action() === 'edit' && showSaveButtonForSystemField()) {
      <div class="drawer-actions">
        <button mat-flat-button color="primary" type="button" (click)="editField()">
          {{ 'ACTIONS.SAVE' | translate }}
        </button>
      </div>
    }
  </div>
</ng-template>
