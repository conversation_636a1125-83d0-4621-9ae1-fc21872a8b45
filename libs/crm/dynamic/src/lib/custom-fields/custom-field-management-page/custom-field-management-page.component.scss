@use 'design-tokens' as dt;

:host {
  ::ng-deep crm-vertical-nav-tab {
    .tab {
      width: 238px;
    }
  }
}

.drawer-container {
  width: 400px;
}
.drawer {
  padding: dt.$spacing-3;
  padding-bottom: 100px;
}

.drawer-actions {
  display: flex;
  gap: dt.$spacing-2;
  position: absolute;
  bottom: 0;
  padding: dt.$spacing-2 dt.$spacing-3;
  border-top: 1px solid dt.$border-color;
  left: 0;
  right: 0;
  background-color: dt.$card-background-color;
  z-index: 10;
}

.alert {
  padding: dt.$spacing-3;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  right: 0;
}

.action {
  margin-right: dt.$spacing-4;
}

// Restricted values styles
.restrict-description {
  font-size: 12px;
  color: dt.$secondary-text-color;
  margin-bottom: dt.$spacing-3;
  line-height: 1.4;
  word-wrap: break-word;
  max-width: 80%;
  white-space: normal;
}

.restricted-values-section {
  display: flex;
  flex-direction: column;
  gap: dt.$spacing-2;
}

.restricted-values-label {
  font-weight: 500;
  font-size: 14px;
  color: dt.$primary-text-color;
  margin-bottom: dt.$spacing-1;
}
