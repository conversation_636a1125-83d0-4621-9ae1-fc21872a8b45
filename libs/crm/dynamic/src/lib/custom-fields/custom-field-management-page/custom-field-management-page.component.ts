import { Component, computed, input, signal, TemplateRef, viewChild, inject } from '@angular/core';
import {
  CrmDrawerComponent,
  ObjectType,
  ObjectTypes,
  SNACKBAR_DURATION,
  TranslateForCrmObjectPipe,
  VerticalNavCardComponent,
  VerticalNavTabComponent,
  CrmInjectionToken,
} from '@galaxy/crm/static';
import { AsyncPipe, NgComponentOutlet } from '@angular/common';
import { MatAnchor, MatButton } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatTab, MatTabGroup } from '@angular/material/tabs';
import { Row } from '@vendasta/galaxy/table';
import {
  DeleteData,
  ListCustomFieldManagementTableComponent,
} from '../custom-field-management-table/custom-field-management-table.component';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { FormBuilder, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { ArchiveFieldSchemaRequestInterface, FieldType } from '@vendasta/crm';
import { firstValueFrom, map, of, switchMap, take } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import {
  CustomFieldsManagementTableChangesService,
  FIELD_OPERATION_STATUS,
} from '../custom-fields-management-table-changes.service';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { ExternalIdComponent } from '../external-id/external-id.component';
import { DasherizePipe } from '../dasherize.pipe';
import { isSystemField, sanitizeName } from '../utils';
import { RestrictedValuesComponent } from '../restricted-values/restricted-values.component';
import { toSignal } from '@angular/core/rxjs-interop';

enum DeleteStatus {
  Skipped,
  Success,
  Failure,
}

interface ExtraNavTab {
  label: string;
  component: any;
  inputs: any;
}

interface fieldTypeOption {
  label: string;
  example?: string;
  fieldType: FieldType;
}

@Component({
  selector: 'crm-custom-fields-page',
  templateUrl: './custom-field-management-page.component.html',
  styleUrls: ['./custom-field-management-page.component.scss'],
  imports: [
    GalaxyPageModule,
    CrmDrawerComponent,
    TranslateModule,
    VerticalNavTabComponent,
    VerticalNavCardComponent,
    MatTabGroup,
    MatTab,
    ListCustomFieldManagementTableComponent,
    DasherizePipe,
    GalaxyAlertModule,
    GalaxyFormFieldModule,
    MatOption,
    ReactiveFormsModule,
    MatSelect,
    ExternalIdComponent,
    NgComponentOutlet,
    MatInput,
    MatAnchor,
    MatButton,
    MatCheckboxModule,
    RestrictedValuesComponent,
    AsyncPipe,
    TranslateForCrmObjectPipe,
  ],
  providers: [CustomFieldsManagementTableChangesService],
})
export class ListCustomFieldManagementPageComponent {
  drawer = viewChild.required<CrmDrawerComponent>('drawer');
  upsertTemplateRef = viewChild.required<TemplateRef<any>>('upsert');
  extraNavs = input<ExtraNavTab[]>([]);
  supportedCrmObjectTypes = input.required<ObjectType[]>();

  private readonly crmDependencies = inject(CrmInjectionToken);

  // Convert the feature flag observable to a signal
  private readonly hasRestrictedFieldValuesFeatureFlag = toSignal(
    this.crmDependencies.hasRestrictedFieldValuesAdminFeatureFlag$ ?? of(false),
    { initialValue: false },
  );

  constructor(
    private readonly snackService: SnackbarService,
    private readonly translateService: TranslateService,
    private readonly tableChangesService: CustomFieldsManagementTableChangesService,
    private readonly modalService: OpenConfirmationModalService,
  ) {
    // Listen to field type changes and update the signal
    this.formGroup.get('fieldType')?.valueChanges.subscribe((fieldType) => {
      this.fieldType.set(fieldType);
    });
  }

  protected readonly action = signal('');
  protected readonly editObjectId = signal('');
  protected readonly isSystemField = signal(false);
  private readonly externalIdPristine = signal(true);
  protected readonly fieldType = signal<FieldType>(FieldType.FIELD_TYPE_INVALID);

  protected readonly isEditingDisabled = computed(() => {
    return !!this.editObjectId() || this.isSystemField();
  });

  protected readonly formGroup = new FormBuilder().nonNullable.group({
    fieldName: ['', Validators.required],
    fieldType: [FieldType.FIELD_TYPE_INVALID, Validators.required],
    fieldDescription: '',
    objectType: ['Company', Validators.required],
    externalId: '',
    restrictedValues: [[] as string[]],
  });

  // Computed property to show/hide restricted values section
  protected readonly showRestrictedValues = computed(() => {
    const fieldType = this.fieldType();
    const isValidFieldType =
      fieldType === FieldType.FIELD_TYPE_STRING || fieldType === FieldType.FIELD_TYPE_STRING_LIST;
    const hasFeatureFlag = this.hasRestrictedFieldValuesFeatureFlag();

    // Only show for string and string list fields AND if the feature flag is enabled
    return isValidFieldType && hasFeatureFlag;
  });

  // Computed property to determine if system field can have restricted values edited
  protected readonly canEditSystemFieldRestrictedValues = computed(() => {
    return this.isSystemField() && this.showRestrictedValues();
  });

  // Computed property to determine if save button should be shown for system fields
  protected readonly showSaveButtonForSystemField = computed(() => {
    return this.isSystemField() && this.canEditSystemFieldRestrictedValues();
  });

  // Computed property to get objectType value
  protected readonly objectType = computed(() => {
    return this.formGroup.get('objectType')?.value as ObjectType;
  });

  // Computed property for field type validation
  protected readonly showFieldTypeError = computed(() => {
    const fieldTypeControl = this.formGroup.get('fieldType');
    return fieldTypeControl?.touched && fieldTypeControl?.hasError('required');
  });

  protected readonly fieldTypes: fieldTypeOption[] = [
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.STRING',
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.STRING',
      fieldType: FieldType.FIELD_TYPE_STRING,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.STRING_LIST',
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.STRING_LIST',
      fieldType: FieldType.FIELD_TYPE_STRING_LIST,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.INTEGER',
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.INTEGER',
      fieldType: FieldType.FIELD_TYPE_INTEGER,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.FLOAT',
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.FLOAT',
      fieldType: FieldType.FIELD_TYPE_FLOAT,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.EMAIL',
      fieldType: FieldType.FIELD_TYPE_EMAIL,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.PHONE_NUMBER',
      fieldType: FieldType.FIELD_TYPE_PHONE,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.DATE',
      fieldType: FieldType.FIELD_TYPE_DATE,
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.DATE',
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.DATE_TIME',
      fieldType: FieldType.FIELD_TYPE_DATETIME,
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.DATE_TIME',
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.BOOLEAN',
      fieldType: FieldType.FIELD_TYPE_BOOLEAN,
    },
  ];

  // Computed property to filter field types based on create vs edit action
  protected readonly availableFieldTypes = computed(() => {
    const currentAction = this.action();

    // When creating new fields, exclude STRING_LIST
    if (currentAction === 'create') {
      return this.fieldTypes.filter((fieldType) => fieldType.fieldType !== FieldType.FIELD_TYPE_STRING_LIST);
    }

    // When editing, show all field types (including STRING_LIST for existing fields)
    return this.fieldTypes;
  });

  createDrawer() {
    //reset all fields except for Object type
    this.formGroup.controls.fieldName.reset();
    this.formGroup.controls.fieldDescription.reset();
    this.formGroup.controls.fieldType.reset();
    this.formGroup.controls.externalId.reset();
    this.formGroup.controls.restrictedValues.reset();

    // Reset signals
    this.fieldType.set(FieldType.FIELD_TYPE_INVALID);

    // Enable all controls
    this.formGroup.controls.fieldName.enable();
    this.formGroup.controls.fieldDescription.enable();
    this.formGroup.controls.fieldType.enable();
    this.formGroup.controls.externalId.enable();
    this.formGroup.controls.restrictedValues.enable();
    this.formGroup.controls.objectType.enable();

    this.action.set('create');
    this.editObjectId.set('');
    this.isSystemField.set(false);
    this.externalIdPristine.set(true);
    this.drawer().open(this.upsertTemplateRef(), this.translateService.instant('CUSTOM_FIELDS.CREATE'));
  }

  editDrawer(row: Row) {
    this.editObjectId.set(row.id);
    this.action.set('edit');

    const fieldType = this.readableTextToCustomFieldType(row.data['field_type']?.value);
    this.formGroup.controls.fieldType.setValue(fieldType);
    this.fieldType.set(fieldType);
    this.formGroup.controls.fieldDescription.setValue(row.data['description']?.value);
    this.formGroup.controls.fieldName.setValue(row.data['name']?.value);
    this.formGroup.controls.externalId.setValue(row.data['external_id']?.value);

    // Reset restricted values initially
    this.formGroup.controls.restrictedValues.setValue([]);

    // Load existing field customizations if this is a string or string list field
    if (fieldType === FieldType.FIELD_TYPE_STRING || fieldType === FieldType.FIELD_TYPE_STRING_LIST) {
      // Get the object type from the form control directly to ensure we have the correct value
      const objectType = this.formGroup.controls.objectType.value as ObjectType;
      this.tableChangesService
        .getFieldCustomization(objectType, row.id)
        .pipe(take(1))
        .subscribe((customization) => {
          if (customization?.restrictedFieldValues?.length) {
            const restrictedValues = customization.restrictedFieldValues
              .map((value) => value.stringValue || '')
              .filter(Boolean);
            this.formGroup.controls.restrictedValues.setValue(restrictedValues);
          }
        });
    }

    this.formGroup.controls.objectType.disable();
    this.formGroup.controls.fieldType.disable();

    if (isSystemField(row.data['namespace']?.value)) {
      this.formGroup.controls.fieldName.disable();
      this.formGroup.controls.fieldDescription.disable();
      this.isSystemField.set(true);
    } else {
      this.isSystemField.set(false);
      // Enable 'fieldName' and 'fieldDescription' if namespace is not 'standard', 'system', or 'extension'
      this.formGroup.controls.fieldName.enable();
      this.formGroup.controls.fieldDescription.enable();
    }
    this.drawer().open(this.upsertTemplateRef(), this.translateService.instant('CUSTOM_FIELDS.EDIT'));
  }

  async confirmDelete(data: DeleteData) {
    const dialogResult$ = this.modalService
      .openModal({
        type: 'warn',
        title: 'CUSTOM_FIELDS.DELETE_MODAL.TITLE',
        message: 'CUSTOM_FIELDS.DELETE_MODAL.DESCRIPTION',
        confirmButtonText: 'ACTIONS.DELETE',
        hideCancel: false,
        cancelButtonText: 'ACTIONS.CANCEL',
        actionOnEnterKey: true,
        cancelOnEscapeKeyOrBackgroundClick: true,
        width: 560,
      })
      .pipe(
        switchMap((result) => {
          if (!result) {
            return of(DeleteStatus.Skipped);
          }
          return this.tableChangesService
            .deleteField({
              namespace: data.namespace,
              crmObjectType: data.objectType,
              fieldId: data.fieldId,
            } as ArchiveFieldSchemaRequestInterface)
            .pipe(
              map((deleteResult) => {
                return deleteResult === FIELD_OPERATION_STATUS.SUCCESS ? DeleteStatus.Success : DeleteStatus.Failure;
              }),
            );
        }),
      );

    const wasDeleted = await firstValueFrom(dialogResult$);
    switch (wasDeleted) {
      case DeleteStatus.Success:
        this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.DELETED', {
          duration: SNACKBAR_DURATION,
        });
        return;
      case DeleteStatus.Failure:
        this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.FAILED_TO_DELETE', {
          duration: SNACKBAR_DURATION,
        });
        return;
    }
    return;
  }

  createField() {
    this.formGroup.markAllAsTouched();
    if (this.formGroup.invalid) {
      this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.INVALID_FORM', { duration: SNACKBAR_DURATION });
      return;
    }
    const fieldType = this.formGroup.value.fieldType;
    const externalId = this.externalIdPristine()
      ? sanitizeName(this.formGroup.value.fieldName ?? '')
      : this.formGroup.value.externalId;

    // Prepare field customization if restricted values exist
    const fieldCustomization = this.formGroup.value.restrictedValues?.length
      ? {
          namespace: '', // This will be set by the service
          fieldId: '', // This will be set by the service
          restrictedFieldValues: this.formGroup.value.restrictedValues.map((value) => ({
            stringValue: value,
          })),
        }
      : undefined;

    this.tableChangesService
      .createField(
        this.formGroup.value.objectType as ObjectType,
        {
          fieldName: this.formGroup.value.fieldName,
          fieldType: fieldType,
          fieldDescription: this.formGroup.value.fieldDescription,
          externalId: externalId,
        },
        fieldCustomization,
      )
      .pipe(take(1))
      .subscribe((result) => {
        switch (result) {
          case FIELD_OPERATION_STATUS.SUCCESS:
            this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.CREATED', {
              duration: SNACKBAR_DURATION,
            });
            break;
          case FIELD_OPERATION_STATUS.FIELD_CREATED_CUSTOMIZATION_FAILED:
            this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.CREATED', {
              duration: SNACKBAR_DURATION,
            });
            // Also show warning about restricted values
            setTimeout(() => {
              this.snackService.openErrorSnack('CUSTOM_FIELDS.WARNING.RESTRICTED_VALUES_SAVE_FAILED');
            }, 1000);
            break;
          case FIELD_OPERATION_STATUS.FIELD_CREATION_FAILED:
          default:
            this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.FAILED_TO_CREATE', {
              duration: SNACKBAR_DURATION,
            });
            break;
        }
        this.drawer().close();
      });
  }

  editField() {
    this.formGroup.controls.objectType.enable();
    this.formGroup.controls.fieldType.enable();
    if (this.formGroup.invalid) {
      this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.INVALID_FORM', { duration: SNACKBAR_DURATION });
      return;
    }

    const fieldType = this.formGroup.value.fieldType;

    // Prepare field customization if restricted values are enabled
    const fieldCustomization = this.formGroup.value.restrictedValues?.length
      ? {
          fieldId: this.editObjectId(),
          namespace: '', // This will be set by the service
          restrictedFieldValues: this.formGroup.value.restrictedValues.map((value) => ({
            fieldId: this.editObjectId(),
            stringValue: value,
          })),
        }
      : undefined;

    // For system fields, use the system field update method
    const updateObservable = this.isSystemField()
      ? this.tableChangesService.updateFieldCustomizationOnly(
          this.formGroup.value.objectType as ObjectType,
          this.editObjectId(),
          fieldCustomization,
        )
      : this.tableChangesService.updateField(
          this.formGroup.value.objectType as ObjectType,
          {
            fieldId: this.editObjectId(),
            fieldName: this.formGroup.value.fieldName,
            fieldType: fieldType,
            fieldDescription: this.formGroup.value.fieldDescription,
            externalId: this.formGroup.value.externalId,
          },
          fieldCustomization,
          this.isSystemField(),
        );

    updateObservable.pipe(take(1)).subscribe((result) => {
      switch (result) {
        case FIELD_OPERATION_STATUS.SUCCESS:
          this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.EDITED', {
            duration: SNACKBAR_DURATION,
          });
          break;
        case FIELD_OPERATION_STATUS.FIELD_UPDATED_CUSTOMIZATION_FAILED:
          this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.EDITED', {
            duration: SNACKBAR_DURATION,
          });
          // Also show warning about restricted values
          setTimeout(() => {
            this.snackService.openErrorSnack('CUSTOM_FIELDS.WARNING.RESTRICTED_VALUES_UPDATE_FAILED');
          }, 1000);
          break;
        case FIELD_OPERATION_STATUS.FIELD_UPDATE_FAILED:
        default:
          this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.FAILED_TO_EDIT', {
            duration: SNACKBAR_DURATION,
          });
          break;
      }
      this.drawer().close();
    });
  }

  protected readonly currentTabClicked = signal('');

  tabClicked(event: string) {
    if (ObjectTypes.includes(event as ObjectType)) {
      this.formGroup.controls['objectType'].setValue(event);
    }
    this.currentTabClicked.set(event);
  }

  externalIdChanged(newExternalIdValue: string) {
    this.externalIdPristine.set(false);
    this.formGroup.controls.externalId.setValue(newExternalIdValue);
  }

  externalIdErrorsChanged(errors: ValidationErrors | null) {
    this.formGroup.controls.externalId.setErrors(errors);
  }

  readableTextToCustomFieldType(text: string): FieldType {
    switch (text) {
      case 'Date':
        return FieldType.FIELD_TYPE_DATE;
      case 'Integer':
        return FieldType.FIELD_TYPE_INTEGER;
      case 'String':
        return FieldType.FIELD_TYPE_STRING;
      case 'String List':
        return FieldType.FIELD_TYPE_STRING_LIST;
      case 'Geopoint':
        return FieldType.FIELD_TYPE_GEOPOINT;
      case 'Float':
        return FieldType.FIELD_TYPE_FLOAT;
      case 'Boolean':
        return FieldType.FIELD_TYPE_BOOLEAN;
      case 'Tag':
        return FieldType.FIELD_TYPE_TAG;
      case 'Phone number':
        return FieldType.FIELD_TYPE_PHONE;
      case 'Email':
        return FieldType.FIELD_TYPE_EMAIL;
      case 'Date time':
        return FieldType.FIELD_TYPE_DATETIME;
      default:
        return FieldType.FIELD_TYPE_INVALID;
    }
  }
}
