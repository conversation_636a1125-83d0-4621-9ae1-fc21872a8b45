<div class="restricted-values-container">
  <div cdkDropList (cdkDropListDropped)="drop($event)" class="restricted-values-list">
    @for (control of valuesControls.controls; track $index; let index = $index) {
      <div class="restricted-value-item" cdkDrag>
        <div class="drag-handle" cdkDragHandle>
          <mat-icon>drag_indicator</mat-icon>
        </div>
        <div class="input-container">
          <glxy-form-field bottomSpacing="none" [required]="false" class="restricted-value-field">
            <input
              type="text"
              matInput
              [formControl]="$any(control)"
              placeholder="{{ 'CUSTOM_FIELDS.RESTRICTED_VALUES.INPUT_VALUE' | translate }}"
            />
          </glxy-form-field>
        </div>
        <button
          mat-icon-button
          type="button"
          class="remove-button"
          (click)="removeValue(index)"
          [disabled]="valuesControls.length === 1"
          [attr.aria-label]="'CUSTOM_FIELDS.RESTRICTED_VALUES.REMOVE_VALUE' | translate"
        >
          <mat-icon>close</mat-icon>
        </button>
      </div>
    }
  </div>

  <button mat-button type="button" class="add-value-button" (click)="addValue()" color="primary">
    {{ 'CUSTOM_FIELDS.RESTRICTED_VALUES.ADD_VALUE' | translate }}
  </button>
</div>
