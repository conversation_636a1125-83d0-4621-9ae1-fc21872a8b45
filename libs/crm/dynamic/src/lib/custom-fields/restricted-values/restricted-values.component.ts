import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  forwardRef,
} from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { distinctUntilChanged, map, takeUntil, tap } from 'rxjs/operators';
import { CdkDragDrop, moveItemInArray, DragDropModule } from '@angular/cdk/drag-drop';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

@Component({
  selector: 'crm-restricted-values',
  templateUrl: './restricted-values.component.html',
  styleUrls: ['./restricted-values.component.scss'],
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    ReactiveFormsModule,
    TranslateModule,
    DragDropModule,
    GalaxyFormFieldModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RestrictedValuesComponent),
      multi: true,
    },
  ],
})
export class RestrictedValuesComponent implements OnInit, OnDestroy, OnChanges, ControlValueAccessor {
  private destroyed$$ = new Subject<void>();
  private onChange = (_value: string[]) => {
    // This function is replaced by Angular's reactive forms
  };
  private onTouched = () => {
    // This function is replaced by Angular's reactive forms
  };

  @Input()
  values: string[] = [];

  @Output()
  valuesChange = new EventEmitter<string[]>();

  valuesControls = new UntypedFormArray([]);

  ngOnInit(): void {
    this.valuesControls.valueChanges
      .pipe(
        takeUntil(this.destroyed$$),
        map((values) => values.filter((value: string) => value && value.trim())),
        distinctUntilChanged(),
        tap((values) => {
          this.valuesChange.emit(values);
          this.onChange(values);
        }),
      )
      .subscribe();

    this.reloadValues();
  }

  reloadValues(): void {
    this.valuesControls.clear();
    if (this.values?.length) {
      this.values.forEach((value) => this.addValue(value));
    }
    // Always have at least one empty input
    if (!this.values?.length) {
      this.addValue();
    }
  }

  addValue(value = ''): void {
    const control = new UntypedFormControl(value);
    this.valuesControls.push(control);
    // Manually trigger value change to ensure parent component is notified
    this.valuesControls.updateValueAndValidity({ emitEvent: true });
  }

  removeValue(index: number): void {
    this.valuesControls.removeAt(index);
    this.valuesControls.updateValueAndValidity({ emitEvent: true });
  }

  drop(event: CdkDragDrop<string[]>): void {
    if (event.previousIndex !== event.currentIndex) {
      // Get the current values
      const currentValues = this.valuesControls.value;

      // Reorder the array
      moveItemInArray(currentValues, event.previousIndex, event.currentIndex);

      // Rebuild the form array with the new order
      this.valuesControls.clear();
      currentValues.forEach((value: string) => {
        this.valuesControls.push(new UntypedFormControl(value));
      });

      // Emit the change
      this.valuesControls.updateValueAndValidity({ emitEvent: true });
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.reloadValues();
    }
  }

  ngOnDestroy(): void {
    this.destroyed$$.next();
    this.destroyed$$.complete();
  }

  // ControlValueAccessor implementation
  writeValue(value: string[]): void {
    this.values = value || [];
    this.reloadValues();
  }

  registerOnChange(fn: (value: string[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    if (isDisabled) {
      this.valuesControls.disable();
    } else {
      this.valuesControls.enable();
    }
  }
}
