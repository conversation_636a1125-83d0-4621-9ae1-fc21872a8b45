@use 'design-tokens' as *;

.restricted-values-container {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  width: 100%;
  margin-top: $spacing-2; /* Add some space after the description */
}

.restricted-values-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.restricted-value-item {
  display: flex;
  align-items: center; /* Back to center alignment */
  gap: $spacing-2;
  width: 100%;
  min-height: 56px; /* Ensure consistent height */
  padding-right: $spacing-2;

  &.cdk-drag-preview {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    background: $card-background-color;
    align-items: center;
  }

  &.cdk-drag-placeholder {
    opacity: 0.4;
  }
}

.drag-handle {
  cursor: grab;
  color: $tertiary-text-color;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 24px;
  height: 24px; /* Smaller, centered height */

  &:active {
    cursor: grabbing;
  }

  .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
}

.input-container {
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 100px); /* Account for drag handle and remove button */
}

.restricted-value-field {
  width: 100%;
}

.remove-button {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  color: $tertiary-text-color;

  &:disabled {
    opacity: 0.3;
  }

  &:hover:not(:disabled) {
    background-color: $secondary-background-color;
    color: $error-text-color;
  }
}

.add-value-button {
  align-self: flex-start;
  margin-top: $spacing-1;
}
