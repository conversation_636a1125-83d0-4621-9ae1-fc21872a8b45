import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  Inject,
  Input,
  Output,
  ViewEncapsulation,
  signal,
  EventEmitter,
} from '@angular/core';
import { ReactiveFormsModule, FormControl, Validators, ValidationErrors } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { GalaxyPopoverModule, PopoverPositions } from '@vendasta/galaxy/popover';
import { Observable, combineLatest, debounceTime, switchMap, map, tap, catchError, of } from 'rxjs';
import { CrmDependencies, CrmInjectionToken, ObjectType } from '@galaxy/crm/static';
import { CRMFieldSchemaApiService } from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { sanitizeName } from '../utils';

@Component({
  selector: 'crm-external-id',
  templateUrl: './external-id.component.html',
  styleUrls: ['./external-id.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyPopoverModule,
    CommonModule,
    GalaxyFormFieldModule,
    GalaxyTooltipModule,
  ],
})
export class ExternalIdComponent {
  externalIdNewValue = new FormControl<string>('', [Validators.pattern('[0-9a-z\\-_]*')]);
  PopoverPositions = PopoverPositions;
  isExternalIdPopoverVisible = false;
  isExternalIdSet = false;
  initialExternalId = signal<string>('');
  @Input() fieldId = '';
  @Input({ required: true }) set externalId(value: string) {
    const sanitizedExternalId = sanitizeName(value);
    this.initialExternalId.set(sanitizedExternalId);
    this.externalIdNewValue.setValue(sanitizedExternalId);
  }
  isNewFieldEditor = signal<boolean>(false);
  @Input({ required: true }) set createNewField(value: boolean) {
    this.isNewFieldEditor.set(value);
  }
  @Input({ required: true }) set name(newName: string) {
    if (this.isNewFieldEditor()) {
      this.externalIdNewValue.setValue(sanitizeName(newName));
    }
  }
  @Input({ required: true }) objectType: ObjectType;
  @Input() disabled = false;
  @Output() externalIdChange = new EventEmitter<string>();
  @Output() externalIdErrors = new EventEmitter<ValidationErrors | null>();

  isDuplicateExternalId$: Observable<boolean> = combineLatest([
    this.config.namespace$,
    this.externalIdNewValue.valueChanges,
  ]).pipe(
    debounceTime(500),
    switchMap(([namespace, externalIdGet]) => {
      if (externalIdGet === '') {
        return of({ fieldSchemas: [] });
      }
      return this.schemaService
        .getMultiFieldSchema({
          namespace: namespace as string,
          fieldId: [externalIdGet],
          crmObjectType: this.objectType as string,
        })
        .pipe(catchError(() => of({ fieldSchemas: [] })));
    }),
    map((resp) =>
      resp.fieldSchemas?.some((fieldSchema) => fieldSchema.fieldId && fieldSchema.fieldId !== this.fieldId),
    ),
    tap((foundDuplicate) => {
      if (foundDuplicate) {
        this.externalIdNewValue.setErrors({ duplicate: true });
        this.externalIdErrors.emit({ duplicate: true });
      } else {
        this.externalIdNewValue.setErrors(null);
        this.externalIdErrors.emit(null);
      }
    }),
  );

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly changeDetector: ChangeDetectorRef,
    private readonly schemaService: CRMFieldSchemaApiService,
  ) {}

  setExternalId(): void {
    this.externalIdChange.emit(this.externalIdNewValue.value);
    this.isExternalIdSet = true;
    this.isExternalIdPopoverVisible = false;
  }

  cancelExternalId(): void {
    this.externalIdNewValue.setValue(this.initialExternalId());
    this.isExternalIdPopoverVisible = false;
    this.isExternalIdSet = false;
  }

  showExternalIdPopover(): void {
    this.initialExternalId.set(this.externalIdNewValue.value ?? '');
    this.isExternalIdPopoverVisible = true;
  }
}
