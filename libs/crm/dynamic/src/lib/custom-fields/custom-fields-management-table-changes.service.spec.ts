import { TestBed } from '@angular/core/testing';
import {
  CustomFieldsManagementTableChangesService,
  FIELD_OPERATION_STATUS,
} from './custom-fields-management-table-changes.service';
import {
  CRMFieldSchemaApiService,
  FieldSchemaInterface,
  CRMFieldSchemaCustomizationApiService,
  FieldSchemaCustomizationInterface,
} from '@vendasta/crm';
import { CrmDependencies, CrmInjectionToken, ObjectType } from '@galaxy/crm/static';
import { BehaviorSubject, of } from 'rxjs';

describe('CustomFieldsManagementTableChangesService', () => {
  let service: CustomFieldsManagementTableChangesService;
  let mockCrmFieldSchemaApiService: any;
  let mockCrmFieldCustomizationApiService: any;
  let mockNamespace$: BehaviorSubject<string>;

  const mockObjectType: ObjectType = 'Contact';

  const mockFieldSchema: FieldSchemaInterface = {
    fieldId: 'test-field-id',
    fieldName: 'Test Field',
    fieldDescription: 'Test Description',
    namespace: 'test-namespace',
    fieldType: 'TEXT' as any,
    externalId: 'test-external-id',
  };

  const mockFieldSchemaResponse = {
    fieldSchemas: [mockFieldSchema],
  };

  const mockFieldCustomization: FieldSchemaCustomizationInterface = {
    fieldId: mockFieldSchema.fieldId,
    namespace: 'test-namespace',
    restrictedFieldValues: [{ stringValue: 'Value1' }, { stringValue: 'Value2' }, { stringValue: 'Value3' }],
  };

  const mockFieldCustomizationResponse = {
    fieldSchemaCustomizations: [mockFieldCustomization],
  };

  // Helper to create a spy function that tracks calls with improved debugging
  function createSpy(returnValue: any) {
    const calls: any[] = [];
    const fn = function (...args: any[]) {
      calls.push(args);
      return returnValue;
    };
    fn.calls = calls;
    fn.toHaveBeenCalledWith = function (...expectedArgs: any[]) {
      // Check if any call matches exactly
      const hasMatchingCall = calls.some((actualArgs) => JSON.stringify(actualArgs) === JSON.stringify(expectedArgs));

      if (hasMatchingCall) {
        return true;
      }

      // If no exact match, provide helpful debugging information
      console.error('Mock function call mismatch:');
      console.error('Expected call with:', JSON.stringify(expectedArgs, null, 2));

      if (calls.length === 0) {
        console.error('But function was never called');
      } else {
        console.error(`Function was called ${calls.length} time(s) with:`);
        calls.forEach((actualArgs, index) => {
          console.error(`Call ${index + 1}:`, JSON.stringify(actualArgs, null, 2));

          // Show detailed differences for the most recent call
          if (index === calls.length - 1 && actualArgs.length === expectedArgs.length) {
            console.error('Detailed comparison for most recent call:');
            expectedArgs.forEach((expectedArg, argIndex) => {
              const actualArg = actualArgs[argIndex];
              if (JSON.stringify(expectedArg) !== JSON.stringify(actualArg)) {
                console.error(`  Argument ${argIndex}:`);
                console.error(`    Expected:`, JSON.stringify(expectedArg, null, 4));
                console.error(`    Actual:  `, JSON.stringify(actualArg, null, 4));
              }
            });
          }
        });
      }

      return false;
    };
    return fn;
  }

  beforeEach(() => {
    mockNamespace$ = new BehaviorSubject<string>('test-namespace');

    // Create plain object mocks with custom spy implementation
    mockCrmFieldSchemaApiService = {
      createFieldSchema: createSpy(of({ fieldId: mockFieldSchema.fieldId })),
      updateFieldSchema: createSpy(of({ fieldId: mockFieldSchema.fieldId })),
      archiveFieldSchema: createSpy(of({ success: true })),
      getMultiFieldSchema: createSpy(of(mockFieldSchemaResponse)),
    };

    mockCrmFieldCustomizationApiService = {
      getMultiFieldSchemaCustomization: createSpy(of(mockFieldCustomizationResponse)),
      createFieldSchemaCustomization: createSpy(of({ fieldId: mockFieldSchema.fieldId })),
      updateFieldSchemaCustomization: createSpy(of({ fieldId: mockFieldSchema.fieldId })),
    };

    TestBed.configureTestingModule({
      providers: [
        CustomFieldsManagementTableChangesService,
        {
          provide: CrmInjectionToken,
          useValue: {
            namespace$: mockNamespace$,
          } as Partial<CrmDependencies>,
        },
        {
          provide: CRMFieldSchemaApiService,
          useValue: mockCrmFieldSchemaApiService,
        },
        {
          provide: CRMFieldSchemaCustomizationApiService,
          useValue: mockCrmFieldCustomizationApiService,
        },
      ],
    });

    service = TestBed.inject(CustomFieldsManagementTableChangesService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('createField', () => {
    it('should create a field and update createdFields signal', (done) => {
      service.createField(mockObjectType, mockFieldSchema).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Check if createFieldSchema was called with correct parameters
        expect(
          mockCrmFieldSchemaApiService.createFieldSchema.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchema: {
              ...mockFieldSchema,
              namespace: 'test-namespace',
            },
          }),
        ).toBe(true);

        // Check if getMultiFieldSchema was called with correct parameters
        expect(
          mockCrmFieldSchemaApiService.getMultiFieldSchema.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldId: [mockFieldSchema.fieldId],
          }),
        ).toBe(true);

        // Verify the changes were tracked correctly
        service.changes$.subscribe((changes) => {
          const createdFields = changes.createdFields.get(mockObjectType);
          expect(createdFields?.length).toBe(1);
          expect(createdFields?.[0]).toEqual(mockFieldSchema);
          done();
        });
      });
    });

    it('should create a field with customization and create field customization', (done) => {
      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId,
        namespace: 'test-namespace',
        restrictedFieldValues: [{ stringValue: 'Option1' }, { stringValue: 'Option2' }, { stringValue: 'Option3' }],
      };

      service.createField(mockObjectType, mockFieldSchema, fieldCustomization).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Check if createFieldSchema was called with correct parameters
        expect(
          mockCrmFieldSchemaApiService.createFieldSchema.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchema: {
              ...mockFieldSchema,
              namespace: 'test-namespace',
            },
          }),
        ).toBe(true);

        // Check if getMultiFieldSchema was called with correct parameters
        expect(
          mockCrmFieldSchemaApiService.getMultiFieldSchema.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldId: [mockFieldSchema.fieldId],
          }),
        ).toBe(true);

        // Check if createFieldSchemaCustomization was called with correct parameters
        expect(
          mockCrmFieldCustomizationApiService.createFieldSchemaCustomization.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchemaCustomization: {
              namespace: 'test-namespace',
              fieldId: mockFieldSchema.fieldId,
              restrictedFieldValues: fieldCustomization.restrictedFieldValues,
            },
          }),
        ).toBe(true);

        // Verify the changes were tracked correctly
        service.changes$.subscribe((changes) => {
          const createdFields = changes.createdFields.get(mockObjectType);
          expect(createdFields?.length).toBe(1);
          expect(createdFields?.[0]).toEqual(mockFieldSchema);
          done();
        });
      });
    });

    it('should create a field without customization when no restrictedFieldValues are provided', (done) => {
      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId,
        namespace: 'test-namespace',
        restrictedFieldValues: [],
      };

      service.createField(mockObjectType, mockFieldSchema, fieldCustomization).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Check if createFieldSchema was called
        expect(
          mockCrmFieldSchemaApiService.createFieldSchema.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchema: {
              ...mockFieldSchema,
              namespace: 'test-namespace',
            },
          }),
        ).toBe(true);

        // Verify that createFieldSchemaCustomization was NOT called
        expect(mockCrmFieldCustomizationApiService.createFieldSchemaCustomization.calls.length).toBe(0);

        // Verify the changes were tracked correctly
        service.changes$.subscribe((changes) => {
          const createdFields = changes.createdFields.get(mockObjectType);
          expect(createdFields?.length).toBe(1);
          expect(createdFields?.[0]).toEqual(mockFieldSchema);
          done();
        });
      });
    });
  });

  describe('updateField', () => {
    it('should update a field and update updatedFields signal', (done) => {
      service.updateField(mockObjectType, mockFieldSchema).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Check if updateFieldSchema was called with correct parameters
        expect(
          mockCrmFieldSchemaApiService.updateFieldSchema.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchema: {
              ...mockFieldSchema,
              namespace: 'test-namespace',
            },
            fieldMask: {
              paths: ['fieldName', 'fieldDescription', 'externalId'],
            },
          }),
        ).toBe(true);

        // Verify the changes were tracked correctly
        service.changes$.subscribe((changes) => {
          const updatedFields = changes.updatedFields.get(mockObjectType);
          expect(updatedFields?.length).toBe(1);
          expect(updatedFields?.[0]).toEqual(mockFieldSchema);
          done();
        });
      });
    });

    it('should update a field with customization when existing customization is found', (done) => {
      // Prepare the field customization with restricted values
      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId,
        namespace: 'test-namespace',
        restrictedFieldValues: [{ stringValue: 'Option1' }, { stringValue: 'Option2' }, { stringValue: 'Option3' }],
      };

      service.updateField(mockObjectType, mockFieldSchema, fieldCustomization).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Check if updateFieldSchema was called with correct parameters
        expect(
          mockCrmFieldSchemaApiService.updateFieldSchema.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchema: {
              ...mockFieldSchema,
              namespace: 'test-namespace',
            },
            fieldMask: {
              paths: ['fieldName', 'fieldDescription', 'externalId'],
            },
          }),
        ).toBe(true);

        // Check if getMultiFieldSchemaCustomization was called to check for existing customization
        expect(
          mockCrmFieldCustomizationApiService.getMultiFieldSchemaCustomization.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldId: [mockFieldSchema.fieldId],
          }),
        ).toBe(true);

        // Check if updateFieldSchemaCustomization was called with correct parameters
        expect(
          mockCrmFieldCustomizationApiService.updateFieldSchemaCustomization.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchemaCustomization: {
              namespace: 'test-namespace',
              fieldId: mockFieldSchema.fieldId,
              restrictedFieldValues: fieldCustomization.restrictedFieldValues,
            },
            fieldMask: {
              paths: ['RestrictedFieldValues'],
            },
          }),
        ).toBe(true);

        // Verify that createFieldSchemaCustomization was NOT called
        expect(mockCrmFieldCustomizationApiService.createFieldSchemaCustomization.calls.length).toBe(0);

        // Verify the changes were tracked correctly
        service.changes$.subscribe((changes) => {
          const updatedFields = changes.updatedFields.get(mockObjectType);
          expect(updatedFields?.length).toBe(1);
          expect(updatedFields?.[0]).toEqual(mockFieldSchema);
          done();
        });
      });
    });

    it('should create a new customization when none exists', (done) => {
      // Mock getMultiFieldSchemaCustomization to return empty response
      mockCrmFieldCustomizationApiService.getMultiFieldSchemaCustomization = createSpy(
        of({ fieldSchemaCustomizations: [] }),
      );

      // Prepare the field customization with restricted values
      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId,
        namespace: 'test-namespace',
        restrictedFieldValues: [{ stringValue: 'Option1' }, { stringValue: 'Option2' }, { stringValue: 'Option3' }],
      };

      service.updateField(mockObjectType, mockFieldSchema, fieldCustomization).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Check if createFieldSchemaCustomization was called with correct parameters
        expect(
          mockCrmFieldCustomizationApiService.createFieldSchemaCustomization.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchemaCustomization: {
              namespace: 'test-namespace',
              fieldId: mockFieldSchema.fieldId,
              restrictedFieldValues: fieldCustomization.restrictedFieldValues,
            },
          }),
        ).toBe(true);

        // Verify that updateFieldSchemaCustomization was NOT called
        expect(mockCrmFieldCustomizationApiService.updateFieldSchemaCustomization.calls.length).toBe(0);

        // Verify the changes were tracked correctly
        service.changes$.subscribe((changes) => {
          const updatedFields = changes.updatedFields.get(mockObjectType);
          expect(updatedFields?.length).toBe(1);
          expect(updatedFields?.[0]).toEqual(mockFieldSchema);
          done();
        });
      });
    });

    it('should update a field without customization when no restrictedFieldValues are provided', (done) => {
      // Prepare the field customization without restricted values
      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId,
        namespace: 'test-namespace',
        restrictedFieldValues: [],
      };

      service.updateField(mockObjectType, mockFieldSchema, fieldCustomization).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Check if getMultiFieldSchemaCustomization was called
        expect(
          mockCrmFieldCustomizationApiService.getMultiFieldSchemaCustomization.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldId: [mockFieldSchema.fieldId],
          }),
        ).toBe(true);

        // Verify that neither create nor update customization was called
        expect(mockCrmFieldCustomizationApiService.createFieldSchemaCustomization.calls.length).toBe(0);
        expect(mockCrmFieldCustomizationApiService.updateFieldSchemaCustomization.calls.length).toBe(0);

        done();
      });
    });

    it('should update system field customization without modifying field schema', (done) => {
      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId,
        namespace: 'test-namespace',
        restrictedFieldValues: [{ stringValue: 'Option1' }, { stringValue: 'Option2' }, { stringValue: 'Option3' }],
      };

      service.updateField(mockObjectType, mockFieldSchema, fieldCustomization, true).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Verify that updateFieldSchema was NOT called for system fields
        expect(mockCrmFieldSchemaApiService.updateFieldSchema.calls.length).toBe(0);

        // Check if getMultiFieldSchemaCustomization was called
        expect(
          mockCrmFieldCustomizationApiService.getMultiFieldSchemaCustomization.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldId: [mockFieldSchema.fieldId],
          }),
        ).toBe(true);

        // Check if updateFieldSchemaCustomization was called
        expect(
          mockCrmFieldCustomizationApiService.updateFieldSchemaCustomization.toHaveBeenCalledWith({
            namespace: 'test-namespace',
            crmObjectType: mockObjectType,
            fieldSchemaCustomization: {
              namespace: 'test-namespace',
              fieldId: mockFieldSchema.fieldId,
              restrictedFieldValues: fieldCustomization.restrictedFieldValues,
            },
            fieldMask: {
              paths: ['RestrictedFieldValues'],
            },
          }),
        ).toBe(true);

        done();
      });
    });
  });

  describe('deleteField', () => {
    it('should delete a field and update deletedFieldIds signal', (done) => {
      const archiveRequest = {
        namespace: 'test-namespace',
        crmObjectType: mockObjectType,
        fieldId: mockFieldSchema.fieldId,
      };

      service.deleteField(archiveRequest).subscribe((result) => {
        expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

        // Check if archiveFieldSchema was called with correct parameters
        expect(mockCrmFieldSchemaApiService.archiveFieldSchema.toHaveBeenCalledWith(archiveRequest)).toBe(true);

        // Verify the changes were tracked correctly
        service.changes$.subscribe((changes) => {
          const deletedFieldIds = changes.deletedFieldIds.get(mockObjectType);
          expect(deletedFieldIds?.length).toBe(1);
          expect(deletedFieldIds?.[0]).toBe(mockFieldSchema.fieldId);
          done();
        });
      });
    });
  });

  describe('resetChanges', () => {
    it('should reset all changes for a specific object type', (done) => {
      // First create some changes
      service.createField(mockObjectType, mockFieldSchema).subscribe(() => {
        service.resetChanges(mockObjectType);

        // Verify all changes were reset
        service.changes$.subscribe((changes) => {
          expect(changes.createdFields.get(mockObjectType)?.length).toBe(0);
          expect(changes.updatedFields.get(mockObjectType)?.length).toBe(0);
          expect(changes.deletedFieldIds.get(mockObjectType)?.length).toBe(0);
          done();
        });
      });
    });
  });

  describe('updateFieldCustomizationOnly', () => {
    it('should update field customization when existing customization is found', (done) => {
      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId,
        namespace: 'test-namespace',
        restrictedFieldValues: [{ stringValue: 'Option1' }, { stringValue: 'Option2' }, { stringValue: 'Option3' }],
      };

      service
        .updateFieldCustomizationOnly(mockObjectType, mockFieldSchema.fieldId!, fieldCustomization)
        .subscribe((result) => {
          expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

          // Check if getMultiFieldSchemaCustomization was called
          expect(
            mockCrmFieldCustomizationApiService.getMultiFieldSchemaCustomization.toHaveBeenCalledWith({
              namespace: 'test-namespace',
              crmObjectType: mockObjectType,
              fieldId: [mockFieldSchema.fieldId!],
            }),
          ).toBe(true);

          // Check if updateFieldSchemaCustomization was called
          expect(
            mockCrmFieldCustomizationApiService.updateFieldSchemaCustomization.toHaveBeenCalledWith({
              namespace: 'test-namespace',
              crmObjectType: mockObjectType,
              fieldSchemaCustomization: {
                namespace: 'test-namespace',
                fieldId: mockFieldSchema.fieldId!,
                restrictedFieldValues: fieldCustomization.restrictedFieldValues,
              },
              fieldMask: {
                paths: ['RestrictedFieldValues'],
              },
            }),
          ).toBe(true);

          // Verify that updateFieldSchema was NOT called
          expect(mockCrmFieldSchemaApiService.updateFieldSchema.calls.length).toBe(0);

          done();
        });
    });

    it('should create new customization when none exists', (done) => {
      // Mock getMultiFieldSchemaCustomization to return empty response
      mockCrmFieldCustomizationApiService.getMultiFieldSchemaCustomization = createSpy(
        of({ fieldSchemaCustomizations: [] }),
      );

      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId!,
        namespace: 'test-namespace',
        restrictedFieldValues: [{ stringValue: 'Option1' }, { stringValue: 'Option2' }, { stringValue: 'Option3' }],
      };

      service
        .updateFieldCustomizationOnly(mockObjectType, mockFieldSchema.fieldId!, fieldCustomization)
        .subscribe((result) => {
          expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

          // Check if createFieldSchemaCustomization was called
          expect(
            mockCrmFieldCustomizationApiService.createFieldSchemaCustomization.toHaveBeenCalledWith({
              namespace: 'test-namespace',
              crmObjectType: mockObjectType,
              fieldSchemaCustomization: {
                namespace: 'test-namespace',
                fieldId: mockFieldSchema.fieldId!,
                restrictedFieldValues: fieldCustomization.restrictedFieldValues,
              },
            }),
          ).toBe(true);

          // Verify that updateFieldSchemaCustomization was NOT called
          expect(mockCrmFieldCustomizationApiService.updateFieldSchemaCustomization.calls.length).toBe(0);

          done();
        });
    });

    it('should return success when no restrictedFieldValues are provided', (done) => {
      const fieldCustomization: FieldSchemaCustomizationInterface = {
        fieldId: mockFieldSchema.fieldId!,
        namespace: 'test-namespace',
        restrictedFieldValues: [],
      };

      service
        .updateFieldCustomizationOnly(mockObjectType, mockFieldSchema.fieldId!, fieldCustomization)
        .subscribe((result) => {
          expect(result).toBe(FIELD_OPERATION_STATUS.SUCCESS);

          // Verify that no API calls were made
          expect(mockCrmFieldCustomizationApiService.createFieldSchemaCustomization.calls.length).toBe(0);
          expect(mockCrmFieldCustomizationApiService.updateFieldSchemaCustomization.calls.length).toBe(0);

          done();
        });
    });
  });
});
