import { Inject, Injectable, signal } from '@angular/core';
import { CrmDependencies, CrmInjectionToken, ObjectType } from '@galaxy/crm/static';
import { of, switchMap, tap, catchError, Observable, map, take, combineLatest, withLatestFrom } from 'rxjs';
import {
  ArchiveFieldSchemaRequestInterface,
  CRMFieldSchemaApiService,
  FieldSchemaInterface,
  CRMFieldSchemaCustomizationApiService,
  FieldSchemaCustomizationInterface,
} from '@vendasta/crm';
import { toObservable } from '@angular/core/rxjs-interop';

export const FIELD_OPERATION_STATUS = {
  SUCCESS: 'success',
  FIELD_CREATION_FAILED: 'field_creation_failed',
  FIELD_CREATED_CUSTOMIZATION_FAILED: 'field_created_customization_failed',
  FIELD_UPDATE_FAILED: 'field_update_failed',
  FIELD_UPDATED_CUSTOMIZATION_FAILED: 'field_updated_customization_failed',
  FIELD_DELETION_FAILED: 'field_deletion_failed',
} as const;

export type FieldOperationStatus = (typeof FIELD_OPERATION_STATUS)[keyof typeof FIELD_OPERATION_STATUS];

@Injectable()
export class CustomFieldsManagementTableChangesService {
  private readonly deletedFieldIds = signal<Map<ObjectType, string[]>>(
    new Map([
      ['Contact', []],
      ['Company', []],
    ]),
  );
  private readonly createdFields = signal<Map<ObjectType, FieldSchemaInterface[]>>(
    new Map([
      ['Contact', []],
      ['Company', []],
    ]),
  );
  private readonly updatedFields = signal<Map<ObjectType, FieldSchemaInterface[]>>(
    new Map([
      ['Contact', []],
      ['Company', []],
    ]),
  );
  private deletedFieldIds$ = toObservable(this.deletedFieldIds);
  private createdFields$ = toObservable(this.createdFields);
  private updatedFields$ = toObservable(this.updatedFields);
  changes$ = combineLatest([this.deletedFieldIds$, this.createdFields$, this.updatedFields$]).pipe(
    map(([deletedFieldIds, createdFields, updatedFields]) => ({
      deletedFieldIds,
      createdFields,
      updatedFields,
    })),
  );

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly crmFieldSchemaApiService: CRMFieldSchemaApiService,
    private readonly crmFieldCustomizationApiService: CRMFieldSchemaCustomizationApiService,
  ) {}

  public createField(
    objectType: ObjectType,
    field: FieldSchemaInterface,
    fieldCustomization?: FieldSchemaCustomizationInterface,
  ): Observable<FieldOperationStatus> {
    return this.config.namespace$.pipe(
      switchMap((namespace) => {
        return this.crmFieldSchemaApiService.createFieldSchema({
          namespace: namespace,
          crmObjectType: objectType,
          fieldSchema: {
            ...field,
            namespace,
          },
        });
      }),
      withLatestFrom(this.config.namespace$),
      switchMap(([resp, namespace]) => {
        // Get the created field
        const getField$ = this.crmFieldSchemaApiService.getMultiFieldSchema({
          namespace: namespace,
          crmObjectType: objectType,
          fieldId: [resp.fieldId],
        });

        // If no customization is needed, just return the field creation result
        if (!fieldCustomization?.restrictedFieldValues?.length) {
          return getField$.pipe(
            tap((getResponse) =>
              this.createdFields.update((fieldMap) => {
                const fieldsPerObjectType = fieldMap.get(objectType) ?? [];
                if (getResponse?.fieldSchemas[0]) {
                  fieldMap.set(objectType, [...fieldsPerObjectType, getResponse.fieldSchemas[0]]);
                }
                return new Map(fieldMap);
              }),
            ),
            map((response) =>
              response != null ? FIELD_OPERATION_STATUS.SUCCESS : FIELD_OPERATION_STATUS.FIELD_CREATION_FAILED,
            ),
          );
        }

        // Create the field customization
        return getField$.pipe(
          switchMap((getResponse) => {
            if (!getResponse?.fieldSchemas[0]) {
              return of(FIELD_OPERATION_STATUS.FIELD_CREATION_FAILED);
            }

            const customization: FieldSchemaCustomizationInterface = {
              namespace,
              fieldId: resp.fieldId,
              restrictedFieldValues: fieldCustomization.restrictedFieldValues,
            };

            return this.crmFieldCustomizationApiService
              .createFieldSchemaCustomization({
                namespace,
                crmObjectType: objectType,
                fieldSchemaCustomization: customization,
              })
              .pipe(
                map(() => {
                  // Update the created fields tracking
                  this.createdFields.update((fieldMap) => {
                    const fieldsPerObjectType = fieldMap.get(objectType) ?? [];
                    fieldMap.set(objectType, [...fieldsPerObjectType, getResponse.fieldSchemas[0]]);
                    return new Map(fieldMap);
                  });
                  return FIELD_OPERATION_STATUS.SUCCESS;
                }),
                catchError((error) => {
                  console.error('Failed to create field customization:', error);
                  // Field was created but customization failed
                  this.createdFields.update((fieldMap) => {
                    const fieldsPerObjectType = fieldMap.get(objectType) ?? [];
                    fieldMap.set(objectType, [...fieldsPerObjectType, getResponse.fieldSchemas[0]]);
                    return new Map(fieldMap);
                  });
                  return of(FIELD_OPERATION_STATUS.FIELD_CREATED_CUSTOMIZATION_FAILED);
                }),
              );
          }),
        );
      }),
      catchError((error) => {
        console.error('Failed to create field:', error);
        return of(FIELD_OPERATION_STATUS.FIELD_CREATION_FAILED);
      }),
      take(1),
    );
  }

  public updateField(
    objectType: ObjectType,
    field: FieldSchemaInterface,
    fieldCustomization?: FieldSchemaCustomizationInterface,
    isSystemField = false,
  ): Observable<FieldOperationStatus> {
    return this.config.namespace$.pipe(
      switchMap((namespace) => {
        // For system fields, skip field schema update and only handle customization
        if (isSystemField) {
          return this.updateFieldCustomizationOnly(objectType, field.fieldId || '', fieldCustomization);
        }

        const updateFieldSchema$ = this.crmFieldSchemaApiService.updateFieldSchema({
          namespace: namespace,
          crmObjectType: objectType,
          fieldSchema: {
            ...field,
            namespace,
          },
          fieldMask: {
            paths: ['fieldName', 'fieldDescription', 'externalId'],
          },
        });

        // If no field customization is provided, just update the field schema
        if (!fieldCustomization) {
          return updateFieldSchema$.pipe(
            map((result) => (result ? FIELD_OPERATION_STATUS.SUCCESS : FIELD_OPERATION_STATUS.FIELD_UPDATE_FAILED)),
          );
        }

        // Get the existing field customization, if any
        const getFieldCustomization$ = this.crmFieldCustomizationApiService
          .getMultiFieldSchemaCustomization({
            namespace: namespace,
            crmObjectType: objectType,
            fieldId: [field.fieldId || ''],
          })
          .pipe(
            map((response) => response?.fieldSchemaCustomizations?.[0] || null),
            catchError((error) => {
              console.error('Failed to get existing field customization:', error);
              return of(null);
            }),
          );

        // After updating the field schema, handle the customization
        return updateFieldSchema$.pipe(
          switchMap((updateResult) => {
            if (!updateResult) {
              return of(FIELD_OPERATION_STATUS.FIELD_UPDATE_FAILED);
            }

            return getFieldCustomization$.pipe(
              switchMap((existingCustomization) => {
                // Only proceed with customization if there are restrictedFieldValues
                if (!fieldCustomization?.restrictedFieldValues?.length) {
                  return of(FIELD_OPERATION_STATUS.SUCCESS);
                }

                const customization: FieldSchemaCustomizationInterface = {
                  namespace,
                  fieldId: field.fieldId || '',
                  restrictedFieldValues: fieldCustomization.restrictedFieldValues,
                };

                // If an existing customization was found, update it
                if (existingCustomization) {
                  return this.crmFieldCustomizationApiService
                    .updateFieldSchemaCustomization({
                      namespace,
                      crmObjectType: objectType,
                      fieldSchemaCustomization: customization,
                      fieldMask: {
                        paths: ['RestrictedFieldValues'],
                      },
                    })
                    .pipe(
                      map(() => FIELD_OPERATION_STATUS.SUCCESS),
                      catchError((error) => {
                        console.error('Failed to update field customization:', error);
                        return of(FIELD_OPERATION_STATUS.FIELD_UPDATED_CUSTOMIZATION_FAILED);
                      }),
                    );
                }

                // Otherwise create a new customization
                return this.crmFieldCustomizationApiService
                  .createFieldSchemaCustomization({
                    namespace,
                    crmObjectType: objectType,
                    fieldSchemaCustomization: customization,
                  })
                  .pipe(
                    map(() => FIELD_OPERATION_STATUS.SUCCESS),
                    catchError((error) => {
                      console.error('Failed to create field customization:', error);
                      return of(FIELD_OPERATION_STATUS.FIELD_UPDATED_CUSTOMIZATION_FAILED);
                    }),
                  );
              }),
            );
          }),
        );
      }),
      catchError((error) => {
        console.error('Failed to update field:', error);
        return of(FIELD_OPERATION_STATUS.FIELD_UPDATE_FAILED);
      }),
      take(1),
      tap((result) => {
        // Only update the fields tracking for non-system fields and successful updates
        if (
          !isSystemField &&
          (result === FIELD_OPERATION_STATUS.SUCCESS ||
            result === FIELD_OPERATION_STATUS.FIELD_UPDATED_CUSTOMIZATION_FAILED)
        ) {
          this.updatedFields.update((fieldMap) => {
            const fieldsPerObjectType = fieldMap.get(objectType) ?? [];
            const index = fieldsPerObjectType.findIndex((f) => f.fieldId === field.fieldId);
            if (index !== -1) {
              fieldsPerObjectType[index] = field;
              fieldMap.set(objectType, fieldsPerObjectType);
            } else {
              fieldMap.set(objectType, [...fieldsPerObjectType, field]);
            }
            return new Map(fieldMap);
          });
        }
      }),
    );
  }

  /**
   * Updates only the field customization (restricted values) without modifying the field schema.
   * This is used for system fields where the schema cannot be modified.
   */
  public updateFieldCustomizationOnly(
    objectType: ObjectType,
    fieldId: string,
    fieldCustomization?: FieldSchemaCustomizationInterface,
  ): Observable<FieldOperationStatus> {
    return this.config.namespace$.pipe(
      switchMap((namespace) => {
        // Get the existing field customization, if any
        const getFieldCustomization$ = this.crmFieldCustomizationApiService
          .getMultiFieldSchemaCustomization({
            namespace: namespace,
            crmObjectType: objectType,
            fieldId: [fieldId],
          })
          .pipe(
            map((response) => response?.fieldSchemaCustomizations?.[0] || null),
            catchError((error) => {
              console.error('Failed to get existing field customization for system field:', error);
              return of(null);
            }),
          );

        return getFieldCustomization$.pipe(
          switchMap((existingCustomization) => {
            // If no customization is provided or no restricted values, remove existing customization
            if (!fieldCustomization?.restrictedFieldValues?.length) {
              // For now, we'll just return success if no customization is needed
              // In the future, we might want to delete existing customizations
              return of(FIELD_OPERATION_STATUS.SUCCESS);
            }

            const customization: FieldSchemaCustomizationInterface = {
              namespace,
              fieldId: fieldId,
              restrictedFieldValues: fieldCustomization.restrictedFieldValues,
            };

            // If an existing customization was found, update it
            if (existingCustomization) {
              return this.crmFieldCustomizationApiService
                .updateFieldSchemaCustomization({
                  namespace,
                  crmObjectType: objectType,
                  fieldSchemaCustomization: customization,
                  fieldMask: {
                    paths: ['RestrictedFieldValues'],
                  },
                })
                .pipe(
                  map(() => FIELD_OPERATION_STATUS.SUCCESS),
                  catchError((error) => {
                    console.error('Failed to update system field customization:', error);
                    return of(FIELD_OPERATION_STATUS.FIELD_UPDATED_CUSTOMIZATION_FAILED);
                  }),
                );
            }

            // Otherwise create a new customization
            return this.crmFieldCustomizationApiService
              .createFieldSchemaCustomization({
                namespace,
                crmObjectType: objectType,
                fieldSchemaCustomization: customization,
              })
              .pipe(
                map(() => FIELD_OPERATION_STATUS.SUCCESS),
                catchError((error) => {
                  console.error('Failed to create system field customization:', error);
                  return of(FIELD_OPERATION_STATUS.FIELD_UPDATED_CUSTOMIZATION_FAILED);
                }),
              );
          }),
        );
      }),
      catchError((error) => {
        console.error('Failed to update system field customization:', error);
        return of(FIELD_OPERATION_STATUS.FIELD_UPDATE_FAILED);
      }),
      take(1),
    );
  }

  public deleteField(request: ArchiveFieldSchemaRequestInterface): Observable<FieldOperationStatus> {
    return this.crmFieldSchemaApiService.archiveFieldSchema(request).pipe(
      catchError((error) => {
        console.error('Failed to delete field:', error);
        return of(null);
      }),
      map((response) => {
        return response != null ? FIELD_OPERATION_STATUS.SUCCESS : FIELD_OPERATION_STATUS.FIELD_DELETION_FAILED;
      }),
      take(1),
      tap((result) => {
        if (result === FIELD_OPERATION_STATUS.SUCCESS) {
          this.deletedFieldIds.update((fieldMap) => {
            const fieldsPerObjectType = fieldMap.get(request?.crmObjectType as ObjectType) ?? [];
            const index = fieldsPerObjectType.findIndex((fieldId) => fieldId === request.fieldId);
            if (index === -1 && request.fieldId) {
              fieldMap.set(request?.crmObjectType as ObjectType, [...fieldsPerObjectType, request.fieldId ?? '']);
            }
            return new Map(fieldMap);
          });
        }
      }),
    );
  }

  public getFieldCustomization(
    objectType: ObjectType,
    fieldId: string,
  ): Observable<FieldSchemaCustomizationInterface | null> {
    return this.config.namespace$.pipe(
      switchMap((namespace) => {
        return this.crmFieldCustomizationApiService.getMultiFieldSchemaCustomization({
          namespace: namespace,
          crmObjectType: objectType,
          fieldId: [fieldId],
        });
      }),
      map((response) => response?.fieldSchemaCustomizations?.[0] || null),
      catchError(() => of(null)),
      take(1),
    );
  }

  public resetChanges(objectType: ObjectType): void {
    const deletedFieldIds = this.deletedFieldIds();
    deletedFieldIds.set(objectType, []);
    const createdFields = this.createdFields();
    createdFields.set(objectType, []);
    const updatedFields = this.updatedFields();
    updatedFields.set(objectType, []);
    this.deletedFieldIds.set(new Map(deletedFieldIds));
    this.createdFields.set(new Map(createdFields));
    this.updatedFields.set(new Map(updatedFields));
  }
}
