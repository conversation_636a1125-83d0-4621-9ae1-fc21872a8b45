import { AbstractControl, ValidationErrors } from '@angular/forms';

const Format12 = new RegExp(/^((0?[1-9])|([1]?[0-2]))(\s*):(\s*)([0-5]\d)(\s*)(am|pm)(\s*)$/i);
const Format24 = new RegExp(/^(([01]?[0-9])|2[0-3])(\s*):(\s*)([0-5]\d)(\s*)$/i);
const FormatGeneral = new RegExp(/^((0?[1-9])|([1]?[0-2]))(\s*)(am|pm)(\s*)$/i);
const specialValues = ['', 'allDay', 'notesOnly'];

export class HoursValidator {
  static HoursValidate(control: AbstractControl): ValidationErrors | null {
    const isSpecialValue = specialValues.indexOf(control.value);
    const isValidFormat = isValidTime(control.value);
    if (isSpecialValue !== -1 || isValidFormat) {
      return null;
    } else {
      return { InvalidHours: { value: control.value } };
    }
  }
}
export function isValidTime(val: string): boolean {
  const isEndOfDay = val === '24:00';
  const isValid12 = Format12.test(val);
  const isValid24 = Format24.test(val);
  const isValidGeneral = FormatGeneral.test(val);
  return isValid12 || isValid24 || isValidGeneral || isEndOfDay;
}
