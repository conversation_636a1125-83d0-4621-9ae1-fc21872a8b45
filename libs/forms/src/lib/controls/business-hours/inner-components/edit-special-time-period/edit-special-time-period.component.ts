import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { SpecialHoursStatus } from '@vendasta/listing-products';
import { SpecialHoursDateTimeInterface } from '../../special-hours/business-hours.interface';
import { googleDateToNativeDate, nativeDateToGoogleDate, timeOfDayToTimeString } from '../../business-hours';
import { TimeSpan } from '../../../hours-of-operation/hours-of-operation.interface';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'forms-edit-special-time-period',
  templateUrl: './edit-special-time-period.component.html',
  styleUrls: ['./edit-special-time-period.component.scss'],
  standalone: false,
})
export class EditSpecialTimePeriodComponent {
  @Input() set dateWithHours(v: SpecialHoursDateTimeInterface) {
    this.selectedDate.setValue(googleDateToNativeDate(v.date));
    this._timePeriod =
      v.timeSpan?.map((s) => {
        return {
          opens: timeOfDayToTimeString(s.startTime),
          closes: timeOfDayToTimeString(s.endTime),
        };
      }) || [];
  }
  _timePeriod: TimeSpan[] = [];
  selectedDate = new FormControl<Date>(new Date());

  @Output() dateValueChange = new EventEmitter();
  @Output() hoursValueChange = new EventEmitter();
  @Output() removeDate = new EventEmitter<void>();
  @Output() hasErrors = new EventEmitter<boolean>();

  hasErrorsStatusForEachDay = false;

  constructor(private cdr: ChangeDetectorRef) {}

  onDateChange(value: Date): void {
    value = new Date(value);
    const googleDate = nativeDateToGoogleDate(value.getDate(), value.getMonth(), value.getFullYear());
    this.dateValueChange.emit(googleDate);
  }

  protected getStatus(isOpen: boolean): SpecialHoursStatus {
    return isOpen ? SpecialHoursStatus.SPECIAL_HOURS_STATUS_OPEN : SpecialHoursStatus.SPECIAL_HOURS_STATUS_CLOSED;
  }

  changeHours(hours: TimeSpan[]): void {
    this._timePeriod = hours;
    this.hoursValueChange.emit(this._timePeriod);
    this.cdr.detectChanges();
  }

  handleHasAnyErrors(error: boolean): void {
    if (this.hasErrorsStatusForEachDay !== error) {
      this.hasErrorsStatusForEachDay = error;
    }
    this.cdr.detectChanges();
  }

  deleteDate(): void {
    this.removeDate.next();
  }
}
