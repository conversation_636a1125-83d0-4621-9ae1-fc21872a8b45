import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { GoogleDateInterface, SpecialHoursPeriodInterface, SpecialHoursStatus } from '@vendasta/listing-products';
import { SpecialHoursDateTimeInterface, SpecialHoursTimeSpan } from '../special-hours/business-hours.interface';
import {
  convertToSpecialHoursDateTime,
  convertToSpecialHoursPeriod,
  convertToTimeOfDay,
  nativeDateToGoogleDate,
} from '../business-hours';
import { TimeSpan } from '../../hours-of-operation/hours-of-operation.interface';

const today = new Date();
const DefaultCurrentDateSpecialHours: SpecialHoursDateTimeInterface = {
  status: SpecialHoursStatus.SPECIAL_HOURS_STATUS_CLOSED,
  date: nativeDateToGoogleDate(today.getUTCDate(), today.getUTCMonth(), today.getUTCFullYear()),
};

@Component({
  templateUrl: './edit-special-hours-dialog.component.html',
  styleUrls: ['./edit-special-hours-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class EditSpecialHoursDialogComponent implements OnInit {
  @Input() control: UntypedFormControl;
  inputSpecialHours: SpecialHoursPeriodInterface[];

  // backend returns different objects for different timespan with same date
  // convertedSpecialHours will have all the different objects merged into one
  // so that one date range can have multiple timespan which will be easier to loop and render
  convertedSpecialHours: SpecialHoursDateTimeInterface[];

  hasErrors = false;

  constructor(
    public dialogRef: MatDialogRef<EditSpecialHoursDialogComponent>,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.inputSpecialHours = [];
    if (this.control?.value && this.control?.value?.length > 0) {
      this.inputSpecialHours = this.control?.value;
    }

    this.convertedSpecialHours = convertToSpecialHoursDateTime(this.inputSpecialHours);
  }

  onAddNewDate(): void {
    this.convertedSpecialHours.push(Object.assign({}, DefaultCurrentDateSpecialHours) as SpecialHoursDateTimeInterface);
  }

  dateValueChange(newDate: GoogleDateInterface, i: number): void {
    // on change, update current date to event date
    if (newDate) {
      this.convertedSpecialHours[i].date = newDate;
      this.cdr.detectChanges();
    }
  }

  hoursValueChange(timePeriod: TimeSpan[], i: number): void {
    this.convertedSpecialHours[i].timeSpan = timePeriod.map((time) => {
      let opens = time.opens;
      let closes = time.closes;
      if (opens === closes) {
        opens = '0:00';
        closes = '24:00';
      }
      return new SpecialHoursTimeSpan(convertToTimeOfDay(opens), convertToTimeOfDay(closes));
    });
    this.convertedSpecialHours[i].status =
      timePeriod?.length > 0
        ? SpecialHoursStatus.SPECIAL_HOURS_STATUS_OPEN
        : SpecialHoursStatus.SPECIAL_HOURS_STATUS_CLOSED;
  }

  save(): void {
    const updatedDate = convertToSpecialHoursPeriod(this.convertedSpecialHours);
    this.dialogRef.close(updatedDate);
  }

  removeDateOnIndex(index: any): void {
    this.convertedSpecialHours.splice(index, 1);
  }
}
