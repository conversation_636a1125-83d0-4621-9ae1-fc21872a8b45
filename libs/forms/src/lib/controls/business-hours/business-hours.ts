import {
  DayOfWeek,
  GoogleDate,
  GoogleDateInterface,
  RegularHoursPeriod,
  SpecialHoursPeriodInterface,
  SpecialHoursStatus,
  TimeOfDay,
  TimeOfDayInterface,
} from '@vendasta/listing-products';
import { SpecialHoursDateTimeInterface, SpecialHoursTimeSpan } from './special-hours/business-hours.interface';
import dayjs from 'dayjs';
import { TimeSpan } from '../hours-of-operation/hours-of-operation.interface';

export const convertToSpecialHoursDateTime = function (
  specialHours: SpecialHoursPeriodInterface[],
): SpecialHoursDateTimeInterface[] {
  const newDayTime: SpecialHoursDateTimeInterface[] = [];

  for (const inputDay of specialHours) {
    const newDate: GoogleDateInterface = inputDay.startDate;

    if (inputDay.status === SpecialHoursStatus.SPECIAL_HOURS_STATUS_OPEN) {
      const newTimeSpan: SpecialHoursTimeSpan = new SpecialHoursTimeSpan(
        new TimeOfDay({
          hours: inputDay.startTime?.hours || 0,
          minutes: inputDay.startTime?.minutes || 0,
        }),
        new TimeOfDay({
          hours: inputDay.endTime?.hours || 0,
          minutes: inputDay.endTime?.minutes || 0,
        }),
      );

      const existingDateIndex = indexOfSameDateRange(newDayTime, inputDay);
      if (!isNaN(existingDateIndex)) {
        newDayTime[existingDateIndex].timeSpan.push(newTimeSpan);
      } else {
        const newOpenDateRange: SpecialHoursDateTimeInterface = {
          status: inputDay.status,
          date: newDate,
          timeSpan: [newTimeSpan],
        };
        newDayTime.push(newOpenDateRange);
      }
    }

    if (inputDay.status === SpecialHoursStatus.SPECIAL_HOURS_STATUS_CLOSED) {
      const newDateRange: SpecialHoursDateTimeInterface = {
        status: inputDay.status,
        date: newDate,
      };
      newDayTime.push(newDateRange);
    }
  }
  return newDayTime;
};

// converts local special hours date time to backend request compatible special hours period
export const convertToSpecialHoursPeriod = function (
  dateTimeSpan: SpecialHoursDateTimeInterface[],
): SpecialHoursPeriodInterface[] {
  const specialHours: SpecialHoursPeriodInterface[] = [];
  for (const dateSpan of dateTimeSpan) {
    const status = dateSpan.status;
    const startDate = dateSpan.date;
    let endDate = dateSpan.date;
    if (status === SpecialHoursStatus.SPECIAL_HOURS_STATUS_OPEN) {
      for (let i = 0; i < dateSpan.timeSpan?.length; i++) {
        // If the end time is before the start time, it means the hours cross midnight, so the end time is on the next day
        if (timeOfDayBefore(dateSpan.timeSpan[i].endTime, dateSpan.timeSpan[i].startTime)) {
          endDate = nextDay(endDate);
        }
        specialHours.push({
          status: status,
          startDate: startDate,
          startTime: dateSpan.timeSpan[i].startTime,
          endDate: endDate,
          endTime: dateSpan.timeSpan[i].endTime,
        });
      }
    } else {
      specialHours.push({
        status: status,
        startDate: startDate,
        endDate: endDate,
      });
    }
  }

  return specialHours;
};

function nextDay(date: GoogleDateInterface): GoogleDateInterface {
  const nativeDate = googleDateToNativeDate(date);
  nativeDate.setDate(nativeDate.getDate() + 1);
  return nativeDateToGoogleDate(nativeDate.getUTCDate(), nativeDate.getUTCDate(), nativeDate.getUTCFullYear());
}

export const indexOfSameDateRange = function (
  newDayTime: SpecialHoursDateTimeInterface[],
  searchDateSpan: SpecialHoursPeriodInterface,
): number {
  for (let i = 0; i < newDayTime.length; i++) {
    if (hasSameDateRange(newDayTime[i], searchDateSpan)) {
      return i;
    }
  }

  return NaN;
};

export const hasSameDateRange = function (
  dateSpan: SpecialHoursDateTimeInterface,
  searchDateSpan: SpecialHoursPeriodInterface,
): boolean {
  if (dateSpan && searchDateSpan) {
    return (
      dateSpan.date.day === searchDateSpan.startDate.day &&
      dateSpan.date.month === searchDateSpan.startDate.month &&
      dateSpan.date.year === searchDateSpan.startDate.year &&
      dateSpan.date.day === searchDateSpan.endDate.day &&
      dateSpan.date.month === searchDateSpan.endDate.month &&
      dateSpan.date.year === searchDateSpan.endDate.year
    );
  }
  return false;
};

export const convertToTimeOfDay = function (timeString: string): TimeOfDayInterface {
  const separatedTime = timeString.split(':');
  if (separatedTime?.length > 1) {
    return {
      hours: parseInt(separatedTime[0], 10),
      minutes: parseInt(separatedTime[1], 10),
    };
  }
  return {
    hours: 0,
    minutes: 0,
  } as TimeOfDayInterface;
};

export const googleDateMonthToNativeDateMonth = function (month: number): number {
  return month - 1;
};

export const nativeDateMonthToGoogleDateMonth = function (month: number): number {
  return month + 1;
};

export const nativeDateToGoogleDate = function (day: number, month: number, year: number): GoogleDateInterface {
  return new GoogleDate({
    day: day,
    month: nativeDateMonthToGoogleDateMonth(month),
    year: year,
  });
};

export const googleDateToNativeDate = function (date?: GoogleDateInterface): Date {
  if (date) {
    return new Date(date.year, googleDateMonthToNativeDateMonth(date.month), date.day);
  }

  return new Date();
};

export const getNewTimeOfDay = function (span?: TimeOfDayInterface): TimeOfDayInterface {
  if (span) {
    return new TimeOfDay(span);
  }

  return new TimeOfDay({ hours: 9, minutes: 0, seconds: 0, nanos: 0 });
};

export function timeOfDayToTimeString(time: TimeOfDayInterface): string {
  if (!time) {
    return '';
  }
  if (time.hours === 24) {
    time.hours = 0;
  }
  if (!time.hours && !time.minutes) {
    return '0:00';
  }
  const timeString = (time.hours || '00') + ':' + (time.minutes || '00');
  const parseFormat = 'H:m';
  return dayjs(timeString, parseFormat).format('HH:mm');
}

export function timeSpanToRegularHoursPeriod(timeSpan: TimeSpan, dayOfWeek: DayOfWeek): RegularHoursPeriod {
  if (timeSpan.opens === timeSpan.closes) {
    timeSpan.opens = '00:00';
    timeSpan.closes = '24:00';
  }

  return new RegularHoursPeriod({
    openDay: dayOfWeek,
    openTime: convertToTimeOfDay(timeSpan.opens),
    closeDay: dayOfWeek,
    closeTime: convertToTimeOfDay(timeSpan.closes),
  });
}

function timeOfDayBefore(time1: TimeOfDayInterface, time2: TimeOfDayInterface): boolean {
  return time1.hours < time2.hours || (time1.hours === time2.hours && time1.minutes < time2.minutes);
}
