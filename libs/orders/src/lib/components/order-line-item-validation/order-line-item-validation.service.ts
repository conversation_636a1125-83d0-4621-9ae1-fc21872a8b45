import { Injectable } from '@angular/core';
import { combineLatest, forkJoin, Observable, of } from 'rxjs';
import { LineItem, Order, SalesOrdersService, ValidationErrorCodes } from '@vendasta/sales-orders';
import { distinctUntilChanged, map, shareReplay, switchMap, take, withLatestFrom } from 'rxjs/operators';
import { OrderStoreService } from '../../core/orders.service';
import { OrderPermissionsService } from '../../core/permissions/permissions.service';
import { OrderFeature } from '../../core/permissions';
import { TranslateService } from '@ngx-translate/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { InvalidBillingTermsDialogComponent } from '../invalid-billing-terms-dialog/invalid-billing-terms-dialog.component';
import { ConfirmationModalMaxWidth, ConfirmationModalWidth } from '@vendasta/galaxy/confirmation-modal';
import * as e from '@vendasta/sales-orders/lib/_internal/enums';
import { AppKey, GetMultiAppRequest, PartnerApiService } from '@vendasta/marketplace-apps';
import { InvalidWholesaleBillingDialogComponent } from '../invalid-wholesale-billing-dialog/invalid-wholesale-billing-dialog.component';

const validationErrorMessage: { [key in ValidationErrorCodes]?: string } = {
  [ValidationErrorCodes.ALREADY_ACTIVE]: 'LIB_ORDERS.SALES_ORDERS.WARNINGS.INVALID_LINE_ITEMS_BANNER_ALREADY_ACTIVE',
  [ValidationErrorCodes.INVALID_BILLING_TERMS]:
    'LIB_ORDERS.SALES_ORDERS.WARNINGS.INVALID_LINE_ITEMS_BANNER_INVALID_BILLING_TERMS',
};

export interface LineItemValidation {
  errorCodes: ValidationErrorCodes[];
  errorCodesToLineItems?: Map<string, string[]>;
}

@Injectable()
export class OrderLineItemValidationService {
  public invalidLineItemErrors$: Observable<LineItemValidation>;
  public formattedLineItemErrors$: Observable<string[]>;
  public hasAlreadyActiveProducts$: Observable<boolean>;
  private areOrderBillingTermsValid$: Observable<boolean>;
  public canLineItemsBeWholesaleBilled$: Observable<boolean>;

  public order$ = this.orderService.order$;

  constructor(
    private salesOrdersService: SalesOrdersService,
    private orderService: OrderStoreService,
    private orderPermissionsService: OrderPermissionsService,
    private translateService: TranslateService,
    private dialog: MatDialog,
    private readonly marketplaceAppsPartnerApiService: PartnerApiService,
  ) {
    const canValidateLineItems = this.orderPermissionsService.CanView(OrderFeature.InvalidLineItemsBanner);
    this.invalidLineItemErrors$ = combineLatest([
      this.order$.pipe(distinctUntilChanged()),
      canValidateLineItems.pipe(distinctUntilChanged()),
    ]).pipe(
      switchMap(([order, canValidate]) => {
        if (!canValidate) {
          return of({ errorCodes: [], errorCodesToLineItems: new Map() });
        }
        if (order?.orderId || order?.businessId) {
          return this.validateLineItems(order.businessId, order.orderId);
        }
        return of({ errorCodes: [], errorCodesToLineItems: new Map() });
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.canLineItemsBeWholesaleBilled$ = this.order$.pipe(
      switchMap((order) => {
        return this.salesOrdersService.canOrderBeWholesaleBilled(order.businessId, order.orderId);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const appNames$ = this.order$.pipe(
      switchMap((order) => {
        const appItems = order.lineItems.filter((item) => !item.packageId);
        if (!appItems.length) return of(new Map<string, string>());

        const getMultiAppRequest = new GetMultiAppRequest({
          projectionFilter: {
            paths: ['sharedMarketingInformation'],
          },
          appKeys: appItems.map((item) => {
            return new AppKey({
              appId: item?.appKey?.appId,
              editionId: item?.appKey?.editionId ?? undefined,
            });
          }),
          partnerId: order.partnerId,
          marketId: order.marketId,
          includeNotEnabled: true,
        });

        return this.marketplaceAppsPartnerApiService.getMultiApp(getMultiAppRequest).pipe(
          map((response) => {
            return response.apps.reduce((map, app) => {
              if (!app.sharedMarketingInformation.editionName) {
                map.set(app.key?.appId, app.sharedMarketingInformation?.name || '');
              } else {
                map.set(
                  app.key?.appId,
                  `${app.sharedMarketingInformation?.name} | ${app.sharedMarketingInformation?.editionName}`,
                );
              }
              return map;
            }, new Map<string, string>());
          }),
        );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.formattedLineItemErrors$ = combineLatest([this.invalidLineItemErrors$, this.order$, appNames$]).pipe(
      switchMap(([errors, order, appNames]) => {
        return this.formatValidationErrorCodeForBanner(errors, appNames, order.requestedActivation, order.lineItems);
      }),
    );

    this.areOrderBillingTermsValid$ = this.invalidLineItemErrors$.pipe(
      distinctUntilChanged(),
      map((errors) => (errors ? !errors?.errorCodes?.includes(ValidationErrorCodes.INVALID_BILLING_TERMS) : true)),
    );

    this.hasAlreadyActiveProducts$ = this.invalidLineItemErrors$.pipe(
      distinctUntilChanged(),
      withLatestFrom(this.order$),
      map(([errors, order]) => {
        return this.orderContainsAlreadyActiveProducts(order, errors);
      }),
    );
  }

  orderContainsAlreadyActiveProducts(order: Order, errors: LineItemValidation): boolean {
    if (
      !errors ||
      !order ||
      !Array.isArray(order.lineItems) ||
      !(errors.errorCodesToLineItems?.get(ValidationErrorCodes.ALREADY_ACTIVE.toString())?.length > 0)
    ) {
      return false;
    }
    const alreadyActiveIndexes: string[] = errors.errorCodesToLineItems.get(
      ValidationErrorCodes.ALREADY_ACTIVE.toString(),
    );
    return alreadyActiveIndexes.some((index: string) => {
      const i = Number(index);
      const lineItem = order.lineItems[i];
      // Ignore packages
      return lineItem && !lineItem.packageId;
    });
  }

  public validateLineItems(businessId: string, orderId: string): Observable<LineItemValidation> {
    return this.salesOrdersService.validateLineItems(businessId, orderId).pipe(
      map((resp) => {
        const liValidation: LineItemValidation = {
          errorCodes: resp?.errorCodes || [],
          errorCodesToLineItems: new Map(),
        };
        if (resp?.lineItemErrors) {
          liValidation.errorCodesToLineItems = this.lineItemErrorsMapToMapOfErrorCodesToIndexes(resp.lineItemErrors);
        }
        return liValidation;
      }),
    );
  }

  /**
   * Converts a map of line item errors into a Map, where each key corresponds to an error code
   * and contains a list of line item indexes associated with that error.
   *
   * @param errorsMap - Map of line item indexes to arrays of validation error codes.
   * @returns - Map where each key represents an error code and stores corresponding line item indexes.
   */
  public lineItemErrorsMapToMapOfErrorCodesToIndexes(errorsMap: {
    [key: string]: e.ValidationErrorCodes[];
  }): Map<string, string[]> {
    const errorsMapToArray = new Map<string, string[]>();

    Object.keys(errorsMap).forEach((lineItemIndex) => {
      const errorCodes = errorsMap[lineItemIndex];
      errorCodes.forEach((errorCode) => {
        if (!errorsMapToArray.has(errorCode.toString())) {
          errorsMapToArray.set(errorCode.toString(), []);
        }
        errorsMapToArray.get(errorCode.toString()).push(lineItemIndex);
      });
    });

    return errorsMapToArray;
  }

  public formatValidationErrorCodeForBanner(
    liValidation: LineItemValidation,
    lineItemNames: Map<string, string>,
    contractDate: Date,
    lineItems: LineItem[],
  ): Observable<string[]> {
    if (!(liValidation?.errorCodes?.length > 0)) {
      return of([]);
    }
    return forkJoin(
      liValidation?.errorCodes.map((errorCode): Observable<string> => {
        const names = [];
        const lineItemIndices = liValidation.errorCodesToLineItems.get(errorCode.toString()) || [];
        lineItemIndices.forEach((index) => {
          if (lineItems[index].packageId) {
            return;
          }
          names.push(lineItemNames.get(lineItems[index].appKey.appId));
        });
        const formattedLineItemNames = this.formatLineItemName(names);
        switch (errorCode) {
          case ValidationErrorCodes.ALREADY_ACTIVE:
            return this.translateService.get(validationErrorMessage[errorCode], {
              invalidLineItems: formattedLineItemNames,
            });
          case ValidationErrorCodes.INVALID_BILLING_TERMS:
            return this.translateService.get(validationErrorMessage[errorCode], {
              date: this.formatLineItemDate(contractDate),
              invalidLineItems: formattedLineItemNames,
            });
          default:
            return of('');
        }
      }),
    );
  }

  private formatLineItemName(lineItemNames: string[]): string {
    return lineItemNames?.length ? `: ${lineItemNames.join(', ')}` : '';
  }

  private formatLineItemDate(contractDate: Date): string {
    return Intl.DateTimeFormat('en', { month: 'short', day: 'numeric', year: 'numeric' }).format(contractDate);
  }

  public areBillingTermsValidOrOpenModal$() {
    return combineLatest([this.order$, this.areOrderBillingTermsValid$]).pipe(
      take(1),
      switchMap(([order, isValid]) => {
        if (isValid) {
          return of(true);
        }
        return this.dialog
          .open(InvalidBillingTermsDialogComponent, {
            width: ConfirmationModalWidth,
            maxWidth: ConfirmationModalMaxWidth,
            data: {
              date: this.formatLineItemDate(order.requestedActivation),
            },
            autoFocus: false,
          } as MatDialogConfig)
          .afterOpened()
          .pipe(map(() => false));
      }),
    );
  }

  public canOrderBeWholesaleBilledOrOpenModal$() {
    return combineLatest([
      this.canLineItemsBeWholesaleBilled$,
      this.orderPermissionsService.canManageWholesaleBilling$,
      this.order$,
    ]).pipe(
      take(1),
      switchMap(([canWholesaleBill, canManageWholesaleBilling, order]) => {
        if (canWholesaleBill) {
          return of(true);
        }
        return this.dialog
          .open(InvalidWholesaleBillingDialogComponent, {
            width: ConfirmationModalWidth,
            maxWidth: ConfirmationModalMaxWidth,
            data: {
              canManageWholesaleBilling: canManageWholesaleBilling,
              partnerId: order?.partnerId,
            },
            autoFocus: false,
          } as MatDialogConfig)
          .afterOpened()
          .pipe(map(() => false));
      }),
    );
  }
}
