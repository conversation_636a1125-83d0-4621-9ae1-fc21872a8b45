import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { WholesaleSummary } from '@galaxy/inventory-ui';
import {
  FuturePaymentsDisplayComponent,
  FuturePaymentDisplayItemsWithCurrency,
} from '../future-payments-display/future-payments-display.component';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { convertFuturePaymentItemsMapIntoFuturePaymentDisplayItemArray } from '../../shared/utils';
import { MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { BillingUiModule } from '@vendasta/billing-ui';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { OrderPermissionsService } from '../../core/permissions';
import { MatDividerModule } from '@angular/material/divider';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { BrandingV2Service } from '@galaxy/partner';
import { OrderLineItemValidationService } from '../order-line-item-validation/order-line-item-validation.service';
@Component({
  selector: 'orders-wholesale-summary',
  imports: [
    MatExpansionPanelTitle,
    TranslateModule,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    BillingUiModule,
    CommonModule,
    MatTooltipModule,
    GalaxyTooltipModule,
    FuturePaymentsDisplayComponent,
    MatDividerModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatIconModule,
    RouterModule,
  ],
  templateUrl: './wholesale-summary.component.html',
  styleUrl: './wholesale-summary.component.scss',
})
export class WholesaleSummaryComponent implements OnInit {
  @Input() set wholesaleSummary(wholesaleSummary: WholesaleSummary) {
    this.wholesaleSummary$$.next(wholesaleSummary);
  }

  @Input() partnerId: string;
  partnerName$: Observable<string>;

  wholesaleSummary$$: BehaviorSubject<WholesaleSummary> = new BehaviorSubject<WholesaleSummary>(undefined);
  protected futurePaymentDisplayItems$: Observable<FuturePaymentDisplayItemsWithCurrency>;
  protected wholesaleSummary$: Observable<WholesaleSummary>;

  canManageWholesaleBilling$: Observable<boolean> = of(false);
  canLineItemsBeWholesaleBilled$: Observable<boolean> = of(true);

  constructor(
    private readonly permissionsService: OrderPermissionsService,
    private brandingService: BrandingV2Service,
    private lineItemValidationService: OrderLineItemValidationService,
  ) {
    this.futurePaymentDisplayItems$ = this.wholesaleSummary$$.pipe(
      distinctUntilChanged(),
      map((wholesaleSummary) =>
        convertFuturePaymentItemsMapIntoFuturePaymentDisplayItemArray(
          wholesaleSummary?.futurePayments,
          wholesaleSummary?.currencyCode,
        ),
      ),
    );
    this.wholesaleSummary$ = this.wholesaleSummary$$.pipe(distinctUntilChanged());

    this.canManageWholesaleBilling$ = this.permissionsService.canManageWholesaleBilling$;
    this.canLineItemsBeWholesaleBilled$ = this.lineItemValidationService.canLineItemsBeWholesaleBilled$;
  }

  ngOnInit(): void {
    // Initialization logic if needed
    this.partnerName$ = this.brandingService.getBranding(this.partnerId).pipe(
      map((branding) => branding.name),
      distinctUntilChanged(),
    );
  }
}
