@if (wholesaleSummary$ | async; as wholesaleSummary) {
  <mat-expansion-panel [expanded]="true">
    <mat-expansion-panel-header>
      <mat-panel-title> {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.WHOLESALE_SUMMARY.TITLE' | translate }}</mat-panel-title>
    </mat-expansion-panel-header>
    <div class="pricing-row due-now">
      <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.DUE_NOW' | translate }}</span>
      <span class="pricing">
        <billing-ui-simple-price-display
          [price]="wholesaleSummary.firstPayment"
          [currencyCode]="wholesaleSummary.currencyCode"
          [alwaysShowNumber]="true"
        ></billing-ui-simple-price-display>
      </span>
    </div>

    @if (futurePaymentDisplayItems$ | async; as futurePaymentLineItems) {
      <orders-future-payments-display
        [futurePayments]="futurePaymentLineItems.lineItems"
        [currencyCode]="futurePaymentLineItems.currencyCode"
      />
    }
    @if ((canManageWholesaleBilling$ | async) && (canLineItemsBeWholesaleBilled$ | async) !== true) {
      <glxy-alert class="invalid-billing-warning" type="warning">
        @if (partnerName$ | async; as partnerName) {
          <div>
            {{
              'LIB_ORDERS.COMMON.EDIT_ORDERS.WHOLESALE_SUMMARY.NEED_TO_ADD_PAYMENT_METHOD'
                | translate: { partnerName: partnerName }
            }}
          </div>
        }
        <a mat-stroked-button class="invalid-billing-button" [routerLink]="['/billing/by-purchase']" target="_blank">
          <mat-icon>open_in_new</mat-icon>
          {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.WHOLESALE_SUMMARY.GO_TO_SETTINGS' | translate }}
        </a>
      </glxy-alert>
    }
  </mat-expansion-panel>
}
