<!-- Edit mode -->
<ng-container *ngIf="tagOptions$ | async">
  <ng-container *ngIf="!viewOnly">
    <glxy-form-field class="tags-form">
      <mat-chip-grid #chipList>
        <mat-chip-row *ngFor="let tag of tags" [removable]="true" (removed)="removeTag(tag)">
          {{ tag }}
          <mat-icon matChipRemove>cancel</mat-icon>
        </mat-chip-row>
        <input
          #tagsInput
          matInput
          placeholder="{{ 'LIB_ORDERS.COMMON.ORDER_DETAILS.EMPTY_TAGS' | translate }}"
          [formControl]="tagsCtrl"
          [matAutocomplete]="auto"
          [matChipInputFor]="chipList"
          [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
        />
      </mat-chip-grid>
      <mat-autocomplete #auto="matAutocomplete">
        <mat-option *ngFor="let tag of filteredTags$ | async" (click)="onAddTag(tag)">
          {{ tag }}
        </mat-option>
        <mat-option *ngIf="newTag$ | async as newTag" (click)="onAddTag(newTag)">
          <mat-icon>add</mat-icon>
          <span class="add-new-text">
            {{ 'LIB_ORDERS.COMMON.ORDER_DETAILS.NEW_TAG' | translate: { newTag: '`' + newTag + '`' } }}
          </span>
        </mat-option>
      </mat-autocomplete>
    </glxy-form-field>
  </ng-container>
</ng-container>

<!-- View mode -->
<div *ngIf="viewOnly">
  <div *ngIf="tags && tags.length > 0; else noTags" class="tags">
    <div *ngFor="let tag of tags">
      <va-badge color="gray">
        <span>{{ tag }}</span>
      </va-badge>
    </div>
  </div>
  <ng-template #noTags>
    <div class="no-tags">
      {{ 'LIB_ORDERS.COMMON.ORDER_DETAILS.EMPTY_TAGS' | translate }}
    </div>
  </ng-template>
</div>
