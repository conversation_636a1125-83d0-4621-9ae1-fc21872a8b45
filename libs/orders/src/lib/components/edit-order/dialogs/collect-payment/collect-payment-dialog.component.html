@if (
  {
    retailPaymentData: retailPaymentData$ | async,
    order: order$ | async,
    loadingOrder: loadingOrder$ | async,
  };
  as context
) {
  <h2 mat-dialog-title>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.TITLE' | translate }}</h2>
  <mat-dialog-content>
    <p>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.SUBTITLE' | translate }}</p>
    @if (!context.retailPaymentData.loading && !context.loadingOrder) {
      @if (canCollectPaymentFromCustomer$ | async) {
        @if (
          context.retailPaymentData.defaultPaymentMethod &&
          (context.retailPaymentData.defaultCustomerRecipientId || context.order.customerRecipient?.userId)
        ) {
          <button
            [trackEvent]="{
              eventName: 'charge-collect-payment-dialog',
              category: 'unified-orders',
              action: 'click',
              properties: { orderStatus: context.order.status },
            }"
            mat-stroked-button
            class="payment-option"
            (click)="onCharge(context.retailPaymentData, context.order)"
          >
            <mat-icon>credit_card</mat-icon>
            <div class="text-content">
              <span>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.CHARGE_SAVED' | translate }}</span>
              <small>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.AFTER_SUCCESS' | translate }}</small>
              <smb-invoicing-payment-method-display
                class="credit-card"
                [paymentMethod]="context.retailPaymentData.defaultPaymentMethod"
              ></smb-invoicing-payment-method-display>
            </div>
          </button>
        } @else {
          <button
            [trackEvent]="{
              eventName: 'add-payment-method-collect-payment-dialog',
              category: 'unified-orders',
              action: 'click',
              properties: { orderStatus: context.order.status },
            }"
            mat-stroked-button
            class="payment-option"
            (click)="addPaymentMethod(context.retailPaymentData, context.order)"
          >
            <mat-icon>credit_card</mat-icon>
            <div class="text-content">
              <span>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.ADD_METHOD' | translate }}</span>
              <small>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.AFTER_SUCCESS' | translate }}</small>
            </div>
          </button>
        }
      }
      <button
        [trackEvent]="{
          eventName: 'submit-for-customer-approval-collect-payment-dialog',
          category: 'unified-orders',
          action: 'click',
          properties: { orderStatus: context.order.status },
        }"
        mat-stroked-button
        class="payment-option"
        (click)="sendForCustomerApproval()"
      >
        <mat-icon>send</mat-icon>
        <div class="text-content">
          <span>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.SEND_TO_CUSTOMER' | translate }}</span>
          <small>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.PROCESS_AFTER_APPROVAL' | translate }}</small>
        </div>
      </button>
      <button
        [trackEvent]="{
          eventName: 'skip-payment-collect-payment-dialog',
          category: 'unified-orders',
          action: 'click',
          properties: { orderStatus: context.order.status },
        }"
        mat-stroked-button
        class="payment-option"
        (click)="processOrder(context.order)"
      >
        <mat-icon>arrow_forward</mat-icon>
        <div class="text-content">
          <span>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.SKIP_PAYMENT' | translate }}</span>
          <small>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.PROCESS_IMMEDIATELY' | translate }}</small>
        </div>
      </button>
    } @else {
      <div class="loading-container">
        <mat-spinner diameter="36"></mat-spinner>
      </div>
    }
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-button mat-dialog-close>
      {{ 'LIB_ORDERS.COMMON.ORDERS.CANCEL' | translate }}
    </button>
  </mat-dialog-actions>
}
