import { Injectable, Injector } from '@angular/core';
import { Observable, of } from 'rxjs';
import { switchMap, take } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { ProcessOrderDialogComponent } from './process-order-dialog.component';
import { Order, SalesOrdersService } from '@vendasta/sales-orders';
import { OrderLineItemValidationService } from '../../../../order-line-item-validation/order-line-item-validation.service';
import { OrdersEditOrderFormValidationService } from '../../../edit-order-form-validation.service';
import { OrderStoreService } from '../../../../../core/orders.service';

export interface SelectPaymentMethodEvent {
  paymentMethodId: string;
  newMethod: boolean;
  userId: string;
}

@Injectable()
export class ProcessOrderDialogService {
  constructor(
    private readonly dialog: MatDialog,
    private readonly salesOrderService: SalesOrdersService,
    private readonly lineItemValidationService: OrderLineItemValidationService,
    private readonly editOrderFormValidationService: OrdersEditOrderFormValidationService,
    public readonly orderService: OrderStoreService,
    private readonly injector: Injector,
  ) {}

  public openProcessOrderDialog(order: Order): Observable<boolean> {
    return this.editOrderFormValidationService.validateFormsForCharging().pipe(
      switchMap((isFormValid) => {
        if (!isFormValid) {
          return of(null);
        }
        return this.lineItemValidationService.areBillingTermsValidOrOpenModal$();
      }),
      switchMap((isBillingTermValid) => {
        if (!isBillingTermValid) {
          return of(null);
        }
        return this.lineItemValidationService.canOrderBeWholesaleBilledOrOpenModal$();
      }),
      switchMap((canBillWholesale) => {
        if (!canBillWholesale) {
          return of(null);
        }
        return this.dialog
          .open(ProcessOrderDialogComponent, {
            width: '600px',
            data: { order },
            autoFocus: false,
            injector: Injector.create({
              providers: [
                { provide: OrderStoreService, useValue: this.orderService },
                { provide: OrdersEditOrderFormValidationService, useValue: this.editOrderFormValidationService },
              ],
              parent: this.injector,
            }),
          })
          .afterClosed();
      }),
      take(1),
    );
  }
}
