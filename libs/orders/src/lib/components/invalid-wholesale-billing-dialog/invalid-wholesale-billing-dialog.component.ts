import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { GalaxyConfirmationModalModule } from '@vendasta/galaxy/confirmation-modal';
import { TranslationModule } from '@galaxy/crm/static';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { BrandingV2Service } from '@galaxy/partner';
import { distinctUntilChanged, map, Observable } from 'rxjs';

interface DialogData {
  canManageWholesaleBilling: boolean;
  partnerId: string;
}

@Component({
  selector: 'orders-invalid-wholesale-billing-dialog',
  standalone: true,
  imports: [
    CommonModule,
    GalaxyConfirmationModalModule,
    TranslationModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
  ],
  templateUrl: './invalid-wholesale-billing-dialog.component.html',
  styleUrl: './invalid-wholesale-billing-dialog.component.scss',
})
export class InvalidWholesaleBillingDialogComponent implements OnInit {
  partnerName$: Observable<string>;
  constructor(
    @Inject(MAT_DIALOG_DATA) public config: DialogData,
    private brandingService: BrandingV2Service,
  ) {}

  ngOnInit(): void {
    this.partnerName$ = this.brandingService.getBranding(this.config.partnerId).pipe(
      map((branding) => branding.name),
      distinctUntilChanged(),
    );
  }
}
