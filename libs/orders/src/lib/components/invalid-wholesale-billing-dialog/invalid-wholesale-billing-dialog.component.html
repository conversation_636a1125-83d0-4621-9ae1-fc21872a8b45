<glxy-confirmation-body>
  <mat-icon glxyConfirmationIcon class="icon" inline="true" [class.warn]="true"> warning </mat-icon>

  <glxy-confirmation-title>
    {{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.INVALID_WHOLESALE_BILLING.TITLE' | translate }}
  </glxy-confirmation-title>

  <glxy-confirmation-message>
    @if (config?.canManageWholesaleBilling) {
      <span
        [innerHTML]="
          'LIB_ORDERS.COMMON.ORDERS.DIALOGS.INVALID_WHOLESALE_BILLING.BILLING_ADMIN_MESSAGE'
            | translate: { partnerName: partnerName$ | async }
        "
      ></span>
    } @else {
      <span>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.INVALID_WHOLESALE_BILLING.DEFAULT_MESSAGE' | translate }}</span>
    }
  </glxy-confirmation-message>
</glxy-confirmation-body>

<glxy-confirmation-actions>
  <glxy-confirmation-primary-actions>
    <div class="actions">
      <button mat-stroked-button id="confirmation-modal-cancel-button" matDialogClose>
        {{ 'LIB_ORDERS.COMMON.ACTION_LABELS.CLOSE' | translate }}
      </button>
    </div>
  </glxy-confirmation-primary-actions>
</glxy-confirmation-actions>
