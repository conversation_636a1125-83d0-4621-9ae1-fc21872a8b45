<glxy-page [isLoading]="loading$ | async">
  <glxy-page-toolbar *ngIf="order$ | async as order" class="non-printable">
    <glxy-page-nav>
      <glxy-page-nav-button
        [previousPageUrl]="previousPageUrl$ | async"
        [previousPageTitle]="'LIB_ORDERS.SALES_ORDERS.ORDERS' | translate"
        [useHistory]="true"
        [historyBackButtonTitle]="'LIB_ORDERS.COMMON_ACTION_LABELS.BACK' | translate"
      ></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>
      <div class="title-container">
        <div>
          {{ order.orderId }}
        </div>
        @if (badge$ | async; as badge) {
          <glxy-badge class="order-status" [color]="badge.color">{{ badge.text | translate }}</glxy-badge>
        }
      </div>
    </glxy-page-title>

    <glxy-page-actions *ngIf="(businessDeleted$ | async) === false">
      <div *ngIf="orderConfirmationActionConfig$ | async as orderConfirmationActionConfig">
        <ng-container *ngIf="processingOrderAction; else showOrderActions">
          <glxy-loading-spinner [size]="'small'"></glxy-loading-spinner>
        </ng-container>
        <ng-template #showOrderActions>
          <orders-actions
            *ngIf="
              order.status !== status.PROCESSING &&
              order.status !== status.CANCELLATION_REQUESTED &&
              order.status !== status.CANCELLED
            "
            [config]="orderConfirmationActionConfig"
            [submitDisabled]="false"
            [orderStatus]="order.status ? order.status : !!order && !order.status ? status.SUBMITTED : null"
            (submitOrderEmitter)="submitDraft(order.businessId)"
            (submitForCustomerApprovalEmitter)="submitForCustomerApproval()"
            (convertToDraftEmitter)="convertToDraft()"
            (requestCancellationEmitter)="onCancel()"
            (submitForAutomationActionEmitter)="startAutomation(order.businessId, $event)"
            (collectPaymentEmitter)="collectPayment()"
            (processOrderEmitter)="processOrderImmediately()"
          ></orders-actions>
        </ng-template>
      </div>
      <button mat-icon-button [matMenuTriggerFor]="menu" [disabled]="processingOrderAction" class="action-button-menu">
        <mat-icon>more_vert</mat-icon>
      </button>
      <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="printPage()">
          {{ 'LIB_ORDERS.COMMON.ORDERS.PRINT' | translate }}
        </button>
        @if (canManageOrders$ | async) {
          <button
            mat-menu-item
            [glxyTooltip]="
              (canIgnoreAllErrorsOnOrder$ | async) === false
                ? ('LIB_ORDERS.SALES_ORDERS.IGNORE_ERRORS_TOOLTIP' | translate)
                : ''
            "
            [disabled]="(canIgnoreAllErrorsOnOrder$ | async) === false"
            (click)="ignoreAllErrors()"
          >
            {{ 'LIB_ORDERS.SALES_ORDERS.IGNORE_ALL_ERRORS' | translate }}
          </button>
        }
        @if ((canCreateInvoicesFromOrder$ | async) === true) {
          <button mat-menu-item (click)="createInvoice()">Create invoice</button>
        }
        <button mat-menu-item (click)="runAutomation()">
          {{ 'LIB_ORDERS.MANUAL_AUTOMATIONS.START_AUTOMATION_TITLE' | translate }}
        </button>
        @if ((this.canSubscribeToUpdates$ | async) === true) {
          @if ((this.isUserSubscribed$ | async) === true) {
            <button mat-menu-item *ngIf="!changingSubscription" (click)="unsubscribeUserFromOrder()">
              {{ 'LIB_ORDERS.SALES_ORDERS.UNSUBSCRIBE' | translate }}
            </button>
          } @else {
            <button mat-menu-item *ngIf="!changingSubscription" (click)="subscribeUserToOrder()">
              {{ 'LIB_ORDERS.SALES_ORDERS.SUBSCRIBE' | translate }}
            </button>
          }
        }
        @if ((canManageOrders$ | async) === true) {
          <button mat-menu-item (click)="updateSalesperson(order)">
            {{ 'LIB_ORDERS.SALES_ORDERS.UPDATE_SALESPERSON' | translate }}
          </button>
        }
        @if (this.canDuplicateOrder$ | async) {
          <button mat-menu-item (click)="duplicateOrder()">
            {{ 'LIB_ORDERS.SALES_ORDERS.DUPLICATE_ORDER' | translate }}
          </button>
        }
        <div *ngIf="changingSubscription" class="converting">
          <mat-spinner [diameter]="16"></mat-spinner>
        </div>
        @if (this.hasCustomFields$ | async) {
          <button
            mat-menu-item
            [routerLink]="[
              '',
              {
                outlets: {
                  action: [
                    'partnerId',
                    order.partnerId,
                    'custom-fields',
                    'order',
                    'object',
                    order.businessId + '|' + order.orderId,
                    'disableManage',
                    false,
                  ],
                },
              },
            ]"
          >
            {{ 'LIB_ORDERS.COMMON.ACTION_LABELS.EDIT_CUSTOM_FIELDS' | translate }}
          </button>
        }
      </mat-menu>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <glxy-page-wrapper [widthPreset]="'full'">
    <orders-view-or-edit-order
      [workOrderPersona]="workOrderPersona"
      [fileUploadUrl]="fileUploadUrl"
    ></orders-view-or-edit-order>
  </glxy-page-wrapper>
</glxy-page>
