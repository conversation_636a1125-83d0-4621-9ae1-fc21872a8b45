import { ChangeDetectorRef, Component, inject, Inject, Injector, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountGroup } from '@galaxy/account-group';
import { StartManualAutomationDialogComponent } from '@galaxy/automata/shared';
import { TranslateService } from '@ngx-translate/core';
import { AutomataOperationsService, Context, EntityType } from '@vendasta/automata';
import { ConfirmationModalMaxWidth, ConfirmationModalWidth } from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import {
  ConfigInterface,
  getOfferExpiry,
  Order,
  SalesOrdersService as SalesOrdersApiService,
  Status,
  SubscriptionLocation,
} from '@vendasta/sales-orders';
import { Badge, SalespersonSelectorDialogComponent, StatusToBadgeMapping } from '@vendasta/sales-ui';
import { WorkOrderPersona } from '@vendasta/work-order';
import { combineLatest, EMPTY, from, Observable, of, throwError } from 'rxjs';
import { map, shareReplay, switchMap, take, tap, withLatestFrom } from 'rxjs/operators';
import { CancelOrderDialogComponent } from '../../components/edit-order/dialogs/cancel-order/cancel-order-dialog.component';
import { OrderConfirmationActionConfig } from '../../components/order-actions/order-actions.component';
import { SubmitCustomerDialogComponent } from '../../components/submit-customer-dialog/submit-customer-dialog.component';
import { ViewOrEditOrderComponent } from '../../components/view-or-edit-order/view-or-edit-order.component';
import { OrderStoreService } from '../../core/orders.service';
import { OrderAction } from '../../core/permissions/permissions';
import { OrderPermissionsService } from '../../core/permissions/permissions.service';
import {
  ORDER_BUSINESS_CONFIG_TOKEN,
  OrderPermissions,
  PAGE_ORDER_CONFIG_TOKEN,
  PageOrderConfig,
} from '../../core/tokens';
import { FieldDataService } from '@vendasta/auxiliary-data-components';
import { OrderLineItemValidationService } from '../../components/order-line-item-validation/order-line-item-validation.service';
import { PreviewPublicOrderDialogComponent } from '../../components/preview-public-order-dialog/preview-public-order-dialog.component';
import { AssociateUserToOrderService } from '../../components/associate-user-to-order/associate-user-to-order.service';
import { getEarliestScheduledLineItemDate } from '../../utils';
import {
  CollectPaymentDialogComponent,
  CollectPaymentDialogResult,
} from '../../components/edit-order/dialogs/collect-payment/collect-payment-dialog.component';
import { RetailPaymentService } from '../../core/retail-payment.service';
import { ProcessOrderDialogService } from '../../components/edit-order/dialogs/collect-payment/process-order-dialog/process-order-dialog.service';
import { OrdersEditOrderFormValidationService } from '../../components/edit-order/edit-order-form-validation.service';

interface RequestCancellationEvent {
  notes: string;
  orderId: string;
  businessId: string;
}
const errors = {
  orderHasNoLineItem: 'Order has no lineItem',
};

@Component({
  selector: 'orders-order-page',
  templateUrl: './order-page.component.html',
  styleUrls: ['./order-page.component.scss'],
  providers: [
    OrderStoreService,
    OrderPermissionsService,
    OrderLineItemValidationService,
    AssociateUserToOrderService,
    RetailPaymentService,
    ProcessOrderDialogService,
  ],
  standalone: false,
})
export class OrderPageComponent implements OnInit {
  business$: Observable<AccountGroup>;
  order$: Observable<Order>;
  loading$: Observable<boolean>;
  businessDeleted$: Observable<boolean>;
  orderConfirmationActionConfig$: Observable<OrderConfirmationActionConfig>;
  salesOrdersConfig$: Observable<ConfigInterface>;
  previousPageUrl$: Observable<string>;
  orderPermissions$: Observable<OrderPermissions>;

  canIgnoreAllErrorsOnOrder$: Observable<boolean>;
  canCreateInvoicesFromOrder$: Observable<boolean>;
  canSubmitForCustomerApproval$: Observable<boolean>;
  canDuplicateOrder$: Observable<boolean>;
  canSubscribeToUpdates$: Observable<boolean>;
  canAccessOnlySalespersonAutomations$: Observable<boolean>;
  canRedirectToCompanyProfileAfterSubmitting$: Observable<boolean>;
  canCollectPaymentFromCustomer$: Observable<boolean>;
  hasCustomFields$: Observable<boolean>;
  status = Status;
  badge$: Observable<Badge>;
  canManageOrders$ = this.permissionsService.canManageOrders$;

  orderPaymentMethodId$: Observable<string>;

  changingSubscription = false;
  isUserSubscribed$: Observable<boolean>;

  processingOrderAction = false;
  workOrderPersona: WorkOrderPersona = WorkOrderPersona.SALES;
  private readonly orderBusinessConfig = inject(ORDER_BUSINESS_CONFIG_TOKEN, { optional: true });

  fileUploadUrl = '/_ajax/v1/marketplace/file/upload';

  @ViewChild(ViewOrEditOrderComponent) viewOrEditComponent: ViewOrEditOrderComponent;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly dialog: MatDialog,
    private readonly cdr: ChangeDetectorRef,
    @Inject(PAGE_ORDER_CONFIG_TOKEN) private readonly pageOrderConfig: PageOrderConfig,
    private readonly injector: Injector,
    public readonly orderService: OrderStoreService,
    public readonly retailPaymentService: RetailPaymentService,
    private readonly automataOperationsService: AutomataOperationsService,
    private readonly permissionsService: OrderPermissionsService,
    private readonly salesOrdersApiService: SalesOrdersApiService,
    private readonly productAnalyticService: ProductAnalyticsService,
    private readonly translateService: TranslateService,
    private readonly snackbarService: SnackbarService,
    private readonly auxiliaryDataFieldDataService: FieldDataService,
    private readonly lineItemValidationService: OrderLineItemValidationService,
    private readonly processOrderDialogService: ProcessOrderDialogService,
    private readonly editOrderFormValidationService: OrdersEditOrderFormValidationService,
  ) {}

  ngOnInit(): void {
    this.business$ = this.orderService.business$;
    this.order$ = this.orderService.order$.pipe(shareReplay(1));
    this.badge$ = this.order$.pipe(map((order) => StatusToBadgeMapping.get(order.status)));
    this.orderConfirmationActionConfig$ = this.orderService.orderConfirmationActionConfig$;
    this.loading$ = this.orderService.loadingOrder$;
    this.businessDeleted$ = this.business$.pipe(map((business) => !business || !!business.deleted));

    this.hasCustomFields$ = this.order$.pipe(
      switchMap((order) =>
        this.auxiliaryDataFieldDataService.hasCustomFields(
          order.partnerId,
          'order',
          order.businessId + '|' + order.orderId,
        ),
      ),
    );
    this.isUserSubscribed$ = this.orderService.isUserSubscribedToOrderAtLocation(
      SubscriptionLocation.SUBSCRIPTION_LOCATION_SALES_CENTER,
    );

    this.salesOrdersConfig$ = this.business$.pipe(
      switchMap((business) =>
        this.salesOrdersApiService.getConfig(
          business.externalIdentifiers.partnerId,
          business.externalIdentifiers.marketId,
        ),
      ),
      take(1),
      map((c) => c as ConfigInterface),
    );

    this.previousPageUrl$ = this.pageOrderConfig.previousPageUrl$ ?? of('../');
    this.canManageOrders$ = this.pageOrderConfig.orderPermissions$.pipe(
      map((permissions) => permissions.accessManageOrders),
    );

    this.canIgnoreAllErrorsOnOrder$ = this.permissionsService.CanDoAction(OrderAction.IgnoreAllErrorsOnOrder);
    this.canCreateInvoicesFromOrder$ = this.permissionsService.CanDoAction(OrderAction.CreateInvoiceFromOrder);
    this.canDuplicateOrder$ = this.permissionsService.CanDoAction(OrderAction.DuplicateOrder);
    this.canSubscribeToUpdates$ = this.permissionsService.CanDoAction(OrderAction.SubscribeToUpdates);
    this.canAccessOnlySalespersonAutomations$ = this.permissionsService.CanDoAction(
      OrderAction.AccessOnlySalespersonAutomations,
    );
    this.canRedirectToCompanyProfileAfterSubmitting$ = this.permissionsService.CanDoAction(
      OrderAction.RedirectToCompanyProfileAfterSubmitting,
    );
    this.canCollectPaymentFromCustomer$ = this.permissionsService.CanDoAction(OrderAction.CollectPaymentFromCustomer);

    this.route.queryParams.pipe(take(1)).subscribe((params) => {
      if (params['utm_campaign'] && params['utm_medium']) {
        this.productAnalyticService.trackEvent(
          'order-fulfillment-cta-tracking',
          params['utm_medium'],
          params['utm_campaign'],
        );
        this.router.navigate([], {
          queryParams: {
            utm_medium: null,
            utm_campaign: null,
          },
          queryParamsHandling: 'merge',
        });
      }
    });
  }

  validateForm(): Observable<boolean> {
    return this.viewOrEditComponent.validateForm();
  }

  printPage(): void {
    this.viewOrEditComponent.preparePrintPage().subscribe(() => {
      window.print();
    });
  }

  collectPayment(): void {
    this.order$
      .pipe(
        take(1),
        switchMap((order) => {
          if (!(order?.lineItems?.length > 0)) {
            return throwError(() => new Error(errors.orderHasNoLineItem));
          }
          return this.dialog
            .open(CollectPaymentDialogComponent, {
              width: '450px',
              injector: Injector.create({
                providers: [
                  { provide: RetailPaymentService, useValue: this.retailPaymentService },
                  { provide: OrderStoreService, useValue: this.orderService },
                  { provide: OrderPermissionsService, useValue: this.permissionsService },
                  { provide: ProcessOrderDialogService, useValue: this.processOrderDialogService },
                  { provide: OrdersEditOrderFormValidationService, useValue: this.editOrderFormValidationService },
                ],
                parent: this.injector,
              }),
            })
            .afterClosed();
        }),
      )
      .subscribe({
        next: (result) => {
          this.processingOrderAction = false;
          this.cdr.detectChanges();
          if (result == CollectPaymentDialogResult.Submitted) {
            this.submitForCustomerApproval(true);
          } else if (result === CollectPaymentDialogResult.Charged || result === CollectPaymentDialogResult.Processed) {
            this.tagOrderAsProductManualActivation();
          }
        },
        error: (err: Error) => {
          if (err?.message === errors.orderHasNoLineItem) {
            this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.ERROR_AT_LEAST_ONE_LINE_ITEM');
          } else {
            this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ERRORS.GENERIC_ERROR');
          }
          this.processingOrderAction = false;
          this.cdr.detectChanges();
        },
      });
  }

  processOrderImmediately(): void {
    this.order$
      .pipe(
        take(1),
        switchMap((order) => {
          if (!(order?.lineItems?.length > 0)) {
            return throwError(() => new Error(errors.orderHasNoLineItem));
          }
          return this.processOrderDialogService.openProcessOrderDialog(order);
        }),
      )
      .subscribe({
        next: () => {
          this.processingOrderAction = false;
          this.cdr.detectChanges();
          this.tagOrderAsProductManualActivation();
        },
        error: (err: Error) => {
          this.processingOrderAction = false;
          let message = this.translateService.instant('LIB_ORDERS.SALES_ORDERS.ERRORS.FAILED_TO_PROCESS_ORDER');

          if (err?.message === errors.orderHasNoLineItem) {
            message = this.translateService.instant('LIB_ORDERS.COMMON.ORDERS.ERROR_AT_LEAST_ONE_LINE_ITEM');
          }
          this.snackbarService.openErrorSnack(message);
        },
      });
  }

  ignoreAllErrors(): void {
    this.processingOrderAction = true;
    this.order$
      .pipe(
        take(1),
        switchMap((order) =>
          this.salesOrdersApiService.ignoreAllProductActivationErrors(order.orderId, order.businessId),
        ),
      )
      .subscribe({
        next: () => {
          this.processingOrderAction = false;
          this.refreshOrderDetails();
        },
        error: () => {
          this.processingOrderAction = false;
          const message = this.translateService.instant(
            'LIB_ORDERS.SALES_ORDERS.ERRORS.FAILED_TO_IGNORE_ALL_PRODUCT_ACTIVATION_ERRORS',
          );
          this.snackbarService.openErrorSnack(message);
        },
      });
  }

  createInvoice(): void {
    this.processingOrderAction = true;
    this.order$
      .pipe(
        take(1),
        switchMap((order) => {
          return this.salesOrdersApiService.createInvoice(order.orderId, order.businessId);
        }),
        withLatestFrom(this.pageOrderConfig.invoicesPageUrl$),
      )
      .subscribe({
        next: ([invoiceId, invoicesPageUrl]) => {
          this.processingOrderAction = false;
          this.router.navigate([invoicesPageUrl, invoiceId, 'edit']);
        },
        error: () => {
          this.processingOrderAction = false;
          const message = this.translateService.instant('LIB_ORDERS.SALES_ORDERS.ERRORS.FAILED_TO_CREATE_INVOICE');
          this.snackbarService.openErrorSnack(message);
        },
      });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  submitDraft(businessId: string, userId = '', expiry?: Date | null): void {
    this.processingOrderAction = true;
    combineLatest([this.order$, this.validateForm(), this.lineItemValidationService.areBillingTermsValidOrOpenModal$()])
      .pipe(
        switchMap(([order, isFormValid, areBillingTermsValid]) => {
          if (!(order?.lineItems?.length > 0)) {
            return throwError(() => new Error(errors.orderHasNoLineItem));
          }
          if (!isFormValid || !areBillingTermsValid) {
            this.processingOrderAction = false;
            return EMPTY;
          }
          return of(undefined);
        }),
        switchMap(() => {
          return this.lineItemValidationService.canOrderBeWholesaleBilledOrOpenModal$();
        }),
        take(1),
        switchMap((valid) => {
          if (!valid) {
            this.processingOrderAction = false;
            return EMPTY;
          }
          // grab order form contents from view child components:
          return from(
            this.orderService.submitDraft({
              customFormData: this.viewOrEditComponent.editOrderComponent.getCustomFormData(),
              extraFieldsData: this.viewOrEditComponent.editOrderComponent.getExtraFieldsData(),
              commonFormData: this.viewOrEditComponent.editOrderComponent.getCommonFormData(),
              userId,
              expiry,
            }),
          );
        }),
      )
      .subscribe({
        next: () => {
          this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_UPDATED');
          this.refreshOrderDetails();
          this.goToOrderViewPage(businessId);
        },
        error: (err) => {
          if (err?.message === errors.orderHasNoLineItem) {
            this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.ERROR_AT_LEAST_ONE_LINE_ITEM');
          } else {
            this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ERRORS.GENERIC_ERROR');
          }
          this.processingOrderAction = false;
          this.cdr.detectChanges();
        },
      });
  }

  submitForCustomerApproval(tagAsProductManualActivation = false): void {
    this.processingOrderAction = true;
    // PROP-1034 - don't try and modify this. SubmitForCustomerDialog is also used in create and does somewhat complex stuff
    const resp$ = combineLatest([
      this.order$,
      this.canCollectPaymentFromCustomer$,
      this.lineItemValidationService.areBillingTermsValidOrOpenModal$(),
      this.lineItemValidationService.hasAlreadyActiveProducts$,
      this.lineItemValidationService.canOrderBeWholesaleBilledOrOpenModal$(),
    ]).pipe(
      take(1),
      switchMap(
        ([
          o,
          canCollectPaymentFromCustomer,
          areBillingTermsValid,
          hasAlreadyActiveProducts,
          canOrderBeWholesaleBilled,
        ]) => {
          if (!(o?.lineItems?.length > 0)) {
            return throwError(() => new Error(errors.orderHasNoLineItem));
          }
          if (!areBillingTermsValid) {
            return of(undefined);
          }
          if (!canOrderBeWholesaleBilled) {
            return of(undefined);
          }
          const earliestScheduledDate = getEarliestScheduledLineItemDate(o?.lineItems);
          const dialogRef = this.dialog.open(SubmitCustomerDialogComponent, {
            data: {
              partnerId: o.partnerId,
              accountGroupId: o.businessId,
              currentExpiry: getOfferExpiry(o),
              canCollectPaymentFromCustomer: canCollectPaymentFromCustomer,
              earliestScheduledLineItemDate: earliestScheduledDate,
              hasAlreadyActiveProducts: hasAlreadyActiveProducts,
              openOrderPreviewDialog: (shouldCollectPayment: boolean) => {
                this.openOrderPreviewDialog(shouldCollectPayment);
              },
            },
            width: '600px',
            maxWidth: '100vw',
            autoFocus: false,
            injector: Injector.create({
              providers: [{ provide: OrderStoreService, useValue: this.orderService }],
              parent: this.injector,
            }),
          });
          return dialogRef.afterClosed().pipe(take(1));
        },
      ),
    );

    combineLatest([resp$, this.order$])
      .pipe(
        take(1),
        tap(([resp, order]) => {
          const eventName = resp?.shouldCollectPayment
            ? 'submitted-to-customer-for-approval-with-payment'
            : 'submitted-to-customer-for-approval-without-payment';
          this.productAnalyticService.trackEvent(eventName, 'unified-orders', 'click', 0, {
            orderStatus: order.status,
          });
        }),
        switchMap(([resp, order]) => {
          if (resp?.user) {
            let orderIsSmbPayable = false;
            if (resp.shouldCollectPayment) {
              orderIsSmbPayable = true;
            }
            return this.salesOrdersApiService.sendExistingOrderToCustomerForApproval(
              order.orderId,
              order.businessId,
              resp.user.userId,
              resp.expiry,
              orderIsSmbPayable,
            );
          }
          return of(false);
        }),
      )
      .subscribe({
        next: (submittedExisting) => {
          this.processingOrderAction = false;
          this.cdr.detectChanges();
          if (submittedExisting) {
            this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_UPDATED');
            if (tagAsProductManualActivation) {
              this.tagOrderAsProductManualActivation();
            } else {
              this.refreshOrderDetails();
            }
          }
        },
        error: (err) => {
          if (err?.message === errors.orderHasNoLineItem) {
            this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.ERROR_AT_LEAST_ONE_LINE_ITEM');
          } else {
            this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ERRORS.GENERIC_ERROR');
          }
          this.processingOrderAction = false;
          this.cdr.detectChanges();
        },
      });
  }

  openOrderPreviewDialog(shouldCollectPayment: boolean) {
    combineLatest([
      this.order$,
      this.orderService.orderConfig$,
      this.orderService.business$,
      this.orderService.businessUsers$,
    ])
      .pipe(
        take(1),
        map(([order, orderConfig, business, users]) => {
          this.dialog
            .open(PreviewPublicOrderDialogComponent, {
              data: {
                shouldCollectPayment: shouldCollectPayment,
                order: order,
                orderConfig: orderConfig,
                business: business,
                users: users,
              },
              width: '700px',
              maxWidth: '100vw',
              autoFocus: false,
            })
            .afterClosed();
        }),
      )
      .subscribe();
  }

  startAutomation(businessId: string, automationId: string): void {
    this.processingOrderAction = true;
    this.order$
      .pipe(
        take(1),
        switchMap((ord) =>
          this.automataOperationsService.startAutomation(
            automationId,
            [],
            [],
            [{ entityId: ord.orderId }],
            ord.partnerId,
          ),
        ),
      )
      .subscribe({
        next: () => {
          this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_SENT');
          this.goToOrderViewPage(businessId);
        },
        error: (error) => {
          console.error(error);
          this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERRORS.ORDER_NOT_SUBMITTED');
          this.processingOrderAction = false;
          this.cdr.detectChanges();
        },
      });
  }

  onCancel(): void {
    this.processingOrderAction = true;
    this.dialog
      .open(CancelOrderDialogComponent, {
        width: ConfirmationModalWidth,
        maxWidth: ConfirmationModalMaxWidth,
        autoFocus: false,
        data: {
          isAdmin: false,
        },
      })
      .afterClosed()
      .pipe(
        withLatestFrom(this.order$),
        switchMap(([cancellationReason, order]) => {
          if (!cancellationReason) {
            return of(null);
          }

          return this.salesOrdersApiService.requestCancellation(order.orderId, order.businessId, cancellationReason);
        }),
      )
      .subscribe({
        next: (result) => {
          this.processingOrderAction = false;
          if (!result) {
            return;
          }

          this.refreshOrderDetails();
          this.snackbarService.openSuccessSnack(
            'LIB_ORDERS.COMMON.ORDERS.DIALOGS.REQUEST_TO_CANCEL_ORDER.SUCCESS_MESSAGE',
          );
        },
        error: () => {
          this.processingOrderAction = false;
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.DIALOGS.REQUEST_TO_CANCEL_ORDER.ERROR_MESSAGE');
        },
      });
  }

  async handleCancellationRequest(event: RequestCancellationEvent): Promise<void> {
    try {
      await this.orderService.requestCancellation(event.notes, {
        orderId: event.orderId,
        businessId: event.businessId,
      });
      // Handle successful cancellation request
      this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.CANCEL_ORDER_DIALOG.CANCELLATION_REQUESTED');
    } catch (error) {
      // Handle error in cancellation request
      this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.CANCEL_ORDER_DIALOG.ERROR_REQUESTING');
    } finally {
      this.dialog.closeAll();
      this.refreshOrderDetails();
    }
  }

  async convertToDraft(): Promise<void> {
    this.processingOrderAction = true;
    try {
      await this.orderService.convertToDraft();
      this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.CONVERTED_TO_DRAFT');
      this.refreshOrderDetails();
    } catch (err) {
      this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERROR_CONVERTING_TO_DRAFT');
    }
    this.processingOrderAction = false;
    this.cdr.detectChanges();
  }

  async subscribeUserToOrder(): Promise<void> {
    this.changingSubscription = true;
    try {
      await this.orderService.subscribeUserToOrderAtLocation(SubscriptionLocation.SUBSCRIPTION_LOCATION_SALES_CENTER);
      this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUBSCRIBED');
    } catch (e) {
      this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERROR_CHANGING_SUBSCRIPTION');
    } finally {
      this.changingSubscription = false;
    }
  }

  async unsubscribeUserFromOrder(): Promise<void> {
    this.changingSubscription = true;
    try {
      await this.orderService.unSubscribeUserToOrderAtLocation(SubscriptionLocation.SUBSCRIPTION_LOCATION_SALES_CENTER);
      this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.UNSUBSCRIBED');
    } catch (e) {
      this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERROR_CHANGING_SUBSCRIPTION');
    } finally {
      this.changingSubscription = false;
    }
  }

  goToOrderViewPage(businessId: string): void {
    this.canRedirectToCompanyProfileAfterSubmitting$
      .pipe(
        take(1),
        switchMap((redirectToCompanyProfile) => {
          if (redirectToCompanyProfile) {
            return this.getCompanyLink(businessId);
          } else {
            return of(null);
          }
        }),
      )
      .subscribe((companyLink) => {
        if (companyLink) {
          // Redirect to company profile
          this.router.navigateByUrl(companyLink);
        } else {
          // Reload the current route.
          // To prevent the loading state on the submit button from spinning indefinitely, we must navigate to the
          // root '/' route before reloading the current route. This ensures that Angular triggers the necessary
          // lifecycle hooks to refresh the component.
          const currentUrl = this.router.url;
          this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
            this.router.navigate([currentUrl]);
          });
        }
      });
  }

  // Get the CRM company link
  getCompanyLink(businessId: string): Observable<string> {
    const url = this.orderBusinessConfig?.getBusinessUrl?.(businessId) ?? `/info/${businessId}`;
    if (url instanceof Promise) {
      return from(url);
    }
    return of(url);
  }

  refreshOrderDetails(): void {
    this.orderService.reloadOrder();
  }

  runAutomation(): void {
    combineLatest([this.order$, this.canAccessOnlySalespersonAutomations$])
      .pipe(take(1))
      .subscribe(([order, canAccessOnlySalespersonAutomations]) => {
        this.dialog.open(StartManualAutomationDialogComponent, {
          data: {
            namespace: order.partnerId,
            entities: new Map<EntityType, string[]>([[EntityType.ENTITY_TYPE_ORDER, [order.orderId]]]),
            context: Context.AUTOMATION_CONTEXT_PARTNER,
            options: {
              onlySelectDontStart: false,
              hideSettingsLink: true,
              onlySalespersonAutomations: canAccessOnlySalespersonAutomations,
            },
          },
        });
      });
  }

  updateSalesperson(order: Order) {
    const dialogRef = this.dialog.open(SalespersonSelectorDialogComponent, {
      data: {
        partnerId: order.partnerId,
        previouslySelectedUser: order.salespersonId,
      },
      width: '600px',
    });
    dialogRef
      .afterClosed()
      .pipe(
        switchMap((selectedSalesperson) => {
          if (selectedSalesperson) {
            return this.salesOrdersApiService.updateSalesperson(
              order.orderId,
              order.businessId,
              selectedSalesperson?.salesperson?.userId,
            );
          }
          return of(null);
        }),
      )
      .subscribe(
        (o: Order) => {
          if (o) {
            this.refreshOrderDetails();
            this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS_UPDATING_SALESPERSON');
          }
        },
        (err) => {
          console.error(err);
          this.snackbarService.openErrorSnack('LIB_ORDERS.SALES_ORDERS.ERROR_UPDATING_SALESPERSON');
        },
      );
  }

  duplicateOrder(): void {
    this.order$
      .pipe(
        take(1),
        switchMap((order) => this.salesOrdersApiService.duplicate(order.orderId, order.businessId)),
        withLatestFrom(this.business$),
        map(([orderId, business]) => {
          const newOrderUrl = this.pageOrderConfig.getOrderUrl(orderId, business.accountGroupId);
          window.open(newOrderUrl, '_blank');
        }),
      )
      .subscribe();
  }

  tagOrderAsProductManualActivation(): void {
    this.order$
      .pipe(
        take(1),
        switchMap((order) => {
          const tags = order.tags || [];
          const productManualActivationTags = this.translateService.instant(
            'LIB_ORDERS.COMMON.ORDER_DETAILS.PRODUCT_MANUAL_ACTIVATION_TAG',
          );
          if (tags.includes(productManualActivationTags)) {
            return of(null);
          }
          tags.push(productManualActivationTags);
          return this.salesOrdersApiService.updateTags(order.orderId, order.businessId, tags);
        }),
      )
      .subscribe({
        next: () => {
          this.refreshOrderDetails();
        },
        error: () => {
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDER_DETAILS.TAGS_UPDATE_ERROR');
          this.refreshOrderDetails();
        },
      });
  }
}
