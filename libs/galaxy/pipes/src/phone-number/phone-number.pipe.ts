import { Pipe, PipeTransform } from '@angular/core';
import { parsePhoneNumberWithError, CountryCode } from 'libphonenumber-js';

const NON_NUMERIC_PATTERN = /[^\d\s()+-.]]/;
const DIGITS_ONLY_PATTERN = /\D/g;
const SHORT_CODE_MAX_LENGTH = 8;

export function formatPhoneNumber(
  value: string | null | undefined,
  showCountryCode = true,
  country: CountryCode = 'US',
): string {
  if (!value) return value || '';

  if (NON_NUMERIC_PATTERN.test(value)) {
    return value;
  }

  const digitsOnly = value.replace(DIGITS_ONLY_PATTERN, '');
  if (digitsOnly.length <= SHORT_CODE_MAX_LENGTH) {
    return value;
  }
  try {
    const hasCountryCode = value.trim().startsWith('+');
    const phoneNumber = hasCountryCode ? parsePhoneNumberWithError(value) : parsePhoneNumberWithError(value, country);

    const isNorthAmericanNumber = phoneNumber.country === 'US' || phoneNumber.country === 'CA';
    if (showCountryCode) {
      if (isNorthAmericanNumber) {
        return `+1 ${phoneNumber.formatNational()}`;
      } else {
        return phoneNumber.formatInternational();
      }
    } else {
      return phoneNumber.formatNational();
    }
  } catch (error) {
    return value;
  }
}

/**
 * Formats a phone number string into a standardized format.
 * Uses the libphonenumber-js library to parse and format the phone number.
 * It's specialized for North American numbers, but it can handle other formats if the country code is present.
 * Short codes and invalid formats will be returned unmodified.
 *
 * @param value - The phone number string to format
 * @param showCountryCode - Whether to include country code in the output (defaults to true)
 * @param country - The country code to use when parsing numbers without country code (defaults to 'US')
 * @returns Formatted phone number string or the original value if formatting fails
 */
@Pipe({
  name: 'glxyPhoneNumber',
})
export class GalaxyPhoneNumberPipe implements PipeTransform {
  transform(value: string | null | undefined, showCountryCode = true, country: CountryCode = 'US'): string {
    return formatPhoneNumber(value, showCountryCode, country);
  }
}
