import { formatPhoneNumber, GalaxyPhoneNumberPipe } from './phone-number.pipe';

describe('PhoneNumberPipe', () => {
  let pipe: GalaxyPhoneNumberPipe;

  beforeEach(() => {
    pipe = new GalaxyPhoneNumberPipe();
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should format US phone numbers correctly with country code by default', () => {
    expect(pipe.transform('**********')).toBe('+****************');
    expect(pipe.transform('**********')).toBe('+****************');
    expect(pipe.transform('8005551234')).toBe('+****************');
    expect(pipe.transform('1**********')).toBe('+****************');
  });

  it('should format US phone numbers correctly without country code when specified', () => {
    expect(pipe.transform('**********', false)).toBe('(*************');
    expect(pipe.transform('**********', false)).toBe('(*************');
    expect(pipe.transform('8005551234', false)).toBe('(*************');
    expect(pipe.transform('+****************', false)).toBe('(*************');
  });

  it('should handle phone numbers with existing formatting', () => {
    expect(pipe.transform('(*************')).toBe('+****************');
    expect(pipe.transform('************')).toBe('+****************');
    expect(pipe.transform('************')).toBe('+****************');
  });

  it('should return the original value for inputs with non-numeric characters', () => {
    expect(pipe.transform('abc123')).toBe('abc123');
    expect(pipe.transform('phone: 555-1234')).toBe('phone: 555-1234');
  });

  it('should handle phone numbers with country code in the input', () => {
    expect(pipe.transform('+1**********')).toBe('+****************');
    expect(pipe.transform('****** 900 9374')).toBe('+****************');
  });

  it('should format numbers according to specified country', () => {
    expect(pipe.transform('**********', true, 'GB')).toBe('+44 20 7123 4567');
    expect(pipe.transform('*********', true, 'AU')).toBe('+61 2 6123 4567');
    expect(pipe.transform('**********', true, 'CA')).toBe('+****************');

    // UK number
    expect(pipe.transform('+44**********', true, 'US')).toBe('+44 20 7123 4567');
    // Australian number
    expect(pipe.transform('+***********', true, 'CA')).toBe('+61 2 6123 4567');
  });

  it('should use US as default country when no country is specified', () => {
    expect(pipe.transform('**********')).toBe('+****************');
    expect(pipe.transform('**********')).toBe('+****************');
    expect(pipe.transform('8005551234')).toBe('+****************');

    // UK number
    expect(pipe.transform('+44**********')).toBe('+44 20 7123 4567');

    // Australian number
    expect(pipe.transform('+***********')).toBe('+61 2 6123 4567');

    // Invalid international number
    expect(pipe.transform('+1*********')).toBe('+1 *********');

    // Short international number
    expect(pipe.transform('+44')).toBe('+44');
  });

  it('should handle short codes correctly', () => {
    expect(pipe.transform('12345678')).toBe('12345678');
    expect(pipe.transform('1234567')).toBe('1234567');
    expect(pipe.transform('**********')).toBe('+****************');
  });

  it('should handle international numbers with explicit formatting', () => {
    // Numbers with international formatting
    expect(pipe.transform('+44 20 7123 4567')).toBe('+44 20 7123 4567');
    expect(pipe.transform('+61 2 6123 4567')).toBe('+61 2 6123 4567');
  });
});

describe('formatPhoneNumber', () => {
  it('should format valid phone numbers', () => {
    expect(formatPhoneNumber('**********')).toBe('+****************');
    expect(formatPhoneNumber('**********', false)).toBe('(*************');
  });

  it('should format numbers according to specified country', () => {
    expect(formatPhoneNumber('**********', true, 'GB')).toBe('+44 20 7123 4567');
    expect(formatPhoneNumber('*********', true, 'AU')).toBe('+61 2 6123 4567');

    // UK number
    expect(formatPhoneNumber('+44**********', true, 'US')).toBe('+44 20 7123 4567');
    // Australian number
    expect(formatPhoneNumber('+***********', true, 'CA')).toBe('+61 2 6123 4567');
  });

  it('should use US as default country when no country is specified', () => {
    expect(formatPhoneNumber('**********')).toBe('+****************');
    expect(formatPhoneNumber('**********')).toBe('+****************');
    expect(formatPhoneNumber('8005551234')).toBe('+****************');

    // UK number
    expect(formatPhoneNumber('+44**********')).toBe('+44 20 7123 4567');
    // Australian number
    expect(formatPhoneNumber('+***********')).toBe('+61 2 6123 4567');
  });

  it('should handle short codes correctly with new length limit', () => {
    expect(formatPhoneNumber('12345678')).toBe('12345678');
    expect(formatPhoneNumber('1234567')).toBe('1234567');
    expect(formatPhoneNumber('**********')).toBe('+****************');
  });

  it('should return original value for invalid inputs', () => {
    expect(formatPhoneNumber('invalid')).toBe('invalid');
  });
});
