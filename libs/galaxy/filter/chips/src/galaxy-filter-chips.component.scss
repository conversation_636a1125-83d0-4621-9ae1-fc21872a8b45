@use '../../../styles/design-tokens' as dt;
@use '@angular/material' as chips;
@include chips.chips-density(1);

.chip-filters {
  overflow: hidden;
}

.table-filters-container {
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: dt.$media--tablet-minimum) {
  .chip-filters {
    margin-bottom: dt.$spacing-2;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .table-filters-container {
    white-space: nowrap;
  }

  .filter-button-group {
    margin-bottom: dt.$spacing-2;
  }
}

.filter-button-group {
  align-content: center;
}

.add-filter-button,
.clear-filters-button {
  font-family: dt.$default-font-family;
  font-size: dt.$font-preset-4-size;
  font-weight: 500;
  -webkit-font-smoothing: antialiased;
  line-height: 1;
}

.add-filter-button {
  background-color: dt.$primary-color;
  color: dt.$contrast-text-color;
  border: 0;
  border-radius: dt.$spacing-3;
  padding: dt.$spacing-1 dt.$spacing-2;
  margin-right: dt.$spacing-2;
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: dt.$spacing-1;
  cursor: pointer;
}

.clear-filters-button {
  background: none;
  border: none;
  margin: 0;
  padding: 0;
  cursor: pointer;
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  color: dt.$primary-color;
}

mat-chip.mat-mdc-standard-chip:last-child {
  // don't need this override if we can conditionally hide the chip-set and put margin on that
  margin-right: dt.$spacing-2 !important;
}

.chip-wrapper {
  display: flex;
  align-items: center;
}

.chip-symbol {
  margin: 0 dt.$spacing-1;

  &:last-child {
    margin-right: 0;
  }
}

.chip-set-container {
  width: 100%;
}
