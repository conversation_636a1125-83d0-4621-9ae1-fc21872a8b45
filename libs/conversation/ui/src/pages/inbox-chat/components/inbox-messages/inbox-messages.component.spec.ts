import { byText, createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslateModule, TranslateService, TranslateStore } from '@ngx-translate/core';
import {
  Conversation,
  ConversationApiService,
  ConversationChannel,
  Event,
  EventType,
  MessageStatus,
  MessageType,
  Participant,
  ParticipantType,
} from '@vendasta/conversation';
import { of } from 'rxjs';
import { ConversationMessage } from '../../../../../../core/src/lib/interface/conversation.interface';
import { ConversationService } from '../../../../../../core/src/lib/state/conversation.service';
import { ConversationUIModule } from '../../../../conversation-ui.module';
import { InboxMessagesComponent } from './inbox-messages.component';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { HttpClient } from '@angular/common/http';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { LexiconModule } from '@galaxy/lexicon';
import { MatDialog } from '@angular/material/dialog';
import { Message } from '@vendasta/conversation/lib/_internal/objects';
import { EmailPreviewDialogComponent } from './preview/email-preview.component';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  PARTNER_ID_TOKEN,
  USER_ID_TOKEN,
} from '../../../../../../core/src/lib/tokens';
import { EmptyMessagesPipe, ReplaceNewLinePipe } from '../../../../../../pipes/src/message.pipes';
import { conversationServiceMock } from '../../../../../../core/src/lib//mocks';
import { DEFAULT_OPENAI_BOT_NAME } from '../../../../../../core/src/lib/inbox.constants';
import { MatIconTestingModule } from '@angular/material/icon/testing';

const hostAppInterfaceMock = {
  getConversationTitleInfo: jest.fn(),
  getRecipient: jest.fn(),
  getSender: jest.fn(),
  isSenderFromOrganization: jest.fn(),
  isRecipientInternalInfoDeleted: jest.fn(),
  buildSendMessageParams: jest.fn(),
  redirectToInternalConversation: jest.fn(),
  createPaymentLink: jest.fn(),
  shortenLink: jest.fn(),
  calculatePaymentLinkTax: jest.fn(),
  getKabobAvailableActions: jest.fn(),
};

describe('InboxMessagesComponent', () => {
  let spectator: Spectator<InboxMessagesComponent>;

  const createComponent = createComponentFactory({
    component: InboxMessagesComponent,
    imports: [
      ConversationUIModule,
      TranslateModule,
      TranslateTestingModule.withTranslations({}),
      HttpClientTestingModule,
      LexiconModule.forRoot(),
      EmailPreviewDialogComponent,
      MatIconTestingModule,
    ],
    providers: [
      {
        provide: TranslateService,
        useValue: {
          get: (key: string) => of(key),
          onTranslationChange: of(),
          onLangChange: of(),
          onDefaultLangChange: of(),
        },
      },
      {
        provide: ProductAnalyticsService,
        useValue: {
          trackEvent: jest.fn(),
        },
      },
      {
        provide: ConversationService,
        useValue: conversationServiceMock,
      },
      {
        provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN,
        useValue: hostAppInterfaceMock,
      },
      { provide: USER_ID_TOKEN, useValue: of('U-123') },
      { provide: PARTNER_ID_TOKEN, useValue: of('ABC') },
      { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-123') },
      { provide: TranslateStore },
      {
        provide: TranslateService,
      },
    ],
    mocks: [HttpClient, MatDialog, ConversationApiService, EmptyMessagesPipe],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  describe('handleOpenAIReplyIndicator()', () => {
    const aiAssistantParticipant = new Participant({
      internalParticipantId: '123',
      name: DEFAULT_OPENAI_BOT_NAME,
      participantType: ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT,
      channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
    });
    const iamUserParticipant = new Participant({
      internalParticipantId: '456',
      name: 'Paul',
      participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
      channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
    });

    it('should show that AI is typing if latestMessage is from IAM user', () => {
      const messages = [
        {
          id: '1',
          sender: aiAssistantParticipant,
          body: 'Hello',
          created: new Date('2021-01-01T00:00:00.000Z'),
          channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        } as ConversationMessage,
        {
          id: '2',
          sender: iamUserParticipant,
          body: 'Hi',
          created: new Date('2021-01-01T00:00:00.000Z'),
          channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        } as ConversationMessage,
      ].reverse();

      const conversation = {
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      } as Conversation;

      spectator.setInput({
        conversation,
        messages,
      });

      expect(spectator.query(byText('Typing ...'))).toExist();
    });

    it('should show that AI assistant has finished typing if latestMessage is from AI assistant', () => {
      const messages = [
        {
          id: '2',
          sender: aiAssistantParticipant,
          body: 'Hello',
          created: new Date('2021-01-01T00:00:00.000Z'),
          channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        } as ConversationMessage,
        {
          id: '1',
          sender: iamUserParticipant,
          body: 'Hi',
          created: new Date('2021-01-01T00:00:00.000Z'),
          channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        } as ConversationMessage,
      ].reverse();

      const conversation = {
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      } as Conversation;

      spectator.setInput({
        conversation,
        messages,
      });

      expect(spectator.query(byText('INBOX.CHAT.TYPING'))).not.toExist();
    });

    it('should not display that AI is typing if channel is not AI_ASSISTANT', () => {
      const conversation = {
        channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
      } as Conversation;

      spectator.setInput({
        conversation,
        messages: [],
      });

      expect(spectator.query(byText('INBOX.CHAT.TYPING'))).not.toExist();
    });
  });

  describe('handleViewOriginalMessage', () => {
    it('should show 2 view original message buttons when one received email message is provided', () => {
      const contactParticipant = new Participant({
        participantId: '123',
        internalParticipantId: 'CONTACT-123',
        name: 'Paul',
        participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
        created: new Date('2021-01-01T00:00:00.000Z'),
      });
      const iamUserParticipant = new Participant({
        participantId: '456',
        internalParticipantId: '456',
        name: 'Paul',
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
      });

      const messages = [
        {
          id: '1',
          channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
          body: 'Hi',
          sender: contactParticipant,
          type: MessageType.MESSAGE_TYPE_MESSAGE,
          created: new Date('2021-01-01T00:00:00.000Z'),
        } as ConversationMessage,
        {
          id: '2',
          channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
          body: 'Hi',
          sender: iamUserParticipant,
          type: MessageType.MESSAGE_TYPE_MESSAGE,
          sendStatus: {
            status: MessageStatus.MESSAGE_STATUS_SENT,
          },
          created: new Date('2021-01-01T00:00:00.000Z'),
        } as ConversationMessage,
        {
          id: '3',
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          body: 'Hi',
          sender: contactParticipant,
          type: MessageType.MESSAGE_TYPE_MESSAGE,
          created: new Date('2021-01-01T00:00:00.000Z'),
        } as ConversationMessage,
        {
          id: '5',
          channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
          body: 'Hi',
          sender: contactParticipant,
          type: MessageType.MESSAGE_TYPE_MESSAGE,
          created: new Date('2021-01-01T00:00:00.000Z'),
        } as ConversationMessage,
      ];

      const conversation = {
        channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
      } as Conversation;

      spectator.setInput({
        conversation,
        messages,
      });

      const items = spectator.queryAll('.view-original-message-button');
      expect(items.length).toEqual(2);
    });
    it('should open modal', async () => {
      const contactParticipant = new Participant({
        participantId: '123',
        internalParticipantId: 'CONTACT-123',
        name: 'Paul',
        participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
        created: new Date('2021-01-01T00:00:00.000Z'),
      });

      const messages = [
        {
          id: '1',
          channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
          body: 'Hi',
          sender: contactParticipant,
          type: MessageType.MESSAGE_TYPE_MESSAGE,
          created: new Date('2021-01-01T00:00:00.000Z'),
        } as ConversationMessage,
      ];

      const conversation = {
        channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
      } as Conversation;

      spectator.setInput({
        conversation,
        messages,
      });

      const dialog = spectator.inject(MatDialog);
      const conversationApiService = spectator.inject(ConversationApiService);

      jest.spyOn(conversationApiService, 'getMessage').mockReturnValue(
        of({
          body: 'Hi',
          channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
          type: MessageType.MESSAGE_TYPE_MESSAGE,
          created: new Date('2021-01-01T00:00:00.000Z'),
          originalContent: 'Hi',
        } as Message),
      );

      const actionButton = spectator.query('.view-original-message-button') || undefined;
      expect(actionButton).toExist();
      spectator.click(actionButton);
      expect(dialog.open).toHaveBeenCalled();
    });
  });
  describe('events in UI', () => {
    it('should show inbox-event for sms request event', () => {
      const events: Event[] = [
        {
          message: 'SMS review request',
          happenedAt: new Date('2023-06-19T09:30:00Z'),
          conversationId: '123',
          eventId: '3',
          initiatorId: '',
          type: EventType.EVENT_TYPE_REVIEW_REQUEST,
          labelKey: '',
          created: new Date('2023-06-19T09:30:00Z'),
          updated: new Date('2023-06-19T09:30:00Z'),
          deleted: new Date('2023-06-19T09:30:00Z'),
        } as Event,
      ];

      spectator.setInput({
        events,
      });

      expect(spectator.query(byText('SMS review request'))).toExist();
      expect(spectator.query('glxy-chat-message-group')).toExist();
      expect(spectator.query('inbox-event')).toExist();
      expect(spectator.query('glxy-avatar')).toExist();
    });
    it('should not show inbox-event for sms message', () => {
      const contactParticipant = new Participant({
        participantId: '123',
        internalParticipantId: 'CONTACT-123',
        name: 'Paul',
        participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
        created: new Date('2021-01-01T00:00:00.000Z'),
      });
      const messages = [
        {
          id: '1',
          sender: contactParticipant,
          body: 'Hi',
          created: new Date('2021-01-01T00:00:00.000Z'),
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        } as ConversationMessage,
        {
          id: '2',
          sender: contactParticipant,
          body: 'Hello',
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          created: new Date('2021-01-01T00:00:00.000Z'),
        } as ConversationMessage,
      ];

      const conversation = {
        channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
      } as Conversation;

      spectator.setInput({
        conversation,
        messages,
      });

      expect(spectator.query('inbox-event')).not.toExist();
    });
    it('should show inbox event, and glxy-chat-message-group for form event', () => {
      const events: Event[] = [
        {
          happenedAt: new Date('2023-06-19T09:30:00Z'),
          conversationId: '123',
          eventId: '3',
          message: 'form',
          labelKey: 'INBOX.EVENTS.LEAD_CAPTURED',
          initiatorId: 'form-init',
          type: EventType.EVENT_TYPE_FORM_SUBMISSION,
          created: new Date('2023-06-19T09:30:00Z'),
          updated: new Date('2023-06-19T09:30:00Z'),
          deleted: new Date('2023-06-19T09:30:00Z'),
        } as Event,
      ];

      spectator.setInput({
        events,
      });

      expect(spectator.query(byText('SMS review request'))).not.toExist();
      expect(spectator.query('inbox-event')).toExist();
      expect(spectator.query('glxy-chat-message-group')).toExist();
      expect(spectator.query('glxy-avatar')).toExist();
    });
    it('should show glxy chat event, but not glxy-chat-message-group for unsubscribed event', () => {
      const events: Event[] = [
        {
          happenedAt: new Date('2023-06-19T09:30:00Z'),
          conversationId: '123',
          eventId: '3',
          message: 'Unsubscribed event',
          labelKey: 'INBOX.EVENTS.UNSUBSCRIBED',
          initiatorId: 'unsubscribed',
          created: new Date('2023-06-19T09:30:00Z'),
          updated: new Date('2023-06-19T09:30:00Z'),
          deleted: new Date('2023-06-19T09:30:00Z'),
        } as Event,
      ];

      spectator.setInput({
        events,
      });

      expect(spectator.query('glxy-chat-event')).toExist();
      expect(spectator.query('glxy-chat-message-group')).not.toExist();
      // these are the only events that are not expected to hav an avatar
      expect(spectator.query('glxy-avatar')).not.toExist();
    });
  });
});

describe('ReplaceNewLinePipe', () => {
  let pipe: ReplaceNewLinePipe;

  beforeEach(() => {
    pipe = new ReplaceNewLinePipe();
  });

  it('should replace newline characters with space space newline', () => {
    const input = 'Line1\nLine2\nLine3';
    const expected = 'Line1  \nLine2  \nLine3';
    expect(pipe.transform(input)).toEqual(expected);
  });

  it('should not change input if there are no newline characters', () => {
    const input = 'No newline characters';
    expect(pipe.transform(input)).toEqual(input);
  });

  it('should fail if ReplaceNewLinePipe is applied after markdown processing', () => {
    const markdownProcessed = 'Hello <br> World';

    expect(pipe.transform(markdownProcessed)).not.toEqual('Hello  \nWorld');
  });
});
