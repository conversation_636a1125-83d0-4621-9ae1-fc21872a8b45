import { Location } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, inject, signal, computed } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { AccountGroup } from '@vendasta/account-group';
import { AssociatedUserInterface } from '@vendasta/business-center/lib/_internal/interfaces/api.interface';
import { CustomerService, FieldMask } from '@vendasta/contacts';
import {
  ConversationChannel,
  Participant,
  ParticipantType as ConversationParticipantType,
} from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { BehaviorSubject, firstValueFrom, map, Observable, take } from 'rxjs';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  COMPANY_NAME_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  ConversationAvailableChannels,
  ConversationService,
  ConversationStatelessService,
  InboxContact,
  InboxService,
  MessageInfo,
  ParticipantService,
  PARTNER_ID_TOKEN,
  redirectToConversationFactory,
  STANDARD_CRM_FIELD_EXTERNAL_IDS,
  USER_ID_TOKEN,
} from '../../../../core/src';
import { AlertOptions } from '../../../../core/src/lib/channels/conversation-channel.abstract';
import { SelectAccountControl } from '../../components/inbox-select-account/inbox-select-account.component';
import { SelectContactControl } from '../../components/inbox-select-contact/inbox-select-contact.component';

const DEFAULT_NEW_MESSAGE_AVAILABLE_CHANNELS = {
  availableChannels: [],
  preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
  channelAvailabilities: [],
};

enum SELECT_OPTIONS {
  ACCOUNTS = 1,
  CONTACTS = 2,
}

@Component({
  selector: 'inbox-new-message',
  templateUrl: './inbox-new-message.component.html',
  styleUrls: ['./inbox-new-message.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class InboxNewMessageComponent {
  isLoading = false;

  readonly selectContactControl = new SelectContactControl('');
  readonly selectAccountControl = new SelectAccountControl('');

  readonly disabledAlert = signal<AlertOptions | null | undefined>(null);

  private readonly redirectToConversation = redirectToConversationFactory();

  private readonly accountGroupId = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN), { requireSync: true });
  private readonly partnerId = toSignal(inject(PARTNER_ID_TOKEN).pipe(), { requireSync: true });
  private readonly userId = toSignal(inject(USER_ID_TOKEN));
  readonly isSMSChannelAvailable$ = this.inboxService.isSMSChannelAvailableForOrg$;
  private readonly isSMSChannelAvailableSignal = toSignal(this.isSMSChannelAvailable$, { initialValue: false });
  readonly canAccessPrivateChat$ = this.inboxService.canAccessPrivateChat$;
  isPartnerCenter = signal(this.inboxService.isPartnerCenter);
  isBusinessApp = signal(this.inboxService.isBusinessApp);

  readonly showRecipientTypeSelectChips = computed(() => this.isPartnerCenter() && this.isSMSChannelAvailableSignal());

  readonly availableChannels$ = this.isSMSChannelAvailable$.pipe(
    map((smsAvailable) => {
      const availableChannels = [
        ...(smsAvailable ? [ConversationChannel.CONVERSATION_CHANNEL_SMS] : []),
        ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
      ];
      return {
        availableChannels: availableChannels,
        preferredChannel: availableChannels[0] || ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
      };
    }),
  );

  private readonly channelsToShow$$ = new BehaviorSubject<ConversationAvailableChannels>(
    DEFAULT_NEW_MESSAGE_AVAILABLE_CHANNELS,
  );
  readonly channelsToShow$ = this.channelsToShow$$.asObservable();
  protected isLoading$ = new BehaviorSubject<boolean>(false);
  readonly userList$ = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN).getUsers(this.accountGroupId());
  private readonly trackingProps = this.inboxService.buildTemplateTrackProperties();
  public selectedOption = signal(this.inboxService.isBusinessApp ? SELECT_OPTIONS.CONTACTS : SELECT_OPTIONS.ACCOUNTS);
  SELECT_OPTIONS = SELECT_OPTIONS;

  constructor(
    @Inject(COMPANY_NAME_TOKEN) readonly companyName$: Observable<string>,
    private router: Router,
    private snackbarService: SnackbarService,
    private location: Location,
    private conversationService: ConversationService,
    private readonly conversationStatelessService: ConversationStatelessService,
    private readonly analyticsService: ProductAnalyticsService,
    private customerService: CustomerService,
    private participantService: ParticipantService,
    protected inboxService: InboxService,
    private cdRef: ChangeDetectorRef,
  ) {
    const routeData = this.router.getCurrentNavigation();
    if (routeData?.extras?.state?.contact) {
      this.selectContactControl.setValue(routeData.extras.state.contact);
      this.selectContact(routeData.extras.state.contact);
    }

    this.selectContactControl.valueChanges.pipe(takeUntilDestroyed()).subscribe((value) => {
      if (typeof value === 'object' && value.contactId) {
        this.startConversationWithContact();
      }
    });

    this.selectAccountControl.valueChanges.pipe(takeUntilDestroyed()).subscribe((value) => {
      if (typeof value === 'object' && value.accountGroupId) {
        this.startConversationWithAccount();
      }
    });

    this.conversationService.setCurrentFirestoreConversationId('');
    this.analyticsService.trackEvent('inbox', 'new-message', 'view', 0, this.trackingProps);
  }

  selectOption(option: SELECT_OPTIONS): void {
    this.selectedOption.set(option);
  }

  async onSubmitDraft(messageInfo: MessageInfo): Promise<void> {
    this.isLoading = true;

    try {
      const contact = this.selectContactControl.value;
      if (!contact || typeof contact === 'string') {
        this.snackbarService.openWithOptions('INBOX.ERROR.INVALID_CONTACT_SELECTED');
        return;
      }

      if (!contact.contactId && (contact.phone || contact.email)) {
        await this.addContactAndStartConversation(contact.phone ?? '', contact.email ?? '', messageInfo);
        this.isLoading = false;
        return;
      }

      if (contact.contactId) {
        if (!contact.permissionToContact) {
          this.updateCustomerPermissionToContact(contact);
        }

        this.conversationService.draftMessages = messageInfo;
        await this.startConversationWithContact();
        this.isLoading = false;
      }
    } finally {
      this.isLoading = false;
    }
  }

  async selectContact(contact: InboxContact): Promise<void> {
    if (contact && (contact?.phone !== '' || contact?.email !== '')) {
      await this.startConversationWithContact();
      this.isLoading = false;
    }
  }

  async startPrivateConversation(user: AssociatedUserInterface): Promise<void> {
    const sender = await firstValueFrom(this.conversationService.currentParticipant$);
    const platformLocation = this.inboxService.platformLocation;

    const participantRecipient = new Participant({
      internalParticipantId: user.userId,
      accountGroupId: this.accountGroupId(),
      partnerId: this.partnerId(),
      participantType: ConversationParticipantType.PARTICIPANT_TYPE_IAM_USER,
      isSubjectParticipant: true,
    });

    sender.isSubjectParticipant = true;
    participantRecipient.isSubjectParticipant = true;

    return firstValueFrom(
      this.conversationStatelessService.createConversation(
        [sender, participantRecipient],
        ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
        platformLocation,
      ),
    )
      .then((response) => {
        if (response && response.conversation) {
          this.analyticsService.trackEvent('inbox', 'new-message', 'create-private-conversation-success', 0, {
            accountGroupId: this.accountGroupId(),
            partnerId: this.partnerId(),
            location: this.inboxService.platformLocation,
            channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
          });
          this.redirectToConversation(response.conversation.conversationId);
        }
      })
      .catch((httpErrorResponse: HttpErrorResponse) => {
        console.warn('Error to create a conversation: ', httpErrorResponse);
        this.snackbarService.openErrorSnack(httpErrorResponse?.error?.message);

        this.analyticsService.trackEvent('inbox', 'new-message', 'create-private-conversation-error', 0, {
          accountGroupId: this.accountGroupId(),
          partnerId: this.partnerId(),
          location: this.inboxService.platformLocation,
          channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
          message: httpErrorResponse?.error?.message,
        });
      });
  }

  private async startConversationWithContact(): Promise<void> {
    const contact = this.selectContactControl.value as InboxContact;
    if (!contact || typeof contact === 'string') {
      this.snackbarService.openWithOptions('INBOX.ERROR.INVALID_CONTACT_SELECTED');
      return;
    }

    this.isLoading$.next(true);

    const isSmsAvailable = await firstValueFrom(this.isSMSChannelAvailable$);
    if (contact.email === '') {
      if (contact.phone !== '' && !isSmsAvailable) {
        await this.changeChannelToShow(ConversationChannel.CONVERSATION_CHANNEL_SMS);
      }

      if (contact.phone === '') {
        this.isLoading$.next(false);
        this.snackbarService.openErrorSnack('INBOX.ERROR.NO_PHONE_NUMBER_OR_EMAIL');
        return;
      }
    }

    if (contact) {
      const platformLocation = this.inboxService.platformLocation;
      const conversationChannel = contact.phone
        ? ConversationChannel.CONVERSATION_CHANNEL_SMS
        : ConversationChannel.CONVERSATION_CHANNEL_EMAIL;

      const participantOrganization = this.isBusinessApp()
        ? this.participantService.buildAccountGroup(this.partnerId(), this.accountGroupId())
        : this.participantService.buildPartner(this.partnerId());

      const participantRecipient = this.participantService.buildContactParticipant(
        contact.contactId ?? '',
        this.partnerId(),
        this.accountGroupId(),
      );
      const participantSender = this.participantService.buildIamUser(
        this.partnerId(),
        this.accountGroupId(),
        this.userId(),
        false,
      );

      const trackingProps = await this.inboxService.buildTemplateTrackProperties();

      return firstValueFrom(
        this.conversationStatelessService.createConversation(
          [participantOrganization, participantSender, participantRecipient],
          conversationChannel,
          platformLocation,
        ),
      )
        .then((response) => {
          if (response && response.conversation) {
            this.analyticsService.trackEvent('inbox', 'new-message', 'create-conversation-success', 0, {
              trackingProps,
              channel: conversationChannel,
            });
            this.redirectToConversation(response.conversation.conversationId);
            this.isLoading$.next(false);
          }
        })
        .catch((httpErrorResponse: HttpErrorResponse) => {
          console.warn('Error to create a conversation: ', httpErrorResponse);
          this.snackbarService.openErrorSnack(httpErrorResponse?.error?.message);

          this.analyticsService.trackEvent('inbox', 'new-message', 'create-conversation-error', 0, {
            trackingProps,
            channel: conversationChannel,
            message: httpErrorResponse?.error?.message,
          });
        });
    }
  }

  private async startConversationWithAccount(): Promise<void> {
    const business = this.selectAccountControl.value as AccountGroup;
    if (!business || typeof business === 'string') {
      this.snackbarService.openWithOptions('INBOX.ERROR.INVALID_ACCOUNT_SELECTED');
      return;
    }

    if (business) {
      const participantPartner = this.participantService.buildPartner(this.partnerId());
      const participantSender = this.participantService.buildIamUser(
        this.partnerId(),
        this.accountGroupId(),
        this.userId(),
        false,
      );
      const participantRecipient = this.participantService.buildAccountGroup(this.partnerId(), business.accountGroupId);

      const conversationChannel = ConversationChannel.CONVERSATION_CHANNEL_INTERNAL;

      const trackingProps = await this.inboxService.buildTemplateTrackProperties();

      return firstValueFrom(
        this.conversationStatelessService.createConversation(
          [participantPartner, participantSender, participantRecipient],
          conversationChannel,
          this.inboxService.platformLocation,
        ),
      )
        .then((response) => {
          if (response && response.conversation) {
            this.analyticsService.trackEvent('inbox', 'new-message', 'create-conversation-success', 0, {
              trackingProps,
              channel: conversationChannel,
            });
            this.redirectToConversation(response.conversation.conversationId);
          }
        })
        .catch((httpErrorResponse: HttpErrorResponse) => {
          console.warn('Error to create a conversation: ', httpErrorResponse);
          this.snackbarService.openErrorSnack(httpErrorResponse?.error?.message);

          this.analyticsService.trackEvent('inbox', 'new-message', 'create-conversation-error', 0, {
            trackingProps,
            channel: conversationChannel,
            message: httpErrorResponse?.error?.message,
          });
        });
    }
  }

  private handleAddContactError(): (error: HttpErrorResponse) => Promise<void> {
    return async (error: HttpErrorResponse) => {
      console.warn('onSubmitAddContact', error.message);
      if (error.status === 409) {
        this.snackbarService.openErrorSnack('INBOX.ERROR.CONTACT_EXISTS');
      } else {
        this.snackbarService.openErrorSnack('INBOX.ERROR.CREATE_CONTACT');
      }

      const trackingProps = await this.inboxService.buildTemplateTrackProperties();
      this.analyticsService.trackEvent('inbox', 'new-message', 'add-contact-error', 0, {
        trackingProps,
        channel: this.conversationService.getConversationChannel(),
        message: error?.message,
      });
    };
  }

  close(): void {
    this.location.back();
  }

  private async addContactAndStartConversation(
    phoneNumber: string,
    email: string,
    messageInfo: MessageInfo,
  ): Promise<void> {
    const trackingProps = await this.inboxService.buildTemplateTrackProperties();

    if (email !== '') {
      const existingContact = await this.getCrmContactByField(STANDARD_CRM_FIELD_EXTERNAL_IDS.email, email);
      if (existingContact?.contactId) {
        this.analyticsService.trackEvent('inbox', 'new-message', 'get-contact-success', 0, {
          trackingProps,
          channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
        });
        this.conversationService.draftMessages = messageInfo;
        this.selectContactControl.setValue(existingContact);
        return;
      }
    }

    return firstValueFrom(
      this.participantService.addContact({
        firstName: '',
        lastName: '',
        phone: phoneNumber,
        email: email,
        accountGroupId: this.accountGroupId(),
        partnerId: this.partnerId(),
      }),
    )
      .then((newContact) => {
        this.analyticsService.trackEvent('inbox', 'new-message', 'add-contact-success', 0, {
          trackingProps,
          channel: phoneNumber
            ? ConversationChannel.CONVERSATION_CHANNEL_SMS
            : ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
        });
        if (!newContact?.lastName) {
          newContact.lastName = '';
        }

        if (newContact?.contactId === undefined) {
          this.snackbarService.openErrorSnack('INBOX.ERROR.CREATE_CONTACT');
        }

        if (newContact?.contactId) {
          this.conversationService.draftMessages = messageInfo;
          //Set newContact value to selectContactControl will trigger the subscription of it in constructor to call startConversationWithContact()
          this.selectContactControl.setValue(newContact);
        }
      })
      .catch(this.handleAddContactError());
  }

  private async getCrmContactByField(fieldId: string, value: string): Promise<InboxContact> {
    const contacts = await firstValueFrom(
      this.participantService.getContactByField(this.accountGroupId(), fieldId, value),
    );
    return contacts?.[0] ?? ({} as InboxContact);
  }

  private updateCustomerPermissionToContact(contact: InboxContact): void {
    this.customerService
      .updateCustomer(
        contact.accountGroupId ?? '',
        contact.contactId ?? '',
        '',
        '',
        '',
        '',
        '',
        '',
        [],
        [],
        [],
        '',
        true,
        new FieldMask({ paths: ['PermissionToContact'] }),
        '',
        '',
      )
      .pipe(take(1))
      .subscribe();
  }

  /**
   * Change the preferred channel and the channel list to show the suggested channel (if available) based on the user input
   * Also, It blocks the composer when an invalid input was given
   * @param {ConversationChannel} channel - suggested channel to show
   */
  async changeChannelToShow(channel: ConversationChannel): Promise<void> {
    const currentAvailableChannels = await firstValueFrom(this.availableChannels$);
    const channelsToShow = await firstValueFrom(this.channelsToShow$);
    this.disabledAlert.set(null);
    if (channel === ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED) {
      if (channelsToShow.availableChannels.length > 0) {
        this.channelsToShow$$.next(DEFAULT_NEW_MESSAGE_AVAILABLE_CHANNELS);
        this.cdRef.markForCheck();
        return;
      }
      return;
    }

    if (currentAvailableChannels?.availableChannels.includes(channel)) {
      this.disabledAlert.set(null);
      this.channelsToShow$$.next({
        availableChannels: [channel],
        preferredChannel: channel,
        channelAvailabilities: [
          {
            channel: channel,
            isAvailable: true,
          },
        ],
      });
    } else {
      const alertMessage = await firstValueFrom(this.conversationService.displayAlertIfExists(null, channel));
      this.disabledAlert.set(alertMessage);
    }
    this.cdRef.markForCheck();
    return;
  }

  getHeaderTitle(): string {
    if (this.isPartnerCenter()) {
      return this.selectedOption() === SELECT_OPTIONS.ACCOUNTS
        ? 'INBOX.NEW_MESSAGE.CHOOSE_AN_ACCOUNT'
        : 'INBOX.NEW_MESSAGE.CHOOSE_A_CONTACT';
    }
    return 'INBOX.NEW_MESSAGE.NEW_MESSAGE_FROM';
  }
}
