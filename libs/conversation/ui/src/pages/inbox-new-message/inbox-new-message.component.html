<mat-drawer-container class="mat-drawer-container flex-container" [hasBackdrop]="false">
  <mat-drawer-content>
    <ng-container *ngIf="canAccessPrivateChat$ | async; else alternativeContent">
      <mat-progress-bar *ngIf="isLoading" mode="indeterminate"></mat-progress-bar>
      <mat-tab-group mat-stretch-tabs="true" mat-align-tabs="start" animationDuration="0ms" class="flex-container">
        <mat-tab label=" {{ 'INBOX.NEW_MESSAGE.CUSTOMER' | translate }}">
          <div class="flex-container">
            <div class="inbox-new-message">
              <div class="inbox-new-message__header">
                <p>
                  {{ 'INBOX.NEW_MESSAGE.NEW_MESSAGE_FROM' | translate }}:
                  {{ companyName$ | async }}
                </p>
                <div class="inbox-new-message__close">
                  <mat-icon (click)="close()">close</mat-icon>
                </div>
              </div>
              <inbox-select-contact
                ngDefaultControl
                [formControl]="selectContactControl"
                (suggestedChannel)="changeChannelToShow($event)"
              ></inbox-select-contact>
              @if (isLoading$ | async) {
                <glxy-loading-spinner [fullHeight]="true"></glxy-loading-spinner>
              }
            </div>
          </div>
        </mat-tab>
        <mat-tab>
          <ng-template mat-tab-label>
            <span class="internal_text">{{ 'INBOX.NEW_MESSAGE.INTERNAL' | translate }}</span>
            <glxy-badge [size]="'small'" color="green-solid">{{ 'INBOX.LABELS.PRIVATE' | translate }}</glxy-badge>
          </ng-template>
          <div class="inbox-new-message">
            <div class="inbox-new-message__header">
              <p>{{ 'INBOX.NEW_MESSAGE.SEND_MESSAGE_TO_TEAM' | translate }}</p>
              <div class="inbox-new-message__close">
                <mat-icon (click)="close()">close</mat-icon>
              </div>
            </div>
            <ng-container *ngIf="userList$ | async as userList">
              <ng-container *ngFor="let user of userList">
                <mat-card class="card" (click)="startPrivateConversation(user)">
                  <mat-card-content>
                    <mat-list-item class="card-list-item">
                      <div class="card-content">
                        <div class="card-info">
                          <mat-card-title>
                            <span>{{ user.firstName }} {{ user.lastName }}</span>
                          </mat-card-title>
                          <p matListItemLine>{{ user.email }}</p>
                        </div>
                      </div>
                    </mat-list-item>
                  </mat-card-content>
                </mat-card>
              </ng-container>
            </ng-container>
          </div>
        </mat-tab>
      </mat-tab-group>
    </ng-container>
  </mat-drawer-content>
</mat-drawer-container>
<ng-template #alternativeContent>
  <mat-progress-bar *ngIf="isLoading" mode="indeterminate"></mat-progress-bar>
  <div class="flex-container">
    <div class="inbox-new-message">
      @if (showRecipientTypeSelectChips()) {
        <ng-container *ngTemplateOutlet="selectChips" />
      }
      <div class="inbox-new-message__header">
        <p class="header-title">
          {{ getHeaderTitle() | translate }}:
          @if (isBusinessApp()) {
            <ng-container>
              {{ companyName$ | async }}
            </ng-container>
          }
        </p>
        <div class="inbox-new-message__close">
          <mat-icon (click)="close()">close</mat-icon>
        </div>
      </div>
      @if (isBusinessApp()) {
        <p>
          {{ 'INBOX.NEW_MESSAGE.CHOOSE_A_CONTACT' | translate }}
        </p>
      }
      @if (selectedOption() === SELECT_OPTIONS.ACCOUNTS) {
        <inbox-select-account ngDefaultControl [formControl]="selectAccountControl"></inbox-select-account>
      } @else if (selectedOption() === SELECT_OPTIONS.CONTACTS) {
        <inbox-select-contact
          ngDefaultControl
          [formControl]="selectContactControl"
          (suggestedChannel)="changeChannelToShow($event)"
        ></inbox-select-contact>
      }
      @if (isLoading$ | async) {
        <glxy-loading-spinner [fullHeight]="true"></glxy-loading-spinner>
      }
    </div>
  </div>
</ng-template>

<ng-template #selectChips>
  <div>
    <mat-chip-listbox class="search-chips">
      <mat-chip-option
        class="search-chip-option"
        [selected]="selectedOption() === SELECT_OPTIONS.ACCOUNTS"
        (click)="selectOption(SELECT_OPTIONS.ACCOUNTS)"
      >
        {{ 'INBOX.SELECTOR_DIALOG.ACCOUNTS' | translate }}
      </mat-chip-option>
      <mat-chip-option
        class="search-chip-option"
        [selected]="selectedOption() === SELECT_OPTIONS.CONTACTS"
        (click)="selectOption(SELECT_OPTIONS.CONTACTS)"
      >
        {{ 'INBOX.SELECTOR_DIALOG.CONTACTS' | translate }}
      </mat-chip-option>
    </mat-chip-listbox>
  </div>
</ng-template>
