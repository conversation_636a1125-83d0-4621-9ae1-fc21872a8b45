import { ChangeDetectionStrategy, Component, computed, signal, ViewChild, inject, OnInit } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  ConversationChannel,
  MessageType,
  Participant,
  ParticipantType,
  PlatformLocation,
} from '@vendasta/conversation';
import { filterNullAndUndefined } from '@vendasta/rx-utils';
import { combineLatest, firstValueFrom, map, Observable } from 'rxjs';
import { ConversationService } from '../../../../core/src/lib/state/conversation.service';
import { ConversationStatelessService } from '../../../../core/src/lib/conversation-stateless.service';
import { InboxService } from '../../../../core/src/lib/inbox.service';
import { ConversationAvailableChannels, MessageInfo } from '../../../../core/src/lib/interface/conversation.interface';
import { InboxMessageTextComponent } from '../inbox-chat/components/inbox-message-text/inbox-message-text.component';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN, USER_ID_TOKEN } from '../../../../core/src/lib/tokens';
import { buildAIContextMetadata, toFirestoreId } from '../../../../core/src/lib/conversation-utils';
import { SYSTEM_ASSISTANT_ID } from '@galaxy/ai-assistant/core';
@Component({
  selector: 'inbox-ai-chat',
  templateUrl: './inbox-ai-chat.component.html',
  styleUrls: ['./inbox-ai-chat.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class InboxAiChatComponent implements OnInit {
  messages$ = this.conversationService.messages$;
  readonly loadingMessages = toSignal(this.conversationService.loadingMessages$);
  readonly currentConversationDetail$ = this.conversationService.currentConversationDetail$;
  readonly loadingConversation = signal(true);
  readonly loading = computed(() => this.loadingConversation() || this.loadingMessages());

  @ViewChild('inboxTextComponent') inboxTextComponent?: InboxMessageTextComponent;

  readonly isImpersonating$: Observable<boolean> = this.conversationService.isUserImpersonated$;

  private readonly userId$ = inject(USER_ID_TOKEN);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);

  AIParticipantRecipient$ = combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
    map(([partnerId, agid]) => {
      return new Participant({
        participantType: ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT,
        partnerId: partnerId,
        accountGroupId: agid,
        internalParticipantId: SYSTEM_ASSISTANT_ID,
        isSubjectParticipant: true,
      });
    }),
  );

  senderParticipant$: Observable<Participant> = combineLatest([this.userId$, this.partnerId$]).pipe(
    map(([user, partnerID]) => {
      return new Participant({
        internalParticipantId: user,
        partnerId: partnerID,
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        isSubjectParticipant: true,
      });
    }),
  );

  availableChannels: ConversationAvailableChannels = {
    availableChannels: [ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT],
    preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
    channelAvailabilities: [
      {
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        isAvailable: true,
      },
    ],
  };

  constructor(
    private readonly conversationService: ConversationService,
    private readonly inboxService: InboxService,
    private readonly conversationStatelessService: ConversationStatelessService,
  ) {}

  ngOnInit(): void {
    this.startConversation();
  }

  async startConversation(): Promise<void> {
    const participants = [
      await firstValueFrom(this.senderParticipant$),
      await firstValueFrom(this.AIParticipantRecipient$),
    ];
    try {
      const resp = await firstValueFrom(
        this.conversationStatelessService.createConversation(
          participants ?? [],
          ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
          PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
        ),
      );

      this.conversationService.setCurrentFirestoreConversationId(
        toFirestoreId(resp?.conversation?.conversationId ?? ''),
      );
    } catch (error) {
      console.warn('Error to get or create the AI conversation: ', error);
    } finally {
      this.loadingConversation.set(false);
    }
  }

  async sendMessage(messageInfo: MessageInfo): Promise<void> {
    const currentConversationDetail = await firstValueFrom(
      this.currentConversationDetail$.pipe(filterNullAndUndefined()),
    );
    if (currentConversationDetail.conversation.channel !== ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT) {
      // Just in case the first non-null conversation we load isn't an AI conversation
      // we don't want messages intended for AI appearing in other conversations
      return;
    }

    // Add current page URL to metadata for AI context
    const metadata = buildAIContextMetadata();

    await this.conversationService.sendAndStageMessage(
      currentConversationDetail.conversation.conversationId,
      MessageType.MESSAGE_TYPE_MESSAGE,
      messageInfo.text!,
      messageInfo.channel,
      this.inboxService.platformLocation,
      messageInfo.attachments,
      undefined,
      undefined,
      metadata,
    );
    this.inboxTextComponent?.clearText?.();
  }
}
