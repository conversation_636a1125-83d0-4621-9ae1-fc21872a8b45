import { Component, effect, HostBinding, inject, OnD<PERSON>roy, OnInit, Signal } from '@angular/core';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../../core/src/lib/tokens';
import { isMobile } from '../../../../core/src/lib/inbox-utils';
import { ConversationService } from '../../../../core/src/lib/state/conversation.service';

import { ProductAnalyticsService } from '@vendasta/product-analytics';
import {
  combineLatest,
  distinctUntilChanged,
  filter,
  firstValueFrom,
  map,
  Observable,
  of,
  shareReplay,
  startWith,
  switchMap,
} from 'rxjs';
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router';
import { AiAssistantService, NAMESPACE_CONFIG_TOKEN } from '@galaxy/ai-assistant';
import { AssistantInterface, AssistantType } from '@vendasta/ai-assistants';
import { CommonModule } from '@angular/common';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { LexiconModule } from '@galaxy/lexicon';
import { MatButtonModule } from '@angular/material/button';
import { ConversationListService } from '../../../../core/src/lib/state/conversation-list.service';
import { toFirestoreId } from '../../../../core/src/lib/conversation-utils';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ParticipantType } from '@vendasta/conversation';
import { MatFormField, MatOption, MatSelect } from '@angular/material/select';
import { InboxGroupedConversationListComponent } from '../../components/inbox-grouped-conversation-list/inbox-grouped-conversation-list.component';
import { InboxConversationViewComponent } from './components/inbox-conversation-view/inbox-conversation-view.component';
import { INBOX_SERVICE_PROVIDERS } from '../../../../core/src/lib/conversation-core.module';
import { AiAssistantConversationService } from '../../../../core/src/lib/state/ai-assistant-conversation.service';
@Component({
  selector: 'inbox-ai-assistant-chat',
  templateUrl: './inbox-ai-assistant-chat.component.html',
  styleUrls: ['./inbox-ai-assistant-chat.component.scss'],
  imports: [
    CommonModule,
    GalaxyPageModule,
    LexiconModule,
    MatButtonModule,
    RouterLink,
    MatSelect,
    MatFormField,
    MatOption,
    InboxGroupedConversationListComponent,
    InboxConversationViewComponent,
  ],
  providers: [...INBOX_SERVICE_PROVIDERS],
})
export class InboxAiAssistantChatComponent implements OnInit, OnDestroy {
  initialViewportHeight?: number;
  @HostBinding('style.--viewport-height') viewportHeight?: string;

  protected showToolbarOnMobile$: Observable<boolean>;
  private resizeListener?: () => void;

  private readonly analyticsService = inject(ProductAnalyticsService);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);

  private readonly conversationListService = inject(ConversationListService);
  private readonly conversationService = inject(ConversationService);
  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly aiConversationService = inject(AiAssistantConversationService);
  private readonly namespaceConfig$ = inject(NAMESPACE_CONFIG_TOKEN);
  readonly assistantId = toSignal(this.aiConversationService.aiAssistantId$);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN).pipe(distinctUntilChanged());
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN).pipe(distinctUntilChanged());
  readonly customAssistants$: Observable<AssistantInterface[]>;
  readonly conversationIdInRoute = toSignal(
    this.route.paramMap.pipe(
      map((params) => params.get('conversationId') || null),
      shareReplay({ refCount: true, bufferSize: 1 }),
    ),
    { initialValue: null },
  );
  readonly $configureAssistantRoute: Signal<string | undefined>;
  private readonly currentConversationDetail = toSignal(this.conversationService.currentConversationDetail$);
  readonly loadingConversations = toSignal(this.conversationListService.loadingConversations$, { initialValue: true });

  constructor() {
    this.route.paramMap
      .pipe(map((params) => params.get('assistantId') || ''))
      .pipe(takeUntilDestroyed())
      .subscribe((assistantId) => this.aiConversationService.setAssistantId(assistantId));

    effect(() => {
      const assistantId = this.assistantId();
      if (assistantId) {
        this.aiConversationService.setAssistantId(assistantId);
      }
    });

    this.$configureAssistantRoute = toSignal(
      this.aiConversationService.aiAssistantId$.pipe(
        switchMap((assistantId) =>
          assistantId ? this.aiAssistantService.buildAssistantConfigurationUrl(assistantId) : of(''),
        ),
      ),
      { initialValue: '' },
    );

    this.showToolbarOnMobile$ = this.router.events.pipe(
      filter((event) => event instanceof NavigationEnd),
      map(() => this.route.firstChild?.snapshot?.data?.['showToolbarOnMobile'] ?? false),
      startWith(this.route.firstChild?.snapshot?.data?.['showToolbarOnMobile'] ?? false),
    );

    this.customAssistants$ = combineLatest([this.namespaceConfig$]).pipe(
      switchMap(([namespaceConfig]) => {
        return this.aiAssistantService.listAssistants(namespaceConfig.namespace);
      }),
      map((assistants) => {
        return assistants.filter((a) => a.type === AssistantType.ASSISTANT_TYPE_CUSTOM);
      }),
    );

    const mostRecentOnAssistantConversationId = toSignal(
      this.conversationListService.conversations$.pipe(
        map((cs) => {
          if (cs && cs.length > 0) {
            return toFirestoreId(cs[0].conversation.conversationId);
          }
          return null;
        }),
      ),
      { initialValue: null },
    );

    const currentConversationId = toSignal(this.conversationService.currentFirestoreConversationId$);
    // Effect to set the current conversation ID from the route
    effect(() => {
      const id = toFirestoreId(this.conversationIdInRoute() ?? '');
      const selectedConversationId = currentConversationId();
      if (id && id !== selectedConversationId) {
        this.conversationService.setCurrentFirestoreConversationId(id);
      }
    });

    const accountGroupId = toSignal(this.accountGroupId$);
    // Effect to switch conversations if the assistant ID doesn't match active conversation
    effect(() => {
      const detail = this.currentConversationDetail();
      const agid = accountGroupId();
      const mostRecentConversationId = mostRecentOnAssistantConversationId();
      const assistantId = this.assistantId();
      if (!assistantId) return;
      if (
        detail?.participants.find((p) => p.participantType === ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT)
          ?.internalParticipantId !== assistantId
      ) {
        if (mostRecentConversationId) {
          const url = this.aiAssistantService.buildAiAssistantChatUrl(agid, assistantId, mostRecentConversationId);
          this.router.navigate([url], { replaceUrl: true });
        } else {
          // No conversations for this assistant: clear selection
          this.conversationService.setCurrentFirestoreConversationId('');
        }
      }
    });
  }

  ngOnInit(): void {
    this.analyticsService.trackEvent('inbox-ai', 'home', 'enter');

    this.initialViewportHeight = window.visualViewport?.height ?? window.innerHeight;

    this.resizeListener = () => this.updateViewportHeight();
    window.visualViewport?.addEventListener('resize', () => this.updateViewportHeight());
  }

  ngOnDestroy(): void {
    this.resizeListener && window.visualViewport?.removeEventListener('resize', this.resizeListener);
    this.conversationService.setCurrentFirestoreConversationId('');
  }

  private updateViewportHeight(): void {
    const viewportHeight = window.visualViewport?.height ?? window.innerHeight;

    // Scroll to the top if the viewport height decreases significantly (when the keyboard opens)
    if (viewportHeight < (this.initialViewportHeight ?? 0) * 0.8) {
      window.scrollTo(0, 0);
    }

    this.viewportHeight = `${viewportHeight}px`;
  }

  get isMobile(): boolean {
    return isMobile();
  }

  async assistantSelected(assistantId: string) {
    const agid = await firstValueFrom(this.accountGroupId$);
    await this.router.navigate([this.aiAssistantService.buildAiAssistantChatUrl(agid, assistantId)]);
  }
}
