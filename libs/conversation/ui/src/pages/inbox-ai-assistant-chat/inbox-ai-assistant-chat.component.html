<glxy-page [pagePadding]="false" [mobileMaxWidth]="0">
  @if ((showToolbarOnMobile$ | async) || !isMobile) {
    <glxy-page-toolbar>
      <glxy-page-nav>
        <glxy-page-nav-button [useHistory]="true"></glxy-page-nav-button>
      </glxy-page-nav>
      <glxy-page-title>
        {{ 'INBOX.HEADER.AI_ASSISTANT_INBOX_TITLE' | translate }}
        &nbsp;
        <mat-form-field size="small" subscriptSizing="dynamic" class="assistant-select-field">
          <mat-select (selectionChange)="assistantSelected($event.value)" [value]="assistantId()">
            @let assistants = customAssistants$ | async;
            @for (assistant of assistants; track assistant.id) {
              <mat-option [value]="assistant.id">{{ assistant.name }}</mat-option>
            }
          </mat-select>
        </mat-form-field>
      </glxy-page-title>
      <glxy-page-actions>
        <button mat-stroked-button [routerLink]="[$configureAssistantRoute()]" class="configure-button">
          <span class="label">{{ 'INBOX.CONFIGURE' | translate }}</span>
        </button>
      </glxy-page-actions>
    </glxy-page-toolbar>
  }
  <div class="inbox-container">
    <div class="inbox-panes">
      @if (!isMobile) {
        <div class="inbox-left-pane">
          <inbox-grouped-conversation-list></inbox-grouped-conversation-list>
        </div>
      }
      <inbox-conversation-view
        [isLoading]="loadingConversations() && !conversationIdInRoute()"
      ></inbox-conversation-view>
    </div>
  </div>
</glxy-page>
