<div class="inbox-question">
  <glxy-chat-container #chatContainer>
    <div class="hardcoded-message">
      <glxy-chat-message-group
        type="received"
        messageFrom="{{ 'INBOX.HEADER.INBOX_AI_TITLE' | translate: { assistantName: getAssistantName() } }}"
        [profilePicUrl]="systemAssistant()?.avatarUrl"
        [profileSVGIcon]="!systemAssistant()?.avatarUrl ? AURORA_AVATAR_SVG_ICON : ''"
      >
        <glxy-chat-message>{{ 'INBOX.CONTACT_ASSISTANT.AI_HELP_MESSAGE' | translate }}</glxy-chat-message>
      </glxy-chat-message-group>
    </div>
  </glxy-chat-container>
  <div class="question-input">
    <glxy-form-field class="question-input-field">
      <textarea
        class="field-input"
        matInput
        type="text"
        [ariaMultiLine]="true"
        [value]="messageInfo().text"
        (input)="updateMessageText($event)"
        (keydown.enter)="sendMessage()"
        [disabled]="sendingMessage()"
        placeholder="{{ 'INBOX.CONTACT_ASSISTANT.QUESTION_PLACEHOLDER' | translate }}"
      ></textarea>
      @if (!sendingMessage()) {
        <button mat-icon-button matSuffix (click)="sendMessage()">
          <mat-icon class="send-button-icon">send</mat-icon>
        </button>
      } @else {
        <div matSuffix class="send-button-loading-spinner">
          <glxy-loading-spinner size="small"></glxy-loading-spinner>
        </div>
      }
    </glxy-form-field>
  </div>
</div>
