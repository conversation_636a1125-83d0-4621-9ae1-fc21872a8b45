import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { firstValueFrom, of } from 'rxjs';
import {
  USER_ID_TOKEN,
  PARTNER_ID_TOKEN,
  ACCOUNT_GROUP_ID_TOKEN,
  InboxService,
  ConversationStatelessService,
} from '../../../../core/src/index';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { CommonModule } from '@angular/common';
import { AiSupportComposerComponent } from './inbox-ai-support-composer.component';
import {
  conversationStatelessServiceMock,
  inboxServiceMock,
  snackbarServiceMock,
  translateServiceMock,
} from '../../../../core/src/lib/mocks';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { HttpClient } from '@angular/common/http';
import {
  PlatformLocation,
  Conversation,
  ConversationChannel,
  ParticipantType,
  MessageType,
  Participant,
} from '@vendasta/conversation';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { GalaxyChatModule } from '@vendasta/galaxy/chat';
import { MatIconModule } from '@angular/material/icon';
import { SYSTEM_ASSISTANT_ID } from '@galaxy/ai-assistant/core';
import { AiAssistantService } from '@galaxy/ai-assistant';

describe('AiSupportComposerComponent', () => {
  let component: AiSupportComposerComponent;
  let fixture: ComponentFixture<AiSupportComposerComponent>;

  const aiAssistantServiceMock = {
    getSystemAssistant: jest.fn().mockReturnValue(
      of({
        id: SYSTEM_ASSISTANT_ID,
        name: 'Aurora',
      }),
    ),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        GalaxyChatModule,
        TranslateModule.forRoot(),
        MatIconModule,
        MatInputModule,
        GalaxyLoadingSpinnerModule,
        GalaxyFormFieldModule,
        MatFormFieldModule,
        MatButtonModule,
        AiSupportComposerComponent,
      ],
    })
      .overrideComponent(AiSupportComposerComponent, {
        set: {
          providers: [
            { provide: HttpClient, useValue: {} },
            { provide: InboxService, useValue: inboxServiceMock },
            { provide: SnackbarService, useValue: snackbarServiceMock },
            { provide: ConversationStatelessService, useValue: conversationStatelessServiceMock },
            { provide: USER_ID_TOKEN, useValue: of('test-user-id') },
            { provide: PARTNER_ID_TOKEN, useValue: of('ABC') },
            { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('') },
            { provide: TranslateService, useValue: translateServiceMock },
            { provide: AiAssistantService, useValue: aiAssistantServiceMock },
          ],
        },
      })
      .compileComponents();

    fixture = TestBed.createComponent(AiSupportComposerComponent);
    fixture.componentRef.setInput('config', {
      platformLocation: PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
      assistantId: SYSTEM_ASSISTANT_ID,
    });
    component = fixture.componentRef.instance;
  });

  describe('should create', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should send message', () => {
      component.sendMessage();
      expect(component.sendingMessage()).toBe(true);
    });
  });

  describe('startConversation', () => {
    it('should successfully start a conversation', async () => {
      const mockConversation = {
        conversationId: 'CONVERSATION-123',
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      } as Conversation;

      conversationStatelessServiceMock.createConversation = jest
        .fn()
        .mockReturnValue(of({ conversation: mockConversation }));

      const result = await component.startConversation();

      expect(conversationStatelessServiceMock.createConversation).toHaveBeenCalled();
      expect(result).toBe(mockConversation);
    });

    it('should return null when conversation creation fails', async () => {
      conversationStatelessServiceMock.createConversation = jest.fn().mockReturnValue(of({ conversation: null }));

      const result = await component.startConversation();

      expect(conversationStatelessServiceMock.createConversation).toHaveBeenCalled();
      expect(result).toBeNull();
    });
    it('should create conversation with exactly 2 participants', async () => {
      let capturedParticipants: any[] = [];

      conversationStatelessServiceMock.createConversation = jest
        .fn()
        .mockImplementation((participants, _channel, _location) => {
          capturedParticipants = participants;
          return of({ conversation: null });
        });

      await component.startConversation();

      expect(capturedParticipants.length).toBe(2);
    });
    it('should pass correct parameters to createConversation', async () => {
      conversationStatelessServiceMock.createConversation = jest.fn().mockReturnValue(of({ conversation: null }));

      await component.startConversation();

      const participants = [
        await firstValueFrom(component.senderParticipant$),
        await firstValueFrom(component.AIParticipantRecipient$),
      ];
      const aiAssistantParticipant = participants.find(
        (participant) => participant.participantType === ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT,
      );

      expect(participants.length).toBe(2);
      expect(aiAssistantParticipant).toBeDefined();
      expect(conversationStatelessServiceMock.createConversation).toHaveBeenCalledWith(
        participants,
        ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
      );
    });
  });

  describe('sendMessage', () => {
    const sender = new Participant({
      internalParticipantId: 'test-user-id',
      partnerId: 'ABC',
      participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
      isSubjectParticipant: true,
    });

    it('should send message with existing conversation', async () => {
      const mockConversation = {
        conversationId: 'CONVERSATION-123',
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      } as Conversation;

      component.messageInfo.set({
        text: 'Test message',
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        attachments: [],
      });

      component['currentConversation'].set(mockConversation);

      conversationStatelessServiceMock.sendMessage = jest.fn().mockResolvedValue({});

      await component.sendMessage();

      expect(component.sendingMessage()).toBe(true);
      expect(conversationStatelessServiceMock.sendMessage).toHaveBeenCalledWith(
        'CONVERSATION-123',
        MessageType.MESSAGE_TYPE_MESSAGE,
        'Test message',
        ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
        [],
        sender,
        undefined,
        [{ data: { currentPageUrl: 'http://localhost/' }, identifier: 2 }],
      );
    });

    it('should create new conversation when none exists', async () => {
      const mockConversation = {
        conversationId: 'CONVERSATION-123',
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      } as Conversation;

      component.messageInfo.set({
        text: 'Test message',
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        attachments: [],
      });

      jest.spyOn(component, 'startConversation').mockResolvedValue(mockConversation);

      conversationStatelessServiceMock.sendMessage = jest.fn().mockResolvedValue(undefined);

      await component.sendMessage();

      expect(component.startConversation).toHaveBeenCalled();
      expect(conversationStatelessServiceMock.sendMessage).toHaveBeenCalledWith(
        'CONVERSATION-123',
        MessageType.MESSAGE_TYPE_MESSAGE,
        'Test message',
        ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
        [],
        sender,
        undefined,
        [{ data: { currentPageUrl: 'http://localhost/' }, identifier: 2 }],
      );
    });

    it('should not send if conversation creation fails', async () => {
      component.messageInfo.set({
        text: 'Test message',
        channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
        attachments: [],
      });

      jest.spyOn(component, 'startConversation').mockResolvedValue(null);

      conversationStatelessServiceMock.sendMessage = jest.fn().mockResolvedValue(undefined);

      await component.sendMessage();

      expect(component.startConversation).toHaveBeenCalled();
      expect(conversationStatelessServiceMock.sendMessage).not.toHaveBeenCalled();
    });

    it('should not send empty message', async () => {
      const mockConversation = {
        conversationId: 'CONVERSATION-123',
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      } as Conversation;

      component.messageInfo.set({
        text: '   ',
        channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
        attachments: [],
      });

      component['currentConversation'].set(mockConversation);

      conversationStatelessServiceMock.sendMessage = jest.fn().mockResolvedValue(undefined);

      await component.sendMessage();

      expect(conversationStatelessServiceMock.sendMessage).not.toHaveBeenCalled();
    });

    it('should not send if conversation channel is not OpenAI', async () => {
      const mockConversation = {
        conversationId: 'CONVERSATION-123',
        channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
      } as Conversation;

      component.messageInfo.set({
        text: 'Test message',
        channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
        attachments: [],
      });

      component['currentConversation'].set(mockConversation);

      conversationStatelessServiceMock.sendMessage = jest.fn().mockResolvedValue(undefined);

      await component.sendMessage();

      expect(conversationStatelessServiceMock.sendMessage).not.toHaveBeenCalled();
    });

    it('should handle error in conversation creation', async () => {
      component.messageInfo.set({
        text: 'Test message',
        channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
        attachments: [],
      });

      jest.spyOn(component, 'startConversation').mockRejectedValue(new Error('Creation failed'));

      conversationStatelessServiceMock.sendMessage = jest.fn().mockResolvedValue(undefined);
      snackbarServiceMock.openErrorSnack = jest.fn();

      await component.sendMessage();

      expect(component.startConversation).toHaveBeenCalled();
      expect(snackbarServiceMock.openErrorSnack).toHaveBeenCalledWith('Error creating conversation');
      expect(conversationStatelessServiceMock.sendMessage).not.toHaveBeenCalled();
      expect(component.sendingMessage()).toBe(false);
    });

    it('should emit message sent after delay', async () => {
      const mockConversation = {
        conversationId: 'CONVERSATION-123',
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
      } as Conversation;

      component.messageInfo.set({
        text: 'Test message',
        channel: ConversationChannel.CONVERSATION_CHANNEL_OPENAI,
        attachments: [],
      });

      component['currentConversation'].set(mockConversation);

      conversationStatelessServiceMock.sendMessage = jest.fn().mockResolvedValue(undefined);

      jest.spyOn(component, 'markMessageAsSentWithDelay').mockResolvedValue(undefined);

      await component.sendMessage();

      expect(component.markMessageAsSentWithDelay).toHaveBeenCalled();
    });
  });
});
