import { Component, inject, input, signal, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyChatModule } from '@vendasta/galaxy/chat';
import {
  Conversation,
  ConversationChannel,
  MessageType,
  Participant,
  ParticipantType,
  PlatformLocation,
} from '@vendasta/conversation';
import { combineLatest, firstValueFrom, map } from 'rxjs';
import {
  InboxService,
  MessageInfo,
  ConversationStatelessService,
  USER_ID_TOKEN,
  PARTNER_ID_TOKEN,
  ACCOUNT_GROUP_ID_TOKEN,
  buildAIContextMetadata,
} from '../../../../core/src/index';
import { Observable } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { AURORA_AVATAR_SVG_ICON, AiAssistantService } from '@galaxy/ai-assistant';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { DEFAULT_OPENAI_BOT_NAME } from '../../../../core/src/lib/inbox.constants';

interface SendMessageConfig {
  platformLocation: PlatformLocation;
  assistantId: string;
}
@Component({
  selector: 'inbox-ai-support-composer',
  imports: [
    CommonModule,
    GalaxyChatModule,
    TranslateModule,
    MatIconModule,
    MatInputModule,
    GalaxyLoadingSpinnerModule,
    GalaxyFormFieldModule,
    MatFormFieldModule,
    MatButtonModule,
  ],
  templateUrl: './inbox-ai-support-composer.component.html',
  styleUrl: './inbox-ai-support-composer.component.scss',
})
export class AiSupportComposerComponent {
  readonly AURORA_AVATAR_SVG_ICON = AURORA_AVATAR_SVG_ICON;

  getAssistantName(): string {
    return this.systemAssistant()?.name || DEFAULT_OPENAI_BOT_NAME;
  }

  messageInfo = signal<MessageInfo>({
    text: '',
    channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
    attachments: [],
  });

  config = input.required<SendMessageConfig>();
  messageSent = output<void>();

  private readonly conversationStatelessService = inject(ConversationStatelessService);
  private readonly inboxService = inject(InboxService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly userId$ = inject(USER_ID_TOKEN);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly aiAssistantService = inject(AiAssistantService);
  readonly systemAssistant = toSignal(this.aiAssistantService.getSystemAssistant());

  private readonly conversationId = signal<string | null>(null);
  private readonly currentConversation = signal<Conversation | null>(null);
  readonly sendingMessage = signal(false);

  AIParticipantRecipient$ = combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
    map(([partnerId, agid]) => {
      return new Participant({
        participantType: ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT,
        partnerId: partnerId,
        accountGroupId: agid,
        internalParticipantId: this.config().assistantId,
        isSubjectParticipant: true,
      });
    }),
  );

  senderParticipant$: Observable<Participant> = combineLatest([this.userId$, this.partnerId$]).pipe(
    map(([user, partnerID]) => {
      return new Participant({
        internalParticipantId: user,
        partnerId: partnerID,
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        isSubjectParticipant: true,
      });
    }),
  );

  async sendMessage(): Promise<void> {
    this.sendingMessage.set(true);
    const messageInfo = this.messageInfo();
    const sender = await firstValueFrom(this.senderParticipant$);

    let currentConversation = this.currentConversation();
    if (!currentConversation) {
      try {
        currentConversation = await this.startConversation();
      } catch (error) {
        console.error('Error creating conversation', error);
        this.snackbarService.openErrorSnack('Error creating conversation');
        this.sendingMessage.set(false);
        return;
      }
    }

    if (!currentConversation) {
      return;
    }

    if (currentConversation.channel !== ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT) {
      return;
    }
    if (messageInfo?.text?.trim() === '') {
      return;
    }

    // Add current page URL to metadata for AI context
    const metadata = buildAIContextMetadata();

    await this.conversationStatelessService.sendMessage(
      currentConversation.conversationId,
      MessageType.MESSAGE_TYPE_MESSAGE,
      messageInfo.text ?? '',
      messageInfo.channel,
      this.inboxService.platformLocation,
      messageInfo.attachments,
      sender,
      undefined,
      metadata,
    );

    await this.markMessageAsSentWithDelay();
  }

  async markMessageAsSentWithDelay(): Promise<void> {
    await setTimeout(() => {
      this.sendingMessage.set(false);
      this.messageSent.emit();
    }, 2000);
  }

  async startConversation(): Promise<Conversation | null> {
    const participants = [
      await firstValueFrom(this.senderParticipant$),
      await firstValueFrom(this.AIParticipantRecipient$),
    ];
    const conversationResp = await firstValueFrom(
      this.conversationStatelessService.createConversation(
        participants ?? [],
        ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        this.config().platformLocation,
      ),
    );

    if (conversationResp.conversation) {
      this.currentConversation.set(conversationResp.conversation);
      this.conversationId.set(conversationResp.conversation.conversationId);
      return conversationResp.conversation;
    }

    return null;
  }

  updateMessageText(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.messageInfo.set({
      ...this.messageInfo(),
      text: input.value,
    });
  }
}
