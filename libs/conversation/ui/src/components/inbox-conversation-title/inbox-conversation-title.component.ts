import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ConversationDetail } from '../../../../core/src/lib/interface/conversation.interface';
import { CONVERSATION_HOST_APP_INTERFACE_TOKEN } from '../../../../core/src/lib/tokens';
import { GalaxyPhoneNumberPipe } from '@vendasta/galaxy/pipes/src/phone-number/phone-number.pipe';

@Component({
  selector: 'inbox-conversation-title',
  templateUrl: './inbox-conversation-title.component.html',
  styleUrls: ['./inbox-conversation-title.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgClass, TranslateModule, GalaxyPhoneNumberPipe],
})
export class InboxConversationTitleComponent {
  private readonly hostAppInterface = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN);

  conversationDetail = input<ConversationDetail>();
  conversationClosed = input<boolean>(false);
  displayOnTopBar = input<boolean>(false);

  isInternalInfoDeleted = computed(() => {
    const detail = this.conversationDetail();
    return detail ? this.hostAppInterface.isRecipientInternalInfoDeleted(detail) : false;
  });

  title = computed(() => {
    const detail = this.conversationDetail();
    return detail ? this.hostAppInterface.getConversationTitleInfo(detail).title : '';
  });
}
