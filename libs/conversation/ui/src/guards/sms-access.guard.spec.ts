import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { InboxService } from '../../../core/src/lib/inbox.service';
import { ACCOUNT_GROUP_ID_TOKEN } from '../../../core/src/lib/tokens';
import { INBOX_ROUTES } from '../../../inbox/src/lib.routes';
import { SmsAccessGuard } from './sms-access.guard';

jest.mock('../pages/inbox-new-message/inbox-new-message.component', () => ({
  InboxNewMessageComponent: jest.fn(),
}));
jest.mock('../components/inbox-welcome/inbox-welcome.component', () => ({
  InboxWelcomeComponent: jest.fn(),
}));
jest.mock('../../../inbox/src/inbox/inbox.component', () => ({
  InboxComponent: jest.fn(),
}));

class MockInboxService implements Pick<InboxService, 'canAccessSMS$'> {
  canAccessSMS$ = of(true);
}

describe('Check if there is Inbox SMS access', () => {
  let router: Router;
  const mockInboxService = new MockInboxService();

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule.withRoutes(INBOX_ROUTES)],
      providers: [
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-1') },
        { provide: InboxService, useValue: mockInboxService },
      ],
    });
  });

  it('should be created', () => {
    const guard = TestBed.inject(SmsAccessGuard);
    expect(guard).toBeTruthy();
  });

  it('should allow navigation to new-message route regardless of SMS access', async () => {
    mockInboxService.canAccessSMS$ = of(true);
    router = TestBed.inject(Router);
    const result = router.navigate(['new-message']);
    return result.then((r) => {
      expect(r).toBe(true);
      expect(router.url).toBe('/new-message');
    });
  });

  it('should allow navigation to new-message route even when SMS access is disabled', async () => {
    mockInboxService.canAccessSMS$ = of(false);
    router = TestBed.inject(Router);
    const result = router.navigate(['new-message']);
    return result.then((r) => {
      expect(r).toBe(true);
      expect(router.url).toBe('/new-message');
    });
  });

  it('should redirect create-contact route if country has no SMS access', async () => {
    mockInboxService.canAccessSMS$ = of(false);
    router = TestBed.inject(Router);
    const result = router.navigate(['create-contact']);
    return result.then((r) => {
      expect(r).toBe(true);
      expect(router.url).toBe('/');
    });
  });
});
