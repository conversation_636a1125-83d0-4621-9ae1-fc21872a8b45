import { Component, computed, DestroyRef, effect, inject, Injectable, output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractControl, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { catchError, combineLatest, firstValueFrom, map, of, shareReplay, switchMap } from 'rxjs';
import { MissedCallTrigger, OwnerType, PhoneNumberConfig, PhoneNumberConfigInterface } from '@vendasta/smsv2';
import { FeatureFlagService } from '@galaxy/partner';
import { MatRadioModule } from '@angular/material/radio';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { MatCardModule } from '@angular/material/card';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';
import {
  AiAssistantReferenceCardComponent,
  AiAssistantService,
  AiConnection,
  ASSISTANT_ID_CHAT_RECEPTIONIST,
  ASSISTANT_ID_VOICE_RECEPTIONIST,
  NAMESPACE_CONFIG_TOKEN,
  SMSConnectionID,
  VoiceConnectionID,
} from '@galaxy/ai-assistant';
import { Assistant, AssistantApiService } from '@vendasta/ai-assistants';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { ITEM_COMMON_MODULE_IMPORTS } from '@galaxy/inventory-ui';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { MaybeAsync } from '@angular/router';
import { InboxService } from '../../../../../core/src/';
import { SmsConfigService, SmsContextToken } from '@galaxy/sms';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { GetAssistantResponse } from '@vendasta/ai-assistants/lib/_internal/objects';
import { GalaxyPhoneNumberPipe } from '@vendasta/galaxy/pipes/src/phone-number/phone-number.pipe';

enum CALL_SETTING_SELECTION {
  END_CALL,
  VOICE_AI,
  FORWARD_CALL,
  SEND_SMS_MESSAGE,
  BROADLY,
}

enum SMS_SETTING_SELECTION {
  NOTHING,
  CHAT_RECEPTIONIST,
}

@Injectable({ providedIn: 'root' })
export class PhoneConfigUnsavedChangesGuard {
  private readonly confirmationModal = inject(OpenConfirmationModalService);
  private readonly hasUnsavedChanges = signal(false);

  canDeactivate(): MaybeAsync<boolean> {
    if (this.hasUnsavedChanges()) {
      return this.confirmationModal
        .openModal({
          type: 'warn',
          title: 'AI_ASSISTANT.SHARED.UNSAVED_CHANGES.TITLE',
          message: 'AI_ASSISTANT.SHARED.UNSAVED_CHANGES.MESSAGE',
          confirmButtonText: 'AI_ASSISTANT.SHARED.UNSAVED_CHANGES.CONFIRM',
        })
        .pipe(map((confirmation) => !!confirmation));
    }
    return true;
  }

  notifyStateChanged(hasChanges: boolean): void {
    this.hasUnsavedChanges.set(hasChanges);
  }
}

@Component({
  selector: 'inbox-sms-number-settings-page',
  imports: [
    CommonModule,
    FormsModule,
    GalaxyButtonLoadingIndicatorModule,
    GalaxyFormFieldModule,
    GalaxyLoadingSpinnerModule,
    MatButtonModule,
    MatSlideToggle,
    MatIconModule,
    MatInputModule,
    MatButtonToggleModule,
    ReactiveFormsModule,
    TranslateModule,
    MatRadioModule,
    GalaxyPageModule,
    MatCardModule,
    GalaxyStickyFooterModule,
    AiAssistantReferenceCardComponent,
    GalaxyAlertModule,
    GalaxyPhoneNumberPipe,
    ITEM_COMMON_MODULE_IMPORTS,
    GalaxyTooltipModule,
  ],
  templateUrl: './sms-number-settings-page.component.html',
  styleUrl: './sms-number-settings-page.component.scss',
})
export class SmsNumberSettingsPageComponent {
  private readonly VOICE_AI_FEATURE_ID = 'voice_ai';
  private readonly MISSED_CALL_MESSAGING_FEATURE_ID = 'missed_call_messaging';
  private readonly PARTNER_VOICE_ASSISTANT_FEATURE_ID = 'partner_voice_receptionist';
  protected readonly missedCallTrigger = MissedCallTrigger;
  protected readonly callSettingSelection = CALL_SETTING_SELECTION;
  protected readonly smsSettingSelection = SMS_SETTING_SELECTION;
  protected readonly saved = output<PhoneNumberConfigInterface>();

  private readonly configService = inject(SmsConfigService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly featureFlagService = inject(FeatureFlagService);
  private readonly assistantApiService = inject(AssistantApiService);
  private readonly assistantService = inject(AiAssistantService);
  private readonly inboxService = inject(InboxService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly smsConfig = inject(SmsContextToken);
  private readonly unsavedGuard = inject(PhoneConfigUnsavedChangesGuard);
  private readonly namespaceConfig$ = inject(NAMESPACE_CONFIG_TOKEN);
  protected readonly showSMSBetaInfo = this.inboxService.isPartnerCenter;
  protected readonly featureFlags$ = this.smsConfig.featureFlagInfo$.pipe(
    switchMap((featureFlagInfo) => {
      return this.featureFlagService.batchGetStatus(featureFlagInfo.partnerId, featureFlagInfo.marketId ?? '', [
        this.VOICE_AI_FEATURE_ID,
        this.MISSED_CALL_MESSAGING_FEATURE_ID,
        this.PARTNER_VOICE_ASSISTANT_FEATURE_ID,
      ]);
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );
  // This depends on AI Assistant now so we need to check if both feature flags
  protected readonly showVoiceAIOptions$ = combineLatest([
    this.featureFlags$,
    this.configService.excludedFromVoiceAI$,
  ]).pipe(
    map(([featureStatuses, excludedFromVoiceAI]) => {
      return (
        featureStatuses[this.VOICE_AI_FEATURE_ID] &&
        (this.smsConfig.ownerType === OwnerType.OWNER_TYPE_ACCOUNT_GROUP ||
          featureStatuses[this.PARTNER_VOICE_ASSISTANT_FEATURE_ID]) &&
        !excludedFromVoiceAI
      );
    }),
  );

  protected readonly showMissedCallMessagingOptions$ = this.featureFlags$.pipe(
    map((featureStatuses) => featureStatuses[this.MISSED_CALL_MESSAGING_FEATURE_ID]),
  );

  private readonly callSettingOptionControl = new FormControl(CALL_SETTING_SELECTION.END_CALL);
  protected callSettingsForm = new FormGroup({
    voiceAIEnabled: new FormControl(false),
    callForwardingEnabled: new FormControl(false),
    callForwardingNumber: new FormControl<string>('', [
      (control: AbstractControl<string>) => {
        return this.callSettingOptionControl.value !== CALL_SETTING_SELECTION.FORWARD_CALL ||
          control.value.match(/^\+[1-9]\d{1,14}$/)
          ? null
          : { invalidPhoneNumber: { value: control.value } };
      },
    ]),
    missedCallMessagingEnabled: new FormControl(false),
    missedCallMessagingTrigger: new FormControl<MissedCallTrigger>(MissedCallTrigger.MISSED_CALL_TRIGGER_UNSPECIFIED),
    missedCallMessagingMessageContent: new FormControl<string>(''),
    endCallMessagingEnabled: new FormControl(false),
    endCallMessage: new FormControl<string>(''),
    callSettingOption: this.callSettingOptionControl,
  });

  get MissedCallMessagingEnabled() {
    return this.callSettingsForm.get('missedCallMessagingEnabled');
  }

  get EndCallMessagingEnabled() {
    return this.callSettingsForm.get('endCallMessagingEnabled');
  }

  protected smsSettingForm = new FormControl(SMS_SETTING_SELECTION.NOTHING);

  protected submitting = signal(false);
  protected readonly showVoiceAiFeatureConflictAlert = signal(false);
  protected readonly phoneNumber = toSignal(this.inboxService.SMSNumber$);
  protected readonly phoneConfig = toSignal(this.configService.phoneNumberConfig$);
  protected readonly voiceReceptionist = toSignal(
    this.smsConfig.ownerId$.pipe(switchMap((ownerId) => this.getAssistant(ownerId, ASSISTANT_ID_VOICE_RECEPTIONIST))),
  );
  protected readonly voiceConnection = toSignal(this.getConnection(ASSISTANT_ID_VOICE_RECEPTIONIST, VoiceConnectionID));
  protected readonly chatReceptionist = toSignal(
    this.smsConfig.ownerId$.pipe(switchMap((ownerId) => this.getAssistant(ownerId, ASSISTANT_ID_CHAT_RECEPTIONIST))),
  );
  protected readonly chatConnection = toSignal(this.getConnection(ASSISTANT_ID_CHAT_RECEPTIONIST, SMSConnectionID));
  protected callSettingOption = toSignal(this.callSettingsForm.controls.callSettingOption.valueChanges);
  protected smsSettingSelected = toSignal(this.smsSettingForm.valueChanges);
  protected callSettingFormValueChanges = toSignal(this.callSettingsForm.valueChanges);
  protected hasUnsavedChanges = signal(false);
  protected useBroadlyTwilio = toSignal(this.configService.usesBroadlyTwilio$);

  protected endCallSelected = computed(() => this.callSettingOption() === CALL_SETTING_SELECTION.END_CALL);
  protected voiceAiSelected = computed(() => this.callSettingOption() === CALL_SETTING_SELECTION.VOICE_AI);
  protected callForwardingSelected = computed(() => this.callSettingOption() === CALL_SETTING_SELECTION.FORWARD_CALL);
  protected sendSMSSelected = computed(() => this.callSettingOption() === CALL_SETTING_SELECTION.SEND_SMS_MESSAGE);
  protected broadlySelected = computed(() => this.callSettingOption() === CALL_SETTING_SELECTION.BROADLY);
  protected chatReceptionistSelected = computed(
    () => this.smsSettingSelected() === SMS_SETTING_SELECTION.CHAT_RECEPTIONIST,
  );

  constructor() {
    const {
      voiceAIEnabled,
      callForwardingEnabled,
      missedCallMessagingEnabled,
      missedCallMessagingTrigger,
      missedCallMessagingMessageContent,
    } = this.callSettingsForm.controls;
    voiceAIEnabled.disable();

    combineLatest([callForwardingEnabled.valueChanges, this.configService.usesBroadlyTwilio$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, usesBroadlyTwilio]) => {
        // You can't send a message when missing a forwarded call, if you're not forwarding calls.
        // ... Unless you use the Broadly Twilio account
        if (!usesBroadlyTwilio) {
          if (
            !value &&
            missedCallMessagingTrigger.value === MissedCallTrigger.MISSED_CALL_TRIGGER_FORWARDED_CALL_MISSED
          ) {
            missedCallMessagingTrigger.setValue(MissedCallTrigger.MISSED_CALL_TRIGGER_CALL_RECEIVED);
            missedCallMessagingTrigger.disable();
          } else {
            value && missedCallMessagingEnabled.value
              ? missedCallMessagingTrigger.enable()
              : missedCallMessagingTrigger.disable();
          }
        }
      });

    missedCallMessagingEnabled.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value) => {
      value ? missedCallMessagingTrigger.enable() : missedCallMessagingTrigger.disable();
      value ? missedCallMessagingMessageContent.enable() : missedCallMessagingMessageContent.disable();
    });

    effect(() => {
      const phoneNumberConfig = this.phoneConfig();
      this.callSettingsForm.setValue({
        voiceAIEnabled: phoneNumberConfig?.voiceAi?.enabled || false,
        callForwardingEnabled: phoneNumberConfig?.callForwarding?.enabled || false,
        callForwardingNumber: phoneNumberConfig?.callForwarding?.phoneNumber || '',
        missedCallMessagingEnabled: phoneNumberConfig?.missedCallMessaging?.enabled || false,
        missedCallMessagingTrigger:
          phoneNumberConfig?.missedCallMessaging?.trigger || MissedCallTrigger.MISSED_CALL_TRIGGER_CALL_RECEIVED,
        missedCallMessagingMessageContent: phoneNumberConfig?.missedCallMessaging?.messageContent || '',
        endCallMessagingEnabled: phoneNumberConfig?.endCall?.enabled || false,
        endCallMessage: phoneNumberConfig?.endCall?.messageContent || '',
        callSettingOption: this.getCallSettingSelection(phoneNumberConfig ?? null),
      });
    });

    effect(() => {
      const chatReceptionist = this.chatReceptionist();
      const chatConnection = this.chatConnection();
      if (chatConnection?.connection?.assistantKeys?.some((assistantKey) => assistantKey.id === chatReceptionist?.id)) {
        this.smsSettingForm.setValue(SMS_SETTING_SELECTION.CHAT_RECEPTIONIST);
      }
    });

    effect(() => {
      if (this.callSettingOption()) {
        const phoneNumberConfig = this.phoneConfig();
        this.callSettingsForm.patchValue({
          voiceAIEnabled: phoneNumberConfig?.voiceAi?.enabled || false,
          callForwardingEnabled: phoneNumberConfig?.callForwarding?.enabled || false,
          callForwardingNumber: phoneNumberConfig?.callForwarding?.phoneNumber || '',
          missedCallMessagingEnabled: phoneNumberConfig?.missedCallMessaging?.enabled || false,
          missedCallMessagingTrigger:
            phoneNumberConfig?.missedCallMessaging?.trigger || MissedCallTrigger.MISSED_CALL_TRIGGER_CALL_RECEIVED,
          missedCallMessagingMessageContent: phoneNumberConfig?.missedCallMessaging?.messageContent || '',
        });
      }
    });

    effect(() => {
      const smsSettingChanges = this.smsSettingSelected();
      const callSettingsChanges = this.callSettingFormValueChanges();
      if (callSettingsChanges || smsSettingChanges) {
        const hasChanges = this.callSettingsForm.dirty || this.smsSettingForm.dirty;
        this.hasUnsavedChanges.set(hasChanges);
      }
    });

    effect(() => {
      const sendSMSSelected = this.sendSMSSelected();
      if (sendSMSSelected) {
        missedCallMessagingMessageContent.enable();
      }
    });

    effect(() => {
      this.unsavedGuard.notifyStateChanged(this.hasUnsavedChanges());
    });
  }

  private getCallSettingSelection(phoneConfig: PhoneNumberConfig | null): CALL_SETTING_SELECTION {
    if (this.useBroadlyTwilio()) {
      switch (true) {
        case phoneConfig?.voiceAi?.enabled:
          return CALL_SETTING_SELECTION.VOICE_AI;
        case phoneConfig?.callForwarding?.enabled:
          return CALL_SETTING_SELECTION.FORWARD_CALL;
        default:
          if (
            phoneConfig?.missedCallMessaging.trigger === MissedCallTrigger.MISSED_CALL_TRIGGER_FORWARDED_CALL_MISSED
          ) {
            return CALL_SETTING_SELECTION.BROADLY;
          }
          return CALL_SETTING_SELECTION.END_CALL;
      }
    } else {
      switch (true) {
        case phoneConfig?.voiceAi?.enabled:
          return CALL_SETTING_SELECTION.VOICE_AI;
        case phoneConfig?.callForwarding?.enabled:
          return CALL_SETTING_SELECTION.FORWARD_CALL;
        default:
          return CALL_SETTING_SELECTION.END_CALL;
      }
    }
  }

  private getAssistant(ownerId: string, assistantId: string) {
    const namespace =
      this.smsConfig.ownerType === OwnerType.OWNER_TYPE_ACCOUNT_GROUP
        ? { accountGroupNamespace: { accountGroupId: ownerId } }
        : { partnerNamespace: { partnerId: ownerId } };
    return this.assistantApiService
      .getAssistant({
        id: assistantId,
        namespace: namespace,
      })
      .pipe(
        catchError((err) => {
          console.error('unable to get assistant: ', err);
          return of({} as GetAssistantResponse);
        }),
        map((response) => response.assistant),
      );
  }

  private getConnection(assistantID: string, connectionId: string) {
    return this.namespaceConfig$.pipe(
      map((config) => config.namespace),
      switchMap((namespace) => this.assistantService.listConnectionsForAssistant(namespace, assistantID)),
      map(
        (aiConnections: AiConnection[]) => aiConnections.find((conn) => conn.connection?.id === connectionId) || null,
      ),
      catchError((err) => {
        console.error('Unable to find connection: ', err);
        return of(null);
      }),
    );
  }

  private async updateReceptionistConnection(
    receptionist: Assistant,
    aiConnection: AiConnection,
    isAssociated: boolean,
  ): Promise<void> {
    await firstValueFrom(
      this.assistantApiService.setAssistantConnections({
        assistantKey: {
          id: receptionist.id,
          namespace: receptionist.namespace,
        },
        associationStates: !aiConnection.connection?.isConnectionLocked
          ? [
              {
                connectionKey: {
                  id: aiConnection.connection?.id,
                  namespace: aiConnection.connection?.namespace,
                  connectionType: aiConnection.connection?.connectionType,
                },
                isAssociated: isAssociated,
              },
            ]
          : [],
      }),
    );
  }

  protected getSendMessageTrigger(): MissedCallTrigger {
    switch (true) {
      case this.voiceAiSelected() || this.endCallSelected():
        return MissedCallTrigger.MISSED_CALL_TRIGGER_CALL_RECEIVED;
      case this.broadlySelected():
        return MissedCallTrigger.MISSED_CALL_TRIGGER_FORWARDED_CALL_MISSED;
      default:
        return (
          this.callSettingsForm.controls['missedCallMessagingTrigger'].value ||
          MissedCallTrigger.MISSED_CALL_TRIGGER_UNSPECIFIED
        );
    }
  }

  private markAsSaved() {
    this.smsSettingForm.markAsPristine();
    this.callSettingsForm.markAsPristine();
    this.hasUnsavedChanges.set(false);
  }

  async handleSaveClicked(): Promise<void> {
    this.submitting.set(true);

    try {
      const newConfig = {
        voiceAi: {
          enabled: this.voiceAiSelected(),
        },
        callForwarding: {
          enabled: this.callForwardingSelected(),
          phoneNumber: this.callSettingsForm.controls['callForwardingNumber'].value || '',
        },
        missedCallMessaging: {
          enabled: this.callSettingsForm.controls['missedCallMessagingEnabled'].value || false,
          trigger: this.getSendMessageTrigger(),
          messageContent: this.callSettingsForm.controls['missedCallMessagingMessageContent'].value || '',
        },
        endCall: {
          enabled: (this.endCallSelected() && this.callSettingsForm.controls['endCallMessagingEnabled'].value) || false,
          messageContent: this.callSettingsForm.controls['endCallMessage'].value || '',
        },
      };

      const phoneConfigPromise = firstValueFrom(this.configService.upsertPhoneNumberConfig(newConfig));
      let chatConnectionPromise: Promise<void> | undefined;
      if (this.smsSettingForm.dirty) {
        chatConnectionPromise = this.updateReceptionistConnection(
          this.chatReceptionist()!,
          this.chatConnection()!,
          this.chatReceptionistSelected(),
        );
      }
      await Promise.all([phoneConfigPromise, chatConnectionPromise]);
      this.snackbarService.openSuccessSnack('INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.PHONE_CONFIG_SAVED');
      this.markAsSaved();
      this.saved.emit(newConfig);
    } catch {
      this.snackbarService.openErrorSnack('INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.PHONE_CONFIG_ERROR');
    } finally {
      this.submitting.set(false);
    }
  }
}
