<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button [useHistory]="true" />
    </glxy-page-nav>
    <glxy-page-title>
      {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.TITLE' | translate }}
    </glxy-page-title>
  </glxy-page-toolbar>
  <glxy-page-wrapper [widthPreset]="'narrow'">
    <div class="config-cards">
      <mat-card>
        <mat-card-content>
          <span class="your-phone">
            {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.YOUR_NUMBER' | translate }}
            <span>
              {{ phoneNumber() || '-' | glxyPhoneNumber }}
            </span>
          </span>
        </mat-card-content>
      </mat-card>
      @if (showSMSBetaInfo) {
        <glxy-alert>
          {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.PARTNER_SMS_BETA_MESSAGE' | translate }}
        </glxy-alert>
      }
      <mat-card>
        <mat-card-header>
          <mat-card-title
            >{{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.INCOMING_CALL_HANDLE_TITLE' | translate }}
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="callSettingsForm">
            <glxy-form-field [bottomSpacing]="'small'">
              <mat-select formControlName="callSettingOption">
                <mat-option [value]="callSettingSelection.END_CALL"
                  >{{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.CALL_OPTIONS.NOTHING' | translate }}
                </mat-option>
                @if (useBroadlyTwilio()) {
                  <mat-option [value]="callSettingSelection.BROADLY">
                    {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.CALL_OPTIONS.USE_BROADLY' | translate }}
                  </mat-option>
                }
                @if ((showVoiceAIOptions$ | async) === true && voiceConnection()) {
                  <mat-option [value]="callSettingSelection.VOICE_AI"
                    >{{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.CALL_OPTIONS.VOICE_AI' | translate }}
                  </mat-option>
                }
                <mat-option [value]="callSettingSelection.FORWARD_CALL"
                  >{{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.CALL_OPTIONS.FORWARD_CALL' | translate }}
                </mat-option>
              </mat-select>
            </glxy-form-field>
            @switch (true) {
              @case (broadlySelected()) {
                @if ((showMissedCallMessagingOptions$ | async) === true) {
                  <glxy-form-field [bottomSpacing]="'small'" class="top-spacing">
                    <mat-slide-toggle formControlName="missedCallMessagingEnabled">
                      <span class="follow-up-text">
                        {{
                          'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.ENABLED_LABEL_MISSED_CALL'
                            | translate
                        }}
                      </span>
                    </mat-slide-toggle>
                  </glxy-form-field>
                  @if (MissedCallMessagingEnabled?.value) {
                    <div class="sub-options">
                      <glxy-form-field [bottomSpacing]="'none'">
                        <glxy-label
                          >{{
                            'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.MESSAGE_CONTENT_LABEL'
                              | translate
                          }}
                        </glxy-label>
                        <textarea
                          matInput
                          formControlName="missedCallMessagingMessageContent"
                          placeholder="{{
                            'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.CALL_FORWARDING_EXAMPLE_FOLLOW_UP_MESSAGE'
                              | translate
                          }}"
                        ></textarea>
                      </glxy-form-field>
                    </div>
                  }
                }
              }
              @case (voiceAiSelected()) {
                <!--Voice AI-->
                <ai-assistant-reference-card [assistant]="voiceReceptionist()"></ai-assistant-reference-card>

                @if ((showMissedCallMessagingOptions$ | async) === true) {
                  <glxy-form-field [bottomSpacing]="'small'" class="top-spacing">
                    <mat-slide-toggle formControlName="missedCallMessagingEnabled">
                      <span class="follow-up-text">
                        {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.ENABLED_LABEL' | translate }}
                      </span>
                    </mat-slide-toggle>
                  </glxy-form-field>
                  @if (MissedCallMessagingEnabled?.value) {
                    <div class="sub-options">
                      <glxy-form-field [bottomSpacing]="'none'">
                        <glxy-label
                          >{{
                            'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.MESSAGE_CONTENT_LABEL_WHEN_CALL_ENDS'
                              | translate
                          }}
                        </glxy-label>
                        <textarea
                          matInput
                          formControlName="missedCallMessagingMessageContent"
                          placeholder="{{
                            'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.VOICE_AI_EXAMPLE_FOLLOW_UP_MESSAGE'
                              | translate
                          }}"
                        ></textarea>
                      </glxy-form-field>
                    </div>
                  }
                }
              }
              @case (callForwardingSelected()) {
                <!-- forward the call -->
                <glxy-form-field prefixIcon="phone" [bottomSpacing]="'none'">
                  <glxy-label
                    >{{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.CALL_FORWARDING.FORWARDING_NUMBER_LABEL' | translate }}
                    <mat-icon
                      [glxyTooltip]="
                        'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.CALL_FORWARDING.FORWARD_TO_TOOLTIP' | translate
                      "
                      [highContrast]="false"
                      class="info-icon"
                    >
                      info_outline
                    </mat-icon>
                  </glxy-label>
                  <input matInput type="text" formControlName="callForwardingNumber" />
                  @if (
                    callSettingsForm.controls.callForwardingNumber.dirty &&
                    callSettingsForm.controls.callForwardingNumber.enabled &&
                    callSettingsForm.controls.callForwardingNumber.invalid
                  ) {
                    <glxy-error
                      >{{
                        'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.CALL_FORWARDING.FORWARDING_NUMBER_INVALID' | translate
                      }}
                    </glxy-error>
                  } @else {
                    <glxy-hint
                      >{{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.CALL_FORWARDING.FORWARDING_NUMBER_HINT' | translate }}
                    </glxy-hint>
                  }
                </glxy-form-field>
                @if ((showMissedCallMessagingOptions$ | async) === true) {
                  <div>
                    <glxy-form-field [bottomSpacing]="'small'" class="top-spacing">
                      <mat-slide-toggle formControlName="missedCallMessagingEnabled">
                        <span class="follow-up-text">
                          {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.ENABLED_LABEL' | translate }}
                        </span>
                      </mat-slide-toggle>
                    </glxy-form-field>
                    @if (MissedCallMessagingEnabled?.value) {
                      <div class="sub-options">
                        <!-- Missed call messaging trigger -->
                        <glxy-form-field>
                          <mat-radio-group formControlName="missedCallMessagingTrigger">
                            <mat-radio-button [value]="missedCallTrigger.MISSED_CALL_TRIGGER_CALL_RECEIVED">
                              {{
                                'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.TRIGGER_LABELS.IMMEDIATE'
                                  | translate
                              }}
                              <mat-icon
                                [glxyTooltip]="
                                  'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.IMMEDIATE_MESSAGE_TOOLTIP'
                                    | translate
                                "
                                [highContrast]="false"
                                class="info-icon"
                              >
                                info_outline
                              </mat-icon>
                            </mat-radio-button>
                            <mat-radio-button [value]="missedCallTrigger.MISSED_CALL_TRIGGER_FORWARDED_CALL_MISSED">
                              {{
                                'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.TRIGGER_LABELS.FORWARDED_CALL_MISSED'
                                  | translate
                              }}
                              <mat-icon
                                [glxyTooltip]="
                                  'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.FORWARDED_CALL_MISSED_TOOLTIP'
                                    | translate
                                "
                                [highContrast]="false"
                                class="info-icon"
                              >
                                info_outline
                              </mat-icon>
                              <extended>
                                {{
                                  'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.FORWARDED_CALL_MISSED_HINT'
                                    | translate
                                }}
                              </extended>
                            </mat-radio-button>
                          </mat-radio-group>
                        </glxy-form-field>

                        <!-- Missed call messaging message content -->
                        <glxy-form-field [bottomSpacing]="'none'">
                          <glxy-label
                            >{{
                              'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.MESSAGE_CONTENT_LABEL'
                                | translate
                            }}
                          </glxy-label>
                          <textarea
                            matInput
                            formControlName="missedCallMessagingMessageContent"
                            placeholder="{{
                              'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.CALL_FORWARDING_EXAMPLE_FOLLOW_UP_MESSAGE'
                                | translate
                            }}"
                          ></textarea>
                        </glxy-form-field>
                      </div>
                    }
                  </div>
                }
              }
              @default {
                <!--                end call-->
                <glxy-form-field [bottomSpacing]="'small'" class="top-spacing">
                  <mat-slide-toggle formControlName="endCallMessagingEnabled">
                    <span class="follow-up-text">
                      {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.END_CALL_MESSAGE.PLAY_VOICE_MESSAGE' | translate }}
                    </span>
                  </mat-slide-toggle>
                </glxy-form-field>
                @if (EndCallMessagingEnabled?.value) {
                  <div class="sub-options">
                    <glxy-form-field [bottomSpacing]="'none'">
                      <glxy-label>
                        {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.END_CALL_MESSAGE.LABEL' | translate }}
                      </glxy-label>
                      <textarea
                        matInput
                        formControlName="endCallMessage"
                        placeholder="{{
                          'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.END_CALL_MESSAGE.PLACEHOLDER' | translate
                        }}"
                      ></textarea>
                    </glxy-form-field>
                  </div>
                }
                @if ((showMissedCallMessagingOptions$ | async) === true) {
                  <glxy-form-field [bottomSpacing]="'small'" class="top-spacing">
                    <mat-slide-toggle formControlName="missedCallMessagingEnabled">
                      <span class="follow-up-text">
                        {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.ENABLED_LABEL' | translate }}
                      </span>
                    </mat-slide-toggle>
                  </glxy-form-field>
                  @if (MissedCallMessagingEnabled?.value) {
                    <div class="sub-options">
                      <glxy-form-field [bottomSpacing]="'none'">
                        <glxy-label
                          >{{
                            'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.MESSAGE_CONTENT_LABEL'
                              | translate
                          }}
                        </glxy-label>
                        <textarea
                          matInput
                          formControlName="missedCallMessagingMessageContent"
                          placeholder="{{
                            'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.MISSED_CALL_MESSAGING.CALL_FORWARDING_EXAMPLE_FOLLOW_UP_MESSAGE'
                              | translate
                          }}"
                        ></textarea>
                      </glxy-form-field>
                    </div>
                  }
                }
              }
            }
          </form>
        </mat-card-content>
      </mat-card>

      <!-- SMS settings -->
      @if (chatConnection()) {
        <mat-card>
          <mat-card-header>
            <mat-card-title>
              {{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.INCOMING_SMS_HANDLE_TITLE' | translate }}
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <glxy-form-field [bottomSpacing]="'small'">
              <mat-select [formControl]="smsSettingForm">
                <mat-option [value]="smsSettingSelection.NOTHING"
                  >{{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.SMS_OPTIONS.NOTHING' | translate }}
                </mat-option>
                <mat-option [value]="smsSettingSelection.CHAT_RECEPTIONIST"
                  >{{ 'INBOX.SETTINGS.PHONE_NUMBER_SETTINGS.SMS_OPTIONS.AI_REPLY' | translate }}
                </mat-option>
              </mat-select>
            </glxy-form-field>
            @if (chatReceptionistSelected()) {
              <ai-assistant-reference-card [assistant]="chatReceptionist()"></ai-assistant-reference-card>
            }
          </mat-card-content>
        </mat-card>
      }
    </div>
    <glxy-sticky-footer>
      <button
        mat-flat-button
        color="primary"
        [disabled]="callSettingsForm.invalid || submitting() || !hasUnsavedChanges()"
        (click)="handleSaveClicked()"
      >
        <glxy-button-loading-indicator [isLoading]="submitting()"
          >{{ 'COMMON.ACTION_LABELS.SAVE' | translate }}
        </glxy-button-loading-indicator>
      </button>
    </glxy-sticky-footer>
  </glxy-page-wrapper>
</glxy-page>
