import { Component, computed, inject, Input, signal, WritableSignal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { A2PRegistrationService, LearnMoreModalComponent, RegistrationStage, SmsConfigService } from '@galaxy/sms';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TranslateModule } from '@ngx-translate/core';
import { MatIcon } from '@angular/material/icon';
import { MatCard, MatCardAvatar, MatCardContent, MatCardHeader, MatCardTitle } from '@angular/material/card';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { combineLatest, firstValueFrom, forkJoin, map, of, switchMap, throwError } from 'rxjs';
import { catchError, shareReplay, tap } from 'rxjs/operators';

import { MatButton } from '@angular/material/button';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { Configuration, InboxApiService } from '@vendasta/conversation';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  InboxService,
  PARTNER_ID_TOKEN,
} from '../../../../../core/src/';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { toSignal } from '@angular/core/rxjs-interop';
import { InboxAiBadgeComponent } from '../../../inbox-ai/inbox-ai-badge/inbox-ai-badge.component';
import { PhoneNumberConfigInterface } from '@vendasta/smsv2';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { AssistantType } from '@vendasta/ai-assistants';
import {
  AiAssistantReferenceCardComponent,
  AiAssistantService,
  ConnectionType,
  SMSConnectionID,
  VoiceConnectionID,
} from '@galaxy/ai-assistant';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPhoneNumberPipe } from '@vendasta/galaxy/pipes/src/phone-number/phone-number.pipe';

enum RegistrationTitle {
  NotApproved = 'Required information for US-based businesses',
  ApprovedStatus = '',
}

enum RegistrationContent {
  NotApproved = 'To send SMS messages to customers, more details about your business are required. Update your business info to send SMS messages.',
  ApprovedStatus = 'Registered for SMS.',
}

type SmsRegistrationCardData = {
  statusBadgeText: 'Setup required' | 'Rejected' | 'In progress' | 'Active';
  statusBadgeColor: 'red' | 'blue' | 'green';
  buttonText: 'Register now' | 'Business info' | 'Update business info' | 'See details' | '';
  buttonType: 'primary' | 'basic' | '';
  title: RegistrationTitle.ApprovedStatus | RegistrationTitle.NotApproved;
  content: RegistrationContent.ApprovedStatus | RegistrationContent.NotApproved;
};

type SMSNumberCardData = {
  statusBadgeText: 'Active';
  statusBadgeColor: 'green';
};

const RegistrationStageToCardData: Record<RegistrationStage, SmsRegistrationCardData> = {
  [RegistrationStage.NotSubmitted]: {
    statusBadgeText: 'Setup required',
    statusBadgeColor: 'red',
    buttonText: 'Register now',
    buttonType: 'primary',
    title: RegistrationTitle.NotApproved,
    content: RegistrationContent.NotApproved,
  },
  [RegistrationStage.ReviewingInfo]: {
    statusBadgeText: 'In progress',
    statusBadgeColor: 'blue',
    buttonText: 'Business info',
    buttonType: 'basic',
    title: RegistrationTitle.NotApproved,
    content: RegistrationContent.NotApproved,
  },
  [RegistrationStage.DataError]: {
    statusBadgeText: 'Rejected',
    statusBadgeColor: 'red',
    buttonText: 'Update business info',
    buttonType: 'primary',
    title: RegistrationTitle.NotApproved,
    content: RegistrationContent.NotApproved,
  },
  [RegistrationStage.CampaignError]: {
    statusBadgeText: 'Rejected',
    statusBadgeColor: 'red',
    buttonText: 'See details',
    buttonType: 'primary',
    title: RegistrationTitle.NotApproved,
    content: RegistrationContent.NotApproved,
  },
  // message campaign registration approved
  [RegistrationStage.RegistrationComplete]: {
    statusBadgeText: 'Active',
    statusBadgeColor: 'green',
    buttonText: '',
    buttonType: '',
    title: RegistrationTitle.ApprovedStatus,
    content: RegistrationContent.ApprovedStatus,
  },
};

const SMSNumberToCardData: SMSNumberCardData = {
  statusBadgeText: 'Active',
  statusBadgeColor: 'green',
};

@Component({
  selector: 'inbox-sms-config-card',
  imports: [
    CommonModule,
    GalaxyPageModule,
    TranslateModule,
    MatIcon,
    MatCardHeader,
    MatCard,
    GalaxyBadgeModule,
    MatCardTitle,
    MatButton,
    MatCardContent,
    MatCardAvatar,
    MatSlideToggle,
    InboxAiBadgeComponent,
    GalaxyAvatarModule,
    AiAssistantReferenceCardComponent,
    GalaxyTooltipModule,
    GalaxyPhoneNumberPipe,
    RouterLink,
    GalaxyLoadingSpinnerModule,
  ],
  templateUrl: './sms-config-card.component.html',
  styleUrls: ['./sms-config-card.component.scss'],
})
export class SmsConfigCardComponent {
  private readonly smsRegistrationService = inject(A2PRegistrationService);
  private readonly inboxApiService = inject(InboxApiService);
  private readonly hostAppInterface = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN);
  private readonly inboxService = inject(InboxService);
  private snackbarService = inject(SnackbarService);
  private readonly configService = inject(SmsConfigService);
  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  protected readonly AssistantType = AssistantType;

  protected readonly $showVoiceReceptionist = toSignal(this.inboxService.isVoiceAiFeatureEnabled$, {
    initialValue: false,
  });

  protected readonly associatedAssistant = toSignal(
    combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
      switchMap(([partnerId, accountGroupId]) => {
        const smsConnections$ = this.aiAssistantService.listAllAssistantsAssociatedToConnection(
          partnerId,
          accountGroupId,
          SMSConnectionID,
          ConnectionType.SMS,
        );
        const voiceConnections$ = this.aiAssistantService.listAllAssistantsAssociatedToConnection(
          partnerId,
          accountGroupId,
          VoiceConnectionID,
          ConnectionType.Voice,
        );

        return forkJoin([smsConnections$, voiceConnections$]);
      }),
      catchError(() => of([], [])),
      map(([smsAssistants, voiceAssistants]) => {
        const assistants = [...(smsAssistants ?? []), ...(voiceAssistants ?? [])];
        if (assistants?.length > 0) {
          return assistants;
        }
        return [];
      }),
    ),
  );

  protected phoneNumberConfig = signal<PhoneNumberConfigInterface | null>(null);

  @Input({ required: true }) set configuration(value: Configuration) {
    this.$smsAIResponderEnabled.set(value?.aiConfiguration?.smsResponderEnabled || false);
  }

  readonly isPartnerCenter = this.inboxService.isPartnerCenter;

  constructor(
    private dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    firstValueFrom(this.configService.phoneNumberConfig$)
      .then((config) => this.phoneNumberConfig.set(config))
      .catch((error) => {
        if (error.status !== 404) {
          this.snackbarService.openErrorSnack('Error fetching phone number config');
        }
        this.phoneNumberConfig.set({ callForwarding: { enabled: false } });
      });
  }

  protected readonly $smsAIResponderEnabled: WritableSignal<boolean> = signal(false);
  protected readonly $showToggle = toSignal(
    this.inboxService.isSMSChannelAvailableForOrg$.pipe(map((smsChannelAvailable) => smsChannelAvailable)),
    { initialValue: false },
  );

  protected readonly $isSmsNumberLoading: WritableSignal<boolean> = signal(true);
  readonly $smsNumber = toSignal(this.inboxService.SMSNumber$.pipe(tap(() => this.$isSmsNumberLoading.set(false))));

  readonly $registrationStage = toSignal(this.smsRegistrationService.registrationStage$, {
    initialValue: RegistrationStage.NotSubmitted,
  });

  $country = toSignal(
    this.smsRegistrationService.getOwnerLocation$().pipe(
      map((location) => location.country),
      shareReplay({ bufferSize: 1, refCount: false }),
    ),
  );

  $smsCardData = computed(() => {
    return this.$country() === 'CA' ? SMSNumberToCardData : RegistrationStageToCardData[this.$registrationStage()];
  });

  showPopover = false;

  openLearnMoreModal(): void {
    this.dialog.open(LearnMoreModalComponent, { width: '600px' });
  }

  redirectToSMSRegistration(): void {
    this.router.navigate(['./sms-registration'], { relativeTo: this.route });
  }

  async handleToggleChange(): Promise<void> {
    try {
      const wc = this.$smsAIResponderEnabled();
      const namespace$ = this.hostAppInterface.getNamespace();

      const resp = await firstValueFrom(
        namespace$.pipe(
          switchMap((namespace) => {
            return this.inboxApiService.upsertProductFeature({
              productFeature: {
                aiSmsResponderEnabled: !wc,
              },
              fieldMask: {
                paths: ['ai_sms_responder_enabled'],
              },
              subjectParticipant: {
                participantType: namespace.namespaceType,
                internalParticipantId: namespace.id,
              },
            });
          }),
          catchError((err) => throwError(() => err)),
        ),
      );

      this.$smsAIResponderEnabled.set(resp.productFeature?.aiSmsResponderEnabled || false);
      this.snackbarService.openSuccessSnack('INBOX.SETTINGS.SMS_MESSAGES.CARD.AI_RESPONSES_UPDATED');
    } catch (e) {
      this.snackbarService.openErrorSnack('INBOX.SETTINGS.SMS_MESSAGES.CARD.AI_RESPONSES_UPDATE_ERROR');
    }
  }

  async claimPhoneNumber(): Promise<void> {
    try {
      this.$isSmsNumberLoading.set(true);
      await firstValueFrom(this.configService.claimPhoneNumber());

      this.inboxService.refreshSMSNumber();

      this.snackbarService.openSuccessSnack('INBOX.SETTINGS.SMS_MESSAGES.CARD.PHONE_NUMBER_CLAIM_SUCCESS');
    } catch (e) {
      this.$isSmsNumberLoading.set(false);
      this.snackbarService.openErrorSnack('INBOX.SETTINGS.SMS_MESSAGES.CARD.PHONE_NUMBER_CLAIM_ERROR');
    }
  }
}
