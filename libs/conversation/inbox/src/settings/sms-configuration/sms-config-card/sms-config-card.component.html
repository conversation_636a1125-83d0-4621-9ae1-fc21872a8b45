<ng-container *ngIf="$smsCardData() as smsCardData">
  <mat-card appearance="outlined">
    <mat-card-header>
      <mat-icon mat-card-avatar class="phone"></mat-icon>
      <mat-card-title class="title">
        {{ 'INBOX.SETTINGS.SMS_MESSAGES.CARD.PHONE_AND_SMS' | translate }}
      </mat-card-title>
      @if ($showToggle()) {
        <inbox-ai-badge></inbox-ai-badge>
      }
      @if ($smsNumber()) {
        <div class="badge">
          <glxy-badge [color]="smsCardData.statusBadgeColor">
            {{ smsCardData.statusBadgeText }}
          </glxy-badge>
        </div>
      }
    </mat-card-header>
    <mat-card-content class="content-container">
      <div class="content-title">
        @if ($isSmsNumberLoading()) {
          <div class="loading-container">
            <glxy-loading-spinner></glxy-loading-spinner>
          </div>
        } @else if ($smsNumber()) {
          <div class="phone-number-container">
            <div>
              <span>
                {{ 'INBOX.SETTINGS.SMS_MESSAGES.CARD.ASSIGNED_NUMBER_TITLE' | translate }}:
                <span class="phone-number">
                  {{ $smsNumber() || '-' | glxyPhoneNumber }}
                  <mat-icon
                    class="info-icon"
                    [glxyTooltip]="'INBOX.SETTINGS.SMS_MESSAGES.CARD.ASSIGNED_NUMBER_DESCRIPTION' | translate"
                    [tooltipTitle]="'INBOX.SETTINGS.SMS_MESSAGES.CARD.ASSIGNED_NUMBER_TITLE' | translate"
                    [highContrast]="false"
                  >
                    info_outline
                  </mat-icon>
                </span>
              </span>
            </div>

            @if (phoneNumberConfig()) {
              <button mat-stroked-button [routerLink]="'./phone-configuration'">
                {{ 'INBOX.SETTINGS.SMS_MESSAGES.CARD.CONFIGURE' | translate }}
              </button>
            }
          </div>
        } @else if (isPartnerCenter) {
          <button mat-flat-button color="primary" (click)="claimPhoneNumber()">
            {{ 'INBOX.SETTINGS.SMS_MESSAGES.CARD.CLAIM_PHONE_NUMBER' | translate }}
          </button>
        }
      </div>
      @if ($country() === 'US') {
        @if (smsCardData.title !== '') {
          <div class="content-title">
            <div>{{ smsCardData.title }}</div>
          </div>
        }
        <div class="description">
          {{ smsCardData.content }}
          <a (click)="openLearnMoreModal()">{{ 'INBOX.SETTINGS.SMS_MESSAGES.CARD.LEARN_MORE' | translate }}</a>
        </div>
        @if (smsCardData.statusBadgeText !== 'Active') {
          <div>
            <button mat-raised-button color="{{ smsCardData.buttonType }}" (click)="redirectToSMSRegistration()">
              {{ smsCardData.buttonText }}
            </button>
          </div>
        }
      }
      @if (associatedAssistant()?.length > 0) {
        <div class="ai-assistant-container">
          <span class="ai-assistant-title">{{ 'INBOX.SETTINGS.AI_ASSISTANT.TITLE' | translate }}</span>
          @for (assistant of associatedAssistant() || []; track assistant) {
            @if (assistant.type !== AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST || $showVoiceReceptionist()) {
              <ai-assistant-reference-card [assistant]="assistant"></ai-assistant-reference-card>
            }
          }
        </div>
      }
    </mat-card-content>
  </mat-card>
</ng-container>
