@let assistant = systemAssistant();
<glxy-page [pagePadding]="false" [mobileMaxWidth]="0">
  <glxy-page-toolbar>
    <glxy-page-title>
      <span>{{ assistant?.name }}</span>
      <mat-icon
        [glxyTooltip]="'INBOX.CHAT.AI_INFO' | translate"
        [highContrast]="false"
        [tooltipTitle]="
          'INBOX.CHAT.AI_INFO_TITLE' | translate: { assistantName: assistant?.name || DEFAULT_OPENAI_BOT_NAME }
        "
        class="info-icon"
      >
        info_outline
      </mat-icon>
      <glxy-badge class="hide-on-mobile beta-badge" color="blue-solid">
        {{ 'INBOX.HEADER.BETA' | translate }}
      </glxy-badge>
    </glxy-page-title>
    <glxy-page-actions class="inbox-actions">
      <button mat-icon-button class="menu-icon close" (click)="closeModal()">
        <mat-icon>close</mat-icon>
      </button>
    </glxy-page-actions>
  </glxy-page-toolbar>
  <inbox-ai-chat></inbox-ai-chat>
</glxy-page>
