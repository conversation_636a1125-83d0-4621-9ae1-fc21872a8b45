import { Component, inject } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { ConversationUIModule } from '../../../../ui/src/conversation-ui.module';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyNavLayoutModule } from '@vendasta/galaxy/nav-layout';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPageNavModule } from '@vendasta/galaxy/page-nav';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { CommonModule } from '@angular/common';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { INBOX_SERVICE_PROVIDERS } from '../../../../core/src/lib/conversation-core.module';
import { AiAssistantService } from '@galaxy/ai-assistant';
import { toSignal } from '@angular/core/rxjs-interop';
import { DEFAULT_OPENAI_BOT_NAME } from '../../../../core/src/lib/inbox.constants';

@Component({
  selector: 'inbox-ai-overlay',
  templateUrl: './inbox-ai-overlay.component.html',
  styleUrls: ['./inbox-ai-overlay.component.scss'],
  imports: [
    CommonModule,
    RouterModule,
    ConversationUIModule,
    MatIconModule,
    GalaxyNavLayoutModule,
    GalaxyPageModule,
    GalaxyPageNavModule,
    MatButtonModule,
    TranslateModule,
    GalaxyBadgeModule,
    GalaxyTooltipModule,
    GalaxyLoadingSpinnerModule,
  ],
  providers: [INBOX_SERVICE_PROVIDERS],
})
export class AIChatInboxOverlayComponent {
  private readonly router = inject(Router);
  private readonly aiAssistantService = inject(AiAssistantService);
  readonly systemAssistant = toSignal(this.aiAssistantService.getSystemAssistant());
  readonly DEFAULT_OPENAI_BOT_NAME = DEFAULT_OPENAI_BOT_NAME;

  closeModal(): void {
    this.close();
  }
  private close(): void {
    this.router.navigate([{ outlets: { inbox: null } }], {
      queryParamsHandling: 'merge',
    });
  }
}
