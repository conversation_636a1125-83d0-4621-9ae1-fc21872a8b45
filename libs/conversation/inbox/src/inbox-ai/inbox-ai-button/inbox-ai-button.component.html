<ng-container *ngIf="isMobile; else desktop">
  <atlas-menu-item (click)="toggle()">
    <span class="ai-inbox-menu">
      <mat-icon svgIcon="galaxy-ai-icon" class="ai-icon"></mat-icon>
      {{ 'INBOX.HEADER.INBOX_AI_TITLE' | translate }}
    </span>
  </atlas-menu-item>
</ng-container>

<ng-template #desktop>
  <atlas-item
    interactable
    matTooltip="{{ 'INBOX.HEADER.TOOLTIP' | translate: { assistantName: getAssistantName() } }}"
    (click)="toggle()"
    [customClass]="buttonSelected$ | async"
  >
    <div class="ai-button">
      <mat-icon svgIcon="galaxy-ai-icon" class="ai-icon"></mat-icon>
    </div>
  </atlas-item>
</ng-template>
