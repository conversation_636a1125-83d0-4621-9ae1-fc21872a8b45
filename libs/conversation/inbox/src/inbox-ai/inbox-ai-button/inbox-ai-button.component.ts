import { Component, Input, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { AtlasModule } from '@galaxy/atlas';
import { ViewModeService } from '../../../../core/src/lib/view-mode.service';
import { Router } from '@angular/router';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { map } from 'rxjs';
import { AiAssistantService } from '@galaxy/ai-assistant';
import { toSignal } from '@angular/core/rxjs-interop';
import { DEFAULT_OPENAI_BOT_NAME } from '../../../../core/src/lib/inbox.constants';
@Component({
  selector: 'inbox-ai-button',
  templateUrl: 'inbox-ai-button.component.html',
  styleUrls: ['./inbox-ai-button.component.scss'],
  imports: [CommonModule, TranslateModule, MatIconModule, MatTooltipModule, AtlasModule],
})
export class InboxAIButtonComponent {
  @Input() isMobile = false;
  private readonly router = inject(Router);
  private readonly aiAssistantService = inject(AiAssistantService);
  readonly systemAssistant = toSignal(this.aiAssistantService.getSystemAssistant());

  constructor(
    private readonly viewModeService: ViewModeService,
    private _: GalaxyAiIconService,
  ) {}

  readonly buttonSelected$ = this.viewModeService.isAIOpen$.pipe(
    map((isOpen) => (isOpen ? 'atlas-navbar__item-selected' : '')),
  );

  getAssistantName(): string {
    return this.systemAssistant()?.name || DEFAULT_OPENAI_BOT_NAME;
  }

  toggle(): void {
    this.viewModeService.toggleAIConversationOverlayOpen();
  }
}
