import { Component, Inject, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import {
  AbstractControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
// FIXME: Remove relative import
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { RichDataPaymentMethods as PaymentMethods } from '@vendasta/account-group';
import {
  CompetitorsInferenceResponse,
  EACUsageTrackingService,
  EasyAccountCreateService,
  MarketsService,
  NapDataInferenceResponse,
  SocialUrlsInferenceResponse,
  Tooltips,
  UsageTracking,
} from '@vendasta/businesses/easy-account-create';
import { ItemFactory, fixControlsForFormArray, phoneValidator } from '@vendasta/forms';
import { SubscriptionList } from '@vendasta/rx-utils';
import { BehaviorSubject, Observable, ReplaySubject, combineLatest } from 'rxjs';
import { filter, map, switchMap, switchMapTo } from 'rxjs/operators';
import { nullDisplayFunction } from '../../display';
import { MARKET_SERVICE_TOKEN } from '../../injection';
import { runOnNextKeyframe } from '../../inputs';
import { Validator } from '../../validator';
import { CompetitorBusinessInputs, FullProfileBusinessInputs, FullProfilePanelConfig } from './inputs';

interface PaymentMethod {
  code: PaymentMethods;
  name: string;
}

interface Competitor {
  name: string;
  url: string;
  placeId: string;
}

const AvailablePaymentMethods: PaymentMethod[] = [
  { code: PaymentMethods.AMERICAN_EXPRESS, name: 'American Express' },
  { code: PaymentMethods.ANDROID_PAY, name: 'Google Pay' },
  { code: PaymentMethods.APPLE_PAY, name: 'Apple Pay' },
  { code: PaymentMethods.CASH, name: 'Cash' },
  { code: PaymentMethods.CHECK, name: 'Check' },
  { code: PaymentMethods.DEBIT, name: 'Debit' },
  { code: PaymentMethods.DINERS_CLUB, name: 'Diners Club' },
  { code: PaymentMethods.DISCOVER, name: 'Discover' },
  { code: PaymentMethods.FINANCING, name: 'Financing' },
  { code: PaymentMethods.MASTERCARD, name: 'MasterCard' },
  { code: PaymentMethods.PAYPAL, name: 'PayPal' },
  { code: PaymentMethods.SAMSUNG_PAY, name: 'Samsung Pay' },
  { code: PaymentMethods.STORE_CARD, name: 'Store Card' },
  { code: PaymentMethods.TRAVELERS_CHECK, name: 'Travelers Check' },
  { code: PaymentMethods.VISA, name: 'Visa' },
  { code: PaymentMethods.FINANCING, name: 'Financing' },
];

const GMB_DESCRIPTION_SUBMISSION_LIMIT = 750;

@Component({
  selector: 'eac-full-profile-panel',
  templateUrl: './full-profile-panel.component.html',
  styleUrls: ['./full-profile-panel.component.scss', '../../easy-account-create.scss'],
  standalone: false,
})
export class FullProfilePanelComponent implements OnInit, OnDestroy {
  @Input() set businessInfo(info: FullProfileBusinessInputs) {
    if (info) {
      runOnNextKeyframe(() => {
        this.businessInfo$$.next(info);
      });
      this.countryCode = info.countryCode;
    } else {
      this.countryCode = 'US';
    }
  }

  @Input() set partnerID(partnerID: string) {
    runOnNextKeyframe(() => {
      this.partnerID$$.next(partnerID);
    });
  }

  @Input() set competitorInfo(competitorInfo: CompetitorBusinessInputs) {
    if (competitorInfo) {
      runOnNextKeyframe(() => {
        this.competitorInfo$$.next(competitorInfo);
      });
    }
  }

  @Input() config: FullProfilePanelConfig;

  @Input() set inferredBusiness(v: NapDataInferenceResponse) {
    runOnNextKeyframe(() => {
      this.inferredBusiness$$.next(v);
    });
  }

  @Input() set inferring(v: boolean) {
    runOnNextKeyframe(() => {
      this.inferring$$.next(v);
    });
  }

  formGroup: UntypedFormGroup;
  hoursOfOperationEnabled = false;

  readonly tooltips = Tooltips;
  readonly nullDisplayFn = nullDisplayFunction;
  readonly longDescriptionLimit: number = GMB_DESCRIPTION_SUBMISSION_LIMIT;

  private readonly paymentMethodSearchTerm$$ = new BehaviorSubject('');
  private readonly businessInfo$$ = new ReplaySubject<FullProfileBusinessInputs>(1);
  private readonly competitorInfo$$ = new ReplaySubject<CompetitorBusinessInputs>(1);
  private accessibleMarketIds$: Observable<string[]>;
  private readonly subscriptions = SubscriptionList.new();
  filteredPaymentMethods$: Observable<PaymentMethod[]>;
  readonly socialUrlsInferring$ = this.easyAccountCreateService.socialUrlsInferring$;
  readonly competitorsInferring$ = this.easyAccountCreateService.competitorsInferring$;
  readonly selectedCompetitorsInferring$ = this.easyAccountCreateService.selectedCompetitorsInferring$;
  readonly competitorInfo$: Observable<CompetitorBusinessInputs> = this.competitorInfo$$.asObservable();

  private readonly inferredBusiness$$ = new ReplaySubject<NapDataInferenceResponse>(1);
  private inferredBusiness$ = this.inferredBusiness$$.pipe(filter((a) => !!a));
  private readonly partnerID$$ = new ReplaySubject<string>(1);
  private readonly inferring$$ = new ReplaySubject<boolean>(1);
  inferring$ = this.inferring$$.asObservable();

  private countryCode = 'US';

  CUSTOM_ERROR_MESSAGES = {
    lengthOfRepeatedField: (error: any): string => {
      return `Must be less than ${error.maxLength} characters across all ${error.friendlyName}, when joined with commas.`;
    },
  };

  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly easyAccountCreateService: EasyAccountCreateService,
    private readonly eacTrackingService: EACUsageTrackingService,
    @Inject(MARKET_SERVICE_TOKEN) private readonly markets: MarketsService,
  ) {}

  private static formStateWithDisabled(value?: any) {
    return { value: value, disabled: false };
  }

  ngOnInit(): void {
    this.formGroup = this.formBuilder.group({
      commonBusinessNames: this.formBuilder.array([[]]),
      hoursOfOperation: [[]],
      paymentMethods: [[]],
      description: [''],
      shortDescription: [''],
      landmark: [''],
      servicesOffered: this.formBuilder.array(
        [[]],
        Validators.compose([Validators.maxLength(15), totalLengthOfRepeatedFieldValidator(256, 'Services Offered')]),
      ),
      brandsCarried: this.formBuilder.array(
        [[]],
        Validators.compose([Validators.maxLength(15), totalLengthOfRepeatedFieldValidator(256, 'Brands Carried')]),
      ),
      competitorNames: this.formBuilder.array([[]]),
      selectedCompetitors: this.formBuilder.array([]),
      facebookUrl: ['', [Validator.isUrl]],
      foursquareUrl: ['', [Validator.isUrl]],
      linkedInUrl: ['', [Validator.isUrl]],
      twitterUrl: ['', [Validator.isUrl]],
      pinterestUrl: ['', [Validator.isUrl]],
      instagramUrl: ['', [Validator.isUrl]],
      youtubeUrl: ['', [Validator.isUrl]],
      blogUrl: [''],
      callTrackingNumbers: this.formBuilder.array([this.phoneNumberItemFactory()], Validators.maxLength(3)),
      businessEmail: ['', [Validator.isEmail]],
      faxNumber: ['', [phoneValidator(() => this.countryCode)]],
      cellNumber: ['', [phoneValidator(() => this.countryCode)]],
    });

    this.filteredPaymentMethods$ = this.paymentMethodSearchTerm$$.asObservable().pipe(
      map((searchTerm) =>
        AvailablePaymentMethods.filter((method) => {
          if (method) {
            return method.name.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1;
          }
          return false;
        }),
      ),
      map((methods) => methods.filter((method) => this.formGroup.controls.paymentMethods.value.indexOf(method) < 0)),
    );
    this.accessibleMarketIds$ = this.markets.markets.pipe(map((l) => l.map((m) => m.market_id)));

    this.addCompetitor();

    this.setUpCompetitorsSubscription(this.inferredBusiness$);
    this.setUpCompetitorsSelectedSubscription();
    this.setUpSocialUrlsSubscription(this.inferredBusiness$);
    this.setUpHoursOfOperationSubscription(this.inferredBusiness$);
    this.disableWhileInferring();
  }

  readonly phoneNumberItemFactory: ItemFactory = (): UntypedFormControl =>
    this.formBuilder.control('', [phoneValidator(() => this.countryCode)]);

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  private setUpCompetitorsSubscription(napData$: Observable<NapDataInferenceResponse>): void {
    const competitors$ = this.competitorInfo$.pipe(
      filter((c) => c?.placeIds?.length === 0), // only do this when no competitor info is supplied
      switchMapTo(this.inferCompetitors(this.businessInfo$$, napData$, this.accessibleMarketIds$)),
    );
    this.subscriptions.add(competitors$, (data) => this.updateFormForCompetitors(data));
  }

  private setUpCompetitorsSelectedSubscription(): void {
    const competitorsSelected$ = this.competitorInfo$.pipe(
      filter((c) => c?.placeIds?.length > 0), // only do this when competitor info is supplied
      switchMapTo(
        this.loadCompetitorsSelectedOnInputChanges(this.competitorInfo$, this.partnerID$$, this.accessibleMarketIds$),
      ),
    );
    this.subscriptions.add(competitorsSelected$, (data) => this.updateFormForCompetitorsSelected(data));
  }

  private inferCompetitors(
    businessInfo$: Observable<FullProfileBusinessInputs>,
    napData$: Observable<NapDataInferenceResponse>,
    marketIds$: Observable<string[]>,
  ): Observable<CompetitorsInferenceResponse> {
    return combineLatest([businessInfo$, napData$, marketIds$]).pipe(
      switchMap(([info, nap, marketIds]) => {
        const categoryNames = info.categories.map((t) => t.name);
        return this.easyAccountCreateService.inferCompetitors(
          info.partnerId,
          marketIds,
          info.placeId,
          nap.companyName,
          nap.city,
          categoryNames,
        );
      }),
    );
  }

  private loadCompetitorsSelectedOnInputChanges(
    competitorInfo$: Observable<CompetitorBusinessInputs>,
    partnerID$: Observable<string>,
    markets$: Observable<string[]>,
  ): Observable<NapDataInferenceResponse[]> {
    return combineLatest([competitorInfo$, partnerID$, markets$]).pipe(
      switchMap(([info, partnerId, markets]) => {
        return this.easyAccountCreateService.inferCompetitorsNapData(partnerId, markets, info.placeIds);
      }),
    );
  }

  private updateFormForCompetitors(data: { competitors: any[] }): void {
    if (data.competitors && data.competitors.length > 0) {
      this.setControlsForFormArray(this.competitorNames, data.competitors.length);
      this.formGroup.controls.competitorNames.setValue(data.competitors);
    }
  }

  get competitorNames(): UntypedFormArray {
    return <UntypedFormArray>this.formGroup.get('competitorNames');
  }
  get selectedCompetitorsArray(): UntypedFormArray {
    return <UntypedFormArray>this.formGroup.get('selectedCompetitors');
  }

  private updateFormForCompetitorsSelected(data: { companyName: string; website: string; placeId: string }[]): void {
    if (data && data.length > 0) {
      this.selectedCompetitorsArray.clear();
      data.forEach((c) => {
        this.addCompetitor({
          name: c.companyName,
          url: c.website,
          placeId: c.placeId,
        });
      });
    }
  }

  public addCompetitor(competitor?: Competitor): void {
    if (!competitor) {
      competitor = { name: '', url: '', placeId: '' };
    }
    const competitorIndex = this.selectedCompetitorsArray.length;
    const field = this.formBuilder.group({
      name: competitor.name || '',
      url: [competitor.url || '', Validator.isUrl],
      placeId: competitor.placeId || null,
    });
    this.subscriptions.add(field.valueChanges, () => {
      if (field.value.placeId) {
        field.setValue({
          name: field.value.name,
          url: field.value.url,
          placeId: null,
        });
      }
      const names = this.competitorNames.value;
      names[competitorIndex] = field.value.name;
      this.competitorNames.setValue(names);
    });
    this.selectedCompetitorsArray.push(field);
    this.setControlsForFormArray(this.competitorNames, this.selectedCompetitorsArray.value.length);
    const competitorNames = this.competitorNames.value;
    competitorNames[competitorIndex] = competitor.name;
    this.competitorNames.setValue(competitorNames);
  }

  public removeCompetitor(index: number): void {
    this.selectedCompetitorsArray.removeAt(index);
    this.competitorNames.removeAt(index);
    if (this.selectedCompetitorsArray.value.length === 0) {
      this.addCompetitor();
    }
  }

  private setUpSocialUrlsSubscription(napData$: Observable<NapDataInferenceResponse>): void {
    const socialUrls$ = this.loadSocialUrlsOnInputChanges(this.partnerID$$, napData$, this.accessibleMarketIds$);
    this.subscriptions.add(socialUrls$, (data) => this.updateFormForSocialUrls(data));
  }

  private loadSocialUrlsOnInputChanges(
    partnerID$: Observable<string>,
    napData$: Observable<NapDataInferenceResponse>,
    marketIds$: Observable<string[]>,
  ): Observable<SocialUrlsInferenceResponse> {
    return combineLatest([partnerID$, napData$, marketIds$]).pipe(
      switchMap(([partnerID, nap, marketIds]) => {
        const workNumber = nap.workNumber?.length > 0 ? nap.workNumber[0] : '';
        return this.easyAccountCreateService.inferSocialUrls(
          partnerID,
          marketIds,
          nap.companyName,
          nap.address,
          nap.city,
          workNumber,
          nap.website,
          nap.zip,
        );
      }),
    );
  }

  private updateFormForSocialUrls(urls: any): void {
    this.formGroup.patchValue({
      facebookUrl: urls.facebookUrl,
      foursquareUrl: urls.foursquareUrl,
      linkedInUrl: urls.linkedinUrl,
      twitterUrl: urls.twitterUrl,
      pinterestUrl: urls.pinterestUrl,
      instagramUrl: urls.instagramUrl,
      youtubeUrl: urls.youtubeUrl,
      blogUrl: urls.rssUrl,
    });
  }

  private setUpHoursOfOperationSubscription(napData$: Observable<NapDataInferenceResponse>): void {
    this.subscriptions.add(napData$, (nap) => {
      if (nap.hoursOfOperationJson) {
        this.formGroup.controls.hoursOfOperation.setValue(nap.hoursOfOperationJson);
        this.hoursOfOperationEnabled = true;
      }
    });
  }

  addPaymentMethod(event: MatAutocompleteSelectedEvent): void {
    this.formGroup.controls.paymentMethods.setValue([
      ...this.formGroup.controls.paymentMethods.value,
      event.option.value,
    ]);
    runOnNextKeyframe(() => this.paymentMethodSearchTerm$$.next(''));
    event.option.deselect();
  }

  removePaymentMethod(method: string): void {
    this.formGroup.controls.paymentMethods.setValue(
      this.formGroup.controls.paymentMethods.value.filter((m) => m !== method),
    );
    runOnNextKeyframe(() => this.paymentMethodSearchTerm$$.next(''));
  }

  updateSearchTerm(target: EventTarget): void {
    runOnNextKeyframe(() => this.paymentMethodSearchTerm$$.next(target['value']));
  }

  getControlArray(name: string): UntypedFormArray {
    return this.formGroup.get(name) as UntypedFormArray;
  }

  getControl(name: string): UntypedFormControl {
    return this.formGroup.controls[name] as UntypedFormControl;
  }

  private setControlsForFormArray(formArray: UntypedFormArray, length: number, controlFactory?: ItemFactory): void {
    // Wrapping helper to provided default control to reduce duplication
    const defaultControlFactory = () => this.formBuilder.control(FullProfilePanelComponent.formStateWithDisabled(''));
    fixControlsForFormArray(formArray, controlFactory || defaultControlFactory, length);
  }

  private disableWhileInferring(): void {
    this.subscriptions.add(this.inferring$, (inferring) => {
      if (inferring) {
        this.formGroup.controls.hoursOfOperation.disable();
      } else {
        this.formGroup.controls.hoursOfOperation.enable();
      }
    });

    this.subscriptions.add(this.socialUrlsInferring$, (inferring) => {
      if (inferring) {
        this.formGroup.controls.facebookUrl.disable();
        this.formGroup.controls.foursquareUrl.disable();
        this.formGroup.controls.linkedInUrl.disable();
        this.formGroup.controls.twitterUrl.disable();
        this.formGroup.controls.pinterestUrl.disable();
        this.formGroup.controls.instagramUrl.disable();
        this.formGroup.controls.youtubeUrl.disable();
        this.formGroup.controls.blogUrl.disable();
      } else {
        this.formGroup.controls.facebookUrl.enable();
        this.formGroup.controls.foursquareUrl.enable();
        this.formGroup.controls.linkedInUrl.enable();
        this.formGroup.controls.twitterUrl.enable();
        this.formGroup.controls.pinterestUrl.enable();
        this.formGroup.controls.instagramUrl.enable();
        this.formGroup.controls.youtubeUrl.enable();
        this.formGroup.controls.blogUrl.enable();
      }
    });

    this.subscriptions.add(this.competitorsInferring$, (inferring) => {
      if (inferring) {
        this.formGroup.controls.competitorNames.disable();
      } else {
        this.formGroup.controls.competitorNames.enable();
      }
    });
  }

  trackCompetitorUsage(): void {
    this.eacTrackingService.track(UsageTracking.CompetitionSelection);
  }
}

const totalLengthOfRepeatedFieldValidator = (maxLength: number, friendlyName: string): ValidatorFn => {
  return (control: AbstractControl): { [key: string]: any } => {
    if (!control.value) {
      return null;
    }
    if (control.value.length === 0) {
      return null;
    }

    const value = control.value.filter((val: string) => !!val).join(',');
    return value.length > maxLength
      ? {
          lengthOfRepeatedField: {
            maxLength: maxLength,
            friendlyName: friendlyName,
          },
        }
      : null;
  };
};
