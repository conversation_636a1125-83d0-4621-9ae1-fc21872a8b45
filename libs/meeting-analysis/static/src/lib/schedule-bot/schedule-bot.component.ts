import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInput } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MeetingBotService as MeetingsMeetingBotService } from '@vendasta/meetings';
import { MEETING_ANALYSIS_NAMESPACE_INJECTION_TOKEN$ } from '../constants';
import { Observable, switchMap, take, withLatestFrom } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { MatDivider } from '@angular/material/divider';
import { MatIcon, MatIconRegistry } from '@angular/material/icon';
import { GalaxyAiIconService, GalaxyAiIconSvg } from '@vendasta/galaxy/ai-icon';
import { AtlasMenuService } from '@galaxy/atlas';
import { DomSanitizer } from '@angular/platform-browser';
import { MeetingBotApiService } from '@vendasta/meeting-analysis';
import { FeatureFlagService } from '@galaxy/partner';
import { map } from 'rxjs/operators';

@Component({
  selector: 'meeting-analysis-schedule-bot',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatInput,
    TranslateModule,
    MatButtonModule,
    MatDivider,
    MatIcon,
  ],
  templateUrl: './schedule-bot.component.html',
  styleUrl: './schedule-bot.component.scss',
})
export class ScheduleBotComponent {
  formControl = new FormControl<string>('');

  constructor(
    private readonly meetingsMeetingBotService: MeetingsMeetingBotService,
    private readonly meetingAnalysisMeetingBotService: MeetingBotApiService,
    private featureFlagService: FeatureFlagService,
    @Inject(MEETING_ANALYSIS_NAMESPACE_INJECTION_TOKEN$) private readonly namespace$: Observable<string>,
    private readonly alertService: SnackbarService,
    private _: GalaxyAiIconService,
    private menuService: AtlasMenuService,
    private readonly iconRegistry: MatIconRegistry,
    private readonly sanitizer: DomSanitizer,
  ) {
    const uniqueId = `ai_meeting_analysis_gradient_${Date.now()}`;
    const modifiedSvg = GalaxyAiIconSvg.replace('id="glxy_ai_svg_gradient"', `id="${uniqueId}"`).replace(
      'url(#glxy_ai_svg_gradient)',
      `url(#${uniqueId})`,
    );
    this.iconRegistry.addSvgIconLiteral(
      'ai-meeting-analysis-icon',
      this.sanitizer.bypassSecurityTrustHtml(modifiedSvg),
    );
  }

  joinMeeting(): void {
    const url = (this.formControl.value || '').trim();
    if (url) {
      this.namespace$
        .pipe(
          take(1),
          switchMap((namespace) =>
            this.featureFlagService
              .batchGetStatus(namespace, '', ['sales_coach_calendar_sync'])
              .pipe(map((featureFlagStatus) => featureFlagStatus['sales_coach_calendar_sync'])),
          ),
          withLatestFrom(this.namespace$),
          switchMap(([useNewService, namespace]) => {
            if (useNewService) {
              return this.meetingAnalysisMeetingBotService.scheduleBot({
                namespace: namespace,
                meetingUrl: url,
              });
            } else {
              return this.meetingsMeetingBotService.scheduleBot(namespace, url);
            }
          }),
        )
        .subscribe({
          next: () => {
            this.formControl.setValue('');
            this.close();
            this.alertService.openSuccessSnack('SCHEDULE_BOT.SUCCESS');
          },
          error: (err) => {
            console.error(err);
            this.alertService.openErrorSnack('SCHEDULE_BOT.ERROR');
          },
        });
    }
  }

  close(): void {
    this.menuService.closeMenu('');
  }
}
