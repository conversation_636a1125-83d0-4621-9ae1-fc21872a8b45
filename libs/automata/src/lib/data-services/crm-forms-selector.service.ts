import { inject, Inject, Injectable } from '@angular/core';
import {
  PagedResponse,
  AutomationReplacementChipsDataService,
} from '../common/components/automation-replacement-chips/interfaces';
import { map, Observable, of, take } from 'rxjs';
import { AUTOMATION_NAMESPACE_INJECTION_TOKEN$ } from '@galaxy/automata/shared';
import { switchMap } from 'rxjs/operators';
import { FormsApiService, FormConfig } from '@vendasta/forms_microservice';

@Injectable({ providedIn: 'root' })
export class CRMFormsSelectorService implements AutomationReplacementChipsDataService<FormConfig> {
  private readonly formsApiService = inject(FormsApiService);

  supportsSearching = true;

  constructor(@Inject(AUTOMATION_NAMESPACE_INJECTION_TOKEN$) public readonly namespace$: Observable<string>) {}

  doLookup(
    searchTerm: string,
    cursor: string,
    pageSize: number,
    extraData?: any,
  ): Observable<PagedResponse<FormConfig>> {
    const { crmObjectType } = extraData;
    return this.namespace$.pipe(
      switchMap((namespace) => {
        return this.formsApiService.listForms({
          filters: {
            namespace,
          },
          pagingOptions: {
            cursor: cursor,
            pageSize: pageSize,
          },
        });
      }),
      take(1),
      map((res) => {
        let results = res?.formRows;
        if (searchTerm !== '') {
          results = results.filter((formRow) => formRow?.form?.name?.toLowerCase().includes(searchTerm.toLowerCase()));
        }
        // right now forms api doesnt allow listing only forms that map to a specific object type
        results = results.filter((formRow) => {
          const includesObjectType = formRow?.form?.fields?.findIndex((value) => {
            return value?.schema?.mappedField?.mappedTo === `crm_${crmObjectType?.toLowerCase()}`;
          });
          return includesObjectType !== -1;
        });
        const forms = results.map((r) => r?.form).filter((f) => f);
        const nextCursor = res?.pagingMetadata?.nextCursor || '';
        const hasMore = res?.pagingMetadata?.hasMore || false;
        return { results: forms, nextCursor, hasMore };
      }),
    );
  }

  convertDataToDisplay(s: FormConfig): Observable<string> {
    return of(s?.name ?? '');
  }

  convertDataToFormValue(s: FormConfig): any {
    return s.formId;
  }

  getMulti(x: string[]): Observable<FormConfig[]> {
    if (x?.length === 0 || x[0] === '') {
      return of([FormConfig.fromProto({})]);
    }
    if (x.length > 1) {
      console.warn('Unsupported: only one form ID is allowed currently');
    }

    return this.formsApiService
      .getForm({
        formId: x[0],
      })
      .pipe(
        map((response) => [response?.formConfig]),
        take(1),
      );
  }

  get(x: string): Observable<FormConfig> {
    return this.getMulti([x]).pipe(map((form) => form[0]));
  }
}
