import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CommonTriggerPanelComponent } from '../../../../common/common-trigger-panel.component';
import { TranslateModule } from '@ngx-translate/core';
import { RuleInterface } from '@vendasta/automata';
import {
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { NoRules } from '../../../common';
import { AutomationReplacementChipsComponent } from '../../../../../../common/components';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { FormTaskConfigMap } from '../crm-object-form-selector-display.service';
import { CRMFormsSelectorService } from '../../../../../../data-services/crm-forms-selector.service';
import { ObjectType } from '@galaxy/crm/static';

@Component({
  selector: 'automata-crm-object-form-selector',
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    AutomationReplacementChipsComponent,
  ],
  styleUrls: ['./crm-object-form-selector-panel.component.scss'],
  templateUrl: './crm-object-form-selector-panel.component.html',
})
export class CrmObjectFormSelectorPanelComponent extends CommonTriggerPanelComponent implements OnInit {
  formNameControl = new FormControl<string>('', [Validators.required]);
  formsService = inject(CRMFormsSelectorService);

  title = '';
  objectType = signal<ObjectType>('Contact');

  ngOnInit(): void {
    super.ngOnInit();
    const panelConfig = FormTaskConfigMap.get(this.taskDefinitionId);
    this.objectType.set(panelConfig?.objectType ?? 'Contact');
    this.title = panelConfig?.description ?? 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.DESCRIPTION';
    const jsonData = this.step ? JSON.parse(this.step?.data ?? '{}') : {};
    this.formNameControl.setValue(jsonData.user_provided_form_config_id ?? '');
  }

  fillRuleValues(rules: RuleInterface[]): RuleInterface[] {
    return rules;
  }

  getBaseRules(): RuleInterface[] {
    return NoRules();
  }

  getData(): any {
    const jsonData = this.step ? JSON.parse(this.step?.data ?? '{}') : {};
    jsonData.user_provided_form_config_id = this.formNameControl.value;
    return jsonData;
  }

  getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    return [this.formNameControl];
  }

  isFormValid(): boolean {
    return !this.formNameControl.invalid;
  }

  finishedLoadingSelector(): void {
    this.savingEnabled$.next(true);
  }
}
