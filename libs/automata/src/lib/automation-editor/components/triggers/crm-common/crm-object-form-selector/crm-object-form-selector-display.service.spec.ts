import { WorkflowStepInterface } from '@vendasta/automata';
import { Observable, of, throwError } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { TestScheduler } from 'rxjs/testing';
import { MockTranslateService, MockTranslateServiceEnglishValueReturn } from '../../../../../translate.mock';
import { TranslateService } from '@ngx-translate/core';
import { TestBed } from '@angular/core/testing';
import { DomSanitizer } from '@angular/platform-browser';
import { AutomationErrorHandlerService } from '../../../../services';
import { CrmObjectFormSelectorDisplayService } from './crm-object-form-selector-display.service';
import { CRMCompanyFormSubmitted, CRMContactFormSubmitted } from '../../../../component-loader/constants';
import { FormConfig } from '@vendasta/forms_microservice';
import { CRMFormsSelectorService } from '../../../../../data-services/crm-forms-selector.service';

class MockCRMFormsSelectorService {
  get(formId: string): Observable<FormConfig> {
    if (formId === 'notFoundFormConfigId') {
      return throwError(<HttpErrorResponse>{
        status: 404,
        error: {},
      });
    } else if (formId === 'missingNameFormConfigId') {
      return of(<FormConfig>{
        namespace: 'VUNI',
        formId: 'FormConfigID-123',
        resourceType: 'Contact',
      });
    } else {
      return of(<FormConfig>{
        namespace: 'VUNI',
        formId: 'FormConfigID-123',
        resourceType: 'Contact',
        name: 'My form',
      });
    }
  }
}

describe('CrmObjectFormSelectorDisplayService', () => {
  let service: CrmObjectFormSelectorDisplayService;
  let scheduler: TestScheduler;
  const translateService = new MockTranslateServiceEnglishValueReturn() as TranslateService;
  const errorHandlerService = {
    setValidationError: jest.fn(() => null),
  } as unknown as AutomationErrorHandlerService;

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
    TestBed.configureTestingModule({
      providers: [
        {
          provide: TranslateService,
          useClass: MockTranslateService,
        },
        DomSanitizer,
        {
          provide: AutomationErrorHandlerService,
          useValue: null,
        },
        {
          provide: CRMFormsSelectorService,
          useClass: MockCRMFormsSelectorService,
        },
        {
          provide: AutomationErrorHandlerService,
          useValue: errorHandlerService,
        },
      ],
    }).compileComponents();
    TestBed.runInInjectionContext(() => {
      service = new CrmObjectFormSelectorDisplayService(translateService);
    });
  });

  afterEach(() => {
    scheduler.flush();
  });

  describe('Contact form submitted', () => {
    it('should create the service', () => {
      expect(service).toBeTruthy();
    });

    it('should get the getActionSubtitle from the workflow step', () => {
      const actionStep = <WorkflowStepInterface>{
        data: JSON.stringify({
          user_provided_form_config_id: 'FormConfigID-123',
        }),
        taskDefinitionId: CRMContactFormSubmitted,
      };
      const actionSubtitle$ = service.getActionSubtitle('TEST', actionStep);
      scheduler.expectObservable(actionSubtitle$).toBe('(x|)', {
        x: 'When form <strong>My form</strong> is submitted for a contact',
      });
    });

    it('should get the getActionSubtitle from the workflow, form doesnt have a name', () => {
      const actionStep = <WorkflowStepInterface>{
        data: JSON.stringify({
          user_provided_form_config_id: 'missingNameFormConfigId',
        }),
        taskDefinitionId: CRMContactFormSubmitted,
      };
      const actionSubtitle$ = service.getActionSubtitle('TEST', actionStep);
      scheduler.expectObservable(actionSubtitle$).toBe('(x|)', {
        x: 'When a form is submitted for a contact',
      });
    });

    it('should get the getActionSubtitle even when form not found', () => {
      const actionStep = <WorkflowStepInterface>{
        data: JSON.stringify({
          user_provided_form_config_id: 'notFoundformId',
        }),
        taskDefinitionId: CRMContactFormSubmitted,
      };
      const actionSubtitle$ = service.getActionSubtitle('TEST', actionStep);
      scheduler.expectObservable(actionSubtitle$).toBe('(x|)', {
        x: 'When form <strong>My form</strong> is submitted for a contact',
      });
    });

    it('should get the activityColumn from the workflow step', () => {
      const activityTableEntry = service.getActivityColumn('TEST', {
        workflowStep: {
          taskDefinitionId: CRMContactFormSubmitted,
        },
      });
      const title$ = activityTableEntry.title$;
      const body$ = activityTableEntry.body$;
      scheduler.expectObservable(title$).toBe('(x|)', {
        x: 'Entered automation',
      });
      scheduler.expectObservable(body$).toBe('(x|)', {
        x: 'A form was submitted for a contact',
      });
    });
  });

  describe('Company form submitted', () => {
    it('should create the service', () => {
      expect(service).toBeTruthy();
    });

    it('should get the getActionSubtitle from the workflow step', () => {
      const actionStep = <WorkflowStepInterface>{
        data: JSON.stringify({
          user_provided_form_config_id: 'FormConfigID-123',
        }),
        taskDefinitionId: CRMCompanyFormSubmitted,
      };
      const actionSubtitle$ = service.getActionSubtitle('TEST', actionStep);
      scheduler.expectObservable(actionSubtitle$).toBe('(x|)', {
        x: 'When form <strong>My form</strong> is submitted for a company',
      });
    });

    it('should get the getActionSubtitle from the workflow, form doesnt have a name', () => {
      const actionStep = <WorkflowStepInterface>{
        data: JSON.stringify({
          user_provided_form_config_id: 'missingNameFormConfigId',
        }),
        taskDefinitionId: CRMCompanyFormSubmitted,
      };
      const actionSubtitle$ = service.getActionSubtitle('TEST', actionStep);
      scheduler.expectObservable(actionSubtitle$).toBe('(x|)', {
        x: 'When a form is submitted for a company',
      });
    });

    it('should get the getActionSubtitle even when form not found', () => {
      const actionStep = <WorkflowStepInterface>{
        data: JSON.stringify({
          user_provided_form_config_id: 'notFoundformId',
        }),
        taskDefinitionId: CRMCompanyFormSubmitted,
      };
      const actionSubtitle$ = service.getActionSubtitle('TEST', actionStep);
      scheduler.expectObservable(actionSubtitle$).toBe('(x|)', {
        x: 'When form <strong>My form</strong> is submitted for a company',
      });
    });

    it('should get the activityColumn from the workflow step', () => {
      const activityTableEntry = service.getActivityColumn('TEST', {
        workflowStep: {
          taskDefinitionId: CRMCompanyFormSubmitted,
        },
      });
      const title$ = activityTableEntry.title$;
      const body$ = activityTableEntry.body$;
      scheduler.expectObservable(title$).toBe('(x|)', {
        x: 'Entered automation',
      });
      scheduler.expectObservable(body$).toBe('(x|)', {
        x: 'A form was submitted for a company',
      });
    });
  });
});
