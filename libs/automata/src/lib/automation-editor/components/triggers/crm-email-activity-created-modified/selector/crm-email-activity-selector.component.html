<automata-automation-replacement-chips
  [label]="'AUTOMATIONS.EDITOR.TRIGGERS.CAMPAIGN_EMAIL.CAMPAIGN'"
  [dataService]="campaignSelectorService"
  [formControl]="campaignIdsFormControl"
  [supportDataPassing]="false"
  [multiple]="true"
  [isRequired]="true"
  [extraData]="{ prependAnyOption: true }"
  [allowCustomValues]="true"
></automata-automation-replacement-chips>

@if (emailStatusSelectorService(); as statusSelectorService) {
  <automata-automation-replacement-chips
    [label]="'AUTOMATIONS.EDITOR.TRIGGERS.CAMPAIGN_EMAIL.EMAIL_STATUS'"
    [dataService]="statusSelectorService"
    [formControl]="emailStatusFormControl"
    [supportDataPassing]="false"
    [multiple]="true"
    [isRequired]="true"
  ></automata-automation-replacement-chips>
}
