/**
 * A category to which a workflow trigger or action belongs, e.g. campaign-related, product-related, etc.
 * Can be used to help sort or filter triggers and actions.
 */

export enum WorkflowTaskCategory {
  Companies,
  Contacts,
  CustomObjects,
  Opportunities,
  Accounts,
  Users,
  Campaigns,
  AccountsAndUsers,
  CampaignsAndEmails,
  Fulfillment,
  Lists,
  Notifications,
  Products,
  Sales,
  Billing,
  Tags,
  Inbox,
  Delays,
  Conditions,
  Manual,
  Internal,
  Workflow,
  SalesOrders,
  Advanced,
  Integrations,
}

export const WorkflowTaskCategoryLabel: { [key in WorkflowTaskCategory]: string } = {
  [WorkflowTaskCategory.AccountsAndUsers]: 'AUTOMATIONS.EDITOR.CATEGORIES.BUSINESSES',
  [WorkflowTaskCategory.CampaignsAndEmails]: 'AUTOMATIONS.EDITOR.CATEGORIES.CAMPAIGNS_AND_EMAILS',
  [WorkflowTaskCategory.Contacts]: 'AUTOMATIONS.EDITOR.CATEGORIES.CONTACTS',
  [WorkflowTaskCategory.Companies]: 'AUTOMATIONS.EDITOR.CATEGORIES.COMPANIES',
  [WorkflowTaskCategory.Users]: 'AUTOMATIONS.EDITOR.CATEGORIES.USERS',
  [WorkflowTaskCategory.Accounts]: 'AUTOMATIONS.EDITOR.CATEGORIES.ACCOUNTS',
  [WorkflowTaskCategory.Campaigns]: 'AUTOMATIONS.EDITOR.CATEGORIES.CAMPAIGNS',
  [WorkflowTaskCategory.Fulfillment]: 'AUTOMATIONS.EDITOR.CATEGORIES.FULFILLMENT',
  [WorkflowTaskCategory.Lists]: 'AUTOMATIONS.EDITOR.CATEGORIES.LISTS',
  [WorkflowTaskCategory.Notifications]: 'AUTOMATIONS.EDITOR.CATEGORIES.NOTIFICATIONS',
  [WorkflowTaskCategory.Products]: 'AUTOMATIONS.EDITOR.CATEGORIES.PRODUCTS',
  [WorkflowTaskCategory.Sales]: 'AUTOMATIONS.EDITOR.CATEGORIES.SALES',
  [WorkflowTaskCategory.Billing]: 'AUTOMATIONS.EDITOR.CATEGORIES.BILLING',
  [WorkflowTaskCategory.Integrations]: 'AUTOMATIONS.EDITOR.CATEGORIES.INTEGRATIONS',
  [WorkflowTaskCategory.SalesOrders]: 'AUTOMATIONS.COMMON.SALES_ORDERS',
  [WorkflowTaskCategory.Tags]: 'AUTOMATIONS.EDITOR.CATEGORIES.TAGS',
  [WorkflowTaskCategory.Inbox]: 'AUTOMATIONS.EDITOR.CATEGORIES.INBOX',
  [WorkflowTaskCategory.Delays]: 'AUTOMATIONS.EDITOR.CATEGORIES.DELAYS',
  [WorkflowTaskCategory.Conditions]: 'AUTOMATIONS.EDITOR.CATEGORIES.CONDITIONS',
  [WorkflowTaskCategory.Manual]: 'AUTOMATIONS.EDITOR.CATEGORIES.MANUAL',
  [WorkflowTaskCategory.Advanced]: 'AUTOMATIONS.COMMON.ADVANCED',
  [WorkflowTaskCategory.Internal]: 'AUTOMATIONS.EDITOR.CATEGORIES.INTERNAL',
  [WorkflowTaskCategory.Workflow]: 'AUTOMATIONS.COMMON.WORKFLOW',
  [WorkflowTaskCategory.Opportunities]: 'AUTOMATIONS.EDITOR.CATEGORIES.OPPORTUNITIES',
  [WorkflowTaskCategory.CustomObjects]: 'AUTOMATIONS.EDITOR.CATEGORIES.CUSTOM_OBJECTS',
};

export const WorkflowTaskCategoryDescription: { [key in WorkflowTaskCategory]: string } = {
  [WorkflowTaskCategory.AccountsAndUsers]: '',
  [WorkflowTaskCategory.CampaignsAndEmails]: '',
  [WorkflowTaskCategory.Fulfillment]: '',
  [WorkflowTaskCategory.Lists]: '',
  [WorkflowTaskCategory.Notifications]: '',
  [WorkflowTaskCategory.Products]: '',
  [WorkflowTaskCategory.Sales]: '',
  [WorkflowTaskCategory.Billing]: '',
  [WorkflowTaskCategory.Integrations]: '',
  [WorkflowTaskCategory.SalesOrders]: '',
  [WorkflowTaskCategory.Tags]: '',
  [WorkflowTaskCategory.Inbox]: '',
  [WorkflowTaskCategory.Delays]: '',
  [WorkflowTaskCategory.Conditions]: '',
  [WorkflowTaskCategory.Manual]: '',
  [WorkflowTaskCategory.Advanced]: '',
  [WorkflowTaskCategory.Internal]: 'AUTOMATIONS.EDITOR.TASK_DESCRIPTIONS.INTERNAL',
  [WorkflowTaskCategory.Workflow]: '',
  [WorkflowTaskCategory.Contacts]: '',
  [WorkflowTaskCategory.Companies]: '',
  [WorkflowTaskCategory.Users]: '',
  [WorkflowTaskCategory.Accounts]: '',
  [WorkflowTaskCategory.Campaigns]: '',
  [WorkflowTaskCategory.Opportunities]: '',
  [WorkflowTaskCategory.CustomObjects]: '',
};

// Account & Account Group Related
export const AccountGroupCreatedTrigger = 'TaskDefinition-account-group-created';
export const AccountGroupUpdatedTrigger = 'TaskDefinition-account-group-updated';
export const ManualAccountGroupTrigger = 'TaskDefinition-manual-account-trigger';
export const SalespersonAssignedToAccountGroupTrigger = 'TaskDefinition-salesperson-assigned-to-account-group-trigger';
export const CustomAccountDataUpdatedTrigger = 'TaskDefinition-account-group-custom-data-updated';
export const CustomAccountFilterTaskDefinitionId = 'TaskDefinition-custom-account-data';
export const ModifyCustomAccountDataTaskDefinitionId = 'TaskDefinition-modify-custom-account-data';

// CRM Related
export const CRMContactCreated = 'TaskDefinition-crm-contact-created';
export const CRMContactCreatedOrModified = 'TaskDefinition-crm-contact-created-or-modified';
export const CRMCompanyCreatedOrModified = 'TaskDefinition-crm-company-created-or-modified';
export const CRMCompanyCreated = 'TaskDefinition-crm-company-created';
export const CRMCustomObjectCreatedOrModified = 'TaskDefinition-crm-custom-object-created-or-modified';
export const CRMOpportunityCreatedOrModified = 'TaskDefinition-crm-opportunity-created-or-modified';
export const UpdateCrmOpportunity = 'TaskDefinition-update-crm-opportunity';
export const CreateCRMOpportunity = 'TaskDefinition-create-crm-opportunity';
export const GetContactFromCrmOpportunity = 'TaskDefinition-get-associated-contact-from-crm-opportunity';
export const GetCrmOpportunityFromCompany = 'TaskDefinition-get-associated-crm-opportunity-from-company';
export const GetCrmOpportunityFromContact = 'TaskDefinition-get-associated-crm-opportunity-from-contact';
export const OpportunityFilterTaskDefinitionId = 'TaskDefinition-crm-opportunity-filter';
export const CompanyFilterTaskDefinitionId = 'TaskDefinition-crm-company-filter';
export const CustomObjectFilterTaskDefinitionId = 'TaskDefinition-crm-custom-object-filter';
export const CloseAllOpportunitiesDefinitionId = 'TaskDefinition-close-all-opportunities';
export const CloseOpportunityDefinitionId = 'TaskDefinition-close-opportunity';
export const associateComapyWithContactTaskDefinitionId = 'TaskDefinition-associate-company-and-contact';
export const GetCompanyFromCrmCustomObject = 'TaskDefinition-get-associated-company-from-crm-custom-object';
export const GetContactFromCrmCustomObject = 'TaskDefinition-get-associated-contact-from-crm-custom-object';
export const GetOpportunityFromCrmCustomObject = 'TaskDefinition-get-associated-opportunity-from-crm-custom-object';

// CRM Contact Specific
export const ContactCreatedViaWebChat = 'TaskDefinition-contact-created-via-web-chat';
export const ContactFollowUpRequested = 'TaskDefinition-contact-requested-follow-up';
export const CRMTaskCreatedContact = 'TaskDefinition-crm-task-created-contact';
export const CRMNoteCreatedContact = 'TaskDefinition-crm-note-created-contact';
export const CRMCallCreatedContact = 'TaskDefinition-crm-call-created-contact';
export const CRMMeetingCreatedContact = 'TaskDefinition-crm-meeting-created-contact';
export const CRMEmailCreatedContact = 'TaskDefinition-crm-email-created-contact';
export const CRMTaskOverdueContact = 'TaskDefinition-crm-task-overdue-contact';
export const CRMContactAddedToList = 'TaskDefinition-crm-contact-added-to-list';
export const CRMContactRemovedFromList = 'TaskDefinition-crm-contact-removed-from-list';
export const UpdateContactTaskDefinitionId = 'TaskDefinition-update-contact';
export const UpdateContactV2TaskDefinitionId = 'TaskDefinition-update-contact-v2';
export const ContactFilterTaskDefinitionId = 'TaskDefinition-contact-filter';
export const CRMContactFormSubmitted = 'TaskDefinition-crm-contact-form-submission';
export const CRMCompanyFormSubmitted = 'TaskDefinition-crm-company-form-submission';

// CRM Company Specific
export const CRMNoteCreatedCompany = 'TaskDefinition-crm-note-created-company';
export const CRMCallCreatedCompany = 'TaskDefinition-crm-call-created-company';
export const CRMMeetingCreatedCompany = 'TaskDefinition-crm-meeting-created-company';
export const CRMTaskCreatedCompany = 'TaskDefinition-crm-task-created-company';
export const CRMEmailCreatedCompany = 'TaskDefinition-crm-email-created-company';
export const CRMTaskOverdueCompany = 'TaskDefinition-crm-task-overdue-company';
export const CRMCompanyAddedToList = 'TaskDefinition-crm-company-added-to-list';
export const CRMCompanyRemovedFromList = 'TaskDefinition-crm-company-removed-from-list';

// Workflow & Branching
export const ABBranchTaskDefinitionId = 'TaskDefinition-ab-branch';
export const IfElseBranchTaskDefinitionId = 'TaskDefinition-********-3111-4462-b342-ee94628245df';
export const IfElseBranchAccountDataTaskDefinitionId = 'TaskDefinition-d15e86da-4166-429d-b054-4006f55d0ea5';
export const IfElseBranchBusinessAppDataTaskDefinitionId = 'TaskDefinition-connections-filter';
export const IfElseBranchUserDataTaskDefinitionId = 'TaskDefinition-user-data';
export const IfElseBranchPartnerDataTaskDefinitionId = 'TaskDefinition-3733042b-2e31-42e1-a08a-da3d494b1e85';
export const IfElseBranchUserInternalDataTaskDefinitionId = 'TaskDefinition-765792f2-c9e6-41d4-8a91-a3b17f403657';
export const IfElseBranchShoppingCartDataTaskDefinitionId = 'TaskDefinition-check-shopping-cart-contents';
export const IfElseBranchAccountInternalDataTaskDefinitionId = 'TaskDefinition-internal-account-filter';
export const IfElseBranchOrderDataTaskDefinitionId = 'TaskDefinition-order-filter';
export const IfElseBranchOpportunityDataTaskDefinitionId = 'TaskDefinition-opportunity-filter';
export const IfElseBranchWorkflowStepTaskDefinitionId = 'TaskDefinition-workflow-data';
export const IfElseBranchOpportunityFilterTaskDefinitionId = 'TaskDefinition-get-open-opportunities';
export const IfElseBranchCrmTaskDataTaskDefinitionId = 'TaskDefinition-CRM-task-data';
export const IfElseBranchCrmNoteDataTaskDefinitionId = 'TaskDefinition-CRM-note-data';
export const IfElseBranchCrmEmailDataTaskDefinitionId = 'TaskDefinition-CRM-email-data';
export const IfElseBranchCrmMeetingDataTaskDefinitionId = 'TaskDefinition-CRM-meeting-data';
export const IfElseBranchCrmCallDataTaskDefinitionId = 'TaskDefinition-CRM-call-data';
export const IfElseIntegrationConnectionFilterTaskDefinitionId = 'TaskDefinition-Integration-Connection-Filter';

// Fulfillment
export const FulfillmentTaskStatusChangedTrigger = 'TaskDefinition-fulfillment-task-status-changes';
export const FulfillmentProjectStatusChangedTrigger = 'TaskDefinition-fulfillment-project-status-changes';
export const FulfillmentProjectOrderStatusChangedTrigger = 'TaskDefinition-fulfillment-project-status-changes-order';

// Sales
export const SalesTaskStatusChangedTrigger = 'TaskDefinition-sales-task-status-changes';

// Campaign Related
export const StartCampaignAction = 'TaskDefinition-7cb3c508-b291-47f6-86cf-4377cd9c3c3c';
export const startCampaignForCompanyTaskDefinitionId = 'TaskDefinition-start-company-campaign';
export const pauseCampaignForContactTaskDefinitionId = 'TaskDefinition-pause-campaign-for-contact';
export const pauseCampaignForCompanyTaskDefinitionId = 'TaskDefinition-pause-campaign-for-company';

// Order & Invoice Related
export const InvoiceFilterTaskDefinitionId = 'TaskDefinition-Invoice-data';
export const ManualOrderTrigger = 'TaskDefinition-manual-order-trigger';
export const CustomOrderFilterTaskDefinitionId = 'TaskDefinition-custom-order-data';
export const ModifyCustomSalesOrderTaskDefinitionId = 'TaskDefinition-modify-custom-sales-order';
export const ProductPriceFilterTaskDefinitionId = 'TaskDefinition-product-price-filter';
export const CustomProductFilterTaskDefinitionId = 'TaskDefinition-custom-product-data';
export const ModifyCustomProductDataTaskDefinitionId = 'TaskDefinition-modify-custom-product-data';
export const InitSubscriptionUpdateTaskDefinitionId = 'TaskDefinition-init-subscription-update';

// Communication
export const SendSMSViaInboxTaskDefinitionId = 'TaskDefinition-sned-contact-sms';
export const SendEventViaInboxTaskDefinitionId = 'TaskDefinition-send-contact-inbox-event';
export const SendEmailViaInboxTaskDefinitionId = 'TaskDefinition-send-contact-email-inbox';
export const SendEmailToUserTaskDefinitionId = 'TaskDefinition-send-html-email-to-user';
export const SendEmailToContactTaskDefinitionId = 'TaskDefinition-send-html-email-to-contact';
export const SendEmailToCompanyTaskDefinitionId = 'TaskDefinition-send-html-email-to-company';
export const SendAiPromptId = 'TaskDefinition-send-ai-prompt';
export const SendEmailEventTrigger = 'TaskDefinition-email-event-trigger';

// Third-Party Integrations
export const QBOInvoiceTaskDefinitionId = 'TaskDefinition-QBO-invoice';
export const QBOSalesReceiptTaskDefinitionId = 'TaskDefinition-QBO-sales-receipt';
export const JobberVisitTaskDefinitionId = 'TaskDefinition-Jobber-visit';
export const JobberJobTaskDefinitionId = 'TaskDefinition-Jobber-job';
export const HousecallProJobTaskDefinitionId = 'TaskDefinition-housecallpro-job-finished';
export const GingrReservationCheckoutTaskDefinitionId = 'TaskDefinition-gingr-reservation-checkout';
export const QBOPaymentTaskDefinitionId = 'TaskDefinition-QBO-payment';
export const PetexecOrderCompleteTaskDefinitionId = 'TaskDefinition-petexec-order-complete';
export const PawPartnerCustomerCheckoutTaskDefinitionId = 'TaskDefinition-pawpartner-customer-checkout';
export const ShopMonkeyInvoicePaidTaskDefinitionId = 'TaskDefinition-shopmonkey-invoice-paid';
export const ShopwareRepairOrderInvoicePickedUpTaskDefinitionId = 'TaskDefinition-shopware-repairorderinvoice-pickedup';
export const ProtractorInvoicePostedTaskDefinitionId = 'TaskDefinition-protractor-invoice-posted';
export const TekmetricRepairorderPostedTaskDefinitionId = 'TaskDefinition-tekmetric-repairorder-posted';
export const PawloyaltyAppointmentCheckedoutTaskDefinitionId = 'TaskDefinition-pawloyalty-appointment-checkedout';
export const MindbodyVisitCompleteTaskDefinitionId = 'TaskDefinition-mindbody-visit-complete';
export const PetresortproInvoiceCheckoutTaskDefinitionId = 'TaskDefinition-petresortpro-invoice-checkout';
export const ServicemonsterInvoiceCreatedTaskDefinitionId = 'TaskDefinition-servicemonster-invoice-created';
export const BroadlypartnerapiTransactionCompleteTaskDefinitionId =
  'TaskDefinition-broadlypartnerapi-transaction-complete';
export const ShopbossRepairorderinvoiceClosedTaskDefinitionId = 'TaskDefinition-shopboss-repairorderinvoice-closed';
export const JobnimbusContactstatusChangedTaskDefinitionId = 'TaskDefinition-jobnimbus-contactstatus-changed';
export const JobnimbusJobstatusChangedTaskDefinitionId = 'TaskDefinition-jobnimbus-jobstatus-changed';
export const CcconeVehicleoutCompleteTaskDefinitionId = 'TaskDefinition-cccone-vehicleout-complete';
export const ServicefusionJobClosedTaskDefinitionId = 'TaskDefinition-servicefusion-job-closed';
export const RbcsSalesorserviceCompletedTaskDefinitionId = 'TaskDefinition-rbcs-salesorservice-completed';
export const LightspeedSaleCompletedTaskDefinitionId = 'TaskDefinition-lightspeed-sale-completed';
export const FieldedgeWorkorderFinalizedTaskDefinitionId = 'TaskDefinition-fieldedge-workorder-finalized';
export const ServicetitanJobCompletedTaskDefinitionId = 'TaskDefinition-servicetitan-job-completed';
export const ClioMatterClosedTaskDefinitionId = 'TaskDefinition-clio-matter-closed';
export const QbdInvoiceorsalesreceiptUpdatedTaskDefinitionId = 'TaskDefinition-qbd-invoiceorsalesreceipt-updated';

// User Related
export const SmbEmailVerifiedTaskDefinitionId = 'TaskDefinition-user-verified-their-email';
export const CustomUserFilterTaskDefinitionId = 'TaskDefinition-custom-user-data';
export const ModifyCustomUserDataTaskDefinitionId = 'TaskDefinition-modify-custom-user-data';

// Partner Related
export const PartnerCreateTrialTaskDefinitionId = 'TaskDefinition-partner-create-trial';

// Connection Related
export const ConnectionChangedTrigger = 'TaskDefinition-connection-changed';
export const GetConnectionDataVendorTaskDefinitionId = 'TaskDefinition-Get-Connection-Data-Vendor';

// Other/Utility
export const DelayUntilEventTaskDefinitionId = 'TaskDefinition-delay-until-event';
export const RateLimitTaskDefinitionId = 'TaskDefinition-rate-limit-entity';
export const EndTaskDefinitionId = 'TaskDefinition-end';
export const JumpTaskDefinitionId = 'TaskDefinition-jump';
export const TerminatingSteps = [EndTaskDefinitionId, JumpTaskDefinitionId];
export const DirectlyReferencedStepTaskDefinitionIds = [JumpTaskDefinitionId];
export const ManualCompanyTrigger = 'TaskDefinition-manual-company-trigger';
export const ManualContactTrigger = 'TaskDefinition-manual-contact-trigger';
export const PauseOrCancelTaskManagerAccountId = 'TaskDefinition-pause-or-cancel-concierge-account';
export const FtpUploadTriggerTaskDefinitionId = 'TaskDefinition-ftp-upload-trigger';
export const NapatracsenterpriseRepairorderCompletedTaskDefinitionId =
  'TaskDefinition-napatracsenterprise-repairorder-completed';
export const NapatracsRepairorderCompletedTaskDefinitionId = 'TaskDefinition-napatracs-repairorder-completed';
export const RowriterRepairorderCompletedTaskDefinitionId = 'TaskDefinition-rowriter-repairorder-completed';
export const DentrixAppointmentCompletedTaskDefinitionId = 'TaskDefinition-dentrix-appointment-completed';
export const MitchellRepairorderCompleteTaskDefinitionId = 'TaskDefinition-mitchell-repairorder-complete';
