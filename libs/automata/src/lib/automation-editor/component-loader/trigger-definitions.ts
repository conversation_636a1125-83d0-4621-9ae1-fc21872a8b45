import { Type } from '@angular/core';
import {
  AccountGroupCreatedTrigger,
  AccountGroupUpdatedTrigger,
  BroadlypartnerapiTransactionCompleteTaskDefinitionId,
  CcconeVehicleoutCompleteTaskDefinitionId,
  ClioMatterClosedTaskDefinitionId,
  ConnectionChangedTrigger,
  ContactCreatedViaWebChat,
  CRMCallCreatedCompany,
  CRMCallCreatedContact,
  CRMCompanyAddedToList,
  CRMCompanyCreated,
  CRMCompanyCreatedOrModified,
  CRMCompanyRemovedFromList,
  CRMContactAddedToList,
  CRMContactCreated,
  CRMContactCreatedOrModified,
  CRMContactRemovedFromList,
  CR<PERSON>mailCreatedCompany,
  CRMEmailCreatedContact,
  CRMMeetingCreatedCompany,
  CR<PERSON>eetingCreatedContact,
  CRMNoteCreatedCompany,
  CRMNoteCreatedContact,
  CRMOpportunityCreatedOrModified,
  CRMTask<PERSON>reated<PERSON>ompany,
  CRMTask<PERSON>reated<PERSON>ontact,
  CRMTaskOverdueCompany,
  CRMTaskOverdueContact,
  CustomAccountDataUpdatedTrigger,
  DentrixAppointmentCompletedTaskDefinitionId,
  FieldedgeWorkorderFinalizedTaskDefinitionId,
  FtpUploadTriggerTaskDefinitionId,
  FulfillmentProjectOrderStatusChangedTrigger,
  FulfillmentProjectStatusChangedTrigger,
  FulfillmentTaskStatusChangedTrigger,
  GingrReservationCheckoutTaskDefinitionId,
  HousecallProJobTaskDefinitionId,
  InitSubscriptionUpdateTaskDefinitionId,
  JobberJobTaskDefinitionId,
  JobberVisitTaskDefinitionId,
  JobnimbusContactstatusChangedTaskDefinitionId,
  JobnimbusJobstatusChangedTaskDefinitionId,
  LightspeedSaleCompletedTaskDefinitionId,
  ManualAccountGroupTrigger,
  ManualCompanyTrigger,
  ManualContactTrigger,
  ManualOrderTrigger,
  MindbodyVisitCompleteTaskDefinitionId,
  MitchellRepairorderCompleteTaskDefinitionId,
  NapatracsenterpriseRepairorderCompletedTaskDefinitionId,
  NapatracsRepairorderCompletedTaskDefinitionId,
  PartnerCreateTrialTaskDefinitionId,
  PawloyaltyAppointmentCheckedoutTaskDefinitionId,
  PawPartnerCustomerCheckoutTaskDefinitionId,
  PetexecOrderCompleteTaskDefinitionId,
  PetresortproInvoiceCheckoutTaskDefinitionId,
  ProtractorInvoicePostedTaskDefinitionId,
  QbdInvoiceorsalesreceiptUpdatedTaskDefinitionId,
  QBOInvoiceTaskDefinitionId,
  QBOPaymentTaskDefinitionId,
  QBOSalesReceiptTaskDefinitionId,
  RbcsSalesorserviceCompletedTaskDefinitionId,
  RowriterRepairorderCompletedTaskDefinitionId,
  SalespersonAssignedToAccountGroupTrigger,
  SalesTaskStatusChangedTrigger,
  SendEmailEventTrigger,
  ServicefusionJobClosedTaskDefinitionId,
  ServicemonsterInvoiceCreatedTaskDefinitionId,
  ServicetitanJobCompletedTaskDefinitionId,
  ShopbossRepairorderinvoiceClosedTaskDefinitionId,
  ShopMonkeyInvoicePaidTaskDefinitionId,
  ShopwareRepairOrderInvoicePickedUpTaskDefinitionId,
  SmbEmailVerifiedTaskDefinitionId,
  TekmetricRepairorderPostedTaskDefinitionId,
  WorkflowTaskCategory,
  ContactFollowUpRequested,
  CRMCustomObjectCreatedOrModified,
  CRMContactFormSubmitted,
  CRMCompanyFormSubmitted,
} from './constants';
import {
  AccountCustomDataUpdatedPanelComponent,
  AccountCustomDataUpdatedService,
  AccountGroupUserClaimedService,
  AddedToListPanelComponent,
  AddedToListService,
  ApiTriggerAccountGroupPanelComponent,
  ApiTriggerAccountGroupService,
  ApiTriggerNoEntityPanelComponent,
  ApiTriggerNoEntityService,
  ApiTriggerOrderPanelComponent,
  ApiTriggerOrderService,
  AskQuestionPanelComponent,
  AskQuestionService,
  BillingPurchaseSuccessPanelComponent,
  BillingPurchaseSuccessService,
  CampaignEmailPanelComponent,
  CampaignEmailService,
  ConnectionChangedPanelComponent,
  ConnectionChangedService,
  CrmActivityCreatedModifiedDisplayService,
  CrmActivityCreatedModifiedPanelComponent,
  CrmCompanyCreatedPanelComponent,
  CrmCompanyCreatedService,
  CrmContactCreatedPanelComponent,
  CrmContactCreatedService,
  CrmObjectListSelectorDisplayService,
  CrmObjectListSelectorPanelComponent,
  CrmCustomObjectCreatedModifiedService,
  CrmCustomObjectCreatedModifiedPanelComponent,
  EmailEventTriggerPanelComponent,
  EmailEventTriggerService,
  FulfillmentProjectStatusChangedPanelComponent,
  FulfillmentTaskStatusChangedPanelComponent,
  InboxMessageSentPanelComponent,
  InboxMessageSentService,
  InboxMessageSentToVmfPanelComponent,
  InboxMessageSentToVmfService,
  InitPartnerSubscriptionUpdatePanelComponent,
  InitPartnerSubscriptionUpdateService,
  ManuallyTriggerPanelComponent,
  ManuallyTriggerService,
  MerchantServicesConnectedPanelComponent,
  MerchantServicesConnectedService,
  NoRulesDisplay,
  NoRulesPanelComponent,
  OpportunityUpdatedPanelComponent,
  OpportunityUpdatedService,
  OrderStatusChangedPanelComponent,
  OrderStatusChangedService,
  PackageInterestPanelComponent,
  PackageInterestService,
  PartnerFreeTrialCancellationReasonPanelComponent,
  PartnerFreeTrialCancellationReasonService,
  PartnerSubscriptionUpdatePanelComponent,
  PartnerSubscriptionUpdateService,
  PartnerTrialCreatedPanelComponent,
  PartnerTrialCreatedService,
  ProductActivatedPanelComponent,
  ProductActivatedService,
  ProductDeactivatedPanelComponent,
  ProductDeactivatedService,
  RetailPaymentPanelComponent,
  RetailPaymentService,
  SalesActivityCreatedPanelComponent,
  SalesActivityCreatedService,
  SalesOrderExpiringPanelComponent,
  SalesOrderExpiringService,
  SalesTaskStatusChangedPanelComponent,
  ShoppingCartUpdatedPanelComponent,
  ShoppingCartUpdatedService,
  SmbUserVerifiedEmailPanelComponent,
  SmbUserVerifiedEmailService,
  SnapshotCreatedService,
  SnapshotOpenedService,
  TaskStatusChangedService,
  UserAssociatedWithAccountGroupService,
  UserOnlinePanelComponent,
  UserOnlineService,
  WholesalePurchaseCreatedPanelComponent,
  WholesalePurchaseCreatedService,
  ZapierTriggerPanelComponent,
  ZapierTriggerService,
  CrmObjectFormSelectorDisplayService,
} from '../components/triggers';
import { DEFAULT_TRIGGER_COLOR, INTERNAL_ICON_COLOR } from '../../common/constants';
import {
  CRM_CUSTOM_OBJECT_FEATURE_FLAG,
  SHOW_BROADLYPARTNERAPI_INTEGRATION,
  SHOW_CCC_INTEGRATION,
  SHOW_CLIO_INTEGRATION,
  SHOW_DENTRIX_INTEGRATION,
  SHOW_FTP_INTEGRATION,
  SHOW_JOBNIMBUS_INTEGRATION,
  SHOW_MINDBODY_INTEGRATION,
  SHOW_MITCHELLMANAGER_INTEGRATION,
  SHOW_NAPATRACS_INTEGRATION,
  SHOW_NAPATRACSENTERPRISE_INTEGRATION,
  SHOW_QBD_INTEGRATION,
  SHOW_ROWRITER_INTEGRATION,
  SHOW_SERVICETITAN_INTEGRATION,
  SHOW_SHOPMONKEY_INTEGRATION,
  SHOW_SHOPWARE_INTEGRATION,
  SHOW_TEKMETRIC_INTEGRATION,
} from '@galaxy/automata/shared';
import { CommonTriggerPanelComponent } from '../components/common/common-trigger-panel.component';
import { CommonDisplayActionService } from '../components/common/common-display-action.service';
import { CommonDisplayActivityService } from '../components/common/common-display-activity.service';
import { CrmEmailActivityCreatedModifiedPanelComponent } from '../components/triggers/crm-email-activity-created-modified/panel/crm-email-activity-created-modified-panel.component';
import { CrmObjectFormSelectorPanelComponent } from '../components/triggers/crm-common/crm-object-form-selector/crm-object-form-selector-panel/crm-object-form-selector-panel.component';

export interface WorkflowTriggerInterface {
  panel: Type<CommonTriggerPanelComponent>;
  actionDisplayService: Type<CommonDisplayActionService>;
  activityDisplayService: Type<CommonDisplayActivityService>;
  icon: string;
  iconColor: string;
  taskDefinitionId: string;
  name: string;
  nameTranslationParams?: unknown;
  categories: WorkflowTaskCategory[];
  featureFlag?: string;
  upgradeSubscriptionTitle?: string;
  upgradeSubscriptionDescription?: string;
  supportsCustomOutputParameters?: boolean;
  // Whether to use `getMultiBranchNodeData` for the node display instead of `getActionSubtitle`. `getActionSubtitle` will still be used
  // for the activity table.
  useMultiBranchingForNodeDisplay?: boolean;
  // Used to provide an alert when using the no rules panel.
  panelAlert?: string;
  // If you don't have any data to show to the user in the node or activity table you can use the no rules service and it will read these
  // value to generate the display strings.
  nodeDisplay?: string;
  // The no-rules service will use this if the automation is wait until, or fallback to the node display if you don't set this.
  waitUntilNodeDisplay?: string;
  activityDisplay?: string;
  labelDisplay?: string;
}

// A map of task definition IDs to their corresponding trigger interfaces.
export const WORKFLOW_TRIGGERS: { [taskKey: string]: WorkflowTriggerInterface } = {
  'TaskDefinition-retail-invoice-overdue': {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'receipt_long',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-retail-invoice-overdue',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.RETAIL_INVOICE_OVERDUE.TITLE',
    categories: [WorkflowTaskCategory.Billing],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.RETAIL_INVOICE_OVERDUE.NODE',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.RETAIL_INVOICE_OVERDUE.TITLE',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.RETAIL_INVOICE_OVERDUE.WAIT_UNTIL',
  },
  [SendEmailEventTrigger]: {
    panel: EmailEventTriggerPanelComponent,
    actionDisplayService: EmailEventTriggerService,
    activityDisplayService: EmailEventTriggerService,
    icon: 'mark_email_read',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: SendEmailEventTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.EMAIL_EVENT.EMAIL_EVENT_TITLE',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  'TaskDefinition-user-online': {
    panel: UserOnlinePanelComponent,
    actionDisplayService: UserOnlineService,
    activityDisplayService: UserOnlineService,
    icon: 'person_pin',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-user-online',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.USER_ONLINE.USER_ONLINE',
    nameTranslationParams: { businessAppName: 'Business App' },
    categories: [WorkflowTaskCategory.Users],
  },
  'TaskDefinition-b7a19e08-bf6d-4234-b219-10479a310550': {
    panel: AddedToListPanelComponent,
    actionDisplayService: AddedToListService,
    activityDisplayService: AddedToListService,
    icon: 'list',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-b7a19e08-bf6d-4234-b219-10479a310550',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.ADDED_TO_LIST.ADDED_TO_LIST',
    categories: [WorkflowTaskCategory.Accounts],
  },
  'TaskDefinition-c3baf605-9a7c-4150-8aad-7df3efe78027': {
    panel: ProductActivatedPanelComponent,
    actionDisplayService: ProductActivatedService,
    activityDisplayService: ProductActivatedService,
    icon: 'shopping_cart',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-c3baf605-9a7c-4150-8aad-7df3efe78027',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.PRODUCT_ACTIVATION.PRODUCT_ACTIVATED.PRODUCT_ACTIVATED',
    categories: [WorkflowTaskCategory.Products],
  },
  'TaskDefinition-4420b440-8b07-42f6-ae0c-a160424250de': {
    panel: ProductDeactivatedPanelComponent,
    actionDisplayService: ProductDeactivatedService,
    activityDisplayService: ProductDeactivatedService,
    icon: 'remove_shopping_cart',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-4420b440-8b07-42f6-ae0c-a160424250de',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.PRODUCT_ACTIVATION.PRODUCT_DEACTIVATED.PRODUCT_DEACTIVATED',
    categories: [WorkflowTaskCategory.Products],
  },
  'TaskDefinition-partner-subscription-change': {
    panel: PartnerSubscriptionUpdatePanelComponent,
    actionDisplayService: PartnerSubscriptionUpdateService,
    activityDisplayService: PartnerSubscriptionUpdateService,
    icon: 'currency_exchange',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-partner-subscription-change',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.PARTNER_SUBSCRIPTION_TIER_UPDATE.PARTNER_SUBSCRIPTION_TIER_CHANGE',
    categories: [WorkflowTaskCategory.Internal],
  },
  [InitSubscriptionUpdateTaskDefinitionId]: {
    panel: InitPartnerSubscriptionUpdatePanelComponent,
    actionDisplayService: InitPartnerSubscriptionUpdateService,
    activityDisplayService: InitPartnerSubscriptionUpdateService,
    icon: 'currency_exchange',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: InitSubscriptionUpdateTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INIT_SUBSCRIPTION_TIER_UPDATE.PARTNER_SUBSCRIPTION_TIER_CHANGE',
    categories: [WorkflowTaskCategory.Internal],
  },
  [PartnerCreateTrialTaskDefinitionId]: {
    panel: PartnerTrialCreatedPanelComponent,
    actionDisplayService: PartnerTrialCreatedService,
    activityDisplayService: PartnerTrialCreatedService,
    icon: 'start',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: PartnerCreateTrialTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.PARTNER_START_TRIAL.PARTNER_START_TRIAL',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-partner-free-trial-cancellation-reason': {
    panel: PartnerFreeTrialCancellationReasonPanelComponent,
    actionDisplayService: PartnerFreeTrialCancellationReasonService,
    activityDisplayService: PartnerFreeTrialCancellationReasonService,
    icon: 'cancel_presentation',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-partner-free-trial-cancellation-reason',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.PARTNER_FREE_TRIAL_CANCELLATION_REASON.PARTNER_FREE_TRIAL_CANCELLATION_REASON',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-31cca704-bc99-4ef4-be84-4538812ab7d2': {
    panel: NoRulesPanelComponent,
    actionDisplayService: SnapshotCreatedService,
    activityDisplayService: SnapshotCreatedService,
    icon: 'note_add',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-31cca704-bc99-4ef4-be84-4538812ab7d2',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.SNAPSHOT_CREATED.SNAPSHOT_CREATED',
    categories: [WorkflowTaskCategory.Internal],
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.SNAPSHOT_CREATED.WAIT_UNTIL_ACTION',
  },
  'TaskDefinition-523a05ec-e437-4cce-afbf-0f11aa1062e5': {
    panel: NoRulesPanelComponent,
    actionDisplayService: SnapshotCreatedService,
    activityDisplayService: SnapshotCreatedService,
    icon: 'note_add',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-523a05ec-e437-4cce-afbf-0f11aa1062e5',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.SNAPSHOT_CREATED.SNAPSHOT_CREATED',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-46102c52-94f6-4d69-b22e-e310b0d5d086': {
    panel: ProductActivatedPanelComponent,
    actionDisplayService: ProductActivatedService,
    activityDisplayService: ProductActivatedService,
    icon: 'shopping_cart',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-46102c52-94f6-4d69-b22e-e310b0d5d086',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.PRODUCT_ACTIVATION.PRODUCT_ACTIVATED.PRODUCT_ACTIVATED',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-8ed53f54-ac3c-430c-a300-6b0c459b44ac': {
    panel: MerchantServicesConnectedPanelComponent,
    actionDisplayService: MerchantServicesConnectedService,
    activityDisplayService: MerchantServicesConnectedService,
    icon: 'payment',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-8ed53f54-ac3c-430c-a300-6b0c459b44ac',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.VENDASTA_PAYMENTS_START_SETUP.VENDASTA_PAYMENTS_START_SETUP',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-opportunity-updated': {
    panel: OpportunityUpdatedPanelComponent,
    actionDisplayService: OpportunityUpdatedService,
    activityDisplayService: OpportunityUpdatedService,
    icon: 'monetization_on',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-opportunity-updated',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.OPPORTUNITY.OPPORTUNITY_UPDATED',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-inbox-message-sent-for-automation': {
    panel: InboxMessageSentPanelComponent,
    actionDisplayService: InboxMessageSentService,
    activityDisplayService: InboxMessageSentService,
    icon: 'question_answer',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-inbox-message-sent-for-automation',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INBOX_MESSAGE_SENT.INBOX_MESSAGE_SENT',
    categories: [WorkflowTaskCategory.Inbox],
  },
  'TaskDefinition-inbox-message-sent-for-vmf': {
    panel: InboxMessageSentToVmfPanelComponent,
    actionDisplayService: InboxMessageSentToVmfService,
    activityDisplayService: InboxMessageSentToVmfService,
    icon: 'question_answer',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-inbox-message-sent-for-vmf',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INBOX_MESSAGE_SENT_TO_VMF.INBOX_MESSAGE_SENT_TO_VMF',
    categories: [WorkflowTaskCategory.Internal],
  },
  [AccountGroupCreatedTrigger]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'business',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: AccountGroupCreatedTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.ACCOUNT_GROUP_CREATED.ACCOUNT_GROUP_CREATED',
    categories: [WorkflowTaskCategory.Accounts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.ACCOUNT_GROUP_CREATED.WHEN_ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.ACCOUNT_GROUP_CREATED.ACTIVITY',
  },
  [AccountGroupUpdatedTrigger]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'business',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: AccountGroupUpdatedTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.ACCOUNT_GROUP_UPDATED.ACCOUNT_GROUP_UPDATED',
    categories: [WorkflowTaskCategory.Accounts],
    panelAlert: 'AUTOMATIONS.EDITOR.TRIGGERS.ACCOUNT_GROUP_UPDATED.DISCLAIMER',
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.ACCOUNT_GROUP_UPDATED.WHEN_ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.ACCOUNT_GROUP_UPDATED.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.ACCOUNT_GROUP_UPDATED.WAIT_UNTIL',
  },
  [ManualAccountGroupTrigger]: {
    panel: ManuallyTriggerPanelComponent,
    actionDisplayService: ManuallyTriggerService,
    activityDisplayService: ManuallyTriggerService,
    icon: 'touch_app',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ManualAccountGroupTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.MANUAL_ACCOUNT_TRIGGER.TITLE',
    categories: [WorkflowTaskCategory.Manual],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.MANUAL_ACCOUNT_TRIGGER.DESCRIPTION_WITH_LINK',
  },
  [SalespersonAssignedToAccountGroupTrigger]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'account_box',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: SalespersonAssignedToAccountGroupTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.SALES_PERSON_ASSIGNED_TO_ACCOUNT_GROUP.SALES_PERSON_ASSIGNED_TO_ACCOUNT_GROUP',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.SALES_PERSON_ASSIGNED_TO_ACCOUNT_GROUP.WAIT_UNTIL',
    categories: [WorkflowTaskCategory.Sales],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.SALES_PERSON_ASSIGNED_TO_ACCOUNT_GROUP.WHEN_ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.SALES_PERSON_ASSIGNED_TO_ACCOUNT_GROUP.ACTIVITY',
  },
  [SmbEmailVerifiedTaskDefinitionId]: {
    panel: SmbUserVerifiedEmailPanelComponent,
    actionDisplayService: SmbUserVerifiedEmailService,
    activityDisplayService: SmbUserVerifiedEmailService,
    icon: 'contact_mail',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: SmbEmailVerifiedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.EMAIL_VERIFIED.TITLE',
    categories: [WorkflowTaskCategory.Users],
  },
  [ManualOrderTrigger]: {
    panel: ManuallyTriggerPanelComponent,
    actionDisplayService: ManuallyTriggerService,
    activityDisplayService: ManuallyTriggerService,
    icon: 'touch_app',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ManualOrderTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.MANUAL_ORDER_TRIGGER.TITLE',
    categories: [WorkflowTaskCategory.Manual],
  },
  [ManualCompanyTrigger]: {
    panel: ManuallyTriggerPanelComponent,
    actionDisplayService: ManuallyTriggerService,
    activityDisplayService: ManuallyTriggerService,
    icon: 'touch_app',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ManualCompanyTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.MANUAL_COMPANY_TRIGGER.TITLE',
    categories: [WorkflowTaskCategory.Manual],
  },
  [ManualContactTrigger]: {
    panel: ManuallyTriggerPanelComponent,
    actionDisplayService: ManuallyTriggerService,
    activityDisplayService: ManuallyTriggerService,
    icon: 'touch_app',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ManualContactTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.MANUAL_CONTACT_TRIGGER.TITLE',
    categories: [WorkflowTaskCategory.Manual],
  },
  'TaskDefinition-manual-api-account-trigger': {
    panel: ApiTriggerAccountGroupPanelComponent,
    actionDisplayService: ApiTriggerAccountGroupService,
    activityDisplayService: ApiTriggerAccountGroupService,
    icon: 'api',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-manual-api-account-trigger',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.API_ACCOUNT_TRIGGER.ACCOUNT_TITLE',
    categories: [WorkflowTaskCategory.Advanced],
    supportsCustomOutputParameters: true,
  },
  'TaskDefinition-manual-api-order-trigger': {
    panel: ApiTriggerOrderPanelComponent,
    actionDisplayService: ApiTriggerOrderService,
    activityDisplayService: ApiTriggerOrderService,
    icon: 'api',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-manual-api-order-trigger',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.API_ORDER_TRIGGER.TITLE',
    categories: [WorkflowTaskCategory.Advanced],
    supportsCustomOutputParameters: true,
  },
  'TaskDefinition-api-no-entity-trigger': {
    panel: ApiTriggerNoEntityPanelComponent,
    actionDisplayService: ApiTriggerNoEntityService,
    activityDisplayService: ApiTriggerNoEntityService,
    icon: 'webhook',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-api-no-entity-trigger',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.API_NO_ENTITY_TRIGGER.TITLE',
    categories: [WorkflowTaskCategory.Advanced],
    supportsCustomOutputParameters: true,
  },
  'TaskDefinition-zapier-trigger': {
    panel: ZapierTriggerPanelComponent,
    actionDisplayService: ZapierTriggerService,
    activityDisplayService: ZapierTriggerService,
    icon: 'webhook',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-zapier-trigger',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.ZAPIER_TRIGGER.TITLE',
    categories: [WorkflowTaskCategory.Advanced],
    supportsCustomOutputParameters: true,
  },
  'TaskDefinition-retail-payment-event': {
    panel: RetailPaymentPanelComponent,
    actionDisplayService: RetailPaymentService,
    activityDisplayService: RetailPaymentService,
    icon: 'payment',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-retail-payment-event',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.RETAIL_PAYMENT.RETAIL_PAYMENT',
    categories: [WorkflowTaskCategory.Billing],
  },
  'TaskDefinition-retail-payment-event-internal': {
    panel: RetailPaymentPanelComponent,
    actionDisplayService: RetailPaymentService,
    activityDisplayService: RetailPaymentService,
    icon: 'payment',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-retail-payment-event-internal',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.RETAIL_PAYMENT.RETAIL_PAYMENT',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-shopping-cart-updated-event': {
    panel: ShoppingCartUpdatedPanelComponent,
    activityDisplayService: ShoppingCartUpdatedService,
    actionDisplayService: ShoppingCartUpdatedService,
    icon: 'add_shopping_cart',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-shopping-cart-updated-event',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.SHOPPING_CART_UPDATED.SHOPPING_CART_UPDATED',
    categories: [WorkflowTaskCategory.Products],
  },
  'TaskDefinition-campaign-email-event': {
    panel: CampaignEmailPanelComponent,
    activityDisplayService: CampaignEmailService,
    actionDisplayService: CampaignEmailService,
    icon: 'mark_email_read',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-campaign-email-event',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CAMPAIGN_EMAIL.CAMPAIGN_EMAIL_NAME',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  'TaskDefinition-order-status-changes-event': {
    panel: OrderStatusChangedPanelComponent,
    activityDisplayService: OrderStatusChangedService,
    actionDisplayService: OrderStatusChangedService,
    icon: 'list_alt',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-order-status-changes-event',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.ORDER_STATUS_CHANGED.ORDER_STATUS_CHANGED',
    categories: [WorkflowTaskCategory.SalesOrders],
  },
  'TaskDefinition-internal-product-deactivation': {
    panel: ProductDeactivatedPanelComponent,
    actionDisplayService: ProductDeactivatedService,
    activityDisplayService: ProductDeactivatedService,
    icon: 'remove_shopping_cart',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-internal-product-deactivation',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.PRODUCT_ACTIVATION.PRODUCT_DEACTIVATED.PRODUCT_DEACTIVATED',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-legacy-hotness-snapshot-opened': {
    panel: NoRulesPanelComponent,
    actionDisplayService: SnapshotOpenedService,
    activityDisplayService: SnapshotOpenedService,
    icon: 'description',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-legacy-hotness-snapshot-opened',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.SNAPSHOT_OPENED.SNAPSHOT_OPENED',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-legacy-hotness-claim-account': {
    panel: NoRulesPanelComponent,
    actionDisplayService: AccountGroupUserClaimedService,
    activityDisplayService: AccountGroupUserClaimedService,
    icon: 'how_to_reg',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-legacy-hotness-claim-account',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CLAIM_ACCOUNT.CLAIM_ACCOUNT',
    categories: [WorkflowTaskCategory.Users],
  },
  'TaskDefinition-legacy-hotness-ask-question': {
    panel: AskQuestionPanelComponent,
    actionDisplayService: AskQuestionService,
    activityDisplayService: AskQuestionService,
    icon: 'help',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-legacy-hotness-ask-question',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CUSTOMER_ASKED_QUESTION.CUSTOMER_ASKED_QUESTION',
    categories: [WorkflowTaskCategory.Users],
  },
  'TaskDefinition-legacy-hotness-package-interest': {
    panel: PackageInterestPanelComponent,
    actionDisplayService: PackageInterestService,
    activityDisplayService: PackageInterestService,
    icon: 'monetization_on',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-legacy-hotness-package-interest',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CUSTOMER_VIEWS_PACKAGE.CUSTOMER_INTERESTED_IN_A_PACKAGE',
    categories: [WorkflowTaskCategory.Products],
  },
  // Triggered when a user is associated with an account group for the first time.
  'TaskDefinition-user-associated-with-account-group': {
    panel: NoRulesPanelComponent,
    actionDisplayService: UserAssociatedWithAccountGroupService,
    activityDisplayService: UserAssociatedWithAccountGroupService,
    icon: 'person_add',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-user-associated-with-account-group',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.USER_ASSOCIATION.NAME',
    categories: [WorkflowTaskCategory.Users],
  },
  // Triggered when a Task Manager fulfillment task's status changes.
  [FulfillmentTaskStatusChangedTrigger]: {
    panel: FulfillmentTaskStatusChangedPanelComponent,
    actionDisplayService: TaskStatusChangedService,
    activityDisplayService: TaskStatusChangedService,
    icon: 'assignment_turned_in',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-fulfillment-task-status-changes',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.TASK_STATUS_CHANGED.TASK_STATUS_TITLE',
    categories: [WorkflowTaskCategory.Fulfillment],
  },
  // Triggered when a Task Manager sales task's status changes.
  [SalesTaskStatusChangedTrigger]: {
    panel: SalesTaskStatusChangedPanelComponent,
    actionDisplayService: TaskStatusChangedService,
    activityDisplayService: TaskStatusChangedService,
    icon: 'assignment_turned_in',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-sales-task-status-changes',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.SALES_TASK_STATUS_CHANGED.TASK_STATUS_TITLE_V2',
    categories: [WorkflowTaskCategory.Sales],
  },
  // Triggered when a Task Manager fulfillment project's status changes for an account.
  [FulfillmentProjectStatusChangedTrigger]: {
    panel: FulfillmentProjectStatusChangedPanelComponent,
    actionDisplayService: TaskStatusChangedService,
    activityDisplayService: TaskStatusChangedService,
    icon: 'fact_check',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: FulfillmentProjectStatusChangedTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.FULFILLMENT_PROJECT_STATUS_CHANGED.PROJECT_STATUS_ACCOUNT_TITLE',
    categories: [WorkflowTaskCategory.Fulfillment],
  },
  // Triggered when a Task Manager fulfillment project's status changes for an order.
  [FulfillmentProjectOrderStatusChangedTrigger]: {
    panel: FulfillmentProjectStatusChangedPanelComponent,
    actionDisplayService: TaskStatusChangedService,
    activityDisplayService: TaskStatusChangedService,
    icon: 'fact_check',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: FulfillmentProjectOrderStatusChangedTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.FULFILLMENT_PROJECT_STATUS_CHANGED.PROJECT_STATUS_ORDER_TITLE',
    categories: [WorkflowTaskCategory.Fulfillment],
  },
  // Triggered when a wholesale purchase is created.
  'TaskDefinition-wholesale-purchase-created': {
    panel: WholesalePurchaseCreatedPanelComponent,
    actionDisplayService: WholesalePurchaseCreatedService,
    activityDisplayService: WholesalePurchaseCreatedService,
    icon: 'payment',
    iconColor: INTERNAL_ICON_COLOR,
    taskDefinitionId: 'TaskDefinition-wholesale-purchase-created',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.WHOLESALE_PURCHASE_CREATED.WHOLESALE_PURCHASE_CREATED',
    categories: [WorkflowTaskCategory.Internal],
  },
  // Triggered
  'TaskDefinition-successful-billing-purchase': {
    panel: BillingPurchaseSuccessPanelComponent,
    actionDisplayService: BillingPurchaseSuccessService,
    activityDisplayService: BillingPurchaseSuccessService,
    icon: 'payments',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-billing-purchase-created',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.BILLING_PURCHASE.PURCHASE_SUCCESSFUL',
    categories: [WorkflowTaskCategory.Billing],
  },
  'TaskDefinition-sales-order-expiring': {
    panel: SalesOrderExpiringPanelComponent,
    actionDisplayService: SalesOrderExpiringService,
    activityDisplayService: SalesOrderExpiringService,
    icon: 'business',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-sales-order-expiring',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.SALES_ORDER_EXPIRING.SALES_ORDER_EXPIRING',
    categories: [WorkflowTaskCategory.SalesOrders],
  },
  [CustomAccountDataUpdatedTrigger]: {
    panel: AccountCustomDataUpdatedPanelComponent,
    actionDisplayService: AccountCustomDataUpdatedService,
    activityDisplayService: AccountCustomDataUpdatedService,
    icon: 'business',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CustomAccountDataUpdatedTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CUSTOM_DATA_UPDATED.CUSTOM_DATA_UPDATED.ACCOUNT',
    categories: [WorkflowTaskCategory.Accounts],
  },
  'TaskDefinition-sales-activity-created': {
    panel: SalesActivityCreatedPanelComponent,
    actionDisplayService: SalesActivityCreatedService,
    activityDisplayService: SalesActivityCreatedService,
    icon: 'business',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-sales-activity-created',
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.SALES_ACTIVITY_CREATED.SALES_ACTIVITY_CREATED',
    categories: [WorkflowTaskCategory.Sales],
  },
  [CRMContactCreated]: {
    panel: CrmContactCreatedPanelComponent,
    actionDisplayService: CrmContactCreatedService,
    activityDisplayService: CrmContactCreatedService,
    icon: 'person',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMContactCreated,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_CREATED.NAME',
    categories: [WorkflowTaskCategory.Contacts],
  },
  [CRMContactCreatedOrModified]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'person',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMContactCreatedOrModified,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_CREATED_OR_MODIFIED.NAME',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_CREATED_OR_MODIFIED.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_CREATED_OR_MODIFIED.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_CREATED_OR_MODIFIED.WAIT_UNTIL',
    labelDisplay: 'CRM_FIELDS_DIALOG.CONTACT_FIELDS',
  },
  [ContactCreatedViaWebChat]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'question_answer',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ContactCreatedViaWebChat,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_CREATED_VIA_WEB_CHAT.NAME',
    categories: [WorkflowTaskCategory.Inbox],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_CREATED_VIA_WEB_CHAT.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_CREATED_VIA_WEB_CHAT.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_CREATED_VIA_WEB_CHAT.WAIT_UNTIL',
    panelAlert: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_CREATED_VIA_WEB_CHAT.DESCRIPTION',
  },
  [ContactFollowUpRequested]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'question_answer',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ContactFollowUpRequested,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_FOLLOW_UP_REQUESTED.NAME',
    categories: [WorkflowTaskCategory.Inbox],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_FOLLOW_UP_REQUESTED.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_FOLLOW_UP_REQUESTED.ACTIVITY',
    panelAlert: 'AUTOMATIONS.EDITOR.TRIGGERS.CONTACT_FOLLOW_UP_REQUESTED.DESCRIPTION',
  },
  [CRMCompanyCreated]: {
    panel: CrmCompanyCreatedPanelComponent,
    actionDisplayService: CrmCompanyCreatedService,
    activityDisplayService: CrmCompanyCreatedService,
    icon: 'location_city',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMCompanyCreated,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_CREATED.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_CREATED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_CREATED_OR_MODIFIED.ACTIVITY',
  },
  [CRMCompanyCreatedOrModified]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'location_city',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMCompanyCreatedOrModified,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_CREATED_OR_MODIFIED.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_CREATED_OR_MODIFIED.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_CREATED_OR_MODIFIED.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_CREATED_OR_MODIFIED.WAIT_UNTIL',
    labelDisplay: 'CRM_FIELDS_DIALOG.COMPANY_FIELDS',
  },
  [CRMCustomObjectCreatedOrModified]: {
    panel: CrmCustomObjectCreatedModifiedPanelComponent,
    actionDisplayService: CrmCustomObjectCreatedModifiedService,
    activityDisplayService: CrmCustomObjectCreatedModifiedService,
    icon: 'dashboard_customize',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMCustomObjectCreatedOrModified,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CUSTOM_OBJECT_CREATED_OR_MODIFIED.NAME',
    categories: [WorkflowTaskCategory.CustomObjects],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CUSTOM_OBJECT_CREATED_OR_MODIFIED.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CUSTOM_OBJECT_CREATED_OR_MODIFIED.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_OPPORTUNITY_CREATED_OR_MODIFIED.WAIT_UNTIL',
    labelDisplay: 'CRM_FIELDS_DIALOG.CUSTOM_OBJECT_FIELDS',
    featureFlag: CRM_CUSTOM_OBJECT_FEATURE_FLAG,
  },
  [CRMOpportunityCreatedOrModified]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'monetization_on',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMOpportunityCreatedOrModified,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_OPPORTUNITY_CREATED_OR_MODIFIED.NAME',
    categories: [WorkflowTaskCategory.Opportunities],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_OPPORTUNITY_CREATED_OR_MODIFIED.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_OPPORTUNITY_CREATED_OR_MODIFIED.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_OPPORTUNITY_CREATED_OR_MODIFIED.WAIT_UNTIL',
    labelDisplay: 'CRM_FIELDS_DIALOG.OPPORTUNITY_FIELDS',
  },
  [CRMTaskCreatedContact]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'assignment_turned_in',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMTaskCreatedContact,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_CREATED_CONTACT.NAME_V2',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_CREATED_CONTACT.ACTION_V2',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_CREATED_CONTACT.ACTIVITY_V2',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_CREATED_CONTACT.WAIT_UNTIL',
  },
  [CRMTaskOverdueContact]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'alarm',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMTaskOverdueContact,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_OVERDUE_CONTACT.NAME',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_OVERDUE_CONTACT.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_OVERDUE_CONTACT.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_OVERDUE_CONTACT.WAIT_UNTIL',
  },
  [CRMNoteCreatedContact]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'notes',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMNoteCreatedContact,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_NOTE_CREATED_CONTACT.NAME',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_NOTE_CREATED_CONTACT.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_NOTE_CREATED_CONTACT.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_NOTE_CREATED_CONTACT.WAIT_UNTIL',
  },
  [CRMNoteCreatedCompany]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'notes',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMNoteCreatedCompany,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_NOTE_CREATED_COMPANY.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_NOTE_CREATED_COMPANY.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_NOTE_CREATED_COMPANY.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_NOTE_CREATED_COMPANY.WAIT_UNTIL',
  },
  [CRMCallCreatedCompany]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'local_phone',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMCallCreatedCompany,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CALL_CREATED_COMPANY.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CALL_CREATED_COMPANY.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CALL_CREATED_COMPANY.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CALL_CREATED_COMPANY.WAIT_UNTIL',
  },
  [CRMCallCreatedContact]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'local_phone',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMCallCreatedContact,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CALL_CREATED_CONTACT.NAME',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CALL_CREATED_CONTACT.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CALL_CREATED_CONTACT.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CALL_CREATED_CONTACT.WAIT_UNTIL',
  },
  [CRMMeetingCreatedCompany]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'calendar_today',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMMeetingCreatedCompany,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_MEETING_CREATED_COMPANY.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_MEETING_CREATED_COMPANY.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_MEETING_CREATED_COMPANY.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_MEETING_CREATED_COMPANY.WAIT_UNTIL',
  },
  [CRMMeetingCreatedContact]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'calendar_today',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMMeetingCreatedContact,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_MEETING_CREATED_CONTACT.NAME',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_MEETING_CREATED_CONTACT.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_MEETING_CREATED_CONTACT.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_MEETING_CREATED_CONTACT.WAIT_UNTIL',
  },
  [CRMTaskCreatedCompany]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'assignment_turned_in',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMTaskCreatedCompany,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_CREATED_COMPANY.NAME_V2',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_CREATED_COMPANY.ACTION_V2',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_CREATED_COMPANY.ACTIVITY_v2',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_CREATED_COMPANY.WAIT_UNTIL',
  },
  [CRMTaskOverdueCompany]: {
    panel: CrmActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'alarm',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMTaskOverdueCompany,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_OVERDUE_COMPANY.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_OVERDUE_COMPANY.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_OVERDUE_COMPANY.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_TASK_OVERDUE_COMPANY.WAIT_UNTIL',
  },
  [CRMEmailCreatedContact]: {
    panel: CrmEmailActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'mark_email_read',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMEmailCreatedContact,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_EMAIL_CREATED_CONTACT.NAME',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_EMAIL_CREATED_CONTACT.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_EMAIL_CREATED_CONTACT.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_EMAIL_CREATED_CONTACT.WAIT_UNTIL',
  },
  [CRMEmailCreatedCompany]: {
    panel: CrmEmailActivityCreatedModifiedPanelComponent,
    actionDisplayService: CrmActivityCreatedModifiedDisplayService,
    activityDisplayService: CrmActivityCreatedModifiedDisplayService,
    icon: 'mark_email_read',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMEmailCreatedCompany,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_EMAIL_CREATED_COMPANY.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_EMAIL_CREATED_COMPANY.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_EMAIL_CREATED_COMPANY.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_EMAIL_CREATED_COMPANY.WAIT_UNTIL',
  },
  [CRMContactAddedToList]: {
    panel: CrmObjectListSelectorPanelComponent,
    actionDisplayService: CrmObjectListSelectorDisplayService,
    activityDisplayService: CrmObjectListSelectorDisplayService,
    icon: 'list',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMContactAddedToList,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_ADDED_TO_LIST.NAME',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_ADDED_TO_LIST.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_ADDED_TO_LIST.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_ADDED_TO_LIST.WAIT_UNTIL',
  },
  [CRMContactRemovedFromList]: {
    panel: CrmObjectListSelectorPanelComponent,
    actionDisplayService: CrmObjectListSelectorDisplayService,
    activityDisplayService: CrmObjectListSelectorDisplayService,
    icon: 'list',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMContactRemovedFromList,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_REMOVED_FROM_LIST.NAME',
    categories: [WorkflowTaskCategory.Contacts],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_REMOVED_FROM_LIST.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_REMOVED_FROM_LIST.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_REMOVED_FROM_LIST.WAIT_UNTIL',
  },
  [CRMCompanyAddedToList]: {
    panel: CrmObjectListSelectorPanelComponent,
    actionDisplayService: CrmObjectListSelectorDisplayService,
    activityDisplayService: CrmObjectListSelectorDisplayService,
    icon: 'list',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMCompanyAddedToList,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_ADDED_TO_LIST.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_ADDED_TO_LIST.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_ADDED_TO_LIST.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_ADDED_TO_LIST.WAIT_UNTIL',
  },
  [CRMCompanyRemovedFromList]: {
    panel: CrmObjectListSelectorPanelComponent,
    actionDisplayService: CrmObjectListSelectorDisplayService,
    activityDisplayService: CrmObjectListSelectorDisplayService,
    icon: 'list',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMCompanyRemovedFromList,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_REMOVED_FROM_LIST.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_REMOVED_FROM_LIST.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_REMOVED_FROM_LIST.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_REMOVED_FROM_LIST.WAIT_UNTIL',
  },
  [CRMContactFormSubmitted]: {
    panel: CrmObjectFormSelectorPanelComponent,
    actionDisplayService: CrmObjectFormSelectorDisplayService,
    activityDisplayService: CrmObjectFormSelectorDisplayService,
    icon: 'person',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMContactFormSubmitted,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.WAIT_UNTIL',
  },
  [CRMCompanyFormSubmitted]: {
    panel: CrmObjectFormSelectorPanelComponent,
    actionDisplayService: CrmObjectFormSelectorDisplayService,
    activityDisplayService: CrmObjectFormSelectorDisplayService,
    icon: 'location_city',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CRMCompanyFormSubmitted,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_FORM_SUBMITTED.NAME',
    categories: [WorkflowTaskCategory.Companies],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_FORM_SUBMITTED.ACTION',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_FORM_SUBMITTED.ACTIVITY',
    waitUntilNodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_FORM_SUBMITTED.WAIT_UNTIL',
  },
  'TaskDefinition-vendor-goo2-invoice': {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-vendor-goo2-invoice',
    name: 'Invoice from GOO2',
    categories: [WorkflowTaskCategory.Billing],
    nodeDisplay: 'Invoice updated by GOO2',
    activityDisplay: 'GOO2 updated an invoice',
    waitUntilNodeDisplay: 'Wait until GOO2 updates an invoice',
  },
  'TaskDefinition-vendor-goo2-extra': {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: 'TaskDefinition-vendor-goo2-extra',
    name: 'Extra trigger from GOO2',
    categories: [WorkflowTaskCategory.Billing],
    nodeDisplay: 'Extra trigger by GOO2',
    activityDisplay: 'GOO2 extra triggered this',
    waitUntilNodeDisplay: 'Wait until GOO2 extra triggers this',
  },
  [QBOInvoiceTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: QBOInvoiceTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.INVOICE_CREATED_MODIFIED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.INVOICE_CREATED_MODIFIED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.INVOICE_CREATED_MODIFIED',
  },
  [QBOSalesReceiptTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: QBOSalesReceiptTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.SALESRECEIPT_CREATED_MODIFIED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.SALESRECEIPT_CREATED_MODIFIED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.SALESRECEIPT_CREATED_MODIFIED',
  },
  [QBOPaymentTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: QBOPaymentTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.PAYMENT_CREATED_MODIFIED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.PAYMENT_CREATED_MODIFIED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QUICKBOOKS.PAYMENT_CREATED_MODIFIED',
  },
  [PetexecOrderCompleteTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: PetexecOrderCompleteTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PETEXEC.ORDER_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PETEXEC.ORDER_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PETEXEC.ORDER_COMPLETED',
  },
  [PawPartnerCustomerCheckoutTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: PawPartnerCustomerCheckoutTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PAWPARTNER.CUSTOMER_CHECKOUT',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PAWPARTNER.CUSTOMER_CHECKOUT',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PAWPARTNER.CUSTOMER_CHECKOUT',
  },
  [ShopMonkeyInvoicePaidTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ShopMonkeyInvoicePaidTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPMONKEY.INVOICE_PAID',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPMONKEY.INVOICE_PAID',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPMONKEY.INVOICE_PAID',
    featureFlag: SHOW_SHOPMONKEY_INTEGRATION,
  },
  [JobberVisitTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: JobberVisitTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBBER.JOBBER_VISIT_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBBER.JOBBER_VISIT_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBBER.JOBBER_VISIT_COMPLETED',
  },
  [JobberJobTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: JobberJobTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBBER.JOBBER_JOB_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBBER.JOBBER_JOB_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBBER.JOBBER_JOB_COMPLETED',
  },
  [HousecallProJobTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: HousecallProJobTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.HOUSECALLPRO.HOUSECALLPRO_JOB_FINISHED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.HOUSECALLPRO.HOUSECALLPRO_JOB_FINISHED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.HOUSECALLPRO.HOUSECALLPRO_JOB_FINISHED',
  },
  [GingrReservationCheckoutTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: GingrReservationCheckoutTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.GINGRAPP.RESERVATION_CHECKEDOUT',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.GINGRAPP.RESERVATION_CHECKEDOUT',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.GINGRAPP.RESERVATION_CHECKEDOUT',
  },
  [ShopwareRepairOrderInvoicePickedUpTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ShopwareRepairOrderInvoicePickedUpTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPWARE.REPAIR_ORDER_INVOICE_PICKEDUP',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPWARE.REPAIR_ORDER_INVOICE_PICKEDUP',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPWARE.REPAIR_ORDER_INVOICE_PICKEDUP',
    featureFlag: SHOW_SHOPWARE_INTEGRATION,
  },
  [ProtractorInvoicePostedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ProtractorInvoicePostedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PROTRACTOR.PROTRACTOR_INVOICE_POSTED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PROTRACTOR.PROTRACTOR_INVOICE_POSTED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PROTRACTOR.PROTRACTOR_INVOICE_POSTED',
  },
  [ConnectionChangedTrigger]: {
    panel: ConnectionChangedPanelComponent,
    actionDisplayService: ConnectionChangedService,
    activityDisplayService: ConnectionChangedService,
    icon: 'api',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ConnectionChangedTrigger,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.CONNECTION_CHANGED.NAME',
    categories: [WorkflowTaskCategory.Advanced],
  },
  [TekmetricRepairorderPostedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: TekmetricRepairorderPostedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.TEKMETRIC.TEKMETRIC_REPAIRORDER_POSTED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.TEKMETRIC.TEKMETRIC_REPAIRORDER_POSTED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.TEKMETRIC.TEKMETRIC_REPAIRORDER_POSTED',
    featureFlag: SHOW_TEKMETRIC_INTEGRATION,
  },

  [PawloyaltyAppointmentCheckedoutTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: PawloyaltyAppointmentCheckedoutTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PAWLOYALTY.PAWLOYALTY_APPOINTMENT_CHECKED_OUT',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PAWLOYALTY.PAWLOYALTY_APPOINTMENT_CHECKED_OUT',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PAWLOYALTY.PAWLOYALTY_APPOINTMENT_CHECKED_OUT',
  },

  [MindbodyVisitCompleteTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: MindbodyVisitCompleteTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.MINDBODY.MINDBODY_VISIT_COMPLETE',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.MINDBODY.MINDBODY_VISIT_COMPLETE',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.MINDBODY.MINDBODY_VISIT_COMPLETE',
    featureFlag: SHOW_MINDBODY_INTEGRATION,
  },

  [PetresortproInvoiceCheckoutTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: PetresortproInvoiceCheckoutTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PETRESORTPRO.PETRESORTPRO_INVOICE_CHECKOUT',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PETRESORTPRO.PETRESORTPRO_INVOICE_CHECKOUT',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.PETRESORTPRO.PETRESORTPRO_INVOICE_CHECKOUT',
  },

  [ServicemonsterInvoiceCreatedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ServicemonsterInvoiceCreatedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICEMONSTER.SERVICEMONSTER_INVOICE_CREATED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICEMONSTER.SERVICEMONSTER_INVOICE_CREATED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICEMONSTER.SERVICEMONSTER_INVOICE_CREATED',
  },

  [BroadlypartnerapiTransactionCompleteTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: BroadlypartnerapiTransactionCompleteTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.BROADLYPARTNERAPI.BROADLYPARTNERAPI_TRANSACTION_COMPLETE',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.BROADLYPARTNERAPI.BROADLYPARTNERAPI_TRANSACTION_COMPLETE',
    activityDisplay:
      'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.BROADLYPARTNERAPI.BROADLYPARTNERAPI_TRANSACTION_COMPLETE',
    featureFlag: SHOW_BROADLYPARTNERAPI_INTEGRATION,
  },

  [ShopbossRepairorderinvoiceClosedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ShopbossRepairorderinvoiceClosedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPBOSS.SHOPBOSS_REPAIRORDERINVOICE_CLOSED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPBOSS.SHOPBOSS_REPAIRORDERINVOICE_CLOSED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SHOPBOSS.SHOPBOSS_REPAIRORDERINVOICE_CLOSED',
  },

  [JobnimbusContactstatusChangedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: JobnimbusContactstatusChangedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBNIMBUS.JOBNIMBUS_CONTACTSTATUS_CHANGED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBNIMBUS.JOBNIMBUS_CONTACTSTATUS_CHANGED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBNIMBUS.JOBNIMBUS_CONTACTSTATUS_CHANGED',
    featureFlag: SHOW_JOBNIMBUS_INTEGRATION,
  },

  [JobnimbusJobstatusChangedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: JobnimbusJobstatusChangedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBNIMBUS.JOBNIMBUS_JOBSTATUS_CHANGED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBNIMBUS.JOBNIMBUS_JOBSTATUS_CHANGED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.JOBNIMBUS.JOBNIMBUS_JOBSTATUS_CHANGED',
    featureFlag: SHOW_JOBNIMBUS_INTEGRATION,
  },

  [CcconeVehicleoutCompleteTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: CcconeVehicleoutCompleteTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.CCC.CCCONE_VEHICLEOUT_COMPLETE',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.CCC.CCCONE_VEHICLEOUT_COMPLETE',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.CCC.CCCONE_VEHICLEOUT_COMPLETE',
    featureFlag: SHOW_CCC_INTEGRATION,
  },

  [ServicefusionJobClosedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ServicefusionJobClosedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICEFUSION.SERVICEFUSION_JOB_CLOSED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICEFUSION.SERVICEFUSION_JOB_CLOSED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICEFUSION.SERVICEFUSION_JOB_CLOSED',
  },

  [ServicetitanJobCompletedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ServicetitanJobCompletedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICETITAN.SERVICETITAN_JOB_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICETITAN.SERVICETITAN_JOB_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.SERVICETITAN.SERVICETITAN_JOB_COMPLETED',
    featureFlag: SHOW_SERVICETITAN_INTEGRATION,
  },

  [ClioMatterClosedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: ClioMatterClosedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.CLIO.CLIO_MATTER_CLOSED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.CLIO.CLIO_MATTER_CLOSED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.CLIO.CLIO_MATTER_CLOSED',
    featureFlag: SHOW_CLIO_INTEGRATION,
  },

  [FieldedgeWorkorderFinalizedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: FieldedgeWorkorderFinalizedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.FIELDEDGE.FIELDEDGE_WORKORDER_FINALIZED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.FIELDEDGE.FIELDEDGE_WORKORDER_FINALIZED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.FIELDEDGE.FIELDEDGE_WORKORDER_FINALIZED',
  },

  [LightspeedSaleCompletedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: LightspeedSaleCompletedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.LIGHTSPEED.LIGHTSPEED_SALE_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.LIGHTSPEED.LIGHTSPEED_SALE_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.LIGHTSPEED.LIGHTSPEED_SALE_COMPLETED',
  },

  [RbcsSalesorserviceCompletedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: RbcsSalesorserviceCompletedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.RBCONTROLSYSTEMS.RBCS_SALESORSERVICE_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.RBCONTROLSYSTEMS.RBCS_SALESORSERVICE_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.RBCONTROLSYSTEMS.RBCS_SALESORSERVICE_COMPLETED',
  },

  [FtpUploadTriggerTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: FtpUploadTriggerTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.FTP.FTP_UPLOAD_TRIGGER',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.FTP.FTP_UPLOAD_TRIGGER',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.FTP.FTP_UPLOAD_TRIGGER',
    featureFlag: SHOW_FTP_INTEGRATION,
  },

  [QbdInvoiceorsalesreceiptUpdatedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: QbdInvoiceorsalesreceiptUpdatedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QBD.QBD_INVOICEORSALESRECEIPT_UPDATED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QBD.QBD_INVOICEORSALESRECEIPT_UPDATED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.QBD.QBD_INVOICEORSALESRECEIPT_UPDATED',
    featureFlag: SHOW_QBD_INTEGRATION,
  },

  [NapatracsRepairorderCompletedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: NapatracsRepairorderCompletedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.NAPATRACS.NAPATRACS_REPAIRORDER_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.NAPATRACS.NAPATRACS_REPAIRORDER_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.NAPATRACS.NAPATRACS_REPAIRORDER_COMPLETED',
    featureFlag: SHOW_NAPATRACS_INTEGRATION,
  },

  [NapatracsenterpriseRepairorderCompletedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: NapatracsenterpriseRepairorderCompletedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.NAPATRACSENTERPRISE.NAPATRACSENTERPRISE_REPAIRORDER_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay:
      'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.NAPATRACSENTERPRISE.NAPATRACSENTERPRISE_REPAIRORDER_COMPLETED',
    activityDisplay:
      'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.NAPATRACSENTERPRISE.NAPATRACSENTERPRISE_REPAIRORDER_COMPLETED',
    featureFlag: SHOW_NAPATRACSENTERPRISE_INTEGRATION,
  },

  [RowriterRepairorderCompletedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: RowriterRepairorderCompletedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.ROWRITER.ROWRITER_REPAIRORDER_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.ROWRITER.ROWRITER_REPAIRORDER_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.ROWRITER.ROWRITER_REPAIRORDER_COMPLETED',
    featureFlag: SHOW_ROWRITER_INTEGRATION,
  },

  [DentrixAppointmentCompletedTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: DentrixAppointmentCompletedTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.DENTRIX.DENTRIX_APPOINTMENT_COMPLETED',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.DENTRIX.DENTRIX_APPOINTMENT_COMPLETED',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.DENTRIX.DENTRIX_APPOINTMENT_COMPLETED',
    featureFlag: SHOW_DENTRIX_INTEGRATION,
  },

  [MitchellRepairorderCompleteTaskDefinitionId]: {
    panel: NoRulesPanelComponent,
    actionDisplayService: NoRulesDisplay,
    activityDisplayService: NoRulesDisplay,
    icon: 'attach_money',
    iconColor: DEFAULT_TRIGGER_COLOR,
    taskDefinitionId: MitchellRepairorderCompleteTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.MITCHELLMANAGER.MITCHELL_REPAIRORDER_COMPLETE',
    categories: [WorkflowTaskCategory.Integrations],
    nodeDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.MITCHELLMANAGER.MITCHELL_REPAIRORDER_COMPLETE',
    activityDisplay: 'AUTOMATIONS.EDITOR.TRIGGERS.INTEGRATIONS.MITCHELLMANAGER.MITCHELL_REPAIRORDER_COMPLETE',
    featureFlag: SHOW_MITCHELLMANAGER_INTEGRATION,
  },
};
