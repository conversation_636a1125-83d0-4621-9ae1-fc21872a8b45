<div class="email-activity-table">
  <glxy-page>
    <glxy-page-toolbar>
      <glxy-page-title>{{ 'EMAIL_ACTIVITY.TITLE' | translate }}</glxy-page-title>
    </glxy-page-toolbar>
    <div>
      <glxy-table-container
        [dataSource]="dataSource"
        [columns]="columns"
        [pageSizeOptions]="[5, 10, 20]"
        [fullWidth]="true"
      >
        <va-filter2-model
          [filterGroup]="filterGroup"
          [isLoading]="dataSource.loading$ | async"
          [defaultSidebarState]="sidebarState"
        >
          <va-filter-content>
            <table mat-table>
              <tr mat-header-row *matHeaderRowDef="[]"></tr>

              <ng-container matColumnDef="eventType">
                <th mat-header-cell *matHeaderCellDef>{{ 'EMAIL_ACTIVITY.EVENT_TYPE' | translate }}</th>
                <td mat-cell *matCellDef="let element" class="narrow-cell">
                  {{ getDisplayTypeFromEnum(element.eventType) }}
                </td>
              </ng-container>
              <ng-container matColumnDef="eventTime">
                <th mat-header-cell *matHeaderCellDef>{{ 'EMAIL_ACTIVITY.EVENT_TIME' | translate }}</th>
                <td mat-cell *matCellDef="let element" class="narrow-cell">
                  {{ element.eventTime | glxyDate: dateFormat }}
                </td>
              </ng-container>

              <tr mat-row *matRowDef="let row; columns: []"></tr>
            </table>
            <glxy-empty-state
              *ngIf="dataSource.state.totalDataMembers === 0 && !dataSource.state.loading"
              [size]="'small'"
            >
              <glxy-empty-state-hero>
                <mat-icon>done_all</mat-icon>
              </glxy-empty-state-hero>
              <p>{{ 'EMAIL_ACTIVITY.NO_EVENTS' | translate }}</p>
            </glxy-empty-state>
          </va-filter-content>
        </va-filter2-model>
      </glxy-table-container>
    </div>
  </glxy-page>
</div>
