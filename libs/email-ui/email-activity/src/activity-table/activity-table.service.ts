import { Params } from '@angular/router';
import { EmailEventService, EventType, SenderType } from '@vendasta/email';
import { EmailEvent } from '@vendasta/email/lib/_internal/objects/email-event';
import { PagedListRequestInterface, PagedResponseInterface, PaginatedAPIInterface } from '@vendasta/galaxy/table';
import { Observable, combineLatest } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { getSenderTypeFromNamespace } from '../email-stats/utils';

interface ParameterServiceInterface {
  namespace$: Observable<string>;
  url: Params;
  senderType$: Observable<SenderType>;
}

export class ParameterService implements ParameterServiceInterface {
  readonly namespace$: Observable<string>;
  readonly url: Params;
  readonly senderType$: Observable<SenderType>;

  constructor(namespaceID$: Observable<string>, url: Params) {
    this.namespace$ = namespaceID$;
    this.url = url;
    this.senderType$ = namespaceID$.pipe(map((namespace: string) => getSenderTypeFromNamespace(namespace)));
  }
}

export class ActivityTableDataService implements PaginatedAPIInterface<EmailEvent> {
  constructor(
    private parameterService: ParameterServiceInterface,
    private emailEventService: EmailEventService,
    private filters$: Observable<EventType[]>,
  ) {}

  get(r: PagedListRequestInterface): Observable<PagedResponseInterface<EmailEvent>> {
    return combineLatest([this.parameterService.namespace$, this.parameterService.senderType$, this.filters$]).pipe(
      switchMap(([namespace, senderType, filters]) => {
        return this.emailEventService
          .listEventsForAttribute(
            {
              type: senderType,
              id: namespace,
            },
            {
              key: this.parameterService.url.attributeKey,
              value: this.parameterService.url.attributeValue,
            },
            filters,
            r.pagingOptions.cursor,
            r.pagingOptions.pageSize,
          )
          .pipe(
            map((r) => {
              return {
                data: r?.events || [],
                pagingMetadata: {
                  nextCursor: r?.cursor || '',
                  hasMore: r?.hasMore || false,
                },
              };
            }),
          );
      }),
      catchError(() => []),
    );
  }
}
