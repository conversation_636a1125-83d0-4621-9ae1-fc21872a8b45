import { Component, Inject, OnInit, OnDestroy } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { EmailEventService, EventType } from '@vendasta/email';
import { ActivatedRoute } from '@angular/router';
import { GalaxyDataSource, GalaxyTableModule } from '@vendasta/galaxy/table';
import { EmailEvent } from '@vendasta/email/lib/_internal/objects/email-event';
import { GalaxyColumnDef } from '@vendasta/galaxy/table/src/table.interface';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';
import { ActivityTableDataService, ParameterService } from './activity-table.service';
import { CheckboxFilterControl, FilterGroup, FilterModule, SidebarState } from '@vendasta/va-filter2';
import { CommonModule } from '@angular/common';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { shareReplay, startWith, map } from 'rxjs/operators';
import { SENDER_ID_TOKEN } from '../../../email-activity/src/dependencies';

@Component({
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyPageModule,
    GalaxyTableModule,
    GalaxyEmptyStateModule,
    FilterModule,
    MatIconModule,
    MatTableModule,
    GalaxyPipesModule,
  ],
  providers: [EmailEventService],
  templateUrl: './activity-table.component.html',
  styleUrls: ['./activity-table.component.scss'],
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
})
export class ActivityTableComponent implements OnInit, OnDestroy {
  dataSource: GalaxyDataSource<EmailEvent>;
  readonly columns: GalaxyColumnDef[] = [
    {
      id: 'eventType',
      title: this.translateService.instant('EMAIL_ACTIVITY.EVENT_TYPE'),
    },
    {
      id: 'eventTime',
      title: this.translateService.instant('EMAIL_ACTIVITY.EVENT_TIME'),
    },
  ];
  readonly dateFormat = DateFormat.medium;
  readonly sidebarState = SidebarState.OPEN;
  filterGroup: FilterGroup;
  filters$: Observable<EventType[]>;
  private subscriptions: Subscription[] = [];

  constructor(
    private readonly translateService: TranslateService,
    @Inject(SENDER_ID_TOKEN) readonly senderId$: Observable<string>,
    private emailEventService: EmailEventService,
    private route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.filterGroup = new FilterGroup('').addSection(
      'Event type',
      this.getEventTypeList().map((eventType) => {
        return new CheckboxFilterControl(eventType, eventType, false, {
          appliedValueMapper: (name: string) => ({ name, label: name }),
        });
      }),
    );
    this.subscriptions.push(
      this.route.queryParams.subscribe((params) => {
        this.filterGroup.initValue({
          [params.eventType]: true,
        });
      }),
    );

    this.filters$ = this.filterGroup.valueChanges.pipe(
      startWith(this.filterGroup.value),
      map((group) => {
        const filterTypes = [];
        if (group.Processed) {
          filterTypes.push(EventType.PROCESSED);
        }
        if (group.Delivered) {
          filterTypes.push(EventType.DELIVERED);
        }
        if (group.Opened) {
          filterTypes.push(EventType.OPENED);
        }
        if (group.Clicked) {
          filterTypes.push(EventType.CLICKED);
        }
        if (group.Bounced) {
          filterTypes.push(EventType.BOUNCED);
        }
        if (group.Deferred) {
          filterTypes.push(EventType.DEFERRED);
        }
        if (group.Dropped) {
          filterTypes.push(EventType.DROPPED);
        }
        if (group.Spamreport) {
          filterTypes.push(EventType.SPAMREPORT);
        }
        if (group.Unsubscribed) {
          filterTypes.push(EventType.UNSUBSCRIBED);
        }
        if (group.Resubscribed) {
          filterTypes.push(EventType.RESUBSCRIBED);
        }
        return filterTypes;
      }),
      shareReplay(1),
    );
    const paramsService = new ParameterService(this.senderId$, this.route.snapshot.params);
    const service = new ActivityTableDataService(paramsService, this.emailEventService, this.filters$);
    this.dataSource = new GalaxyDataSource<EmailEvent>(service);
  }

  getEventTypeList(): string[] {
    return Object.keys(EventType)
      .reduce((arr, key) => {
        if (!arr.includes(key)) {
          arr.push(EventType[key]);
        }
        return arr;
      }, [])
      .map((eventType) => eventType.charAt(0) + eventType.slice(1).toLowerCase());
  }

  getDisplayTypeFromEnum(value: EventType): string {
    switch (value) {
      case EventType.PROCESSED:
        return 'Processed';
      case EventType.DELIVERED:
        return 'Delivered';
      case EventType.OPENED:
        return 'Opened';
      case EventType.CLICKED:
        return 'Clicked';
      case EventType.BOUNCED:
        return 'Bounced';
      case EventType.DEFERRED:
        return 'Deferred';
      case EventType.DROPPED:
        return 'Dropped';
      case EventType.SPAMREPORT:
        return 'Spamreport';
      case EventType.UNSUBSCRIBED:
        return 'Unsubscribed';
      case EventType.RESUBSCRIBED:
        return 'Resubscribed';
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
