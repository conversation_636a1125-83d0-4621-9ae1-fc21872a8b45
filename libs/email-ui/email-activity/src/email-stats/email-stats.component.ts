import { Component, Inject, Input, OnInit } from '@angular/core';
import {
  AttributeInterface,
  EmailEventService,
  EventType,
  GetAttributeEventStatsResponseEventStats,
  SenderType,
} from '@vendasta/email';
import { distinctUntilChanged, map, startWith, switchMap } from 'rxjs/operators';
import { createEmailStatsData, DisplayLinkStats, EmailStatsData } from './email-stats';
import { Observable } from 'rxjs';
import { formatPercentage } from './utils';
import { SENDER_ID_TOKEN } from '../dependencies';

@Component({
  selector: 'email-stats',
  templateUrl: './email-stats.component.html',
  standalone: false,
})
export class EmailStatsComponent implements OnInit {
  @Input() attribute: Partial<AttributeInterface>;
  @Input() backUrl: string;
  @Input() senderType: SenderType = SenderType.SENDER_TYPE_PARTNER;
  statsData$: Observable<EmailStatsData>;
  linkActivity$: Observable<DisplayLinkStats[]>;

  campaignActivityTableUrl$: Observable<string>;

  constructor(
    @Inject(SENDER_ID_TOKEN) readonly senderId$: Observable<string>,
    private emailEventService: EmailEventService,
  ) {
    this.campaignActivityTableUrl$ = this.getEmailActivityUrl();
  }

  ngOnInit(): void {
    this.statsData$ = this.senderId$.pipe(
      switchMap((pid) =>
        this.emailEventService.getAttributeEventStats(
          {
            type: this.senderType,
            id: pid,
          },
          {
            key: this.attribute.key,
            value: this.attribute.value,
          },
        ),
      ),
      map((r) => this.convertEventStatsToEmailStatsData(r?.data)),
      startWith(createEmailStatsData({})),
    );

    this.linkActivity$ = this.senderId$.pipe(
      switchMap((senderId) => {
        const senderType = senderId.startsWith('AG') ? SenderType.SENDER_TYPE_BUSINESS : SenderType.SENDER_TYPE_PARTNER;
        return this.emailEventService.getAttributeEventLinkStats(
          {
            type: senderType,
            id: senderId,
          },
          {
            key: this.attribute.key,
            value: this.attribute.value,
          },
        );
      }),
      map((resp) => {
        return (resp?.data || []).map((stat) => {
          return {
            url: stat.url,
            displayUrl: stat.url?.replace('{{', '').replace('}}', '').trim(),
            totalClicks: stat.totalClicks,
            uniqueClicks: stat.uniqueClicks,
            displayClickRate: stat.clickRate ? formatPercentage(stat.clickRate) : '0%',
          };
        });
      }),
    );
  }

  convertEventStatsToEmailStatsData(data: GetAttributeEventStatsResponseEventStats[]): EmailStatsData {
    const statsData = createEmailStatsData({});
    if (data?.length > 0) {
      for (const d of data) {
        switch (d.eventType) {
          case EventType.PROCESSED:
            statsData.processed = d.count;
            break;
          case EventType.DELIVERED:
            statsData.delivered = d.count;
            break;
          case EventType.OPENED:
            statsData.opened = d.count;
            statsData.unique_opened = d.uniqueCount;
            break;
          case EventType.CLICKED:
            statsData.clicked = d.count;
            statsData.unique_clicked = d.uniqueCount;
            break;
          case EventType.BOUNCED:
            statsData.bounced = d.count;
            break;
          case EventType.DEFERRED:
            statsData.deferred = d.count;
            break;
          case EventType.DROPPED:
            statsData.dropped = d.count;
            break;
          case EventType.SPAMREPORT:
            statsData.spamreport = d.count;
            break;
          case EventType.UNSUBSCRIBED:
            statsData.unsubscribed = d.count;
            break;
          case EventType.RESUBSCRIBED:
            statsData.resubscribed = d.count;
            break;
        }
      }
    }
    return statsData;
  }

  getEmailActivityUrl(): Observable<string> {
    return this.senderId$.pipe(
      distinctUntilChanged(),
      map((senderId: string) => {
        if (this.senderType === SenderType.SENDER_TYPE_BUSINESS) {
          return `/account/location/${senderId}/email-activity`;
        } else {
          return `/email-activity`;
        }
      }),
    );
  }
}
