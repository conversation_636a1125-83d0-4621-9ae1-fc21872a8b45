import { Component, Input, OnChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { EmailStatsData } from '../email-stats';
import { AttributeInterface } from '@vendasta/email';
import { formatTranslation, getPercentage } from '../utils';

@Component({
  selector: 'email-performance',
  templateUrl: './email-performance.component.html',
  styles: [
    `
      glxy-statistic-value {
        font-size: 24px;
      }
    `,
  ],
  standalone: false,
})
export class EmailPerformanceComponent implements OnChanges {
  @Input() attribute: Partial<AttributeInterface>;
  @Input() stats: EmailStatsData;
  @Input() emailActivityUrl: string;

  openRateTooltip: string;
  ctrTooltip: string;
  openedTooltip: string;
  clickedTooltip: string;

  openRate = '-';
  clickThroughRate = '-';
  opened = 0;
  clicked = 0;

  constructor(private translate: TranslateService) {}

  ngOnChanges(): void {
    this.openRate = getPercentage(this.stats?.unique_opened || 0, this.stats?.delivered || 0);
    this.clickThroughRate = getPercentage(this.stats?.unique_clicked || 0, this.stats?.opened || 0);
    this.opened = this.stats?.opened || 0;
    this.clicked = this.stats?.clicked || 0;
    this.updateTooltips(this.stats);
  }

  updateTooltips(stats: EmailStatsData): void {
    const opened = stats?.opened || 0;
    const clicked = stats?.clicked || 0;
    const unique_opened = stats?.unique_opened || 0;
    const unique_clicked = stats?.unique_clicked || 0;
    this.openRateTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.OPEN_RATE_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.OPEN_RATE_TOOLTIP', {
        opened: unique_opened,
      }),
      unique_opened,
    );
    this.ctrTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.CLICK_RATE_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.CLICK_RATE_TOOLTIP', {
        clickedThrough: unique_clicked,
      }),
      unique_clicked,
    );
    this.openedTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.OPENED_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.OPENED_TOOLTIP', {
        opened: opened,
      }),
      opened,
    );
    this.clickedTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.CLICKED_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.CLICKED_TOOLTIP', {
        clicked: clicked,
      }),
      clicked,
    );
  }
}
