<h3>{{ 'EMAIL_STATS.EMAIL_PERFORMANCE' | translate }}</h3>
<mat-card>
  <mat-card-content>
    <mat-grid-list cols="2" rowHeight="4rem">
      <mat-grid-tile>
        <glxy-statistic>
          <glxy-statistic-title [matTooltip]="openRateTooltip">
            {{ 'EMAIL_STATS.OPENED' | translate }}
          </glxy-statistic-title>
          <glxy-statistic-value>{{ this.openRate }}</glxy-statistic-value>
        </glxy-statistic>
      </mat-grid-tile>
      <mat-grid-tile>
        <glxy-statistic>
          <glxy-statistic-title [matTooltip]="ctrTooltip">
            {{ 'EMAIL_STATS.CTOR' | translate }}
          </glxy-statistic-title>
          <glxy-statistic-value>{{ this.clickThroughRate }}</glxy-statistic-value>
        </glxy-statistic>
      </mat-grid-tile>
      <mat-grid-tile>
        <glxy-statistic>
          <glxy-statistic-title [matTooltip]="openedTooltip">
            {{ 'EMAIL_STATS.OPEN_RATE' | translate }}
          </glxy-statistic-title>
          <glxy-statistic-value>
            <a
              [routerLink]="[emailActivityUrl, attribute.key, attribute.value]"
              [queryParams]="{ eventType: 'Opened' }"
            >
              {{ this.opened }}
            </a>
          </glxy-statistic-value>
        </glxy-statistic>
      </mat-grid-tile>
      <mat-grid-tile>
        <glxy-statistic>
          <glxy-statistic-title [matTooltip]="clickedTooltip">
            {{ 'EMAIL_STATS.CLICKED' | translate }}
          </glxy-statistic-title>
          <glxy-statistic-value>
            <a
              [routerLink]="[emailActivityUrl, attribute.key, attribute.value]"
              [queryParams]="{ eventType: 'Clicked' }"
            >
              {{ this.clicked }}
            </a>
          </glxy-statistic-value>
        </glxy-statistic>
      </mat-grid-tile>
    </mat-grid-list>
  </mat-card-content>
</mat-card>
