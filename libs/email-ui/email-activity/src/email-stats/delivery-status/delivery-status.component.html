<h3>{{ 'EMAIL_STATS.DELIVERY_STATUS' | translate }}</h3>
<mat-card class="spacer">
  <mat-grid-list cols="3" rowHeight="4rem">
    <mat-grid-tile>
      <glxy-statistic>
        <glxy-statistic-title [matTooltip]="this.deliveredTooltip">
          {{ 'EMAIL_STATS.DELIVERED' | translate }}
        </glxy-statistic-title>
        <glxy-statistic-value>{{ this.delivered }}</glxy-statistic-value>
      </glxy-statistic>
    </mat-grid-tile>
    <mat-grid-tile>
      <glxy-statistic>
        <glxy-statistic-title [matTooltip]="this.unsubscribedTooltip">
          {{ 'EMAIL_STATS.UNSUBSCRIBED' | translate }}
        </glxy-statistic-title>
        <glxy-statistic-value>
          <a
            [routerLink]="[emailActivityUrl, attribute.key, attribute.value]"
            [queryParams]="{ eventType: 'Unsubscribed' }"
          >
            {{ this.unsubscribed }}
          </a>
        </glxy-statistic-value>
      </glxy-statistic>
    </mat-grid-tile>
    <mat-grid-tile>
      <glxy-statistic>
        <glxy-statistic-title [matTooltip]="this.spamreportTooltip">
          {{ 'EMAIL_STATS.SPAM' | translate }}
        </glxy-statistic-title>
        <glxy-statistic-value>
          <a
            [routerLink]="[emailActivityUrl, attribute.key, attribute.value]"
            [queryParams]="{ eventType: 'Spamreport' }"
          >
            {{ this.spamreport }}
          </a>
        </glxy-statistic-value>
      </glxy-statistic>
    </mat-grid-tile>
  </mat-grid-list>
</mat-card>
<mat-card>
  <mat-grid-list cols="2" rowHeight="4rem">
    <mat-grid-tile>
      <glxy-statistic>
        <glxy-statistic-title [matTooltip]="this.bouncedTooltip">
          {{ 'EMAIL_STATS.BOUNCED' | translate }}
        </glxy-statistic-title>
        <glxy-statistic-value>
          <a [routerLink]="[emailActivityUrl, attribute.key, attribute.value]" [queryParams]="{ eventType: 'Bounced' }">
            {{ this.bounced }}
          </a>
        </glxy-statistic-value>
      </glxy-statistic>
    </mat-grid-tile>
    <mat-grid-tile>
      <glxy-statistic>
        <glxy-statistic-title [matTooltip]="this.droppedTooltip">
          {{ 'EMAIL_STATS.DROPPED' | translate }}
        </glxy-statistic-title>
        <glxy-statistic-value>
          <a [routerLink]="[emailActivityUrl, attribute.key, attribute.value]" [queryParams]="{ eventType: 'Dropped' }">
            {{ this.dropped }}
          </a>
        </glxy-statistic-value>
      </glxy-statistic>
    </mat-grid-tile>
  </mat-grid-list>
</mat-card>
