import { Component, Input, OnChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { EmailStatsData } from '../email-stats';
import { AttributeInterface } from '@vendasta/email';
import { formatTranslation } from '../../../src/email-stats/utils';

@Component({
  selector: 'email-delivery-status',
  templateUrl: './delivery-status.component.html',
  styles: [
    `
      glxy-statistic-value {
        font-size: 24px;
      }

      .spacer {
        margin-bottom: 24px;
      }
    `,
  ],
  standalone: false,
})
export class DeliveryStatusComponent implements OnChanges {
  @Input() attribute: Partial<AttributeInterface>;
  @Input() stats: EmailStatsData;
  @Input() emailActivityUrl: string;

  deliveredTooltip: string;
  bouncedTooltip: string;
  spamreportTooltip: string;
  unsubscribedTooltip: string;
  droppedTooltip: string;

  delivered = 0;
  bounced = 0;
  unsubscribed = 0;
  spamreport = 0;
  dropped = 0;

  constructor(private translate: TranslateService) {}

  ngOnChanges(): void {
    this.delivered = this.stats.delivered;
    this.bounced = this.stats.bounced;
    this.unsubscribed = this.stats.unsubscribed;
    this.spamreport = this.stats.spamreport;
    this.dropped = this.stats.dropped;
    this.updateTooltips(this.stats);
  }

  updateTooltips(stats: EmailStatsData): void {
    const delivered = stats?.delivered || 0;
    const unsubscribed = stats?.unsubscribed || 0;
    const dropped = stats?.dropped || 0;
    const bounced = stats?.bounced || 0;
    const spamreport = stats?.spamreport || 0;
    this.deliveredTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.DELIVERED_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.DELIVERED_TOOLTIP', {
        delivered: delivered,
      }),
      delivered,
    );
    this.bouncedTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.BOUNCED_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.BOUNCED_TOOLTIP', {
        bounced: bounced,
      }),
      bounced,
    );
    this.spamreportTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.SPAM_REPORT_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.SPAM_REPORT_TOOLTIP', {
        spamReport: spamreport,
      }),
      spamreport,
    );
    this.unsubscribedTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.UNSUBSCRIBED_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.UNSUBSCRIBED_TOOLTIP', {
        unsubscribed: unsubscribed,
      }),
      unsubscribed,
    );
    this.droppedTooltip = formatTranslation(
      this.translate.instant('EMAIL_STATS.DROPPED_TOOLTIP_SINGLE'),
      this.translate.instant('EMAIL_STATS.DROPPED_TOOLTIP', {
        dropped: dropped,
      }),
      dropped,
    );
  }
}
