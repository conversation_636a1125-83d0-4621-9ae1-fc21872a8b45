import { SenderType } from '@vendasta/email';

export function formatTranslation(
  singularTranslation: string,
  pluralTranslation: string,
  comparisonValue: number,
): string {
  return comparisonValue === 1 ? singularTranslation : pluralTranslation;
}

export function getPercentage(numerator = 0, denominator = 0): string {
  let formattedValue = '-';
  if (denominator) {
    // convert to a percentage then round to 1 decimal point
    const percentage = Math.round((numerator / denominator) * 100 * 10) / 10;
    formattedValue = percentage % 1 === 0 ? percentage.toFixed(0) + '%' : percentage.toFixed(1) + '%';
  }
  return formattedValue;
}

export function formatPercentage(ratio: number): string {
  const percent = ratio * 100.0;
  if (percent <= 0.5) {
    return '<1%';
  } else if (percent <= 1.5) {
    return '1%';
  } else {
    return `${Math.round(percent)}%`;
  }
}

export function getSenderTypeFromNamespace(namespace: string): SenderType {
  if (!namespace) {
    return SenderType.SENDER_TYPE_INVALID;
  }
  if (namespace.startsWith('AG-')) {
    return SenderType.SENDER_TYPE_BUSINESS;
  }
  return SenderType.SENDER_TYPE_PARTNER;
}
